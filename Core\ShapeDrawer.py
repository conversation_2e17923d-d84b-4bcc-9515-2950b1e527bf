import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon
import pyproj
import os

class ShapeDrawer:
    def __init__(self, file_path, crs):
        """
        初始化Shape文件绘制器
        :param file_path: Shape文件路径
        :param crs: 坐标系统（EPSG代码）
        """
        self.file_path = file_path
        self.crs = crs
        self.points = []
        
        # 创建或加载GeoDataFrame
        try:
            self.gdf = gpd.read_file(file_path)
            # 确保坐标系统正确
            if self.gdf.crs is None or str(self.gdf.crs) != str(crs):
                self.gdf.set_crs(crs, inplace=True, allow_override=True)
        except:
            self.gdf = gpd.GeoDataFrame(columns=['geometry'], geometry='geometry')
            self.gdf.set_crs(crs, inplace=True, allow_override=True)
            self.gdf.to_file(file_path)
    
    def add_point(self, lat, lng):
        """
        添加一个点到当前多边形
        :param lat: 纬度
        :param lng: 经度
        """
        self.points.append((lng, lat))  # GeoJSON格式使用(经度,纬度)顺序
        return len(self.points)
    
    def complete_polygon(self):
        """完成多边形绘制并保存"""
        try:
            if len(self.points) < 3:
                return False
                
            # 创建新的多边形
            new_polygon = Polygon(self.points)
            
            # 如果文件已存在，读取现有数据
            if os.path.exists(self.file_path):
                gdf = gpd.read_file(self.file_path)
                
                # 创建包含新多边形的GeoDataFrame
                new_row = gpd.GeoDataFrame({'geometry': [new_polygon]}, crs="EPSG:4326")
                
                # 如果需要，转换坐标系统
                if self.crs != "EPSG:4326":
                    if self.crs.startswith("+proj="):
                        new_row.set_crs("EPSG:4326", inplace=True)
                        new_row = new_row.to_crs(self.crs)
                    else:
                        new_row = new_row.to_crs(self.crs)
                
                # 添加新的多边形到现有数据
                gdf = gpd.GeoDataFrame(pd.concat([gdf, new_row], ignore_index=True))
            else:
                # 创建新的GeoDataFrame
                gdf = gpd.GeoDataFrame({'geometry': [new_polygon]}, crs="EPSG:4326")
                
                # 如果需要，转换坐标系统
                if self.crs != "EPSG:4326":
                    if self.crs.startswith("+proj="):
                        gdf.set_crs("EPSG:4326", inplace=True)
                        gdf = gdf.to_crs(self.crs)
                    else:
                        gdf = gdf.to_crs(self.crs)
            
            # 保存到文件
            gdf.to_file(self.file_path)
            
            # 清空点列表，为下一个多边形做准备
            self.points = []
            
            return True
            
        except Exception as e:
            print(f"保存多边形时出错: {str(e)}")
            return False
    
    def cancel_drawing(self):
        """
        取消当前多边形的绘制
        """
        self.points = []
    
    def get_current_points(self):
        """
        获取当前多边形的所有点
        :return: 点列表
        """
        return self.points

def transform_polygon(polygon, transformer):
    """
    转换多边形的坐标系统
    :param polygon: 输入的多边形
    :param transformer: pyproj转换器
    :return: 转换后的多边形
    """
    if polygon.is_empty:
        return polygon
    
    if polygon.has_z:
        # 处理3D坐标
        exterior = [[transformer.transform(x, y)[:2] for x, y, z in polygon.exterior.coords]]
        interiors = [[[transformer.transform(x, y)[:2] for x, y, z in interior.coords]]
                    for interior in polygon.interiors]
    else:
        # 处理2D坐标
        exterior = [[transformer.transform(x, y) for x, y in polygon.exterior.coords]]
        interiors = [[[transformer.transform(x, y) for x, y in interior.coords]]
                    for interior in polygon.interiors]
    
    return Polygon(exterior[0], [inner[0] for inner in interiors]) 