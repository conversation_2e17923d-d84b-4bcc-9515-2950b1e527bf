import os
import cv2
import numpy as np
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.font_manager as fm
import time
import random
import tensorflow as tf
import keras
from keras.utils import to_categorical, Sequence
import segmentation_models as sm
from keras.optimizers import Adam
from keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard, Callback
import sys
import json
from datetime import datetime
import argparse
from sklearn.utils.class_weight import compute_class_weight

# 设置混合精度
tf.keras.mixed_precision.set_global_policy('mixed_float16')

# 设置 segmentation_models 的后端
os.environ['SM_FRAMEWORK'] = 'tf.keras'
sm.set_framework('tf.keras')

class OneLineProgressCallback(Callback):
    """一行进度条回调，强制在同一行显示进度"""

    def __init__(self):
        super().__init__()
        self.epoch_start_time = None
        self.current_epoch = 0
        self.total_epochs = 0
        self.last_line_length = 0

    def on_train_begin(self, logs=None):
        self.total_epochs = self.params['epochs']

    def on_epoch_begin(self, epoch, logs=None):
        self.current_epoch = epoch + 1
        self.epoch_start_time = time.time()
        # 清除之前的行并显示新的epoch信息
        import sys
        sys.stdout.write(f"\rEpoch {self.current_epoch}/{self.total_epochs}: ")
        sys.stdout.flush()

    def on_batch_end(self, batch, logs=None):
        # 计算进度
        current_step = batch + 1
        total_steps = self.params['steps']
        progress = current_step / total_steps

        # 创建进度条
        bar_length = 20  # 缩短进度条长度以适应一行显示
        filled_length = int(bar_length * progress)
        if filled_length == bar_length:
            bar = '█' * bar_length
        else:
            bar = '█' * filled_length + '▒' * (bar_length - filled_length)

        # 计算时间
        elapsed = time.time() - self.epoch_start_time
        if progress > 0:
            eta = int((elapsed / progress) - elapsed)
            eta_str = f"{eta:3d}s"
        else:
            eta_str = "  ?s"

        # 获取训练指标
        metrics_str = ""
        if logs:
            # 显示主要训练指标
            if 'loss' in logs:
                metrics_str += f" loss:{logs['loss']:.4f}"
            if 'categorical_iou_score' in logs:
                metrics_str += f" iou:{logs['categorical_iou_score']:.4f}"
            if 'categorical_f1_score' in logs:
                metrics_str += f" f1:{logs['categorical_f1_score']:.4f}"
            if 'accuracy' in logs:
                metrics_str += f" acc:{logs['accuracy']:.2f}"

        # 构建进度行
        progress_percent = int(progress * 100)
        progress_line = f"\rEpoch {self.current_epoch}/{self.total_epochs}: {progress_percent:3d}%|{bar}| {current_step}/{total_steps} ETA:{eta_str}{metrics_str}"

        # 清除之前的行内容（如果新行更短）
        if len(progress_line) < self.last_line_length:
            progress_line += " " * (self.last_line_length - len(progress_line))

        self.last_line_length = len(progress_line)

        # 显示进度（覆盖当前行）
        import sys
        sys.stdout.write(progress_line)
        sys.stdout.flush()

    def on_epoch_end(self, epoch, logs=None):
        # 完成当前epoch，显示最终结果并换行
        elapsed = time.time() - self.epoch_start_time
        total_steps = self.params['steps']

        # 获取训练和验证指标
        metrics_str = ""
        if logs:
            # 训练指标
            if 'loss' in logs:
                metrics_str += f" loss:{logs['loss']:.4f}"
            if 'categorical_iou_score' in logs:
                metrics_str += f" iou:{logs['categorical_iou_score']:.4f}"
            if 'categorical_f1_score' in logs:
                metrics_str += f" f1:{logs['categorical_f1_score']:.4f}"
            if 'accuracy' in logs:
                metrics_str += f" acc:{logs['accuracy']:.2f}"

            # 验证指标
            if 'val_loss' in logs and not np.isnan(logs['val_loss']):
                metrics_str += f" val_loss:{logs['val_loss']:.4f}"
            if 'val_categorical_iou_score' in logs and not np.isnan(logs['val_categorical_iou_score']):
                metrics_str += f" val_iou:{logs['val_categorical_iou_score']:.4f}"
            if 'val_categorical_f1_score' in logs and not np.isnan(logs['val_categorical_f1_score']):
                metrics_str += f" val_f1:{logs['val_categorical_f1_score']:.4f}"
            if 'val_accuracy' in logs and not np.isnan(logs['val_accuracy']):
                metrics_str += f" val_acc:{logs['val_accuracy']:.2f}"

        # 显示完成的进度条
        bar = '█' * 20
        final_line = f"\rEpoch {self.current_epoch}/{self.total_epochs}: 100%|{bar}| {total_steps}/{total_steps} {int(elapsed):3d}s{metrics_str}"

        # 清除之前的行内容
        if len(final_line) < self.last_line_length:
            final_line += " " * (self.last_line_length - len(final_line))

        import sys
        sys.stdout.write(final_line + "\n")
        sys.stdout.flush()
        self.last_line_length = 0

class DeepLabV3PlusTrainingReporter:
    """DeepLabV3+ 训练报告生成器"""

    def __init__(self, save_dir, model_name="DeepLabV3Plus"):
        self.model_name = model_name
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.save_dir = os.path.join(save_dir, f'{model_name}_{self.timestamp}')
        os.makedirs(self.save_dir, exist_ok=True)
        self.start_time = None
        self.end_time = None

    def start_training(self, config, dataset_info):
        """开始训练记录"""
        self.start_time = time.time()
        self.config = config
        self.dataset_info = dataset_info

    def setup_chinese_font(self):
        """设置中文字体"""
        try:
            # 查找可用的中文字体
            font_list = [f.name for f in fm.fontManager.ttflist]
            chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
            available_font = None

            for font in chinese_fonts:
                if font in font_list:
                    available_font = font
                    break

            if available_font:
                plt.rcParams['font.sans-serif'] = [available_font]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"✅ 使用字体: {available_font}")
                return True
            else:
                # 如果没有中文字体，使用英文字体
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                print("⚠️ 未找到中文字体，使用英文字体")
                return False
        except Exception as e:
            print(f"⚠️ 字体设置失败: {e}")
            return False

    def generate_training_report(self, history, model_save_path, training_duration):
        """生成完整的训练报告"""
        self.end_time = time.time()

        # 设置字体
        self.setup_chinese_font()

        # 生成训练曲线图
        self._plot_training_curves(history)

        # 生成详细报告
        self._generate_detailed_report(history, model_save_path, training_duration)

        # 生成HTML报告
        self._generate_html_report(history, model_save_path, training_duration)

        # 保存训练历史数据
        self._save_training_history(history)

        print(f"📈 训练报告已生成完成，保存在: {self.save_dir}")

    def _plot_training_curves(self, history):
        """绘制训练曲线"""
        try:
            plt.figure(figsize=(15, 5))

            mode_title = f"多分类({self.config.get('num_classes', 2)}类)" if self.config.get('multiclass_mode', False) else "二分类"

            plt.subplot(1, 3, 1)
            plt.plot(history.history['loss'], label='Training Loss')
            plt.plot(history.history['val_loss'], label='Validation Loss')
            plt.title(f'DeepLabV3+ {mode_title} 模型损失')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            plt.subplot(1, 3, 2)
            plt.plot(history.history['accuracy'], label='Training Accuracy')
            plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
            plt.title(f'DeepLabV3+ {mode_title} 模型准确率')
            plt.xlabel('Epoch')
            plt.ylabel('Accuracy')
            plt.legend()

            plt.subplot(1, 3, 3)
            plt.plot(history.history.get('categorical_iou_score', []), label='Training IoU')
            plt.plot(history.history.get('val_categorical_iou_score', []), label='Validation IoU')
            plt.title(f'DeepLabV3+ {mode_title} IoU分数')
            plt.xlabel('Epoch')
            plt.ylabel('IoU Score')
            plt.legend()

            plt.tight_layout()
            curves_path = os.path.join(self.save_dir, f'training_curves_{self.timestamp}.png')
            plt.savefig(curves_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 训练曲线图已保存: {curves_path}")

        except Exception as e:
            print(f"⚠️ 生成训练曲线图失败: {e}")

    def _generate_detailed_report(self, history, model_save_path, training_duration):
        """生成详细的训练报告"""
        try:
            report_path = os.path.join(self.save_dir, f'training_report_{self.timestamp}.md')

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"# {self.model_name} 训练报告\n\n")

                # 基本信息
                f.write("## 📊 基本信息\n\n")
                f.write(f"- **模型名称**: {self.model_name}\n")
                f.write(f"- **训练开始时间**: {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"- **训练结束时间**: {datetime.fromtimestamp(self.end_time).strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"- **训练总时长**: {self._format_duration(training_duration)}\n")
                f.write(f"- **模型保存路径**: {model_save_path}\n\n")

                # 训练配置
                f.write("## ⚙️ 训练配置\n\n")
                for key, value in self.config.items():
                    f.write(f"- **{key}**: {value}\n")
                f.write("\n")

                # 数据集信息
                f.write("## 📁 数据集信息\n\n")
                for key, value in self.dataset_info.items():
                    f.write(f"- **{key}**: {value}\n")
                f.write("\n")

                # 训练结果
                f.write("## 📈 训练结果\n\n")
                f.write(f"- **总训练轮数**: {len(history.history['loss'])}\n")

                if 'loss' in history.history:
                    final_loss = history.history['loss'][-1]
                    best_loss = min(history.history['loss'])
                    best_loss_epoch = history.history['loss'].index(best_loss) + 1
                    f.write(f"- **最终训练损失**: {final_loss:.6f}\n")
                    f.write(f"- **最佳训练损失**: {best_loss:.6f} (第{best_loss_epoch}轮)\n")

                if 'val_loss' in history.history:
                    final_val_loss = history.history['val_loss'][-1]
                    # 过滤掉nan值
                    valid_val_losses = [x for x in history.history['val_loss'] if not np.isnan(x)]
                    if valid_val_losses:
                        best_val_loss = min(valid_val_losses)
                        best_val_loss_epoch = history.history['val_loss'].index(best_val_loss) + 1
                        f.write(f"- **最终验证损失**: {final_val_loss:.6f}\n")
                        f.write(f"- **最佳验证损失**: {best_val_loss:.6f} (第{best_val_loss_epoch}轮)\n")

                if 'categorical_iou_score' in history.history:
                    final_iou = history.history['categorical_iou_score'][-1]
                    best_iou = max(history.history['categorical_iou_score'])
                    best_iou_epoch = history.history['categorical_iou_score'].index(best_iou) + 1
                    f.write(f"- **最终训练IoU**: {final_iou:.6f}\n")
                    f.write(f"- **最佳训练IoU**: {best_iou:.6f} (第{best_iou_epoch}轮)\n")

                if 'val_categorical_iou_score' in history.history:
                    final_val_iou = history.history['val_categorical_iou_score'][-1]
                    # 过滤掉极小值
                    valid_val_ious = [x for x in history.history['val_categorical_iou_score'] if x > 1e-5]
                    if valid_val_ious:
                        best_val_iou = max(valid_val_ious)
                        best_val_iou_epoch = history.history['val_categorical_iou_score'].index(best_val_iou) + 1
                        f.write(f"- **最终验证IoU**: {final_val_iou:.6f}\n")
                        f.write(f"- **最佳验证IoU**: {best_val_iou:.6f} (第{best_val_iou_epoch}轮)\n")

                # 文件列表
                f.write(f"\n## 📊 生成文件\n\n")
                f.write(f"- 训练曲线图: `training_curves_{self.timestamp}.png`\n")
                f.write(f"- 训练历史数据: `training_history_{self.timestamp}.json`\n")
                f.write(f"- HTML报告: `training_report_{self.timestamp}.html`\n")

                f.write(f"\n---\n")
                f.write(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")

            print(f"✅ 详细报告已保存: {report_path}")

        except Exception as e:
            print(f"⚠️ 生成详细报告失败: {e}")

    def _format_duration(self, duration):
        """格式化时间长度"""
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def _generate_html_report(self, history, model_save_path, training_duration):
        """生成HTML格式的训练报告"""
        try:
            html_path = os.path.join(self.save_dir, f'training_report_{self.timestamp}.html')

            # 准备训练数据
            epochs = list(range(1, len(history.history['loss']) + 1))
            train_loss = history.history['loss']
            val_loss = history.history.get('val_loss', [])
            train_iou = history.history.get('categorical_iou_score', [])
            val_iou = history.history.get('val_categorical_iou_score', [])
            train_acc = history.history.get('accuracy', [])
            val_acc = history.history.get('val_accuracy', [])

            # 处理NaN值
            def clean_data(data):
                """清理数据中的NaN值"""
                if not data:
                    return []
                return [float(x) if not np.isnan(x) else 0.0 for x in data]

            val_loss = clean_data(val_loss)
            val_iou = clean_data(val_iou)
            val_acc = clean_data(val_acc)

            # 计算数据集信息
            dataset_info = self.dataset_info
            total_files = len(dataset_info) if isinstance(dataset_info, list) else 0
            train_samples = int(total_files * 0.8) if total_files > 0 else 0
            val_samples = total_files - train_samples if total_files > 0 else 0

            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.model_name} 训练报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .content {{
            padding: 30px;
        }}
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .info-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }}
        .info-card h3 {{
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }}
        .info-card p {{
            margin: 5px 0;
            color: #666;
        }}
        .chart-container {{
            margin: 30px 0;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-title {{
            text-align: center;
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
            font-weight: 500;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        .metric-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .metric-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .suggestions {{
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        .suggestions h3 {{
            color: #2e7d32;
            margin-top: 0;
        }}
        .suggestions ul {{
            color: #2e7d32;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛰️ {self.model_name} 训练报告</h1>
            <p>卫星图像建筑物分割模型训练结果</p>
        </div>

        <div class="content">
            <!-- 基本信息 -->
            <div class="info-grid">
                <div class="info-card">
                    <h3>📊 训练信息</h3>
                    <p><strong>开始时间:</strong> {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>结束时间:</strong> {datetime.fromtimestamp(self.end_time).strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>训练时长:</strong> {self._format_duration(training_duration)}</p>
                    <p><strong>训练轮数:</strong> {len(epochs)}</p>
                </div>

                <div class="info-card">
                    <h3>⚙️ 模型配置</h3>"""

            for key, value in self.config.items():
                html_content += f"<p><strong>{key}:</strong> {value}</p>"

            html_content += f"""
                </div>

                <div class="info-card">
                    <h3>📁 数据集信息</h3>
                    <p><strong>文件对数量:</strong> {total_files}</p>
                    <p><strong>训练样本:</strong> {train_samples}</p>
                    <p><strong>验证样本:</strong> {val_samples}</p>
                </div>

                <div class="info-card">
                    <h3>🔧 模型参数</h3>
                    <p><strong>模型文件:</strong> {os.path.basename(model_save_path)}</p>
                    <p><strong>保存路径:</strong> {os.path.dirname(model_save_path)}</p>
                </div>
            </div>

            <!-- 最终指标 -->
            <div class="metrics-grid">"""

            # 添加最终指标卡片
            if train_loss:
                html_content += f"""
                <div class="metric-card">
                    <div class="metric-value">{train_loss[-1]:.4f}</div>
                    <div class="metric-label">最终训练损失</div>
                </div>"""

            if val_loss and len(val_loss) > 0:
                html_content += f"""
                <div class="metric-card">
                    <div class="metric-value">{val_loss[-1]:.4f}</div>
                    <div class="metric-label">最终验证损失</div>
                </div>"""

            if train_iou:
                html_content += f"""
                <div class="metric-card">
                    <div class="metric-value">{train_iou[-1]:.4f}</div>
                    <div class="metric-label">最终训练IoU</div>
                </div>"""

            if val_iou and len(val_iou) > 0:
                html_content += f"""
                <div class="metric-card">
                    <div class="metric-value">{val_iou[-1]:.4f}</div>
                    <div class="metric-label">最终验证IoU</div>
                </div>"""

            html_content += """
            </div>

            <!-- 训练曲线图表 -->
            <div class="chart-container">
                <div class="chart-title">📈 训练损失曲线</div>
                <canvas id="lossChart" width="400" height="200"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">📊 IoU系数曲线</div>
                <canvas id="iouChart" width="400" height="200"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">🎯 准确率曲线</div>
                <canvas id="accChart" width="400" height="200"></canvas>
            </div>"""

            # 生成训练分析建议
            suggestions = self._generate_training_suggestions(history)
            html_content += f"""
            <div class="suggestions">
                <h3>💡 训练分析与建议</h3>
                <ul>{suggestions}
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>🛰️ 卫星图像自动偏移校正系统 - {self.model_name}训练模块</p>
        </div>
    </div>

    <script>
        // 损失曲线图表
        const lossCtx = document.getElementById('lossChart').getContext('2d');
        new Chart(lossCtx, {{
            type: 'line',
            data: {{
                labels: {epochs},
                datasets: [{{
                    label: '训练损失',
                    data: {train_loss},
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }}"""

            if val_loss and len(val_loss) > 0:
                html_content += f""", {{
                    label: '验证损失',
                    data: {val_loss},
                    borderColor: 'rgb(118, 75, 162)',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4
                }}"""

            html_content += """]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // IoU曲线图表
        const iouCtx = document.getElementById('iouChart').getContext('2d');
        new Chart(iouCtx, {
            type: 'line',
            data: {
                labels: """ + str(epochs) + """,
                datasets: ["""

            if train_iou:
                html_content += f"""{{
                    label: '训练IoU',
                    data: {train_iou},
                    borderColor: 'rgb(76, 175, 80)',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }}"""

            if val_iou and len(val_iou) > 0:
                if train_iou:
                    html_content += ", "
                html_content += f"""{{
                    label: '验证IoU',
                    data: {val_iou},
                    borderColor: 'rgb(255, 152, 0)',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    tension: 0.4
                }}"""

            html_content += """]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });

        // 准确率曲线图表
        const accCtx = document.getElementById('accChart').getContext('2d');
        new Chart(accCtx, {
            type: 'line',
            data: {
                labels: """ + str(epochs) + """,
                datasets: ["""

            if train_acc:
                html_content += f"""{{
                    label: '训练准确率',
                    data: {train_acc},
                    borderColor: 'rgb(233, 30, 99)',
                    backgroundColor: 'rgba(233, 30, 99, 0.1)',
                    tension: 0.4
                }}"""

            if val_acc and len(val_acc) > 0:
                if train_acc:
                    html_content += ", "
                html_content += f"""{{
                    label: '验证准确率',
                    data: {val_acc},
                    borderColor: 'rgb(156, 39, 176)',
                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                    tension: 0.4
                }}"""

            html_content += """]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });
    </script>
</body>
</html>"""

            # 保存HTML文件
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"✅ HTML报告已保存: {html_path}")

        except Exception as e:
            print(f"⚠️ 生成HTML报告失败: {e}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")

    def _generate_training_suggestions(self, history):
        """生成训练分析建议"""
        suggestions = []

        try:
            train_loss = history.history['loss']
            val_loss = history.history.get('val_loss', [])
            train_iou = history.history.get('categorical_iou_score', [])
            val_iou = history.history.get('val_categorical_iou_score', [])

            # 清理NaN值
            val_loss = [x for x in val_loss if not np.isnan(x)] if val_loss else []
            val_iou = [x for x in val_iou if not np.isnan(x) and x > 1e-5] if val_iou else []

            # 分析训练损失趋势
            if len(train_loss) >= 3:
                recent_loss_trend = train_loss[-3:]
                if all(recent_loss_trend[i] >= recent_loss_trend[i+1] for i in range(len(recent_loss_trend)-1)):
                    suggestions.append("<li>✅ 训练损失持续下降，模型学习良好</li>")
                elif all(recent_loss_trend[i] <= recent_loss_trend[i+1] for i in range(len(recent_loss_trend)-1)):
                    suggestions.append("<li>⚠️ 训练损失上升，可能需要降低学习率</li>")
                else:
                    suggestions.append("<li>📊 训练损失波动，建议监控更多轮次</li>")

            # 分析验证损失
            if len(val_loss) >= 3:
                if len(train_loss) > 0 and len(val_loss) > 0:
                    final_train_loss = train_loss[-1]
                    final_val_loss = val_loss[-1]
                    if abs(final_train_loss - final_val_loss) < 0.1:
                        suggestions.append("<li>✅ 训练平衡：训练损失和验证损失相近</li>")
                    elif final_val_loss > final_train_loss * 1.5:
                        suggestions.append("<li>⚠️ 可能过拟合：验证损失明显高于训练损失</li>")
                    else:
                        suggestions.append("<li>📈 模型泛化良好：验证损失合理</li>")
            else:
                suggestions.append("<li>⚠️ 缺少验证数据，建议添加验证集</li>")

            # 分析IoU性能
            if train_iou:
                final_train_iou = train_iou[-1]
                if final_train_iou > 0.8:
                    suggestions.append("<li>🎯 优秀的分割性能：训练IoU > 0.8</li>")
                elif final_train_iou > 0.6:
                    suggestions.append("<li>📊 良好的分割性能：训练IoU > 0.6</li>")
                else:
                    suggestions.append("<li>⚠️ 分割性能较低，建议调整模型参数或增加训练数据</li>")

            if val_iou:
                final_val_iou = val_iou[-1]
                if final_val_iou > 0.8:
                    suggestions.append("<li>🎯 优秀的验证性能：验证IoU > 0.8</li>")
                elif final_val_iou > 0.6:
                    suggestions.append("<li>📊 良好的验证性能：验证IoU > 0.6</li>")
                else:
                    suggestions.append("<li>⚠️ 验证性能较低，可能需要更多训练或数据增强</li>")

            # 分析收敛情况
            if len(train_loss) >= 5:
                recent_losses = train_loss[-5:]
                loss_variance = np.var(recent_losses)
                if loss_variance < 0.001:
                    suggestions.append("<li>📈 模型可能已收敛：最近几轮损失变化很小</li>")
                else:
                    suggestions.append("<li>📊 训练稳定：损失变化平稳</li>")

            # 如果没有生成任何建议，添加默认建议
            if not suggestions:
                suggestions.append("<li>📊 训练完成，建议分析训练曲线以评估模型性能</li>")

        except Exception as e:
            suggestions.append(f"<li>⚠️ 分析生成失败: {e}</li>")

        return "".join(suggestions)

    def _format_duration(self, duration_seconds):
        """格式化训练时长"""
        try:
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)

            if hours > 0:
                return f"{hours}小时{minutes}分钟{seconds}秒"
            elif minutes > 0:
                return f"{minutes}分钟{seconds}秒"
            else:
                return f"{seconds}秒"
        except:
            return "未知"

    def _save_training_history(self, history):
        """保存训练历史数据为JSON格式"""
        try:
            history_path = os.path.join(self.save_dir, f'training_history_{self.timestamp}.json')

            # 准备保存的数据
            save_data = {
                'training_info': {
                    'model_name': self.model_name,
                    'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                    'end_time': datetime.fromtimestamp(self.end_time).isoformat(),
                    'duration_seconds': self.end_time - self.start_time,
                    'timestamp': self.timestamp
                },
                'config': self.config,
                'dataset_info': self.dataset_info,
                'history': {},
                'export_time': datetime.now().isoformat()
            }

            # 转换历史数据，处理numpy类型
            for key, values in history.history.items():
                save_data['history'][key] = [float(v) if not np.isnan(v) else None for v in values]

            # 保存JSON文件
            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            print(f"✅ 训练历史已保存: {history_path}")

        except Exception as e:
            print(f"⚠️ 保存训练历史失败: {e}")

# Monitor GPU memory and set device availability
print("GPU 检测:")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        # 设置GPU内存增长，避免一次性分配所有内存
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"发现 {len(gpus)} 个 GPU，已配置内存增长模式")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu.name}")
    except RuntimeError as e:
        print(f"GPU 配置错误: {e}")
else:
    print("未检测到 GPU，将使用 CPU 进行训练")

# 定义 DataGenerator 类 - 内存优化版本，支持数据打乱和数据增强
class DataGenerator(keras.utils.Sequence):
    def __init__(self, image_dataset, label_dataset, indices, batch_size, shuffle=True,
                 sample_weights=None, use_augmentation=False, is_training=True):
        self.image_dataset = image_dataset
        self.label_dataset = label_dataset
        self.indices = indices.copy()  # 复制索引以避免修改原始数组
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.sample_weights = sample_weights
        self.use_augmentation = use_augmentation and is_training  # 只在训练时使用增强
        self.is_training = is_training
        self.on_epoch_end()

    def __len__(self):
        return int(np.ceil(len(self.indices) / float(self.batch_size)))

    def __getitem__(self, idx):
        # 获取当前批次的索引
        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]

        # 按需加载数据
        batch_x = self.image_dataset[batch_indices].copy()
        batch_y = self.label_dataset[batch_indices].copy()

        # 应用数据增强
        if self.use_augmentation:
            batch_x, batch_y = self._apply_augmentation(batch_x, batch_y)

        return batch_x, batch_y

    def _apply_augmentation(self, images, masks):
        """应用数据增强"""
        augmented_images = []
        augmented_masks = []

        for img, mask in zip(images, masks):
            # 随机水平翻转
            if np.random.random() > 0.5:
                img = np.fliplr(img)
                mask = np.fliplr(mask)

            # 随机垂直翻转
            if np.random.random() > 0.5:
                img = np.flipud(img)
                mask = np.flipud(mask)

            # 随机旋转（90度的倍数）
            if np.random.random() > 0.5:
                k = np.random.randint(1, 4)  # 旋转90, 180, 或270度
                img = np.rot90(img, k)
                mask = np.rot90(mask, k)

            # 随机亮度调整（仅对图像）
            if np.random.random() > 0.5:
                brightness_factor = np.random.uniform(0.8, 1.2)
                img = np.clip(img * brightness_factor, 0, 1)

            augmented_images.append(img)
            augmented_masks.append(mask)

        return np.array(augmented_images), np.array(augmented_masks)

    def on_epoch_end(self):
        """在每个epoch结束时打乱数据"""
        if self.shuffle:
            np.random.shuffle(self.indices)

class TrainingLogger:
    def __init__(self, log_path="training_log.txt"):
        self.log_file = open(log_path, 'w')
        
    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.log_file.write(log_message + "\n")
        self.log_file.flush()

    def close(self):
        if hasattr(self, 'log_file') and self.log_file:
            self.log_file.close()

def load_config(config_path):
    with open(config_path, 'r') as f:
        return json.load(f)

def get_dataset_paths(config):
    # 优先使用模型参数中的路径
    model_args = config.get('model', {}).get('args', {})

    # 检查数据模式
    data_mode = model_args.get('data_mode', 'image+shape')

    if data_mode == 'image+mask':
        # image+mask模式：使用文件夹路径
        image_folder = model_args.get('image_folder', '')
        mask_folder = model_args.get('mask_folder', '')

        if image_folder and os.path.exists(image_folder):
            # 查找图像文件夹中的第一个tif文件
            tif_files = [f for f in os.listdir(image_folder) if f.endswith('.tif')]
            if tif_files:
                image_path = os.path.join(image_folder, tif_files[0])
            else:
                raise FileNotFoundError(f"在图像文件夹中未找到.tif文件: {image_folder}")
        else:
            raise FileNotFoundError(f"图像文件夹不存在: {image_folder}")

        # 对于image+mask模式，我们需要将其转换为image+shape模式
        # 这里暂时返回None作为shape_path，后续需要处理
        return image_path, None

    elif data_mode == 'image+shape':
        # image+shape模式：使用单文件路径
        if 'image_path' in model_args and 'shape_path' in model_args:
            image_path = model_args['image_path']
            shape_path = model_args['shape_path']

            # 如果路径是目录，则查找文件
            if os.path.isdir(image_path):
                tif_files = [f for f in os.listdir(image_path) if f.endswith('.tif')]
                if tif_files:
                    image_path = os.path.join(image_path, tif_files[0])
                else:
                    raise FileNotFoundError(f"在图像目录中未找到.tif文件: {image_path}")

            if os.path.isdir(shape_path):
                shp_files = [f for f in os.listdir(shape_path) if f.endswith('.shp')]
                if shp_files:
                    shape_path = os.path.join(shape_path, shp_files[0])
                else:
                    raise FileNotFoundError(f"在形状目录中未找到.shp文件: {shape_path}")

            return image_path, shape_path
        else:
            # 使用旧的dataset_path参数
            dataset_path = config.get('dataset_path', '')
            image_dir = os.path.join(dataset_path, 'image')
            shape_dir = os.path.join(dataset_path, 'shape')

            # 查找实际的文件
            if os.path.exists(image_dir):
                tif_files = [f for f in os.listdir(image_dir) if f.endswith('.tif')]
                if tif_files:
                    image_path = os.path.join(image_dir, tif_files[0])
                else:
                    raise FileNotFoundError(f"在图像目录中未找到.tif文件: {image_dir}")
            else:
                raise FileNotFoundError(f"图像目录不存在: {image_dir}")

            if os.path.exists(shape_dir):
                shp_files = [f for f in os.listdir(shape_dir) if f.endswith('.shp')]
                if shp_files:
                    shape_path = os.path.join(shape_dir, shp_files[0])
                else:
                    raise FileNotFoundError(f"在形状目录中未找到.shp文件: {shape_dir}")
            else:
                raise FileNotFoundError(f"形状目录不存在: {shape_dir}")

            return image_path, shape_path

    else:
        raise ValueError(f"不支持的数据模式: {data_mode}")

def create_deeplabv3plus_model(input_shape, num_classes, backbone='resnet101', output_stride=16):
    """创建DeepLabV3+分割模型（自定义实现）"""
    from tensorflow.keras.layers import Input, Conv2D, BatchNormalization, Activation, UpSampling2D, Concatenate, GlobalAveragePooling2D, Reshape, Dense, Multiply
    from tensorflow.keras.models import Model
    from tensorflow.keras.applications import ResNet101, ResNet50

    # 输入层
    inputs = Input(shape=input_shape)

    # 选择骨干网络
    if backbone == 'resnet101':
        backbone_model = ResNet101(weights='imagenet', include_top=False, input_tensor=inputs)
        # 获取不同层的输出用于特征融合
        low_level_features = backbone_model.get_layer('conv2_block3_out').output  # 低级特征
        high_level_features = backbone_model.output  # 高级特征
    else:
        backbone_model = ResNet50(weights='imagenet', include_top=False, input_tensor=inputs)
        low_level_features = backbone_model.get_layer('conv2_block3_out').output
        high_level_features = backbone_model.output

    # ASPP (Atrous Spatial Pyramid Pooling) 模块
    # 1x1 卷积
    aspp1 = Conv2D(256, (1, 1), padding='same', use_bias=False)(high_level_features)
    aspp1 = BatchNormalization()(aspp1)
    aspp1 = Activation('relu')(aspp1)

    # 3x3 卷积，不同的膨胀率
    aspp2 = Conv2D(256, (3, 3), padding='same', dilation_rate=6, use_bias=False)(high_level_features)
    aspp2 = BatchNormalization()(aspp2)
    aspp2 = Activation('relu')(aspp2)

    aspp3 = Conv2D(256, (3, 3), padding='same', dilation_rate=12, use_bias=False)(high_level_features)
    aspp3 = BatchNormalization()(aspp3)
    aspp3 = Activation('relu')(aspp3)

    aspp4 = Conv2D(256, (3, 3), padding='same', dilation_rate=18, use_bias=False)(high_level_features)
    aspp4 = BatchNormalization()(aspp4)
    aspp4 = Activation('relu')(aspp4)

    # 全局平均池化
    global_avg_pool = GlobalAveragePooling2D()(high_level_features)
    global_avg_pool = Reshape((1, 1, -1))(global_avg_pool)
    global_avg_pool = Conv2D(256, (1, 1), padding='same', use_bias=False)(global_avg_pool)
    global_avg_pool = BatchNormalization()(global_avg_pool)
    global_avg_pool = Activation('relu')(global_avg_pool)

    # 上采样到与其他ASPP特征相同的尺寸
    h, w = high_level_features.shape[1:3]
    global_avg_pool = UpSampling2D(size=(h, w), interpolation='bilinear')(global_avg_pool)

    # 连接所有ASPP特征
    aspp_concat = Concatenate()([aspp1, aspp2, aspp3, aspp4, global_avg_pool])

    # 1x1 卷积降维
    aspp_output = Conv2D(256, (1, 1), padding='same', use_bias=False)(aspp_concat)
    aspp_output = BatchNormalization()(aspp_output)
    aspp_output = Activation('relu')(aspp_output)

    # 处理低级特征
    low_level_features = Conv2D(48, (1, 1), padding='same', use_bias=False)(low_level_features)
    low_level_features = BatchNormalization()(low_level_features)
    low_level_features = Activation('relu')(low_level_features)

    # 计算正确的上采样倍数，使ASPP输出与低级特征尺寸匹配
    low_level_h, low_level_w = low_level_features.shape[1:3]
    aspp_h, aspp_w = aspp_output.shape[1:3]

    # 计算上采样倍数
    upsample_h = low_level_h // aspp_h if low_level_h % aspp_h == 0 else (low_level_h // aspp_h) + 1
    upsample_w = low_level_w // aspp_w if low_level_w % aspp_w == 0 else (low_level_w // aspp_w) + 1

    # 上采样ASPP输出到与低级特征相同的尺寸
    aspp_upsampled = UpSampling2D(size=(upsample_h, upsample_w), interpolation='bilinear')(aspp_output)

    # 如果上采样后尺寸不完全匹配，进行裁剪或填充
    if aspp_upsampled.shape[1] != low_level_features.shape[1] or aspp_upsampled.shape[2] != low_level_features.shape[2]:
        from tensorflow.keras.layers import Lambda
        import tensorflow as tf

        def resize_to_match(x):
            target_h, target_w = low_level_features.shape[1], low_level_features.shape[2]
            return tf.image.resize(x, [target_h, target_w], method='bilinear')

        aspp_upsampled = Lambda(resize_to_match)(aspp_upsampled)

    # 连接高级和低级特征
    concat_features = Concatenate()([aspp_upsampled, low_level_features])

    # 最终的卷积层
    x = Conv2D(256, (3, 3), padding='same', use_bias=False)(concat_features)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)

    x = Conv2D(256, (3, 3), padding='same', use_bias=False)(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)

    # 最终上采样到原始尺寸
    x = UpSampling2D(size=(4, 4), interpolation='bilinear')(x)

    # 输出层
    if num_classes == 1:
        outputs = Conv2D(1, (1, 1), activation='sigmoid', name='output')(x)
    else:
        outputs = Conv2D(num_classes, (1, 1), activation='softmax', name='output')(x)

    model = Model(inputs=inputs, outputs=outputs, name='DeepLabV3Plus')

    return model

def create_multiclass_mask_from_shapefile(gdf, image_shape, transform, multiclass_config):
    """从shapefile创建多分类掩膜"""
    # 获取多分类配置
    class_mapping = multiclass_config.get('class_mapping', {})
    attribute_field = multiclass_config.get('shape_attribute_field', 'class_type')
    background_class_id = multiclass_config.get('background_class_id', 0)

    print(f"创建多分类掩膜，类别映射: {class_mapping}")
    print(f"属性字段: {attribute_field}")

    # 初始化掩膜为背景类别
    mask = np.full(image_shape, background_class_id, dtype=np.uint8)

    # 检查shapefile是否包含指定的属性字段
    if attribute_field not in gdf.columns:
        print(f"警告: shapefile中未找到属性字段 '{attribute_field}'，将所有要素标记为类别1")
        # 如果没有属性字段，将所有要素标记为第一个非背景类别
        shapes_with_values = [(shape, 1) for shape in gdf.geometry if shape is not None]
    else:
        # 根据属性字段创建类别映射
        shapes_with_values = []
        for idx, row in gdf.iterrows():
            shape = row.geometry
            if shape is None:
                continue

            # 获取属性值，可能是数值或字符串
            attr_value = row[attribute_field]

            # 尝试直接使用数值作为类别ID（适用于ClassValue=1,2,3的情况）
            if isinstance(attr_value, (int, float)):
                class_id = int(attr_value)
                # 验证类别ID是否在有效范围内
                if 1 <= class_id <= len(class_mapping):
                    shapes_with_values.append((shape, class_id))
                    continue
                else:
                    print(f"警告: 类别ID '{class_id}' 超出有效范围 [1, {len(class_mapping)}]，跳过该要素")
                    continue

            # 如果是字符串，则使用原有的映射逻辑
            class_name = str(attr_value)
            class_id = class_mapping.get(class_name, None)
            if class_id is None:
                print(f"警告: 未知类别 '{class_name}'，跳过该要素")
                continue

            shapes_with_values.append((shape, class_id))

        print(f"处理了 {len(shapes_with_values)} 个有效要素")

    # 栅格化
    if shapes_with_values:
        print(f"开始栅格化 {len(shapes_with_values)} 个几何体...")

        # 借鉴UNet的成功经验：过滤无效几何体
        print(f"开始过滤 {len(shapes_with_values)} 个几何体...")
        valid_shapes = [(geom, class_id) for geom, class_id in shapes_with_values if geom.is_valid]
        print(f"过滤后有效几何体数量: {len(valid_shapes)}")

        if len(valid_shapes) != len(shapes_with_values):
            invalid_count = len(shapes_with_values) - len(valid_shapes)
            print(f"警告: 过滤掉了 {invalid_count} 个无效几何体")

        if valid_shapes:
            mask = rasterize(
                valid_shapes,
                out_shape=image_shape,
                transform=transform,
                fill=background_class_id,
                dtype='uint8',
                all_touched=True
            )

            # 验证栅格化结果
            unique_values = np.unique(mask)
            print(f"栅格化后的唯一值: {unique_values}")
            for value in unique_values:
                count = np.sum(mask == value)
                print(f"  类别 {value}: {count} 像素")
        else:
            print("警告: 没有有效的几何体在图像范围内")
            mask = np.full(image_shape, background_class_id, dtype=np.uint8)
    else:
        print("警告: 没有要栅格化的几何体")
        mask = np.full(image_shape, background_class_id, dtype=np.uint8)

    return mask

def train_deeplabv3plus(config_path):
    logger = TrainingLogger()
    logger.log("=== 开始训练 DeepLabV3+ 分割模型 ===")
    logger.log(f"使用配置: {config_path}")
    
    try:
        # 加载配置
        config = load_config(config_path)
        
        # 获取训练参数
        model_args = config['model']['args']
        trainer_args = config['trainer']

        batch_size = trainer_args['batch_size']
        epochs = trainer_args['epochs']
        learning_rate = trainer_args['learning_rate']
        slice_size = trainer_args['slice_size']
        overlap = trainer_args['overlap']

        # 检查是否启用多分类模式
        multiclass_mode = model_args.get('multiclass_mode', False)
        multiclass_config = model_args.get('multiclass_config', {})

        if multiclass_mode:
            num_classes = multiclass_config.get('num_classes', 2)
            class_names = multiclass_config.get('class_names', [f'类别{i}' for i in range(num_classes)])
            logger.log(f"启用多分类模式，类别数量: {num_classes}")
            logger.log(f"类别名称: {class_names}")
        else:
            num_classes = 2  # 默认二分类
            class_names = ['背景', '前景']
            logger.log("使用默认二分类模式")
        
        # 获取数据路径和模式
        model_args = config['model']['args']
        data_mode = model_args.get('data_mode', 'image+shape')

        logger.log(f"数据模式: {data_mode}")

        if data_mode == 'image+mask':
            # image+mask模式：直接使用mask文件
            image_folder = model_args.get('image_folder', '')
            mask_folder = model_args.get('mask_folder', '')

            logger.log(f"图像文件夹: {image_folder}")
            logger.log(f"掩膜文件夹: {mask_folder}")

            # 查找图像文件
            if not os.path.exists(image_folder):
                logger.log(f"错误：图像文件夹不存在: {image_folder}")
                return False

            tif_files = [f for f in os.listdir(image_folder) if f.endswith('.tif')]
            if not tif_files:
                logger.log(f"错误：在图像文件夹中未找到.tif文件: {image_folder}")
                return False

            image_path = os.path.join(image_folder, tif_files[0])
            logger.log(f"使用图像文件: {image_path}")

            # 查找对应的mask文件
            if not os.path.exists(mask_folder):
                logger.log(f"错误：掩膜文件夹不存在: {mask_folder}")
                return False

            mask_files = [f for f in os.listdir(mask_folder) if f.endswith('.tif')]
            if not mask_files:
                logger.log(f"错误：在掩膜文件夹中未找到.tif文件: {mask_folder}")
                return False

            mask_path = os.path.join(mask_folder, mask_files[0])
            logger.log(f"使用掩膜文件: {mask_path}")

            # 验证文件存在
            if not os.path.exists(image_path):
                logger.log(f"错误：图像文件不存在: {image_path}")
                return False

            if not os.path.exists(mask_path):
                logger.log(f"错误：掩膜文件不存在: {mask_path}")
                return False

        else:
            # image+shape模式：使用形状文件生成mask
            image_path, shape_path = get_dataset_paths(config)

            logger.log(f"图像文件: {image_path}")
            logger.log(f"形状文件: {shape_path}")

            # 验证文件存在
            if not os.path.exists(image_path):
                logger.log(f"错误：图像文件不存在: {image_path}")
                return False

            if not os.path.exists(shape_path):
                logger.log(f"错误：形状文件不存在: {shape_path}")
                return False
        
        # 读取数据
        logger.log("正在读取图像数据...")
        with rasterio.open(image_path) as src:
            image = src.read()
            transform = src.transform
            
            # 转换为 (H, W, C) 格式
            if image.ndim == 3:
                image = np.transpose(image, (1, 2, 0))
            else:
                image = np.expand_dims(image, axis=2)
            
            logger.log(f"图像形状: {image.shape}")
            logger.log(f"图像数据类型: {image.dtype}")
            logger.log(f"图像值范围: {image.min()} - {image.max()}")
        
        # 确保图像是3波段
        if image.shape[2] == 1:
            image = np.repeat(image, 3, axis=2)
        elif image.shape[2] > 3:
            image = image[:, :, :3]

        # 根据数据模式处理掩码
        if data_mode == 'image+mask':
            # image+mask模式：直接读取mask文件
            logger.log("正在读取掩膜数据...")
            with rasterio.open(mask_path) as src:
                mask = src.read(1)  # 读取第一个波段
                logger.log(f"掩膜形状: {mask.shape}")
                logger.log(f"掩膜数据类型: {mask.dtype}")
                logger.log(f"掩膜值范围: {mask.min()} - {mask.max()}")

                # 确保mask和image尺寸匹配
                if mask.shape != image.shape[:2]:
                    logger.log(f"警告：掩膜尺寸 {mask.shape} 与图像尺寸 {image.shape[:2]} 不匹配")
                    # 可以在这里添加尺寸调整逻辑

                # 统计掩膜中的类别
                unique_classes, counts = np.unique(mask, return_counts=True)
                logger.log(f"掩膜中的类别: {unique_classes}")
                for class_id, count in zip(unique_classes, counts):
                    logger.log(f"  类别 {class_id}: {count} 像素")
        else:
            # image+shape模式：从形状文件生成mask
            logger.log("正在读取形状数据...")
            gdf = gpd.read_file(shape_path)
            logger.log(f"形状数据包含 {len(gdf)} 个要素")

            # 确保CRS匹配 - 这是关键步骤！
            if gdf.crs != src.crs:
                logger.log(f"转换CRS: {gdf.crs} -> {src.crs}")
                gdf = gdf.to_crs(src.crs)
                logger.log(f"CRS转换完成")

            # 创建掩码
            logger.log("正在创建掩码...")
            if multiclass_mode:
                # 多分类模式
                mask = create_multiclass_mask_from_shapefile(gdf, image.shape[:2], transform, multiclass_config)

                # 统计各类别像素数
                unique_classes, counts = np.unique(mask, return_counts=True)
                logger.log(f"掩码形状: {mask.shape}")
                for class_id, count in zip(unique_classes, counts):
                    class_name = class_names[class_id] if class_id < len(class_names) else f"类别{class_id}"
                    logger.log(f"  {class_name} (ID:{class_id}): {count} 像素")
            else:
                # 二分类模式（保持原有逻辑）
                mask = rasterize(
                    [(shape, 1) for shape in gdf.geometry],
                    out_shape=image.shape[:2],
                    transform=transform,
                    fill=0,
                    dtype='uint8'
                )

                logger.log(f"掩码形状: {mask.shape}")
                logger.log(f"掩码中前景像素数: {np.sum(mask)}")
        
        # 归一化图像
        if image.dtype != np.float32:
            if image.max() > 1.0:
                image = image.astype(np.float32) / 255.0
            else:
                image = image.astype(np.float32)
        
        # 图像切片
        logger.log(f"开始图像切片，切片大小: {slice_size}, 重叠: {overlap}")
        
        step = slice_size - overlap
        image_patches = []
        mask_patches = []
        
        for i in range(0, image.shape[0] - slice_size + 1, step):
            for j in range(0, image.shape[1] - slice_size + 1, step):
                img_patch = image[i:i+slice_size, j:j+slice_size]
                mask_patch = mask[i:i+slice_size, j:j+slice_size]

                # 改进的过滤逻辑，适用于多分类
                if np.sum(img_patch) > 0:  # 图像不能全黑
                    if multiclass_mode:
                        # 多分类模式：检查是否有前景类别（非背景）
                        foreground_pixels = np.sum(mask_patch > 0)
                        total_pixels = mask_patch.size
                        foreground_ratio = foreground_pixels / total_pixels
                        min_fg_ratio = model_args.get('min_foreground_ratio', 0.01)

                        # 保留有前景的块，或随机保留一些背景块用于平衡
                        if foreground_ratio >= min_fg_ratio or np.random.random() < 0.2:
                            image_patches.append(img_patch)
                            mask_patches.append(mask_patch)
                    else:
                        # 二分类模式：使用原有逻辑
                        if np.sum(mask_patch) > 0 or np.random.random() < 0.3:
                            image_patches.append(img_patch)
                            mask_patches.append(mask_patch)
        
        image_patches = np.array(image_patches, dtype=np.float32)
        mask_patches = np.array(mask_patches, dtype=np.uint8)
        
        logger.log(f"生成了 {len(image_patches)} 个图像块")
        
        # 内存优化：如果批次大小小，限制样本数量
        memory_optimization = trainer_args.get('memory_optimization', {})
        if memory_optimization.get('reduce_samples', False):
            max_samples = memory_optimization.get('max_samples', 15000)
            if len(image_patches) > max_samples:
                logger.log(f"[内存优化] 启用样本数量限制")
                logger.log(f"[内存优化] 将样本数从 {len(image_patches)} 减少到 {max_samples}")
                
                # 随机选择样本
                indices = np.random.choice(len(image_patches), max_samples, replace=False)
                image_patches = image_patches[indices]
                mask_patches = mask_patches[indices]
                
                logger.log(f"[内存优化] 样本减少完成，当前样本数: {len(image_patches)}")
        
        # 数据分割 - 使用索引分割而不是数据分割以节省内存
        total_samples = len(image_patches)
        train_size = int(0.8 * total_samples)
        
        # 创建索引数组并打乱
        indices = np.arange(total_samples)
        np.random.shuffle(indices)
        
        train_indices = indices[:train_size]
        test_indices = indices[train_size:]
        
        logger.log(f"最终训练样本数: {len(train_indices)}, 测试样本数: {len(test_indices)}")
        
        # 转换标签为分类格式
        mask_patches_categorical = to_categorical(mask_patches, num_classes=num_classes)
        
        # 获取backbone配置（确保一致性）
        backbone = model_args.get('backbone', 'resnet50')

        # 应用backbone预处理
        logger.log(f"应用backbone预处理，使用: {backbone}")

        # 手动实现ImageNet预处理（因为segmentation_models的ResNet预处理有问题）
        def manual_imagenet_preprocess(x):
            """手动ImageNet预处理"""
            x = x.copy().astype(np.float32)
            # ImageNet均值和标准差
            mean = np.array([123.675, 116.28, 103.53])
            std = np.array([58.395, 57.12, 57.375])

            # 标准化
            x[..., 0] -= mean[0]
            x[..., 1] -= mean[1]
            x[..., 2] -= mean[2]
            x[..., 0] /= std[0]
            x[..., 1] /= std[1]
            x[..., 2] /= std[2]

            return x

        # 预处理图像数据
        use_simple_preprocessing = model_args.get('use_simple_preprocessing', False)

        if use_simple_preprocessing:
            # 使用简单预处理（类似UNet）
            image_patches_preprocessed = image_patches.astype(np.float32) / 255.0
            logger.log("使用简单预处理（X/255.0）")
        elif backbone.startswith('resnet'):
            # 对ResNet使用手动预处理
            image_patches_preprocessed = manual_imagenet_preprocess(image_patches)
            logger.log("使用手动ImageNet预处理（ResNet）")
        else:
            # 对其他backbone使用segmentation_models预处理
            preprocess_input = sm.get_preprocessing(backbone)
            image_patches_preprocessed = preprocess_input(image_patches.copy())
            logger.log("使用segmentation_models预处理")

        logger.log(f"预处理前图像范围: [{image_patches.min():.4f}, {image_patches.max():.4f}]")
        logger.log(f"预处理后图像范围: [{image_patches_preprocessed.min():.4f}, {image_patches_preprocessed.max():.4f}]")

        # 创建数据生成器（训练时启用打乱和数据增强，验证时不打乱）
        use_data_augmentation = model_args.get('use_data_augmentation', False)
        train_generator = DataGenerator(image_patches_preprocessed, mask_patches_categorical, train_indices,
                                      batch_size, shuffle=True, use_augmentation=use_data_augmentation, is_training=True)
        test_generator = DataGenerator(image_patches_preprocessed, mask_patches_categorical, test_indices,
                                     batch_size, shuffle=False, use_augmentation=False, is_training=False)

        # 创建模型（使用相同的backbone）
        output_stride = model_args.get('output_stride', 16)
        logger.log(f"创建DeepLabV3+模型，骨干网络: {backbone}, 输出步长: {output_stride}")
        
        model = create_deeplabv3plus_model(
            input_shape=(slice_size, slice_size, 3),
            num_classes=num_classes,
            backbone=backbone,
            output_stride=output_stride
        )
        
        # 计算类别权重（基于分类格式的标签）
        if multiclass_mode:
            # 多分类模式：基于one-hot编码的标签计算权重
            class_labels = np.argmax(mask_patches_categorical, axis=-1).flatten()
            unique_labels = np.unique(class_labels)
            class_weights = compute_class_weight('balanced', classes=unique_labels, y=class_labels)
            class_weight_dict = dict(zip(unique_labels, class_weights))
            logger.log(f"多分类类别权重: {class_weight_dict}")

            # 统计各类别样本数
            for class_id in unique_labels:
                count = np.sum(class_labels == class_id)
                class_name = class_names[class_id] if class_id < len(class_names) else f"类别{class_id}"
                logger.log(f"  {class_name} (ID:{class_id}): {count} 个像素")
        else:
            # 二分类模式：使用原有逻辑
            unique_labels = np.unique(mask_patches.flatten())
            class_weights = compute_class_weight('balanced', classes=unique_labels, y=mask_patches.flatten())
            class_weight_dict = dict(zip(unique_labels, class_weights))
            logger.log(f"二分类类别权重: {class_weight_dict}")

        # 定义UNet风格的组合损失函数（提升分割性能）
        def dice_loss(y_true, y_pred, smooth=1e-6):
            """Dice损失函数"""
            y_true_f = tf.cast(tf.reshape(y_true[..., 1], [-1]), tf.float32)  # 前景类
            y_pred_f = tf.cast(tf.reshape(y_pred[..., 1], [-1]), tf.float32)  # 前景类预测

            intersection = tf.reduce_sum(y_true_f * y_pred_f)
            union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f)
            dice = (2. * intersection + smooth) / (union + smooth)
            return 1 - dice

        def focal_loss(y_true, y_pred, alpha=0.25, gamma=2.0):
            """Focal损失函数"""
            epsilon = tf.keras.backend.epsilon()
            y_pred = tf.clip_by_value(y_pred, epsilon, 1. - epsilon)

            # 计算前景类的focal loss
            y_true_fg = y_true[..., 1]
            y_pred_fg = y_pred[..., 1]

            p_t = tf.where(tf.equal(y_true_fg, 1), y_pred_fg, 1 - y_pred_fg)
            alpha_factor = tf.ones_like(y_true_fg) * alpha
            alpha_t = tf.where(tf.equal(y_true_fg, 1), alpha_factor, 1 - alpha_factor)
            cross_entropy = -tf.math.log(p_t)
            weight = alpha_t * tf.pow((1 - p_t), gamma)
            return tf.reduce_mean(weight * cross_entropy)

        def combined_loss(y_true, y_pred):
            """组合损失函数：Dice + Focal"""
            dice = dice_loss(y_true, y_pred)
            focal = focal_loss(y_true, y_pred)

            # 确保损失值是有限的
            dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))
            focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))

            combined = dice + focal
            combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))
            return tf.cast(combined, tf.float32)

        # 使用segmentation_models库的标准指标，确保一致性
        def create_metrics():
            """创建标准化的指标函数"""
            return [
                sm.metrics.IOUScore(threshold=0.5, name='categorical_iou_score'),
                sm.metrics.FScore(threshold=0.5, name='categorical_f1_score'),
                'accuracy'
            ]

        # 编译模型 - 使用配置文件中的学习率（已经是合适的值）
        optimizer = Adam(
            learning_rate=learning_rate,  # 直接使用配置文件中的学习率
            clipnorm=1.0,      # 梯度范数裁剪
            clipvalue=0.5,     # 梯度值裁剪
            epsilon=1e-7       # 数值稳定性
        )

        logger.log(f"优化器配置: lr={learning_rate:.1e}, clipnorm=1.0, clipvalue=0.5")

        # 暂时保持标准损失函数（避免GPU内存问题）
        # TODO: 后续可尝试组合损失函数进一步提升性能
        loss = sm.losses.CategoricalCELoss()
        metrics = create_metrics()

        logger.log("使用标准分类交叉熵损失 + 改进的优化器配置")
        model.compile(optimizer=optimizer, loss=loss, metrics=metrics)
        
        logger.log(f"模型参数总数: {model.count_params():,}")
        
        # 设置回调
        save_dir = trainer_args['save_dir']
        os.makedirs(save_dir, exist_ok=True)

        # 获取模型保存路径
        model_save_path = trainer_args.get('model_save_path', None)
        if not model_save_path:
            # 如果没有指定完整路径，使用默认命名
            model_suffix = f"multiclass_{num_classes}classes" if multiclass_mode else "binary"
            model_save_path = os.path.join(save_dir, f'deeplabv3plus_{model_suffix}_best_model.h5')

        logger.log(f"模型将保存到: {model_save_path}")

        # 创建训练报告生成器
        report_dir = os.path.join(save_dir, 'training_reports')
        reporter = DeepLabV3PlusTrainingReporter(report_dir, model_name="DeepLabV3Plus")

        # 准备配置和数据集信息
        training_config = {
            'batch_size': batch_size,
            'epochs': epochs,
            'learning_rate': learning_rate,
            'slice_size': slice_size,
            'overlap': overlap,
            'backbone': backbone,
            'output_stride': output_stride,
            'multiclass_mode': multiclass_mode,
            'num_classes': num_classes,
            'data_mode': data_mode
        }

        dataset_info = {
            'image_path': image_path,
            'shape_path': shape_path if data_mode == 'image+shape' else mask_path,
            'image_shape': image.shape,
            'mask_shape': mask.shape,
            'total_patches': len(image_patches),
            'train_patches': len(train_indices),
            'test_patches': len(test_indices)
        }

        # 开始训练记录
        reporter.start_training(training_config, dataset_info)

        # 从配置中获取回调参数
        early_stopping_config = trainer_args.get('early_stopping', {})
        lr_scheduler_config = trainer_args.get('lr_scheduler', {})

        callbacks = [
            OneLineProgressCallback(),  # 一行进度条
            ModelCheckpoint(
                model_save_path,
                save_best_only=True,
                monitor='val_categorical_iou_score',  # 使用自定义IoU指标名称
                mode='max',
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=lr_scheduler_config.get('factor', 0.5),
                patience=lr_scheduler_config.get('patience', 5),
                min_lr=lr_scheduler_config.get('min_lr', 1e-7),
                verbose=1
            ),
            EarlyStopping(
                monitor=early_stopping_config.get('monitor', 'val_categorical_iou_score'),
                mode=early_stopping_config.get('mode', 'max'),
                patience=early_stopping_config.get('patience', 5),
                restore_best_weights=True,
                verbose=1
            ),
            TensorBoard(
                log_dir=os.path.join(save_dir, 'logs'),
                histogram_freq=1
            )
        ]
        
        # 训练模型
        logger.log("开始训练模型...")
        start_time = time.time()
        history = model.fit(
            train_generator,
            epochs=epochs,
            validation_data=test_generator,
            callbacks=callbacks,
            verbose=0  # 使用自定义一行进度条
        )
        end_time = time.time()
        training_duration = end_time - start_time

        # 保存最终模型（使用用户指定的路径）
        # 为最终模型创建一个带后缀的文件名
        base_name = os.path.splitext(model_save_path)[0]
        extension = os.path.splitext(model_save_path)[1]
        final_model_path = f"{base_name}_final{extension}"

        model.save(final_model_path)
        logger.log(f"最终模型已保存到: {final_model_path}")
        logger.log(f"最佳模型已保存到: {model_save_path}")

        # 生成训练报告
        logger.log("正在生成训练报告...")
        reporter.generate_training_report(history, model_save_path, training_duration)

        # 输出报告文件信息
        logger.log(f"📊 训练报告已生成:")
        logger.log(f"   - Markdown报告: {os.path.join(reporter.save_dir, f'training_report_{reporter.timestamp}.md')}")
        logger.log(f"   - HTML报告: {os.path.join(reporter.save_dir, f'training_report_{reporter.timestamp}.html')}")
        logger.log(f"   - 训练曲线: {os.path.join(reporter.save_dir, f'training_curves_{reporter.timestamp}.png')}")
        logger.log(f"   - 训练历史: {os.path.join(reporter.save_dir, f'training_history_{reporter.timestamp}.json')}")

        logger.log("=== DeepLabV3+ 训练完成 ===")
        return True
        
    except Exception as e:
        logger.log(f"训练过程中出现错误: {str(e)}")
        import traceback
        logger.log(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        logger.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='训练DeepLabV3+分割模型')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    args = parser.parse_args()
    
    success = train_deeplabv3plus(args.config)
    if success:
        print("DeepLabV3+模型训练成功完成！")
    else:
        print("DeepLabV3+模型训练失败！")
        sys.exit(1)
