{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepLabV3+ 模型训练 - 云端版本\n", "本notebook用于在云端平台（如Google Colab）上训练DeepLabV3+模型进行卫星图像分割。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 安装必要的依赖\n", "%%capture\n", "!pip install rasterio\n", "!pip install geopandas\n", "!pip install patchify\n", "!pip install segmentation-models\n", "!pip install tensorflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 导入必要的库\n", "import os\n", "import cv2\n", "import numpy as np\n", "import rasterio\n", "import geopandas as gpd\n", "from rasterio.features import rasterize\n", "from patchify import patchify\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "import random\n", "import tensorflow as tf\n", "import keras\n", "from keras.utils import to_categorical, Sequence\n", "from keras.optimizers import Adam\n", "from keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard\n", "from datetime import datetime\n", "import json\n", "\n", "# 设置 segmentation_models 的后端\n", "os.environ['SM_FRAMEWORK'] = 'tf.keras'\n", "\n", "import segmentation_models as sm\n", "sm.set_framework('tf.keras')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 设置混合精度\n", "tf.keras.mixed_precision.set_global_policy('mixed_float16')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 配置参数\n", "CONFIG = {\n", "    'dataset_path': './dataset',  # 数据集路径\n", "    'model': {\n", "        'args': {\n", "            'backbone': 'resnet101',\n", "            'input_shape': (256, 256, 3),\n", "            'classes': 2,\n", "            'activation': 'softmax',\n", "            'pretrained': True\n", "        }\n", "    },\n", "    'trainer': {\n", "        'batch_size': 8,\n", "        'epochs': 100,\n", "        'learning_rate': 0.001,\n", "        'slice_size': 256,\n", "        'overlap': 0.2,\n", "        'save_dir': './models'  # 模型保存路径\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 数据生成器类\n", "class DataGenerator(keras.utils.Sequence):\n", "    def __init__(self, x_set, y_set, batch_size):\n", "        self.x, self.y = x_set, y_set\n", "        self.batch_size = batch_size\n", "\n", "    def __len__(self):\n", "        return int(np.ceil(len(self.x) / float(self.batch_size)))\n", "\n", "    def __getitem__(self, idx):\n", "        batch_x = self.x[idx * self.batch_size:(idx + 1) * self.batch_size]\n", "        batch_y = self.y[idx * self.batch_size:(idx + 1) * self.batch_size]\n", "        return batch_x, batch_y"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 上传数据集\n", "from google.colab import files\n", "\n", "print(\"请上传卫星图像文件（.tif）...\")\n", "uploaded = files.upload()\n", "\n", "print(\"\\n请上传shape文件（.shp）...\")\n", "uploaded_shape = files.upload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 训练函数\n", "def train_deeplabv3plus(config):\n", "    \"\"\"训练DeepLabV3+模型\"\"\"\n", "    print(\"=== 开始训练 DeepLabV3+ ===\")\n", "    \n", "    # 获取训练参数\n", "    model_args = config['model']['args']\n", "    trainer_args = config['trainer']\n", "    \n", "    # 设置训练参数\n", "    batch_size = trainer_args['batch_size']\n", "    epochs = trainer_args['epochs']\n", "    learning_rate = trainer_args['learning_rate']\n", "    slice_size = trainer_args['slice_size']\n", "    overlap = trainer_args['overlap']\n", "    save_dir = trainer_args['save_dir']\n", "    \n", "    # 创建保存目录\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    \n", "    # 读取数据\n", "    with rasterio.open('test.tif') as src:  # 根据实际上传的文件名修改\n", "        image = src.read().transpose((1, 2, 0))\n", "        \n", "    gdf = gpd.read_file('Buildings.shp')  # 根据实际上传的文件名修改\n", "    \n", "    # 确保图像是3波段\n", "    if image.shape[2] > 3:\n", "        image = image[:, :, :3]\n", "    \n", "    # 创建掩码\n", "    mask = rasterize(\n", "        [(shape, 1) for shape in gdf.geometry],\n", "        out_shape=image.shape[:2],\n", "        transform=src.transform,\n", "        fill=0,\n", "        dtype='uint8'\n", "    )\n", "    \n", "    # 数据预处理\n", "    minmaxscaler = MinMaxScaler()\n", "    image = minmaxscaler.fit_transform(image.reshape(-1, image.shape[-1])).reshape(image.shape)\n", "    mask = np.expand_dims(mask, axis=-1)\n", "    \n", "    # 创建图像块\n", "    def create_patches(img, mask, size=slice_size, stride=slice_size-overlap):\n", "        patches_img = patchify(img, (size, size, img.shape[-1]), step=stride)\n", "        patches_mask = patchify(mask, (size, size, 1), step=stride)\n", "        \n", "        patches_img = patches_img.reshape(-1, size, size, img.shape[-1])\n", "        patches_mask = patches_mask.reshape(-1, size, size, 1)\n", "        \n", "        return patches_img, patches_mask\n", "    \n", "    image_patches, mask_patches = create_patches(image, mask)\n", "    print(f\"创建了 {len(image_patches)} 个图像块\")\n", "    \n", "    # 转换标签为分类格式\n", "    mask_categorical = to_categorical(mask_patches, num_classes=2)\n", "    \n", "    # 划分训练集和验证集\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        image_patches, mask_categorical, \n", "        test_size=0.2, random_state=42\n", "    )\n", "    \n", "    # 创建 DeepLabV3+ 模型\n", "    backbone = model_args.get('backbone', 'resnet101')\n", "    preprocess_input = sm.get_preprocessing(backbone)\n", "    \n", "    # 预处理训练数据\n", "    X_train = preprocess_input(X_train)\n", "    X_val = preprocess_input(X_val)\n", "    \n", "    # 创建模型\n", "    model = sm.Deeplabv3plus(\n", "        backbone_name=backbone,\n", "        input_shape=(slice_size, slice_size, 3),\n", "        classes=2,\n", "        activation='softmax',\n", "        encoder_weights='imagenet' if model_args.get('pretrained', True) else None\n", "    )\n", "    \n", "    # 配置优化器和损失函数\n", "    optimizer = Adam(learning_rate=learning_rate)\n", "    loss = sm.losses.CategoricalCELoss()\n", "    metrics = [\n", "        sm.metrics.IOUScore(threshold=0.5),\n", "        sm.metrics.FScore(threshold=0.5),\n", "        'accuracy'\n", "    ]\n", "    \n", "    model.compile(optimizer, loss, metrics)\n", "    \n", "    # 创建回调函数\n", "    callbacks = [\n", "        ReduceLROnPlateau(\n", "            monitor='val_loss',\n", "            factor=0.5,\n", "            patience=5,\n", "            min_lr=1e-6\n", "        ),\n", "        ModelCheckpoint(\n", "            os.path.join(save_dir, 'best_model.h5'),\n", "            monitor='val_iou_score',\n", "            mode='max',\n", "            save_best_only=True\n", "        ),\n", "        EarlyStopping(\n", "            monitor='val_loss',\n", "            patience=10,\n", "            restore_best_weights=True\n", "        ),\n", "        TensorBoard(log_dir=os.path.join(save_dir, 'logs'))\n", "    ]\n", "    \n", "    # 创建数据生成器\n", "    train_gen = DataGenerator(X_train, y_train, batch_size)\n", "    val_gen = DataGenerator(X_val, y_val, batch_size)\n", "    \n", "    # 训练模型\n", "    print(\"开始训练...\")\n", "    history = model.fit(\n", "        train_gen,\n", "        validation_data=val_gen,\n", "        epochs=epochs,\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    return history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 开始训练\n", "history = train_deeplabv3plus(CONFIG)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 绘制训练历史\n", "plt.figure(figsize=(15, 5))\n", "\n", "# 绘制损失\n", "plt.subplot(1, 3, 1)\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "\n", "# 绘制IoU\n", "plt.subplot(1, 3, 2)\n", "plt.plot(history.history['iou_score'], label='Training IoU')\n", "plt.plot(history.history['val_iou_score'], label='Validation IoU')\n", "plt.title('Model IoU')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('IoU')\n", "plt.legend()\n", "\n", "# 绘制准确率\n", "plt.subplot(1, 3, 3)\n", "plt.plot(history.history['accuracy'], label='Training Accuracy')\n", "plt.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "plt.title('Model Accuracy')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}