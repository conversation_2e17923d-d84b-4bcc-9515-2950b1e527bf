关于备份的存储位置：
这些脚本的功能说明：
backup_code.py:
自动检查是否初始化了 Git 仓库
显示需要备份的文件
提示输入版本号和描述
创建 Git 标签并推送到备份仓库
显示备份结果
restore_code.py:
显示所有可用的版本列表
检查是否有未保存的更改
允许选择要恢复的版本
安全地恢复到选定版本
显示恢复结果
code_manager.bat:
提供友好的菜单界面
可以方便地选择备份或恢复操作
支持中文显示
操作完成后自动返回菜单
使用方法：
将这三个文件放在项目根目录下
双击运行 code_manager.bat
在菜单中选择需要的操作：
选择 1 进行代码备份
选择 2 恢复之前的版本
选择 3 退出程序
备份示例：
恢复示例：
这些脚本提供了一个简单但完整的代码版本管理解决方案。您可以轻松地：
备份不同版本的代码
为每个版本添加描述信息
查看所有可用的版本
4. 安全地恢复到任何之前的版本


本地 Git 仓库：
在项目目录下的 .git 文件夹中存储了所有的版本信息
包含了所有的提交历史、标签和分支信息
这个文件夹是隐藏的，但你可以在文件资源管理器中设置显示隐藏文件来查看它
备份仓库：
位于 D:\Satellite_Image_Auto_Offset\backup\autooffset_backup.git
这是一个裸仓库（bare repository），只包含 Git 的数据库，没有工作目录
所有的版本信息都在这里有一个完整的备份
具体的文件结构：
D:\Satellite_Image_Auto_Offset\
├── Autooffset_Test\              # 项目目录
│   ├── .git\                     # 本地Git仓库
│   │   ├── objects\             # Git对象数据库
│   │   ├── refs\                # 引用（分支和标签）
│   │   └── ...
│   ├── .gitignore               # 忽略文件配置
│   ├── backup_code.py           # 备份脚本
│   ├── restore_code.py          # 恢复脚本
│   └── code_manager.bat         # 管理脚本
│
└── backup\                       # 备份目录
    └── autooffset_backup.git\    # 备份仓库
        ├── objects\             # Git对象数据库
        ├── refs\                # 引用（分支和标签）
        └── ...
当你执行备份操作时：
首先会在本地 .git 文件夹中保存版本信息
然后通过 git push 命令将这些信息同步到备份仓库
两个位置都会保存完整的版本历史，这样即使一个位置的数据损坏，还有另一个备份
如果你想查看具体的版本信息：
1. 在项目目录下运行 git log 可以查看提交历史
运行 git tag 可以查看所有的版本标签
运行 git show <tag-name> 可以查看特定版本的详细信息
这种双重备份机制可以确保你的代码版本历史的安全性。即使项目目录下的 .git 文件夹损坏，你仍然可以从备份仓库恢复所有的版本历史。