"""
LogCAN训练核心逻辑
包括损失函数、优化器、学习率调度、指标计算等，支持GPU/CPU训练
"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import json

class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡"""
    
    def __init__(self, alpha=1, gamma=2, ignore_index=255, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, ignore_index=self.ignore_index, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class DiceLoss(nn.Module):
    """Dice Loss用于分割任务"""
    
    def __init__(self, smooth=1e-5, ignore_index=255):
        super().__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index
    
    def forward(self, inputs, targets):
        # 转换为概率
        inputs = F.softmax(inputs, dim=1)
        
        # 创建mask排除ignore_index
        mask = (targets != self.ignore_index)
        targets = targets * mask.long()
        
        # 计算每个类别的Dice
        dice_scores = []
        num_classes = inputs.size(1)
        
        for c in range(num_classes):
            input_c = inputs[:, c]
            target_c = (targets == c).float()
            
            # 应用mask
            input_c = input_c * mask.float()
            target_c = target_c * mask.float()
            
            intersection = (input_c * target_c).sum()
            union = input_c.sum() + target_c.sum()
            
            dice = (2 * intersection + self.smooth) / (union + self.smooth)
            dice_scores.append(dice)
        
        return 1 - torch.stack(dice_scores).mean()

class CombinedLoss(nn.Module):
    """组合损失函数"""
    
    def __init__(self, num_classes=2, use_focal=False, use_dice=False, 
                 ce_weight=1.0, focal_weight=1.0, dice_weight=1.0, 
                 aux_weight=0.4, ignore_index=255):
        super().__init__()
        
        self.ce_loss = nn.CrossEntropyLoss(ignore_index=ignore_index)
        self.focal_loss = FocalLoss(ignore_index=ignore_index) if use_focal else None
        self.dice_loss = DiceLoss(ignore_index=ignore_index) if use_dice else None
        
        self.ce_weight = ce_weight
        self.focal_weight = focal_weight
        self.dice_weight = dice_weight
        self.aux_weight = aux_weight
        
        self.use_focal = use_focal
        self.use_dice = use_dice
    
    def forward(self, outputs, targets):
        if isinstance(outputs, tuple):
            main_out, aux_out = outputs
        else:
            main_out = outputs
            aux_out = None
        
        # 主损失
        total_loss = self.ce_weight * self.ce_loss(main_out, targets)
        
        if self.use_focal and self.focal_loss is not None:
            total_loss += self.focal_weight * self.focal_loss(main_out, targets)
        
        if self.use_dice and self.dice_loss is not None:
            total_loss += self.dice_weight * self.dice_loss(main_out, targets)
        
        # 辅助损失
        if aux_out is not None:
            aux_loss = self.ce_loss(aux_out, targets)
            total_loss += self.aux_weight * aux_loss
        
        return total_loss

class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self, num_classes=2, ignore_index=255):
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.reset()
    
    def reset(self):
        """重置指标"""
        self.total_correct = 0
        self.total_pixels = 0
        self.class_correct = np.zeros(self.num_classes)
        self.class_total = np.zeros(self.num_classes)
        self.confusion_matrix = np.zeros((self.num_classes, self.num_classes))
    
    def update(self, predictions, targets):
        """更新指标"""
        # 转换为numpy
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.cpu().numpy()
        
        # 创建有效像素mask
        valid_mask = (targets != self.ignore_index)
        
        if valid_mask.sum() == 0:
            return
        
        valid_preds = predictions[valid_mask]
        valid_targets = targets[valid_mask]
        
        # 总体准确率
        correct = (valid_preds == valid_targets).sum()
        self.total_correct += correct
        self.total_pixels += len(valid_targets)
        
        # 每类准确率
        for c in range(self.num_classes):
            class_mask = (valid_targets == c)
            if class_mask.sum() > 0:
                self.class_correct[c] += (valid_preds[class_mask] == c).sum()
                self.class_total[c] += class_mask.sum()
        
        # 混淆矩阵
        for i in range(self.num_classes):
            for j in range(self.num_classes):
                self.confusion_matrix[i, j] += ((valid_targets == i) & (valid_preds == j)).sum()
    
    def compute_metrics(self):
        """计算最终指标"""
        metrics = {}
        
        # 总体准确率
        metrics['overall_accuracy'] = self.total_correct / max(self.total_pixels, 1)
        
        # 每类准确率
        class_accuracies = []
        for c in range(self.num_classes):
            if self.class_total[c] > 0:
                acc = self.class_correct[c] / self.class_total[c]
                class_accuracies.append(acc)
            else:
                class_accuracies.append(0.0)
        
        metrics['class_accuracies'] = class_accuracies
        metrics['mean_class_accuracy'] = np.mean(class_accuracies)
        
        # IoU计算
        ious = []
        for c in range(self.num_classes):
            tp = self.confusion_matrix[c, c]
            fp = self.confusion_matrix[:, c].sum() - tp
            fn = self.confusion_matrix[c, :].sum() - tp
            
            if tp + fp + fn > 0:
                iou = tp / (tp + fp + fn)
                ious.append(iou)
            else:
                ious.append(0.0)
        
        metrics['class_ious'] = ious
        metrics['mean_iou'] = np.mean(ious)
        
        # F1分数
        f1_scores = []
        for c in range(self.num_classes):
            tp = self.confusion_matrix[c, c]
            fp = self.confusion_matrix[:, c].sum() - tp
            fn = self.confusion_matrix[c, :].sum() - tp
            
            precision = tp / max(tp + fp, 1)
            recall = tp / max(tp + fn, 1)
            f1 = 2 * precision * recall / max(precision + recall, 1e-8)
            f1_scores.append(f1)
        
        metrics['class_f1_scores'] = f1_scores
        metrics['mean_f1_score'] = np.mean(f1_scores)
        
        return metrics

class LogCANTrainer:
    """LogCAN训练器"""
    
    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.device = torch.device(config.training_config['device'])
        
        # 移动模型到设备
        self.model.to(self.device)
        
        # 创建损失函数
        loss_config = config.training_config.get('loss', {})
        self.criterion = CombinedLoss(
            num_classes=config.model_config['num_classes'],
            use_focal=loss_config.get('use_focal', False),
            use_dice=loss_config.get('use_dice', False),
            ce_weight=loss_config.get('ce_weight', 1.0),
            focal_weight=loss_config.get('focal_weight', 1.0),
            dice_weight=loss_config.get('dice_weight', 1.0),
            aux_weight=loss_config.get('aux_weight', 0.4)
        )
        
        # 创建优化器
        self.optimizer = self._create_optimizer()
        
        # 创建学习率调度器
        self.scheduler = self._create_scheduler()
        
        # 指标计算器
        self.train_metrics = MetricsCalculator(config.model_config['num_classes'])
        self.val_metrics = MetricsCalculator(config.model_config['num_classes'])
        
        # 训练历史
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': [],
            'train_miou': [],
            'val_miou': [],
            'learning_rates': [],
            'train_class_metrics': [],  # 训练分类指标历史
            'val_class_metrics': []     # 验证分类指标历史
        }
        
        # 最佳指标
        self.best_val_miou = 0.0
        self.best_epoch = 0
    
    def _create_optimizer(self):
        """创建优化器"""
        optimizer_config = self.config.training_config.get('optimizer', {})
        optimizer_type = optimizer_config.get('type', 'Adam')
        
        if optimizer_type == 'Adam':
            return optim.Adam(
                self.model.parameters(),
                lr=self.config.training_config['learning_rate'],
                weight_decay=self.config.training_config.get('weight_decay', 1e-4)
            )
        elif optimizer_type == 'SGD':
            return optim.SGD(
                self.model.parameters(),
                lr=self.config.training_config['learning_rate'],
                momentum=optimizer_config.get('momentum', 0.9),
                weight_decay=self.config.training_config.get('weight_decay', 1e-4)
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler_config = self.config.training_config.get('scheduler', {})
        scheduler_type = scheduler_config.get('type', 'CosineAnnealingLR')
        
        if scheduler_type == 'CosineAnnealingLR':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.training_config['epochs'],
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
        elif scheduler_type == 'StepLR':
            return optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 10),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_type == 'ReduceLROnPlateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=scheduler_config.get('factor', 0.5),
                patience=scheduler_config.get('patience', 5),
                verbose=True
            )
        else:
            return None
    
    def train_epoch(self, train_loader, epoch, total_epochs=None):
        """训练一个epoch"""
        self.model.train()
        self.train_metrics.reset()
        
        total_loss = 0
        num_batches = len(train_loader)
        
        print(f"\nEpoch {epoch+1} - 训练阶段")
        print("-" * 50)
        
        for batch_idx, (images, targets) in enumerate(train_loader):
            images = images.to(self.device)
            targets = targets.to(self.device)
            
            # 前向传播
            outputs = self.model(images)
            loss = self.criterion(outputs, targets)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            if self.config.training_config.get('gradient_clip_norm'):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config.training_config['gradient_clip_norm']
                )
            
            self.optimizer.step()
            
            # 计算指标
            if isinstance(outputs, tuple):
                pred = outputs[0].argmax(dim=1)
            else:
                pred = outputs.argmax(dim=1)
            
            self.train_metrics.update(pred.cpu().numpy(), targets.cpu().numpy())
            total_loss += loss.item()
            
            # 显示统一进度条（覆盖式显示）
            if batch_idx % 5 == 0 or batch_idx == num_batches - 1:
                if total_epochs:
                    # 计算总体进度：当前epoch进度 + 之前epoch的进度
                    current_epoch_progress = (batch_idx + 1) / num_batches
                    total_progress = ((epoch + current_epoch_progress) / total_epochs) * 100

                    # 创建进度条
                    bar_length = 30
                    filled_length = int(bar_length * current_epoch_progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)

                    # 覆盖式显示进度条
                    progress_text = (f"\r  训练进度: {total_progress:6.2f}% |{bar}| "
                                   f"Epoch {epoch+1}/{total_epochs} | "
                                   f"Batch {batch_idx+1}/{num_batches} | "
                                   f"Loss: {loss.item():.4f} | "
                                   f"LR: {self.optimizer.param_groups[0]['lr']:.6f}")
                    print(progress_text, end='', flush=True)
                else:
                    # 只显示当前epoch进度
                    progress = (batch_idx + 1) / num_batches
                    bar_length = 30
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)

                    progress_text = (f"\r  训练进度: |{bar}| "
                                   f"{batch_idx+1:4d}/{num_batches} | "
                                   f"Loss: {loss.item():.4f} | "
                                   f"LR: {self.optimizer.param_groups[0]['lr']:.6f}")
                    print(progress_text, end='', flush=True)
        
        # 计算epoch指标
        avg_loss = total_loss / num_batches
        train_metrics = self.train_metrics.compute_metrics()
        
        # 记录历史
        self.history['train_loss'].append(avg_loss)
        self.history['train_acc'].append(train_metrics['overall_accuracy'])
        self.history['train_miou'].append(train_metrics['mean_iou'])
        self.history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
        
        print(f"\n训练结果:")
        print(f"  损失: {avg_loss:.4f}")
        print(f"  准确率: {train_metrics['overall_accuracy']:.4f}")
        print(f"  mIoU: {train_metrics['mean_iou']:.4f}")
        print(f"  mF1: {train_metrics['mean_f1_score']:.4f}")

        # 显示分类精度表格
        self._print_class_metrics_table(train_metrics, "训练")

        # 保存分类指标历史
        self.history['train_class_metrics'].append({
            'epoch': epoch + 1,
            'class_accuracies': train_metrics.get('class_accuracies', []),
            'class_ious': train_metrics.get('class_ious', []),
            'class_f1_scores': train_metrics.get('class_f1_scores', []),
            'mean_class_accuracy': train_metrics.get('mean_class_accuracy', 0),
            'mean_iou': train_metrics.get('mean_iou', 0),
            'mean_f1_score': train_metrics.get('mean_f1_score', 0)
        })

        return avg_loss, train_metrics

    def validate(self, val_loader, epoch):
        """验证模型"""
        self.model.eval()
        self.val_metrics.reset()

        total_loss = 0
        num_batches = len(val_loader)

        print(f"\nEpoch {epoch+1} - 验证阶段")
        print("-" * 50)

        with torch.no_grad():
            for batch_idx, (images, targets) in enumerate(val_loader):
                images = images.to(self.device)
                targets = targets.to(self.device)

                # 前向传播
                outputs = self.model(images)
                loss = self.criterion(outputs, targets)

                # 计算指标
                if isinstance(outputs, tuple):
                    pred = outputs[0].argmax(dim=1)
                else:
                    pred = outputs.argmax(dim=1)

                self.val_metrics.update(pred.cpu().numpy(), targets.cpu().numpy())
                total_loss += loss.item()

                # 显示验证进度条（覆盖式显示）
                if batch_idx % 3 == 0 or batch_idx == num_batches - 1:
                    progress = (batch_idx + 1) / num_batches
                    bar_length = 30
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)

                    progress_text = (f"\r  验证进度: |{bar}| "
                                   f"{batch_idx+1:4d}/{num_batches} | "
                                   f"Loss: {loss.item():.4f}")
                    print(progress_text, end='', flush=True)

        # 计算epoch指标
        avg_loss = total_loss / num_batches
        val_metrics = self.val_metrics.compute_metrics()

        # 记录历史
        self.history['val_loss'].append(avg_loss)
        self.history['val_acc'].append(val_metrics['overall_accuracy'])
        self.history['val_miou'].append(val_metrics['mean_iou'])

        print(f"\n验证结果:")
        print(f"  损失: {avg_loss:.4f}")
        print(f"  准确率: {val_metrics['overall_accuracy']:.4f}")
        print(f"  mIoU: {val_metrics['mean_iou']:.4f}")
        print(f"  mF1: {val_metrics['mean_f1_score']:.4f}")

        # 显示分类精度表格
        self._print_class_metrics_table(val_metrics, "验证")

        # 保存分类指标历史
        self.history['val_class_metrics'].append({
            'epoch': epoch + 1,
            'class_accuracies': val_metrics.get('class_accuracies', []),
            'class_ious': val_metrics.get('class_ious', []),
            'class_f1_scores': val_metrics.get('class_f1_scores', []),
            'mean_class_accuracy': val_metrics.get('mean_class_accuracy', 0),
            'mean_iou': val_metrics.get('mean_iou', 0),
            'mean_f1_score': val_metrics.get('mean_f1_score', 0)
        })

        # 检查是否是最佳模型
        current_miou = val_metrics['mean_iou']
        if current_miou > self.best_val_miou:
            self.best_val_miou = current_miou
            self.best_epoch = epoch
            print(f"  🎉 新的最佳模型! mIoU: {current_miou:.4f}")
            return avg_loss, val_metrics, True

        return avg_loss, val_metrics, False

    def _print_class_metrics_table(self, metrics, phase_name):
        """打印分类精度表格"""
        try:
            class_accuracies = metrics.get('class_accuracies', [])
            class_ious = metrics.get('class_ious', [])
            class_f1_scores = metrics.get('class_f1_scores', [])

            if not class_accuracies:
                return

            num_classes = len(class_accuracies)

            # 定义类别名称
            if num_classes == 2:
                class_names = ['背景', '目标']
            else:
                class_names = [f'类别{i}' for i in range(num_classes)]

            print(f"\n📊 {phase_name}分类精度详情:")
            print("+" + "-" * 65 + "+")
            print(f"| {'类别':<8} | {'准确率':<10} | {'IoU':<10} | {'F1分数':<10} | {'像素数':<8} |")
            print("+" + "-" * 65 + "+")

            for i in range(num_classes):
                class_name = class_names[i] if i < len(class_names) else f'类别{i}'
                accuracy = class_accuracies[i] if i < len(class_accuracies) else 0.0
                iou = class_ious[i] if i < len(class_ious) else 0.0
                f1 = class_f1_scores[i] if i < len(class_f1_scores) else 0.0

                # 获取像素数（从混淆矩阵）
                if hasattr(self, 'train_metrics') and phase_name == "训练":
                    pixel_count = int(self.train_metrics.class_total[i]) if i < len(self.train_metrics.class_total) else 0
                elif hasattr(self, 'val_metrics') and phase_name == "验证":
                    pixel_count = int(self.val_metrics.class_total[i]) if i < len(self.val_metrics.class_total) else 0
                else:
                    pixel_count = 0

                print(f"| {class_name:<8} | {accuracy:>8.4f}% | {iou:>8.4f} | {f1:>8.4f} | {pixel_count:>6d} |")

            print("+" + "-" * 65 + "+")
            print(f"| {'平均':<8} | {metrics.get('mean_class_accuracy', 0):>8.4f}% | {metrics.get('mean_iou', 0):>8.4f} | {metrics.get('mean_f1_score', 0):>8.4f} | {'--':<6} |")
            print("+" + "-" * 65 + "+")

        except Exception as e:
            print(f"⚠️  显示分类精度表格失败: {e}")

    def save_checkpoint(self, save_path, epoch, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_miou': self.best_val_miou,
            'best_epoch': self.best_epoch,
            'history': self.history,
            'config': self.config.to_dict()
        }

        if is_best:
            # 如果是最佳模型，只保存最佳模型文件，避免重复
            best_path = save_path.replace('.pth', '_best.pth')
            torch.save(checkpoint, best_path)
            print(f"  💾 最佳模型已保存: {best_path}")

            # 删除之前的最佳模型文件以节省空间
            import glob
            import os
            save_dir = os.path.dirname(save_path)
            pattern = os.path.join(save_dir, '*_best.pth')
            existing_best_files = glob.glob(pattern)
            for file in existing_best_files:
                if file != best_path and os.path.exists(file):
                    try:
                        os.remove(file)
                        print(f"  🗑️  删除旧的最佳模型: {os.path.basename(file)}")
                    except:
                        pass
        else:
            # 常规保存
            torch.save(checkpoint, save_path)
            print(f"  💾 检查点已保存: {save_path}")

    def load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        self.best_val_miou = checkpoint.get('best_val_miou', 0.0)
        self.best_epoch = checkpoint.get('best_epoch', 0)
        self.history = checkpoint.get('history', {
            'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': [],
            'train_miou': [], 'val_miou': [], 'learning_rates': []
        })

        print(f"✅ 检查点已加载: {checkpoint_path}")
        print(f"   最佳epoch: {self.best_epoch}, 最佳mIoU: {self.best_val_miou:.4f}")

        return checkpoint['epoch']

    def plot_training_curves(self, save_path):
        """绘制训练曲线"""
        if not self.history['train_loss']:
            print("⚠️  没有训练历史数据，跳过绘图")
            return

        # 设置字体和解决负号显示问题（参考UNet的实现）
        try:
            # 设置matplotlib后端为Agg，避免GUI相关的字体问题
            import matplotlib
            matplotlib.use('Agg')

            # 尝试设置中文字体
            import matplotlib.font_manager as fm
            import matplotlib.pyplot as plt

            # 查找可用的中文字体
            font_list = [f.name for f in fm.fontManager.ttflist]
            chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
            available_font = None

            for font in chinese_fonts:
                if font in font_list:
                    available_font = font
                    break

            if available_font:
                plt.rcParams['font.sans-serif'] = [available_font]
                print(f"✅ 使用字体: {available_font}")
                use_chinese = True
            else:
                # 如果没有中文字体，使用英文字体
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                print("⚠️ 未找到中文字体，使用英文字体")
                use_chinese = False

            # 解决负号显示问题
            plt.rcParams['axes.unicode_minus'] = False
            # 设置字体大小
            plt.rcParams['font.size'] = 10

        except Exception as e:
            print(f"⚠️ 字体设置失败，使用默认设置: {e}")
            # 使用默认字体
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            use_chinese = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('LoGCAN 训练报告' if use_chinese else 'LoGCAN Training Report', fontsize=16, fontweight='bold')

        # 根据字体可用性选择标签语言
        if use_chinese:
            # 中文标签
            train_loss_label, val_loss_label = '训练损失', '验证损失'
            train_acc_label, val_acc_label = '训练准确率', '验证准确率'
            train_miou_label, val_miou_label = '训练mIoU', '验证mIoU'
            loss_title, acc_title, miou_title, lr_title = '损失曲线', '准确率曲线', 'mIoU曲线', '学习率曲线'
            lr_label = '学习率'
        else:
            # 英文标签
            train_loss_label, val_loss_label = 'Train Loss', 'Val Loss'
            train_acc_label, val_acc_label = 'Train Accuracy', 'Val Accuracy'
            train_miou_label, val_miou_label = 'Train mIoU', 'Val mIoU'
            loss_title, acc_title, miou_title, lr_title = 'Loss Curves', 'Accuracy Curves', 'mIoU Curves', 'Learning Rate'
            lr_label = 'Learning Rate'

        # 损失曲线
        axes[0, 0].plot(self.history['train_loss'], label=train_loss_label, color='blue', linewidth=2)
        if self.history['val_loss']:
            axes[0, 0].plot(self.history['val_loss'], label=val_loss_label, color='red', linewidth=2)
        axes[0, 0].set_title(loss_title, fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 准确率曲线
        axes[0, 1].plot(self.history['train_acc'], label=train_acc_label, color='blue', linewidth=2)
        if self.history['val_acc']:
            axes[0, 1].plot(self.history['val_acc'], label=val_acc_label, color='red', linewidth=2)
        axes[0, 1].set_title(acc_title, fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # mIoU曲线
        axes[1, 0].plot(self.history['train_miou'], label=train_miou_label, color='blue', linewidth=2)
        if self.history['val_miou']:
            axes[1, 0].plot(self.history['val_miou'], label=val_miou_label, color='red', linewidth=2)
        axes[1, 0].set_title(miou_title, fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('mIoU')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 学习率曲线
        axes[1, 1].plot(self.history['learning_rates'], label=lr_label, color='green', linewidth=2)
        axes[1, 1].set_title(lr_title, fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Learning Rate')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].set_yscale('log')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 训练曲线已保存: {save_path}")

    def save_training_report(self, save_path):
        """保存训练报告"""
        report = {
            'training_config': self.config.to_dict(),
            'best_epoch': self.best_epoch,
            'best_val_miou': self.best_val_miou,
            'final_metrics': {
                'train_loss': self.history['train_loss'][-1] if self.history['train_loss'] else 0,
                'val_loss': self.history['val_loss'][-1] if self.history['val_loss'] else 0,
                'train_acc': self.history['train_acc'][-1] if self.history['train_acc'] else 0,
                'val_acc': self.history['val_acc'][-1] if self.history['val_acc'] else 0,
                'train_miou': self.history['train_miou'][-1] if self.history['train_miou'] else 0,
                'val_miou': self.history['val_miou'][-1] if self.history['val_miou'] else 0,
            },
            'training_history': self.history,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📋 训练报告已保存: {save_path}")

    def save_training_history(self, save_path):
        """保存详细的训练历史数据"""
        history_data = {
            'model_info': {
                'model_type': 'LoGCAN',
                'config': self.config.to_dict(),
                'best_epoch': self.best_epoch + 1,  # 转换为1-based
                'best_val_miou': self.best_val_miou,
                'total_epochs': len(self.history['train_loss'])
            },
            'training_history': self.history,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False)

        print(f"📊 训练历史已保存: {save_path}")

    def save_markdown_report(self, save_path, timestamp):
        """生成Markdown格式的训练报告"""
        best_epoch = self.best_epoch + 1  # 转换为1-based
        total_epochs = len(self.history['train_loss'])

        # 计算最终指标
        final_train_loss = self.history['train_loss'][-1] if self.history['train_loss'] else 0
        final_val_loss = self.history['val_loss'][-1] if self.history['val_loss'] else 0
        final_train_miou = self.history['train_miou'][-1] if self.history['train_miou'] else 0
        final_val_miou = self.history['val_miou'][-1] if self.history['val_miou'] else 0

        markdown_content = f"""# LoGCAN 训练报告

## 训练概览
- **模型类型**: LoGCAN (Localization-Guided Convolutional Attention Network)
- **训练时间**: {timestamp.replace('_', ' ')}
- **总训练轮数**: {total_epochs}
- **最佳轮数**: {best_epoch}
- **最佳验证mIoU**: {self.best_val_miou:.4f}

## 模型配置
- **学习率**: {self.config.training_config['learning_rate']}
- **批次大小**: {self.config.training_config['batch_size']}
- **权重衰减**: {self.config.training_config['weight_decay']}
- **设备**: {self.config.training_config['device']}

## 最终训练结果
| 指标 | 训练集 | 验证集 |
|------|--------|--------|
| 损失 | {final_train_loss:.4f} | {final_val_loss:.4f} |
| mIoU | {final_train_miou:.4f} | {final_val_miou:.4f} |

## 训练历史
### 损失变化
- 最低训练损失: {min(self.history['train_loss']) if self.history['train_loss'] else 0:.4f}
- 最低验证损失: {min(self.history['val_loss']) if self.history['val_loss'] else 0:.4f}

### mIoU变化
- 最高训练mIoU: {max(self.history['train_miou']) if self.history['train_miou'] else 0:.4f}
- 最高验证mIoU: {self.best_val_miou:.4f} (Epoch {best_epoch})

## 文件说明
- `training_curves_{timestamp}.png`: 训练曲线图
- `training_history_{timestamp}.json`: 详细训练历史数据
- `training_report_{timestamp}.json`: 结构化训练报告

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        print(f"📄 Markdown报告已保存: {save_path}")

    def save_html_report(self, save_path, timestamp):
        """生成HTML格式的训练报告"""
        try:
            best_epoch = self.best_epoch + 1  # 转换为1-based
            total_epochs = len(self.history['train_loss'])

            # 计算最终指标
            final_train_loss = self.history['train_loss'][-1] if self.history['train_loss'] else 0
            final_val_loss = self.history['val_loss'][-1] if self.history['val_loss'] else 0
            final_train_miou = self.history['train_miou'][-1] if self.history['train_miou'] else 0
            final_val_miou = self.history['val_miou'][-1] if self.history['val_miou'] else 0

            # 准备图表数据
            epochs = list(range(1, total_epochs + 1))

            html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoGCAN 训练报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .content {{
            padding: 30px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .metric-card {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-title {{
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }}
        .config-section {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        .config-title {{
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }}
        .config-item {{
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        .config-item:last-child {{
            border-bottom: none;
        }}
        .footer {{
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛰️ LoGCAN 训练报告</h1>
            <p>卫星图像分割模型训练结果 - 训练完成于 {timestamp.replace('_', ' ')}</p>
        </div>

        <div class="content">
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">最佳轮数</div>
                    <div class="metric-value">{best_epoch}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">最佳验证mIoU</div>
                    <div class="metric-value">{self.best_val_miou:.4f}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">最终训练损失</div>
                    <div class="metric-value">{final_train_loss:.4f}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">最终验证损失</div>
                    <div class="metric-value">{final_val_loss:.4f}</div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">📊 损失曲线</div>
                <canvas id="lossChart" width="400" height="200"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">🎯 mIoU曲线</div>
                <canvas id="miouChart" width="400" height="200"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">📈 学习率变化</div>
                <canvas id="lrChart" width="400" height="200"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">📋 分类精度详情</div>
                <div id="classMetricsTable"></div>
            </div>

            <div class="config-section">
                <div class="config-title">⚙️ 训练配置</div>
                <div class="config-item">
                    <span>学习率:</span>
                    <span>{self.config.training_config['learning_rate']}</span>
                </div>
                <div class="config-item">
                    <span>批次大小:</span>
                    <span>{self.config.training_config['batch_size']}</span>
                </div>
                <div class="config-item">
                    <span>总训练轮数:</span>
                    <span>{total_epochs}</span>
                </div>
                <div class="config-item">
                    <span>训练设备:</span>
                    <span>{self.config.training_config['device']}</span>
                </div>
                <div class="config-item">
                    <span>权重衰减:</span>
                    <span>{self.config.training_config['weight_decay']}</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p>🛰️ 卫星图像自动偏移系统 - LoGCAN训练模块</p>
        </div>
    </div>

    <script>
        // 损失曲线图表
        const lossCtx = document.getElementById('lossChart').getContext('2d');
        new Chart(lossCtx, {{
            type: 'line',
            data: {{
                labels: {epochs},
                datasets: [{{
                    label: '训练损失',
                    data: {self.history['train_loss']},
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                }}, {{
                    label: '验证损失',
                    data: {self.history['val_loss'] if self.history['val_loss'] else []},
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'top',
                    }},
                    title: {{
                        display: true,
                        text: '训练和验证损失变化'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: '损失值'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: '训练轮数'
                        }}
                    }}
                }}
            }}
        }});

        // mIoU曲线图表
        const miouCtx = document.getElementById('miouChart').getContext('2d');
        new Chart(miouCtx, {{
            type: 'line',
            data: {{
                labels: {epochs},
                datasets: [{{
                    label: '训练mIoU',
                    data: {self.history['train_miou']},
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                }}, {{
                    label: '验证mIoU',
                    data: {self.history['val_miou'] if self.history['val_miou'] else []},
                    borderColor: 'rgb(255, 159, 64)',
                    backgroundColor: 'rgba(255, 159, 64, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'top',
                    }},
                    title: {{
                        display: true,
                        text: '平均交并比(mIoU)变化'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        max: 1,
                        title: {{
                            display: true,
                            text: 'mIoU值'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: '训练轮数'
                        }}
                    }}
                }}
            }}
        }});

        // 学习率曲线图表
        const lrCtx = document.getElementById('lrChart').getContext('2d');
        new Chart(lrCtx, {{
            type: 'line',
            data: {{
                labels: {epochs},
                datasets: [{{
                    label: '学习率',
                    data: {self.history['learning_rates']},
                    borderColor: 'rgb(153, 102, 255)',
                    backgroundColor: 'rgba(153, 102, 255, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'top',
                    }},
                    title: {{
                        display: true,
                        text: '学习率调度变化'
                    }}
                }},
                scales: {{
                    y: {{
                        type: 'logarithmic',
                        title: {{
                            display: true,
                            text: '学习率(对数尺度)'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: '训练轮数'
                        }}
                    }}
                }}
            }}
        }});

        // 生成分类精度表格
        function generateClassMetricsTable() {{
            const tableContainer = document.getElementById('classMetricsTable');

            // 获取最新的训练和验证指标
            const trainMetrics = {self.history.get('train_class_metrics', [])};
            const valMetrics = {self.history.get('val_class_metrics', [])};

            if (trainMetrics.length === 0 && valMetrics.length === 0) {{
                tableContainer.innerHTML = '<p>暂无分类精度数据</p>';
                return;
            }}

            // 确定类别数量
            const numClasses = Math.max(
                trainMetrics.length > 0 ? trainMetrics[trainMetrics.length - 1].class_accuracies?.length || 2 : 2,
                valMetrics.length > 0 ? valMetrics[valMetrics.length - 1].class_accuracies?.length || 2 : 2
            );

            const classNames = numClasses === 2 ? ['背景', '目标'] : Array.from({{length: numClasses}}, (_, i) => `类别${{i}}`);

            let tableHTML = `
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">类别</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">训练准确率</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">验证准确率</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">训练IoU</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">验证IoU</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">训练F1</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">验证F1</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 获取最新的指标数据
            const latestTrainMetrics = trainMetrics.length > 0 ? trainMetrics[trainMetrics.length - 1] : {{}};
            const latestValMetrics = valMetrics.length > 0 ? valMetrics[valMetrics.length - 1] : {{}};

            for (let i = 0; i < numClasses; i++) {{
                const trainAcc = latestTrainMetrics.class_accuracies?.[i] || 0;
                const valAcc = latestValMetrics.class_accuracies?.[i] || 0;
                const trainIoU = latestTrainMetrics.class_ious?.[i] || 0;
                const valIoU = latestValMetrics.class_ious?.[i] || 0;
                const trainF1 = latestTrainMetrics.class_f1_scores?.[i] || 0;
                const valF1 = latestValMetrics.class_f1_scores?.[i] || 0;

                tableHTML += `
                    <tr style="${{i % 2 === 0 ? 'background-color: #f9f9f9;' : ''}}">
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${{classNames[i]}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{(trainAcc * 100).toFixed(2)}}%</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{(valAcc * 100).toFixed(2)}}%</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{trainIoU.toFixed(4)}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{valIoU.toFixed(4)}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{trainF1.toFixed(4)}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{valF1.toFixed(4)}}</td>
                    </tr>
                `;
            }}

            // 添加平均值行
            const trainMeanAcc = latestTrainMetrics.mean_class_accuracy || 0;
            const valMeanAcc = latestValMetrics.mean_class_accuracy || 0;
            const trainMeanIoU = latestTrainMetrics.mean_iou || 0;
            const valMeanIoU = latestValMetrics.mean_iou || 0;
            const trainMeanF1 = latestTrainMetrics.mean_f1_score || 0;
            const valMeanF1 = latestValMetrics.mean_f1_score || 0;

            tableHTML += `
                    <tr style="background-color: #e9ecef; font-weight: bold;">
                        <td style="border: 1px solid #ddd; padding: 12px;">平均值</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{(trainMeanAcc * 100).toFixed(2)}}%</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{(valMeanAcc * 100).toFixed(2)}}%</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{trainMeanIoU.toFixed(4)}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{valMeanIoU.toFixed(4)}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{trainMeanF1.toFixed(4)}}</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${{valMeanF1.toFixed(4)}}</td>
                    </tr>
                </tbody>
            </table>
            <p style="color: #666; font-size: 0.9em; margin-top: 10px;">
                📊 表格显示最终训练轮次的分类精度详情，包含每个类别的准确率、IoU和F1分数
            </p>
            `;

            tableContainer.innerHTML = tableHTML;
        }}

        generateClassMetricsTable();
    </script>
</body>
</html>"""

            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"🌐 HTML报告已保存: {save_path}")

        except Exception as e:
            print(f"⚠️  生成HTML报告失败: {e}")
            import traceback
            traceback.print_exc()

    def update_learning_rate(self, val_metric=None):
        """更新学习率"""
        if self.scheduler is None:
            return

        if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            if val_metric is not None:
                self.scheduler.step(val_metric)
        else:
            self.scheduler.step()
