# DeepLab多分类训练功能使用说明

## 概述

现在你的项目已经支持DeepLab多分类训练功能！这个功能在现有的DeepLabV3+基础上添加了多分类支持，同时保持与原有二分类模式的完全兼容。

## 主要特性

### ✅ 已实现的功能

1. **向后兼容**: 原有的二分类模式完全保持不变
2. **多分类支持**: 支持2-20个类别的语义分割
3. **GUI集成**: 在现有界面中添加了多分类参数控制
4. **智能配置**: 自动生成类别映射和颜色配置
5. **灵活数据源**: 支持Shapefile属性字段的多分类标注

### 🎯 DeepLab适合多分类的原因

- **语义分割专用**: DeepLab本身就是为多类别语义分割设计的
- **空洞卷积**: 能够捕获多尺度上下文信息，适合复杂场景
- **编码器-解码器结构**: 能够恢复精细的边界信息
- **预训练权重**: ImageNet预训练提供良好的特征提取能力

## 使用方法

### 1. 启动训练界面
1. 打开主程序
2. 点击"模型训练"按钮
3. 在模型类型中选择"DeepLabV3+"

### 2. 启用多分类模式
在模型参数区域，你会看到以下新增的参数：

- **启用多分类模式**: 勾选此选项启用多分类功能
- **类别数量**: 设置总类别数（包括背景）
- **类别名称**: 用逗号分隔的类别名称，如"背景,建筑物,道路,植被"
- **属性字段名**: Shapefile中包含类别信息的字段名

### 3. 配置示例

#### 二分类模式（默认）
```
启用多分类模式: ❌ (不勾选)
```
这时使用原有的二分类逻辑，所有要素标记为前景类别。

#### 多分类模式
```
启用多分类模式: ✅ (勾选)
类别数量: 4
类别名称: 背景,建筑物,道路,植被
属性字段名: land_use
```

### 4. 数据准备

#### Shapefile要求
你的Shapefile需要包含一个属性字段来标识不同的类别：

```
属性表示例:
ID | land_use  | area
1  | 建筑物    | 1500
2  | 道路      | 800
3  | 植被      | 2000
4  | 建筑物    | 1200
```

#### 支持的属性字段名
- `class_type`
- `land_use`
- `category`
- `type`
- 或任何自定义字段名

### 5. 训练过程

1. 设置好所有参数后，点击"开始训练"
2. 系统会自动：
   - 根据类别数量生成配置
   - 创建类别映射关系
   - 生成不同的类别颜色
   - 启动多分类训练

### 6. 输出结果

训练完成后会生成：

#### 模型文件
- `deeplabv3plus_multiclass_Nclasses_best_model.h5`: 最佳模型
- `deeplabv3plus_multiclass_Nclasses_final_model.h5`: 最终模型

#### 可视化结果
- `deeplabv3plus_multiclass_Nclasses_training_history.png`: 训练曲线

## 配置文件

### 自动生成的配置
当你在GUI中设置多分类参数时，系统会自动生成如下配置：

```json
{
  "model": {
    "args": {
      "multiclass_mode": true,
      "multiclass_config": {
        "num_classes": 4,
        "class_names": ["背景", "建筑物", "道路", "植被"],
        "class_colors": ["#000000", "#FF0000", "#00FF00", "#0000FF"],
        "class_mapping": {
          "建筑物": 1,
          "道路": 2,
          "植被": 3
        },
        "shape_attribute_field": "land_use",
        "background_class_id": 0,
        "ignore_index": 255
      }
    }
  }
}
```

### 手动配置示例
你也可以直接编辑配置文件 `config/deeplabv3plus_multiclass_example.json`。

## 常见问题

### Q: 如何在二分类和多分类之间切换？
A: 只需要在GUI中勾选或取消勾选"启用多分类模式"即可。

### Q: 我的Shapefile没有类别字段怎么办？
A: 不勾选多分类模式，系统会使用原有的二分类逻辑。

### Q: 可以支持多少个类别？
A: 理论上支持2-20个类别，建议不超过10个以保证训练效果。

### Q: 训练时显示内存不足怎么办？
A: 
1. 减少批次大小
2. 减少切片大小
3. 减少类别数量

### Q: 如何验证多分类效果？
A: 查看生成的训练曲线图，关注准确率和IoU指标的变化。

## 技术细节

### 模型架构
- 基于DeepLabV3+ with ResNet101 backbone
- 支持动态类别数量的输出层
- 使用softmax激活函数进行多分类

### 损失函数
- 分类交叉熵损失
- 自动类别权重平衡
- 支持类别不平衡处理

### 数据处理
- 自动从Shapefile提取类别信息
- 智能类别映射生成
- 支持缺失类别的处理

## 测试验证

运行测试脚本验证功能：
```bash
python test_deeplab_multiclass.py
```

## 总结

这个多分类功能的优势：

1. **无破坏性**: 完全保持原有功能不变
2. **易于使用**: 只需勾选一个选项即可启用
3. **智能配置**: 自动处理复杂的多分类配置
4. **灵活扩展**: 支持任意数量的类别
5. **完整集成**: 与现有GUI和训练流程无缝集成

现在你可以根据数据集的实际情况，选择使用二分类（UNet或DeepLab）或多分类（DeepLab）来训练最适合的模型了！
