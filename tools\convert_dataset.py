#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集转换工具
将GEE下载的float64图像转换为标准的uint8格式，便于查看和训练
"""

import os
import rasterio
import numpy as np
from pathlib import Path
from sklearn.preprocessing import MinMaxScaler
import shutil

def convert_image_to_uint8(input_path, output_path, percentile_clip=True):
    """
    将float64图像转换为uint8格式，正确处理NaN值

    Args:
        input_path: 输入文件路径
        output_path: 输出文件路径
        percentile_clip: 是否使用百分位数裁剪来改善对比度
    """
    print(f"转换: {Path(input_path).name} -> {Path(output_path).name}")

    with rasterio.open(input_path) as src:
        # 读取所有波段
        image_data = src.read()
        profile = src.profile.copy()

        print(f"  原始数据类型: {image_data.dtype}")
        print(f"  图像尺寸: {image_data.shape}")

        # 检查NaN值
        total_pixels = image_data.size
        nan_pixels = np.isnan(image_data).sum()
        print(f"  NaN像素数: {nan_pixels:,} ({nan_pixels/total_pixels*100:.1f}%)")

        # 转换每个波段
        converted_bands = []
        for band_idx in range(image_data.shape[0]):
            band_data = image_data[band_idx].copy()

            # 获取有效数据（非NaN）
            valid_mask = ~np.isnan(band_data)
            valid_data = band_data[valid_mask]

            if len(valid_data) == 0:
                print(f"  ⚠️  波段{band_idx+1}没有有效数据，填充为0")
                converted_bands.append(np.zeros_like(band_data, dtype=np.uint8))
                continue

            print(f"  波段{band_idx+1} 有效数据范围: [{valid_data.min():.2f}, {valid_data.max():.2f}]")

            if percentile_clip and len(valid_data) > 100:
                # 使用有效数据的百分位数进行裁剪
                p2, p98 = np.percentile(valid_data, [2, 98])
                print(f"  波段{band_idx+1} 裁剪范围: [{p2:.2f}, {p98:.2f}]")

                # 裁剪有效数据
                valid_data_clipped = np.clip(valid_data, p2, p98)

                # 归一化到0-255
                if p98 > p2:
                    normalized_valid = ((valid_data_clipped - p2) / (p98 - p2) * 255)
                else:
                    normalized_valid = np.zeros_like(valid_data_clipped)
            else:
                # 直接归一化有效数据
                data_min, data_max = valid_data.min(), valid_data.max()
                if data_max > data_min:
                    normalized_valid = ((valid_data - data_min) / (data_max - data_min) * 255)
                else:
                    normalized_valid = np.zeros_like(valid_data)

            # 创建输出波段
            output_band = np.zeros_like(band_data, dtype=np.uint8)
            output_band[valid_mask] = normalized_valid.astype(np.uint8)

            # NaN区域填充为0（黑色）
            output_band[~valid_mask] = 0

            converted_bands.append(output_band)

        # 合并波段
        converted_image = np.stack(converted_bands, axis=0)

        print(f"  转换后数据类型: {converted_image.dtype}")
        print(f"  转换后数据范围: [{converted_image.min()}, {converted_image.max()}]")

        # 更新profile
        profile.update({
            'dtype': 'uint8',
            'compress': 'lzw',
            'tiled': True,
            'blockxsize': 512,
            'blockysize': 512
        })

        # 保存转换后的图像
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with rasterio.open(output_path, 'w', **profile) as dst:
            dst.write(converted_image)

        # 检查输出文件大小
        output_size = os.path.getsize(output_path) / (1024 * 1024)
        print(f"  输出文件大小: {output_size:.1f} MB")
        print("  ✅ 转换完成")

def convert_dataset(input_folder, output_folder, convert_masks=False):
    """
    转换整个数据集
    """
    print("=" * 60)
    print("数据集转换工具")
    print("=" * 60)
    
    input_path = Path(input_folder)
    output_path = Path(output_folder)
    
    if not input_path.exists():
        print(f"❌ 输入文件夹不存在: {input_folder}")
        return
    
    # 创建输出文件夹
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 查找所有TIFF文件
    tiff_files = list(input_path.glob("*.tif")) + list(input_path.glob("*.tiff"))
    
    if not tiff_files:
        print(f"❌ 在 {input_folder} 中未找到TIFF文件")
        return
    
    print(f"📁 输入文件夹: {input_folder}")
    print(f"📁 输出文件夹: {output_folder}")
    print(f"📊 找到 {len(tiff_files)} 个文件")
    print()
    
    # 转换每个文件
    for i, tiff_file in enumerate(tiff_files, 1):
        print(f"[{i}/{len(tiff_files)}]")
        
        output_file = output_path / tiff_file.name
        
        try:
            # 检查是否是掩膜文件
            is_mask = 'mask' in tiff_file.name.lower()
            
            if is_mask and not convert_masks:
                # 掩膜文件直接复制
                print(f"复制掩膜文件: {tiff_file.name}")
                shutil.copy2(tiff_file, output_file)
                print("  ✅ 复制完成")
            else:
                # 转换图像文件
                convert_image_to_uint8(tiff_file, output_file, percentile_clip=True)
                
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")
        
        print()
    
    print("=" * 60)
    print("转换完成！")
    print("=" * 60)
    print(f"转换后的文件保存在: {output_folder}")
    print("现在您可以:")
    print("1. 使用标准图像查看器打开转换后的文件")
    print("2. 使用转换后的数据进行模型训练")

def main():
    """主函数"""
    # 默认路径
    input_image_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\image"
    input_mask_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\mask"
    
    output_base = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted"
    output_image_folder = os.path.join(output_base, "image")
    output_mask_folder = os.path.join(output_base, "mask")
    
    print("开始转换图像文件...")
    convert_dataset(input_image_folder, output_image_folder, convert_masks=False)
    
    print("\n" + "="*60 + "\n")
    
    if os.path.exists(input_mask_folder):
        print("开始处理掩膜文件...")
        convert_dataset(input_mask_folder, output_mask_folder, convert_masks=False)
    
    print("\n🎉 所有转换完成！")
    print(f"\n转换后的数据集位置:")
    print(f"  图像: {output_image_folder}")
    print(f"  掩膜: {output_mask_folder}")
    
    print(f"\n建议:")
    print(f"1. 检查转换后的文件是否可以正常打开")
    print(f"2. 更新训练配置文件，使用转换后的数据路径")

if __name__ == "__main__":
    main()
