{"training_config": {"batch_size": 8, "epochs": 50, "learning_rate": 0.0001, "validation_split": 0.2, "patch_size": 256, "patch_step": 224, "min_foreground_ratio": 0.001}, "model_config": {"input_shape": [256, 256, 3], "num_classes": 2, "dropout_rates": {"encoder_light": 0.1, "encoder_heavy": 0.2, "bottleneck": 0.3, "decoder_heavy": 0.2, "decoder_light": 0.1}}, "optimizer_config": {"type": "<PERSON>", "learning_rate": 0.0001, "clipnorm": 1.0, "clipvalue": 0.5, "epsilon": 1e-07}, "loss_config": {"type": "combined", "dice_smooth": 1e-05, "focal_gamma": 2.0, "focal_alpha": 0.25}, "callbacks_config": {"model_checkpoint": {"monitor": "val_iou_coef", "mode": "max", "save_best_only": true, "verbose": 1}, "early_stopping": {"monitor": "val_iou_coef", "mode": "max", "patience": 15, "restore_best_weights": true, "verbose": 1}, "reduce_lr": {"monitor": "val_iou_coef", "mode": "max", "factor": 0.8, "patience": 5, "min_lr": 1e-06, "verbose": 1}}, "data_config": {"augmentation": {"enabled": true, "horizontal_flip": true, "vertical_flip": true, "rotation_90": true, "brightness_range": [0.9, 1.1], "contrast_range": [0.9, 1.1]}, "preprocessing": {"percentile_clip": true, "clip_percentiles": [2, 98], "normalize_range": [0, 255], "handle_nan": true, "nan_fill_value": 0}}, "paths_config": {"gee_exports_folder": "GEE_Exports", "work_directory": "UNet_Training", "models_folder": "models", "logs_folder": "logs", "processed_data_folder": "processed_data", "results_folder": "results"}, "gpu_config": {"memory_growth": true, "allow_soft_placement": true, "log_device_placement": false}, "logging_config": {"save_training_history": true, "save_model_summary": true, "save_prediction_samples": true, "verbose_level": 1}, "advanced_config": {"mixed_precision": false, "gradient_accumulation": false, "custom_metrics": ["iou_coef"], "class_weights": "balanced", "sample_weights": false}}