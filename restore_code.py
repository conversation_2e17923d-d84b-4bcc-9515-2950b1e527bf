import os
import subprocess

def run_command(command):
    """安全执行 Git 命令"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',  # 强制统一编码
            errors='replace'
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"错误: {e.stderr.strip()}")
        return None
    except Exception as e:
        print(f"执行命令失败: {str(e)}")
        return None

def get_versions():
    """获取按时间排序的版本列表"""
    # 按标签创建时间降序排序
    tags = run_command('git tag --list --sort=-creatordate')
    if not tags:
        return []
    
    versions = []
    for tag in tags.split('\n'):
        tag = tag.strip()
        if not tag:
            continue
        # 获取标签描述（精确提取）
        message = run_command(f'git show {tag} --no-patch --format="%(contents:subject)"')
        versions.append((tag, message if message else "（无描述）"))
    
    return versions

def restore_code():
    """恢复指定版本代码"""
    try:
        # 修正路径至E盘
        repo_path = r'D:\Satellite_Image_Auto_Offset\Autooffset_Test'
        if not os.path.exists(repo_path):
            print("错误: 代码仓库不存在")
            return
        os.chdir(repo_path)
        
        # 检查备份仓库配置
        backup_remote = run_command('git remote get-url backup')
        if not backup_remote:
            print("错误: 未配置备份仓库，请先运行备份功能初始化")
            return
        
        # 同步远程标签
        print("正在从备份仓库获取最新版本...")
        run_command('git fetch backup --tags --force')
        
        # 检查未提交更改
        status = run_command('git status --porcelain')
        if status:
            print("\n警告: 当前工作区有未提交的更改:")
            print(status)
            confirm = input("\n恢复操作将丢弃所有未提交更改！确认继续？(y/n): ").strip().lower()
            if confirm != 'y':
                print("取消恢复")
                return
        
        # 获取版本列表
        versions = get_versions()
        if not versions:
            print("没有可恢复的版本")
            return
        
        # 显示版本选择菜单
        print("\n可恢复版本列表:")
        for idx, (tag, msg) in enumerate(versions, 1):
            print(f"{idx}. {tag} - {msg}")
        
        # 用户选择
        while True:
            choice = input("\n请输入要恢复的版本序号: ").strip()
            try:
                idx = int(choice) - 1
                if 0 <= idx < len(versions):
                    selected_tag = versions[idx][0]
                    break
                else:
                    print("序号超出范围，请重新输入")
            except ValueError:
                print("请输入有效数字")
        
        # 二次确认
        print(f"\n即将恢复到版本: {selected_tag}")
        print(f"描述: {versions[idx][1]}")
        confirm = input("\n确定执行此操作？(y/n): ").strip().lower()
        if confirm != 'y':
            print("取消操作")
            return
        
        # 执行恢复
        print("\n开始恢复...")
        run_command('git reset --hard HEAD')  # 清理工作区
        run_command('git clean -fd')         # 删除未跟踪文件
        run_command(f'git checkout {selected_tag}')  # 切换到标签
        
        # 关联到分支（避免分离头指针）
        current_branch = run_command('git rev-parse --abbrev-ref HEAD')
        if not current_branch or 'HEAD' in current_branch:
            print("正在创建恢复分支...")
            run_command('git checkout -b restore-branch')
        
        print(f"\n成功恢复到版本 {selected_tag}!")

    except Exception as e:
        print(f"恢复失败: {str(e)}")

if __name__ == '__main__':
    try:
        restore_code()
    except KeyboardInterrupt:
        print("\n操作已取消")
    input("\n按回车键返回主菜单...")