#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Shape文件的数据集下载对话框
"""

import os
import sys
from pathlib import Path
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QGroupBox, QFormLayout,
                            QLineEdit, QFileDialog, QMessageBox, QComboBox,
                            QTextEdit, QCheckBox, QSlider,
                            QTabWidget, QWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

# 导入工具类
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from tools.shape_boundary_dataset_downloader import ShapeBoundaryDatasetDownloader
from tools.dataset_manager import DatasetManager

class DownloadWorker(QThread):
    """下载工作线程"""

    progress_updated = pyqtSignal(str)  # 进度更新信号
    finished = pyqtSignal(bool)  # 完成信号

    def __init__(self, shapefile_path, output_dir, tile_source, download_delay=150):
        super().__init__()
        self.shapefile_path = shapefile_path
        self.output_dir = output_dir
        self.tile_source = tile_source
        self.download_delay = download_delay / 1000.0  # 转换为秒
        self.is_cancelled = False

    def run(self):
        """执行下载任务"""
        try:
            # 使用新版下载器（按边界下载）
            downloader = ShapeBoundaryDatasetDownloader(self.output_dir)
            downloader.set_download_delay(self.download_delay)

            success = downloader.process_shapefile_boundary(
                self.shapefile_path,
                zoom_level=18,  # 固定使用18级缩放
                tile_source=self.tile_source
            )

            self.finished.emit(success)

        except Exception as e:
            self.progress_updated.emit(f"❌ 下载失败: {str(e)}")
            self.finished.emit(False)

    def cancel(self):
        """取消下载"""
        self.is_cancelled = True
        self.terminate()

class ShapeBasedDownloadDialog(QDialog):
    """基于Shape文件的数据集下载对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Shape文件范围下载")
        self.setMinimumSize(600, 500)
        self.download_worker = None
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 基本设置选项卡
        basic_tab = self.create_basic_settings_tab()
        tab_widget.addTab(basic_tab, "基本设置")

        # 高级设置选项卡
        advanced_tab = self.create_advanced_settings_tab()
        tab_widget.addTab(advanced_tab, "高级设置")

        layout.addWidget(tab_widget)

        # 日志显示区域
        log_group = QGroupBox("下载日志")
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setPlaceholderText("下载日志将在这里显示...")
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("开始下载")
        self.start_button.clicked.connect(self.start_download)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_download)

        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)

        layout.addLayout(button_layout)

    def create_basic_settings_tab(self):
        """创建基本设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Shape文件选择组
        shape_group = QGroupBox("Shape文件设置")
        shape_layout = QFormLayout()

        # Shape文件路径
        file_layout = QHBoxLayout()
        self.shapefile_path = QLineEdit()
        self.shapefile_path.setPlaceholderText("选择包含建筑物或障碍物的Shape文件")

        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.browse_shapefile)

        file_layout.addWidget(self.shapefile_path)
        file_layout.addWidget(browse_button)
        shape_layout.addRow("Shape文件:", file_layout)

        shape_group.setLayout(shape_layout)
        layout.addWidget(shape_group)

        # 下载说明组
        info_group = QGroupBox("下载说明")
        info_layout = QFormLayout()

        # 功能说明
        self.feature_description = QLabel()
        self.feature_description.setText(
            "本工具根据Shape文件的整体边界下载一张大图像和对应的大mask。"
            "训练时可以直接使用这些大图像，训练代码会自动进行切片处理。"
            "同时生成可视化mask文件，方便用户查看标注效果。"
        )
        self.feature_description.setWordWrap(True)
        self.feature_description.setStyleSheet("color: #0066cc; font-size: 11px; padding: 5px;")
        info_layout.addRow("", self.feature_description)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 输出设置组
        output_group = QGroupBox("输出设置")
        output_layout = QFormLayout()

        # 输出目录
        dir_layout = QHBoxLayout()
        self.output_dir = QLineEdit()
        default_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dataset')
        self.output_dir.setText(default_dir)

        browse_dir_button = QPushButton("浏览...")
        browse_dir_button.clicked.connect(self.browse_output_dir)

        dir_layout.addWidget(self.output_dir)
        dir_layout.addWidget(browse_dir_button)
        output_layout.addRow("输出目录:", dir_layout)

        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # 下载设置组
        download_group = QGroupBox("下载设置")
        download_layout = QFormLayout()

        # 数据源选择
        self.tile_source = QComboBox()
        self.tile_source.addItem("Google卫星图像", "google")
        self.tile_source.addItem("ArcGIS在线服务", "arcgis")
        self.tile_source.addItem("Bing卫星图像", "bing")
        self.tile_source.addItem("OpenStreetMap", "osm")
        download_layout.addRow("数据源:", self.tile_source)



        download_group.setLayout(download_layout)
        layout.addWidget(download_group)

        layout.addStretch()
        return widget

    def create_advanced_settings_tab(self):
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 数据处理设置
        processing_group = QGroupBox("数据处理设置")
        processing_layout = QFormLayout()

        # 自动数据验证
        self.auto_validate = QCheckBox("下载完成后自动验证数据集")
        self.auto_validate.setChecked(True)
        processing_layout.addRow("数据验证:", self.auto_validate)

        # 生成统计报告
        self.generate_report = QCheckBox("生成数据集统计报告")
        self.generate_report.setChecked(True)
        processing_layout.addRow("统计报告:", self.generate_report)

        processing_group.setLayout(processing_layout)
        layout.addWidget(processing_group)

        # 下载优化建议
        optimization_group = QGroupBox("下载优化建议")
        optimization_layout = QVBoxLayout()

        optimization_text = QTextEdit()
        optimization_text.setMaximumHeight(120)
        optimization_text.setReadOnly(True)
        optimization_info = """
🚀 下载时间优化建议:

• 1024x1024瓦片: 比512x512快75%，推荐用于大数据集
• 2048x2048瓦片: 最快，但可能影响训练精度
• 最大瓦片数: 建议50-100个/几何体，平衡质量与速度

⏱️ 下载延迟说明:
• 0-50ms: ⚠️ 高风险，可能被服务器限制
• 100-200ms: ✅ 推荐设置，平衡速度与稳定性
• 300ms+: 🛡️ 超安全，适合大数据集或不稳定网络

💡 针对1322250个几何体:
• 使用1024x1024可节省约110小时下载时间
• 建议150ms延迟确保下载稳定性
• 推荐分批训练避免硬件崩溃
"""
        optimization_text.setText(optimization_info)
        optimization_layout.addWidget(optimization_text)

        optimization_group.setLayout(optimization_layout)
        layout.addWidget(optimization_group)

        # 下载控制设置
        control_group = QGroupBox("下载控制")
        control_layout = QFormLayout()

        # 下载延迟
        self.download_delay = QSlider(Qt.Horizontal)
        self.download_delay.setRange(0, 2000)
        self.download_delay.setValue(150)  # 默认150ms，更安全
        self.delay_label = QLabel("150 ms (推荐)")
        self.download_delay.valueChanged.connect(self.update_delay_label)

        delay_layout = QHBoxLayout()
        delay_layout.addWidget(self.download_delay)
        delay_layout.addWidget(self.delay_label)
        control_layout.addRow("下载延迟:", delay_layout)

        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # 说明文本
        info_group = QGroupBox("使用说明")
        info_layout = QVBoxLayout()

        info_text = QLabel("""
<b>功能说明：</b><br>
• 根据Shape文件中的多边形范围下载卫星图像<br>
• 自动将Shape文件转换为训练用的mask文件<br>
• 生成标准的image+mask训练数据集<br><br>

<b>注意事项：</b><br>
• Shape文件应包含建筑物或障碍物的多边形<br>
• 建议瓦片大小设置为256或512像素<br>
• 下载速度取决于网络状况和瓦片源<br>
• 大范围下载可能需要较长时间
        """)
        info_text.setWordWrap(True)
        info_text.setStyleSheet("QLabel { background-color: #f0f8ff; padding: 10px; border-radius: 5px; }")

        info_layout.addWidget(info_text)
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        layout.addStretch()
        return widget

    def update_delay_label(self, value):
        """更新延迟标签，提供智能建议"""
        if value <= 50:
            self.delay_label.setText(f"{value} ms (⚠️ 高风险)")
            self.delay_label.setStyleSheet("color: red;")
        elif value <= 100:
            self.delay_label.setText(f"{value} ms (⚡ 快速)")
            self.delay_label.setStyleSheet("color: orange;")
        elif value <= 200:
            self.delay_label.setText(f"{value} ms (✅ 推荐)")
            self.delay_label.setStyleSheet("color: green;")
        elif value <= 500:
            self.delay_label.setText(f"{value} ms (🛡️ 安全)")
            self.delay_label.setStyleSheet("color: blue;")
        else:
            self.delay_label.setText(f"{value} ms (🐌 很慢)")
            self.delay_label.setStyleSheet("color: gray;")





    def browse_shapefile(self):
        """浏览Shape文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Shape文件",
            "",
            "Shape文件 (*.shp);;所有文件 (*)"
        )
        if file_path:
            self.shapefile_path.setText(file_path)

    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            self.output_dir.text()
        )
        if dir_path:
            self.output_dir.setText(dir_path)

    def validate_settings(self):
        """验证设置"""
        if not self.shapefile_path.text().strip():
            QMessageBox.warning(self, "警告", "请选择Shape文件")
            return False

        if not os.path.exists(self.shapefile_path.text()):
            QMessageBox.warning(self, "警告", "Shape文件不存在")
            return False

        if not self.output_dir.text().strip():
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return False

        return True

    def start_download(self):
        """开始下载"""
        if not self.validate_settings():
            return

        # 禁用开始按钮
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # 清空日志
        self.log_text.clear()
        self.log_text.append("🚀 开始下载数据集...")
        self.log_text.append("💡 详细进度请查看控制台输出")

        # 获取设置
        shapefile_path = self.shapefile_path.text()
        output_dir = self.output_dir.text()
        tile_source = self.tile_source.currentData()  # 获取数据源标识符
        download_delay = self.download_delay.value()  # 获取下载延迟

        # 创建工作线程
        self.download_worker = DownloadWorker(
            shapefile_path, output_dir, tile_source, download_delay
        )

        # 连接信号
        self.download_worker.progress_updated.connect(self.update_progress)
        self.download_worker.finished.connect(self.download_finished)

        # 启动线程
        self.download_worker.start()

    def cancel_download(self):
        """取消下载"""
        if self.download_worker and self.download_worker.isRunning():
            self.download_worker.cancel()
            self.log_text.append("⏹️ 正在取消下载...")

    def update_progress(self, message):
        """更新进度显示"""
        self.log_text.append(message)
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def download_finished(self, success):
        """下载完成处理"""
        # 恢复按钮状态
        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

        if success:
            self.log_text.append("✅ 下载完成！")

            # 执行后处理
            if self.auto_validate.isChecked() or self.generate_report.isChecked():
                self.post_process()

            self.show_uniform_message("成功", "数据集下载完成！\n\n现在可以使用这些数据进行模型训练。", "information")
        else:
            self.log_text.append("❌ 下载失败！")
            self.show_uniform_message("失败", "数据集下载失败，请检查设置和网络连接。", "warning")

    def post_process(self):
        """后处理：验证和报告生成"""
        self.log_text.append("\n📊 执行后处理...")

        try:
            # 导入数据集管理器
            from tools.dataset_manager import DatasetManager

            # 创建数据集管理器
            manager = DatasetManager(self.output_dir.text())

            if self.auto_validate.isChecked():
                self.log_text.append("🔍 验证数据集完整性...")
                validation_result = manager.validate_dataset()

                self.log_text.append(f"📊 验证结果:")
                self.log_text.append(f"  图像文件: {validation_result.get('image_count', 0)}")
                self.log_text.append(f"  Mask文件: {validation_result.get('mask_count', 0)}")
                self.log_text.append(f"  有效配对: {validation_result.get('paired_count', 0)}")

                if validation_result["valid"]:
                    self.log_text.append("✅ 数据集验证通过")
                else:
                    self.log_text.append("⚠️ 数据集验证发现问题")
                    for error in validation_result["errors"]:
                        self.log_text.append(f"  ❌ {error}")

            if self.generate_report.isChecked():
                self.log_text.append("📄 生成统计报告...")
                try:
                    report_path = manager.export_dataset_report()
                    self.log_text.append(f"✅ 报告已保存: {report_path}")
                except Exception as report_error:
                    self.log_text.append(f"⚠️ 后处理失败: {str(report_error)}")
                    print(f"详细错误信息: {report_error}")

            # 记录下载历史
            self.log_text.append("✅ 下载历史已记录")

        except Exception as e:
            self.log_text.append(f"⚠️ 后处理失败: {str(e)}")
            print(f"后处理详细错误: {e}")
            import traceback
            traceback.print_exc()

    def show_uniform_message(self, title, message, msg_type="information"):
        """显示统一样式的消息框"""
        try:
            from PyQt5.QtGui import QFont

            # 创建消息框
            if msg_type == "information":
                msg_box = QMessageBox(QMessageBox.Information, title, message, QMessageBox.Ok, self)
            elif msg_type == "warning":
                msg_box = QMessageBox(QMessageBox.Warning, title, message, QMessageBox.Ok, self)
            elif msg_type == "critical":
                msg_box = QMessageBox(QMessageBox.Critical, title, message, QMessageBox.Ok, self)
            else:
                msg_box = QMessageBox(QMessageBox.Information, title, message, QMessageBox.Ok, self)

            # 设置统一字体
            font = QFont("Microsoft YaHei UI", 9)
            font.setBold(False)
            msg_box.setFont(font)

            # 设置按钮字体
            for button in msg_box.buttons():
                button.setFont(font)

            return msg_box.exec_()

        except Exception as e:
            print(f"显示消息框失败: {e}")
            # 回退到标准消息框
            if msg_type == "information":
                return QMessageBox.information(self, title, message)
            elif msg_type == "warning":
                return QMessageBox.warning(self, title, message)
            elif msg_type == "critical":
                return QMessageBox.critical(self, title, message)

    def closeEvent(self, event):
        """关闭事件处理"""
        if self.download_worker and self.download_worker.isRunning():
            reply = QMessageBox.question(
                self, "确认", "下载正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.download_worker.cancel()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
