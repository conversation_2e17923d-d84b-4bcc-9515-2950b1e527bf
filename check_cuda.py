import torch
import platform

def check_cuda_info():
    print(f"Python Version: {platform.python_version()}")
    print(f"PyTorch Version: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"CUDA Available: Yes")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")
        print(f"GPU Device: {torch.cuda.get_device_name(0)}")
    else:
        print("CUDA Available: No")

if __name__ == "__main__":
    check_cuda_info() 