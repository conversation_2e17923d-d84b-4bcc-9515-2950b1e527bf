# 🛰️ Google Colab UNet训练指南

本文档详细说明如何在Google Colab上使用`UNet_Training_on_Google_Colab.ipynb`进行卫星图像建筑物分割模型训练。

## 📁 文件夹内容

### 🎯 核心训练脚本
| 文件名 | 描述 | 适用平台 | 推荐指数 |
|--------|------|----------|----------|
| `UNet_Training_on_Google_Colab.ipynb` | Google Colab专用版 | Google Colab | ⭐⭐⭐⭐⭐ |
| `UNet_Training_Universal.ipynb` | 通用平台版 | 所有Jupyter环境 | ⭐⭐⭐⭐⭐ |

### 📚 使用指南
| 文件名 | 描述 | 用途 |
|--------|------|------|
| `README.md` | Google Colab详细指南 | Colab专用操作说明 |
| `Universal_Platform_Guide.md` | 通用平台使用指南 | 多平台配置和使用 |
| `Platform_Comparison.md` | 平台选择对比 | 帮助选择最适合的平台 |
| `QUICK_START.md` | 快速开始指南 | 5分钟快速上手 |

### ⚙️ 配置文件
| 文件名 | 描述 | 用途 |
|--------|------|------|
| `config_template.json` | 配置模板 | 自定义训练参数 |

## 🎯 版本选择指南

### 🌟 Google Colab专用版 (`UNet_Training_on_Google_Colab.ipynb`)
**适用场景：**
- ✅ 专门在Google Colab上训练
- ✅ 数据存储在Google Drive
- ✅ 需要自动挂载Drive和路径配置
- ✅ 初学者友好，一键运行

**特点：**
- 自动挂载Google Drive
- 预配置Colab环境
- 集成下载功能
- 针对Colab GPU优化

### 🌐 通用平台版 (`UNet_Training_Universal.ipynb`)
**适用场景：**
- ✅ 多平台部署（Jupyter、Kaggle、Azure ML等）
- ✅ 需要灵活的路径配置
- ✅ 企业环境或本地部署
- ✅ 高级用户自定义需求

**特点：**
- 支持多种平台
- 灵活的路径配置
- 详细的平台适配注释
- 可自定义训练参数

## 🚀 快速选择

| 您的情况 | 推荐版本 | 原因 |
|----------|----------|------|
| 第一次使用，数据在Google Drive | Colab专用版 | 零配置，一键运行 |
| 需要在多个平台使用 | 通用版 | 灵活适配 |
| 企业环境，本地Jupyter | 通用版 | 支持企业级平台 |
| Kaggle竞赛 | 通用版 | 支持Kaggle环境 |
| 学习和实验 | Colab专用版 | 简单易用 |

## 📋 使用前准备

### 1. 数据准备
确保您已经从Google Earth Engine (GEE) 导出了训练数据到Google Drive：

```
Google Drive/
└── GEE_Exports/
    ├── tile_0_0_img-0000000000-0000000000.tif
    ├── tile_0_0_img-0000000000-0000013568.tif
    ├── tile_0_0_img-0000013568-0000000000.tif
    ├── tile_0_0_img-0000013568-0000013568.tif
    └── tile_0_0_mask.tif
```

### 2. Google Colab设置
- 运行时类型：**GPU** (T4或更高)
- 内存：**高RAM** (推荐)
- 磁盘空间：至少5GB可用空间

### 3. 预期训练时间
- 数据预处理：10-20分钟
- 模型训练：2-4小时 (取决于数据量和GPU类型)

## 🚀 使用步骤

### 第一步：打开Notebook
1. 将`UNet_Training_on_Google_Colab.ipynb`上传到Google Colab
2. 确保运行时类型设置为GPU
3. 按顺序执行所有代码单元

### 第二步：环境设置 (Cell 1-2)
```python
# 自动安装依赖包
!pip install rasterio geopandas patchify scikit-learn matplotlib seaborn

# 检查GPU可用性
import tensorflow as tf
print(f"GPU可用: {tf.config.list_physical_devices('GPU')}")
```

**预期输出：**
```
GPU可用: [PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU')]
```

### 第三步：挂载Google Drive (Cell 3)
```python
from google.colab import drive
drive.mount('/content/drive')
```

**操作：**
1. 点击授权链接
2. 选择Google账户
3. 复制授权码并粘贴

**预期输出：**
```
✅ 工作目录创建完成: /content/drive/MyDrive/UNet_Training
📁 GEE数据路径: /content/drive/MyDrive/GEE_Exports
📊 发现 5 个文件/文件夹
```

### 第四步：数据分析 (Cell 4-5)
系统会自动：
- 扫描GEE_Exports文件夹
- 识别图像和掩膜文件
- 分析数据质量和格式
- 进行数据预处理和格式转换

**预期输出：**
```
🔍 分析GEE导出数据...
📊 找到 4 个图像文件
🎭 找到 1 个掩膜文件
🔄 开始图像预处理...
✅ 图像预处理完成，共处理 4 个文件
```

### 第五步：模型训练 (Cell 6-8)
训练过程包括：
- UNet模型创建和编译
- 数据加载和增强
- 模型训练和验证

**训练配置：**
```python
BATCH_SIZE = 8        # 批次大小
EPOCHS = 50          # 最大训练轮数
LEARNING_RATE = 1e-4 # 学习率
VALIDATION_SPLIT = 0.2 # 验证集比例
```

**预期输出：**
```
🧠 创建UNet模型...
Total params: 31,055,362
📦 数据集划分:
  训练集: 704 样本
  验证集: 176 样本
🚀 开始训练...
Epoch 1/50
176/176 [==============================] - 82s - loss: 0.4518 - accuracy: 0.6642 - iou_coef: 0.3258
```

### 第六步：结果可视化 (Cell 9-10)
训练完成后会自动生成：
- 训练历史曲线图
- 预测结果演示
- 性能指标统计

### 第七步：下载结果 (Cell 11)
系统会创建包含以下内容的压缩包：
- `unet_model_colab.h5` - 训练好的模型
- `training_history.png` - 训练曲线图
- `prediction_demo.png` - 预测演示图
- `training_log.json` - 训练日志

## 📊 性能指标说明

### 主要指标
- **IoU (Intersection over Union)**: 目标检测精度，越高越好 (0-1)
- **Accuracy**: 像素级准确率，越高越好 (0-1)
- **Loss**: 损失值，越低越好

### 预期性能
根据数据质量，预期达到：
- **验证IoU**: 0.4-0.7
- **验证准确率**: 0.7-0.9
- **训练时间**: 2-4小时

## 🔧 常见问题解决

### 问题1：GPU内存不足
**错误信息：** `ResourceExhaustedError: OOM when allocating tensor`

**解决方案：**
```python
# 减小批次大小
BATCH_SIZE = 4  # 从8改为4

# 或者减小图像块大小
patch_size = 128  # 从256改为128
```

### 问题2：数据文件未找到
**错误信息：** `未找到GEE_Exports文件夹`

**解决方案：**
1. 检查Google Drive中是否存在`GEE_Exports`文件夹
2. 确认文件夹名称拼写正确
3. 重新挂载Google Drive

### 问题3：训练中断
**解决方案：**
```python
# 从检查点恢复训练
if os.path.exists(model_save_path):
    model = keras.models.load_model(model_save_path, custom_objects={...})
    # 继续训练
```

### 问题4：内存不足
**解决方案：**
1. 使用高RAM运行时
2. 减少数据加载量
3. 启用梯度检查点

## 💡 优化建议

### 数据优化
1. **数据增强**: 已启用翻转和旋转
2. **数据平衡**: 自动过滤空白图像块
3. **数据质量**: 使用百分位数裁剪增强对比度

### 训练优化
1. **学习率调度**: 自动降低学习率
2. **早停机制**: 防止过拟合
3. **模型检查点**: 保存最佳模型

### 性能监控
```python
# 实时监控训练指标
def plot_training_progress():
    # 在训练过程中调用此函数查看进度
    pass
```

## 📁 输出文件说明

### 模型文件
- **unet_model_colab.h5**: 完整的Keras模型，可直接用于预测
- **大小**: 约120MB
- **格式**: HDF5

### 可视化文件
- **training_history.png**: 包含损失、准确率、IoU曲线
- **prediction_demo.png**: 4个样本的预测对比
- **尺寸**: 300 DPI高清图像

### 日志文件
- **training_log.json**: 详细的训练历史数据
- **内容**: 每个epoch的所有指标值

## 🔄 后续使用

### 模型部署
下载的模型可以用于：
1. 本地预测脚本
2. Web应用部署
3. 移动端应用

### 模型加载示例
```python
import tensorflow as tf

# 加载模型
model = tf.keras.models.load_model('unet_model_colab.h5')

# 进行预测
predictions = model.predict(input_images)
```

## 📞 技术支持

### 常用资源
- [TensorFlow文档](https://tensorflow.org/guide)
- [Keras文档](https://keras.io/guides/)
- [Google Colab使用指南](https://colab.research.google.com/notebooks/intro.ipynb)

### 故障排除
1. **重启运行时**: Runtime → Restart runtime
2. **清理内存**: Runtime → Factory reset runtime
3. **检查GPU**: Runtime → Change runtime type

---

## 📝 更新日志

**v1.0 (2025-07-17)**
- 初始版本发布
- 支持完整UNet训练流程
- 集成数据预处理和可视化

---

*最后更新：2025-07-17*
*适用于：Google Colab Pro/Pro+*
