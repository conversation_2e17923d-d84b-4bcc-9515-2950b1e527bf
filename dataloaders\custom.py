from torch.utils.data import Dataset
import os

class CustomDataset(Dataset):
    def __init__(self, **loader_args):
        # 从loader_args获取所有参数
        self.base_size = loader_args.get('base_size', 512)
        self.crop_size = loader_args.get('crop_size', 512)
        self.overlap = loader_args.get('overlap', 64)
        self.mean = loader_args.get('mean', [0.485, 0.456, 0.406])
        self.std = loader_args.get('std', [0.229, 0.224, 0.225])
        # 从project_config.json读取路径
        self.image_dir = loader_args.get('data_dir')  # 直接使用传入的参数
        self.mask_dir = loader_args.get('shape_dir')
        self.transform = loader_args.get('transform')
        self.images = [f for f in os.listdir(self.image_dir) if f.endswith('.tif')] 