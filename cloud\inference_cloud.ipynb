{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 卫星图像分割云端推理\n", "\n", "这个notebook展示了如何在云端环境中使用不同的模型对卫星图像进行分割。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import sys\n", "sys.path.append('..')\n", "from cloud.inference_cloud import run_hf_inference, run_sam_inference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 使用HuggingFace模型进行分割"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 设置图像路径\n", "image_path = 'path/to/your/satellite_image.tif'\n", "\n", "# 运行HuggingFace模型推理\n", "output_path = run_hf_inference(image_path)\n", "print(f'HuggingFace分割结果保存在: {output_path}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 使用SAM模型进行分割"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 设置SAM模型路径\n", "sam_checkpoint = 'path/to/sam_vit_h_4b8939.pth'\n", "\n", "# 运行SAM模型推理\n", "output_path = run_sam_inference(image_path, sam_checkpoint)\n", "print(f'SAM分割结果保存在: {output_path}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 显示分割结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from PIL import Image\n", "import matplotlib.pyplot as plt\n", "\n", "def show_results(original_path, segmentation_path):\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))\n", "    \n", "    # 显示原始图像\n", "    original = Image.open(original_path)\n", "    ax1.imshow(original)\n", "    ax1.set_title('原始图像')\n", "    ax1.axis('off')\n", "    \n", "    # 显示分割结果\n", "    segmentation = Image.open(segmentation_path)\n", "    ax2.imshow(segmentation, cmap='gray')\n", "    ax2.set_title('分割结果')\n", "    ax2.axis('off')\n", "    \n", "    plt.show()\n", "\n", "# 显示HuggingFace结果\n", "show_results(image_path, output_path)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}