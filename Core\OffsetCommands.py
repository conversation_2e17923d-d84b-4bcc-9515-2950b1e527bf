from typing import List, Tuple
from abc import ABC, abstractmethod

class Command(ABC):
    @abstractmethod
    def execute(self):
        pass
        
    @abstractmethod
    def undo(self):
        pass

class MovePointCommand(Command):
    def __init__(self, point_id: int, old_pos: Tu<PERSON>[float, float], 
                 new_pos: <PERSON><PERSON>[float, float], update_callback):
        self.point_id = point_id
        self.old_pos = old_pos
        self.new_pos = new_pos
        self.update_callback = update_callback
        
    def execute(self):
        self.update_callback(self.point_id, self.new_pos)
        
    def undo(self):
        self.update_callback(self.point_id, self.old_pos)

class BatchOffsetCommand(Command):
    def __init__(self, point_moves: List[Tuple[int, Tuple[float, float], <PERSON><PERSON>[float, float]]], 
                 update_callback):
        """
        Args:
            point_moves: [(point_id, old_pos, new_pos), ...]
            update_callback: 更新点位的回调函数
        """
        self.point_moves = point_moves
        self.update_callback = update_callback
        
    def execute(self):
        for point_id, _, new_pos in self.point_moves:
            self.update_callback(point_id, new_pos)
            
    def undo(self):
        for point_id, old_pos, _ in self.point_moves:
            self.update_callback(point_id, old_pos)

class CommandManager:
    def __init__(self):
        self.undo_stack = []
        self.redo_stack = []
        
    def execute_command(self, command: Command):
        command.execute()
        self.undo_stack.append(command)
        self.redo_stack.clear()  # 清空重做栈
        
    def undo(self):
        if not self.undo_stack:
            return
            
        command = self.undo_stack.pop()
        command.undo()
        self.redo_stack.append(command)
        
    def redo(self):
        if not self.redo_stack:
            return
            
        command = self.redo_stack.pop()
        command.execute()
        self.undo_stack.append(command)
        
    def can_undo(self) -> bool:
        return len(self.undo_stack) > 0
        
    def can_redo(self) -> bool:
        return len(self.redo_stack) > 0
