from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QMessageBox)
from PyQt5.QtCore import Qt
import os

class CreateShapeFileDialog(QDialog):
    def __init__(self, parent=None, save_path=None):
        super().__init__(parent)
        self.save_path = save_path
        self.setWindowTitle("新建Polygon Shape文件")
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 文件名输入
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("文件名:"))
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入文件名（不需要.shp后缀）")
        name_layout.addWidget(self.name_edit)
        
        layout.addLayout(name_layout)
        
        # 显示保存路径
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("保存位置:"))
        path_label = QLabel(self.save_path)
        path_label.setStyleSheet("color: gray;")
        path_layout.addWidget(path_label)
        
        layout.addLayout(path_layout)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.ok_button.clicked.connect(self.validate_and_accept)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
    def validate_and_accept(self):
        """验证文件名并接受对话框"""
        file_name = self.name_edit.text().strip()
        
        if not file_name:
            QMessageBox.warning(self, "警告", "请输入文件名！")
            return
            
        # 检查文件名是否合法
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', file_name):
            QMessageBox.warning(self, "警告", "文件名只能包含字母、数字、下划线和连字符！")
            return
            
        # 检查文件是否已存在
        file_path = os.path.join(self.save_path, file_name + '.shp')
        if os.path.exists(file_path):
            QMessageBox.warning(self, "警告", "同名文件已存在！")
            return
            
        self.accept()
        
    def get_file_name(self):
        """获取文件名"""
        return self.name_edit.text().strip() 