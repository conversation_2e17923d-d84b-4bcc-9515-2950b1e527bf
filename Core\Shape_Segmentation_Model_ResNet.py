import os
import cv2
import numpy as np
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import random
import tensorflow as tf
import keras
from keras.utils import to_categorical, Sequence
import segmentation_models as sm
from keras.optimizers import Adam
from keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard, Callback
import sys
import json
from datetime import datetime
import argparse
from sklearn.utils.class_weight import compute_class_weight

# 设置混合精度
tf.keras.mixed_precision.set_global_policy('mixed_float16')

# 设置 segmentation_models 的后端
os.environ['SM_FRAMEWORK'] = 'tf.keras'
sm.set_framework('tf.keras')

# Monitor GPU memory and set device availability
print("GPU 检测:")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        # 设置GPU内存增长，避免一次性分配所有内存
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"发现 {len(gpus)} 个 GPU，已配置内存增长模式")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu.name}")
    except RuntimeError as e:
        print(f"GPU 配置错误: {e}")
else:
    print("未检测到 GPU，将使用 CPU 进行训练")

# 定义 DataGenerator 类 - 内存优化版本
class DataGenerator(keras.utils.Sequence):
    def __init__(self, image_dataset, label_dataset, indices, batch_size, sample_weights=None):
        self.image_dataset = image_dataset
        self.label_dataset = label_dataset
        self.indices = indices
        self.batch_size = batch_size
        self.sample_weights = sample_weights

    def __len__(self):
        return int(np.ceil(len(self.indices) / float(self.batch_size)))

    def __getitem__(self, idx):
        # 获取当前批次的索引
        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]

        # 按需加载数据
        batch_x = self.image_dataset[batch_indices]
        batch_y = self.label_dataset[batch_indices]

        return batch_x, batch_y

class TrainingLogger:
    def __init__(self, log_path="training_log.txt"):
        self.log_file = open(log_path, 'w')

    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.log_file.write(log_message + "\n")
        self.log_file.flush()

    def close(self):
        if hasattr(self, 'log_file') and self.log_file:
            self.log_file.close()

def load_config(config_path):
    with open(config_path, 'r') as f:
        return json.load(f)

def get_dataset_paths(config):
    # 优先使用模型参数中的image_path和shape_path
    model_args = config.get('model', {}).get('args', {})

    if 'image_path' in model_args and 'shape_path' in model_args:
        # 使用新的参数
        image_path = model_args['image_path']
        shape_path = model_args['shape_path']

        # 如果路径是目录，则使用默认文件名
        if os.path.isdir(image_path):
            # 查找目录中的tif文件
            tif_files = [f for f in os.listdir(image_path) if f.endswith('.tif')]
            if tif_files:
                image_path = os.path.join(image_path, tif_files[0])
            else:
                image_path = os.path.join(image_path, 'test.tif')

        if os.path.isdir(shape_path):
            # 查找目录中的shp文件
            shp_files = [f for f in os.listdir(shape_path) if f.endswith('.shp')]
            if shp_files:
                shape_path = os.path.join(shape_path, shp_files[0])
            else:
                shape_path = os.path.join(shape_path, 'Buildings.shp')

        return image_path, shape_path
    else:
        # 使用旧的dataset_path参数
        dataset_path = config.get('dataset_path', '')
        image_path = os.path.join(dataset_path, 'image', 'test.tif')
        shape_path = os.path.join(dataset_path, 'shape', 'Buildings.shp')
        return image_path, shape_path

def create_resnet_segmentation_model(input_shape, num_classes, backbone='resnet50'):
    """创建基于ResNet的分割模型"""
    # 使用segmentation_models库创建ResNet UNet模型
    model = sm.Unet(
        backbone_name=backbone,
        input_shape=input_shape,
        classes=num_classes,
        activation='sigmoid' if num_classes == 1 else 'softmax',
        encoder_weights='imagenet'
    )

    return model

def train_resnet(config_path):
    logger = TrainingLogger()
    logger.log("=== 开始训练 ResNet 分割模型 ===")
    logger.log(f"使用配置: {config_path}")

    try:
        # 加载配置
        config = load_config(config_path)

        # 获取训练参数
        model_args = config['model']['args']
        trainer_args = config['trainer']

        batch_size = trainer_args['batch_size']
        epochs = trainer_args['epochs']
        learning_rate = trainer_args['learning_rate']
        slice_size = trainer_args['slice_size']
        overlap = trainer_args['overlap']

        # 获取数据路径
        image_path, shape_path = get_dataset_paths(config)

        logger.log(f"图像文件: {image_path}")
        logger.log(f"形状文件: {shape_path}")

        # 验证文件存在
        if not os.path.exists(image_path):
            logger.log(f"错误：图像文件不存在: {image_path}")
            return False

        if not os.path.exists(shape_path):
            logger.log(f"错误：形状文件不存在: {shape_path}")
            return False

        # 读取数据
        logger.log("正在读取图像数据...")
        with rasterio.open(image_path) as src:
            image = src.read()
            transform = src.transform

            # 转换为 (H, W, C) 格式
            if image.ndim == 3:
                image = np.transpose(image, (1, 2, 0))
            else:
                image = np.expand_dims(image, axis=2)

            logger.log(f"图像形状: {image.shape}")
            logger.log(f"图像数据类型: {image.dtype}")
            logger.log(f"图像值范围: {image.min()} - {image.max()}")

        # 读取形状数据
        logger.log("正在读取形状数据...")
        gdf = gpd.read_file(shape_path)
        logger.log(f"形状数据包含 {len(gdf)} 个要素")

        # 确保图像是3波段
        if image.shape[2] == 1:
            image = np.repeat(image, 3, axis=2)
        elif image.shape[2] > 3:
            image = image[:, :, :3]

        # 创建掩码
        logger.log("正在创建掩码...")
        mask = rasterize(
            [(shape, 1) for shape in gdf.geometry],
            out_shape=image.shape[:2],
            transform=transform,
            fill=0,
            dtype='uint8',
            all_touched=True  # 🔑 添加all_touched参数
        )

        logger.log(f"掩码形状: {mask.shape}")
        logger.log(f"掩码中前景像素数: {np.sum(mask)}")

        # 归一化图像
        if image.dtype != np.float32:
            if image.max() > 1.0:
                image = image.astype(np.float32) / 255.0
            else:
                image = image.astype(np.float32)

        # 图像切片
        logger.log(f"开始图像切片，切片大小: {slice_size}, 重叠: {overlap}")

        step = slice_size - overlap
        image_patches = []
        mask_patches = []

        for i in range(0, image.shape[0] - slice_size + 1, step):
            for j in range(0, image.shape[1] - slice_size + 1, step):
                img_patch = image[i:i+slice_size, j:j+slice_size]
                mask_patch = mask[i:i+slice_size, j:j+slice_size]

                # 过滤掉全黑的图像块和纯背景块
                if np.sum(img_patch) > 0 and (np.sum(mask_patch) > 0 or np.random.random() < 0.3):
                    image_patches.append(img_patch)
                    mask_patches.append(mask_patch)

        image_patches = np.array(image_patches, dtype=np.float32)
        mask_patches = np.array(mask_patches, dtype=np.uint8)

        logger.log(f"生成了 {len(image_patches)} 个图像块")

        # 内存优化：如果批次大小小，限制样本数量
        memory_optimization = trainer_args.get('memory_optimization', {})
        if memory_optimization.get('reduce_samples', False):
            max_samples = memory_optimization.get('max_samples', 15000)
            if len(image_patches) > max_samples:
                logger.log(f"[内存优化] 启用样本数量限制")
                logger.log(f"[内存优化] 将样本数从 {len(image_patches)} 减少到 {max_samples}")

                # 随机选择样本
                indices = np.random.choice(len(image_patches), max_samples, replace=False)
                image_patches = image_patches[indices]
                mask_patches = mask_patches[indices]

                logger.log(f"[内存优化] 样本减少完成，当前样本数: {len(image_patches)}")

        # 数据分割 - 使用索引分割而不是数据分割以节省内存
        total_samples = len(image_patches)
        train_size = int(0.8 * total_samples)

        # 创建索引数组并打乱
        indices = np.arange(total_samples)
        np.random.shuffle(indices)

        train_indices = indices[:train_size]
        test_indices = indices[train_size:]

        logger.log(f"最终训练样本数: {len(train_indices)}, 测试样本数: {len(test_indices)}")

        # 转换标签为分类格式
        mask_patches_categorical = to_categorical(mask_patches, num_classes=2)

        # 创建数据生成器
        train_generator = DataGenerator(image_patches, mask_patches_categorical, train_indices, batch_size)
        test_generator = DataGenerator(image_patches, mask_patches_categorical, test_indices, batch_size)

        # 创建模型
        backbone = model_args.get('backbone', 'resnet50')
        logger.log(f"创建ResNet模型，骨干网络: {backbone}")

        model = create_resnet_segmentation_model(
            input_shape=(slice_size, slice_size, 3),
            num_classes=2,
            backbone=backbone
        )

        # 计算类别权重
        unique_labels = np.unique(mask_patches.flatten())
        class_weights = compute_class_weight('balanced', classes=unique_labels, y=mask_patches.flatten())
        class_weight_dict = dict(zip(unique_labels, class_weights))
        logger.log(f"类别权重: {class_weight_dict}")

        # 编译模型
        optimizer = Adam(learning_rate=learning_rate)
        loss = sm.losses.CategoricalCELoss()
        metrics = [
            sm.metrics.IOUScore(threshold=0.5),
            sm.metrics.FScore(threshold=0.5),
            'accuracy'
        ]

        model.compile(optimizer=optimizer, loss=loss, metrics=metrics)

        logger.log(f"模型参数总数: {model.count_params():,}")

        # 设置回调
        save_dir = trainer_args['save_dir']
        os.makedirs(save_dir, exist_ok=True)

        callbacks = [
            ModelCheckpoint(
                os.path.join(save_dir, 'resnet_best_model.h5'),
                save_best_only=True,
                monitor='val_iou_score',
                mode='max',
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            TensorBoard(
                log_dir=os.path.join(save_dir, 'logs'),
                histogram_freq=1
            )
        ]

        # 训练模型
        logger.log("开始训练模型...")
        history = model.fit(
            train_generator,
            epochs=epochs,
            validation_data=test_generator,
            callbacks=callbacks,
            class_weight=class_weight_dict,
            verbose=1
        )

        # 保存最终模型
        final_model_path = os.path.join(save_dir, 'resnet_final_model.h5')
        model.save(final_model_path)
        logger.log(f"模型已保存到: {final_model_path}")

        # 绘制训练历史
        plt.figure(figsize=(15, 5))

        plt.subplot(1, 3, 1)
        plt.plot(history.history['loss'], label='Training Loss')
        plt.plot(history.history['val_loss'], label='Validation Loss')
        plt.title('ResNet Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.subplot(1, 3, 2)
        plt.plot(history.history['accuracy'], label='Training Accuracy')
        plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
        plt.title('ResNet Model Accuracy')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()

        plt.subplot(1, 3, 3)
        plt.plot(history.history['iou_score'], label='Training IoU')
        plt.plot(history.history['val_iou_score'], label='Validation IoU')
        plt.title('ResNet Model IoU Score')
        plt.xlabel('Epoch')
        plt.ylabel('IoU Score')
        plt.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'resnet_training_history.png'))
        plt.close()

        logger.log("=== ResNet 训练完成 ===")
        return True

    except Exception as e:
        logger.log(f"训练过程中出现错误: {str(e)}")
        import traceback
        logger.log(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        logger.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='训练ResNet分割模型')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    args = parser.parse_args()

    success = train_resnet(args.config)
    if success:
        print("ResNet模型训练成功完成！")
    else:
        print("ResNet模型训练失败！")
        sys.exit(1)
