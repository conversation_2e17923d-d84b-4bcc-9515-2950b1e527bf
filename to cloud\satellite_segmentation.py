# -*- coding: utf-8 -*-
"""
卫星影像智能解译系统
功能：下载指定区域的卫星图像并进行语义分割
"""

import os
import math
import random
import numpy as np
import math
import requests
import rasterio
import mercantile
from PIL import Image
from io import BytesIO
from tqdm import tqdm
from rasterio.transform import from_origin
from huggingface_hub import snapshot_download
from transformers import SegformerImageProcessor, SegformerForSemanticSegmentation
import torch
from skimage.transform import resize
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import geopandas as gpd
from shapely.geometry import Polygon, mapping
import rasterio.features
from rasterio.warp import transform_geom
import cv2

# 配置参数
ZOOM_LEVEL = 18  # 卫星图像缩放级别
TILE_SIZE = 256  # 瓦片大小
MAX_TILES = 100  # 最大下载瓦片数
MODEL_NAME = "nvidia/segformer-b5-finetuned-ade-640-640"  # Hugging Face模型
HF_TOKEN = None  # Hugging Face Token
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

class CoordinateReader:
    """坐标文件读取器"""
    @staticmethod
    def read_coords(filename):
        """读取坐标文件"""
        try:
            with open(filename, 'r') as f:
                coords = []
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    try:
                        lon, lat = map(float, line.split(','))
                        coords.append((lon, lat))
                    except ValueError:
                        print(f"跳过无效行: {line}")
                
                if len(coords) < 3:
                    raise ValueError("至少需要3个坐标点")
                
                # 确保多边形闭合
                if coords[0] != coords[-1]:
                    coords.append(coords[0])
                
                print(f"成功读取多边形坐标，顶点数: {len(coords)}")
                return coords
        except Exception as e:
            print(f"读取坐标文件失败: {e}")
            return None

class SatelliteDownloader:
    """卫星图像下载器"""
    def __init__(self, map_source="Google"):
        self.map_source = map_source
        self.session = self._configure_session()
        self.tile_url_template = "https://mt{}.google.com/vt/lyrs=s&x={}&y={}&z={}"
        
    def _configure_session(self):
        """配置请求会话"""
        session = requests.Session()
        retry = Retry(
            total=5,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        session.mount('https://', HTTPAdapter(max_retries=retry))
        return session
    
    def download_tiles(self, bounds):
        """下载卫星图像瓦片"""
        try:
            print("\n=== 下载瓦片调试信息 ===")
            print("输入边界 (minlon,minlat,maxlon,maxlat):", bounds)
            
            # 打印转换后的边界
            print("\n转换后的边界:")
            print(f"西经: {bounds[0]:.6f}")
            print(f"南纬: {bounds[1]:.6f}")
            print(f"东经: {bounds[2]:.6f}")
            print(f"北纬: {bounds[3]:.6f}")
            
            # 计算需要下载的瓦片
            west, south, east, north = bounds
            tiles = list(mercantile.tiles(
                west=west,
                south=south,
                east=east,
                north=north,
                zooms=[ZOOM_LEVEL]  # 使用zooms参数代替zoom
            ))
            
            print(f"\n需要下载的瓦片数量: {len(tiles)}")
            if len(tiles) > MAX_TILES:
                raise Exception(f"瓦片数量({len(tiles)})超过限制({MAX_TILES})")
            
            # 下载并拼接瓦片
            tile_size = TILE_SIZE
            rows = set(tile.y for tile in tiles)  # 使用y代替row
            cols = set(tile.x for tile in tiles)  # 使用x代替col
            
            total_width = len(cols) * tile_size
            total_height = len(rows) * tile_size
            
            print(f"拼接图像大小: {total_width}x{total_height}")
            mosaic = np.zeros((total_height, total_width, 3), dtype=np.uint8)
            
            min_row = min(rows)
            min_col = min(cols)
            
            for tile in tiles:
                # 计算瓦片在拼接图像中的位置
                x = (tile.x - min_col) * tile_size
                y = (tile.y - min_row) * tile_size
                
                # 构建瓦片URL
                server = random.randint(0, 3)  # 随机选择服务器
                url = f"https://mt{server}.google.com/vt/lyrs=s&x={tile.x}&y={tile.y}&z={tile.z}"
                
                print(f"\n下载瓦片: z={tile.z} x={tile.x} y={tile.y}")
                print(f"位置: ({x},{y})")
                
                # 下载瓦片
                try:
                    response = requests.get(url)
                    if response.status_code == 200:
                        # 将瓦片图像数据转换为numpy数组
                        img_data = np.frombuffer(response.content, np.uint8)
                        img = Image.open(BytesIO(response.content))
                        img_array = np.array(img)
                        
                        if img_array.shape[:2] == (tile_size, tile_size):
                            mosaic[y:y+tile_size, x:x+tile_size] = img_array
                        else:
                            print(f"警告: 瓦片图像格式不正确")
                    else:
                        print(f"警告: 下载瓦片失败，状态码: {response.status_code}")
                except Exception as e:
                    print(f"警告: 下载瓦片出错: {e}")
                    continue
            
            # 保存拼接后的图像
            with rasterio.open(
                'raw.tif',
                'w',
                driver='GTiff',
                height=total_height,
                width=total_width,
                count=3,
                dtype=mosaic.dtype,
                crs='EPSG:4326',  # WGS84
                transform=rasterio.transform.from_bounds(
                    south=bounds[1],  # 最小纬度
                    west=bounds[0],   # 最小经度
                    north=bounds[3],  # 最大纬度
                    east=bounds[2],   # 最大经度
                    width=total_width,
                    height=total_height
                )
            ) as dst:
                # 写入RGB通道
                for i in range(3):
                    dst.write(mosaic[:, :, i], i + 1)
            
            print("\n=== 下载完成 ===")
            return mosaic
            
        except Exception as e:
            print(f"下载瓦片失败: {e}")
            import traceback
            traceback.print_exc()
            return None

class ImageProcessor:
    """图像处理器"""
    @staticmethod
    def prepare_inference_data(coords):
        """准备推理数据"""
        try:
            print("\n=== 准备推理数据 ===")
            # 计算边界框
            lons = [p[0] for p in coords]  # 经度
            lats = [p[1] for p in coords]  # 纬度
            
            # 交换经纬度顺序
            bounds = [
                min(lats),  # 最小纬度
                min(lons),  # 最小经度
                max(lats),  # 最大纬度
                max(lons)   # 最大经度
            ]
            
            print("输入坐标:")
            for i, (lon, lat) in enumerate(coords):
                print(f"点{i+1}: 经度={lon:.6f}, 纬度={lat:.6f}")
            
            print("\n边界框 (minlat,minlon,maxlat,maxlon):", bounds)
            
            # 计算中心点
            center_lat = (bounds[0] + bounds[2]) / 2
            center_lon = (bounds[1] + bounds[3]) / 2
            print(f"中心点: 纬度={center_lat:.6f}, 经度={center_lon:.6f}")
            
            # 下载卫星图像，注意：需要将边界框转换回[minlon,minlat,maxlon,maxlat]格式
            bounds_converted = [
                bounds[1],  # minlon (最小经度)
                bounds[0],  # minlat (最小纬度)
                bounds[3],  # maxlon (最大经度)
                bounds[2]   # maxlat (最大纬度)
            ]
            
            print("\n转换后边界框 (minlon,minlat,maxlon,maxlat):", bounds_converted)
            
            downloader = SatelliteDownloader()
            mosaic = downloader.download_tiles(bounds_converted)
            if mosaic is None:
                raise Exception("下载卫星图像失败")
            
            print("=== 准备数据完成 ===\n")
            return True
            
        except Exception as e:
            print(f"准备推理数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False

class ModelInference:
    """模型推理"""
    def __init__(self):
        self.model = None
        self.processor = None
    
    def setup_model(self):
        """初始化模型"""
        try:
            if not os.path.exists(MODEL_NAME):
                print("开始下载模型...")
                cache_dir = snapshot_download(
                    repo_id=MODEL_NAME,
                    local_dir=MODEL_NAME,
                    token=HF_TOKEN
                )
            
            self.model = SegformerForSemanticSegmentation.from_pretrained(
                MODEL_NAME
            ).to(DEVICE)
            self.processor = SegformerImageProcessor.from_pretrained(MODEL_NAME)
            print("模型初始化完成")
            return True
            
        except Exception as e:
            print(f"模型初始化失败: {e}")
            return False
    
    def run_inference(self):
        """运行推理"""
        try:
            # 读取图像
            with rasterio.open("raw.tif") as src:
                img = src.read([1, 2, 3]).transpose(1, 2, 0)
                height, width = img.shape[:2]
                print(f"原始图像大小: {width}x{height}")
                
                # 获取原始变换矩阵
                orig_transform = src.transform
                
                # 创建新的变换矩阵，交换经纬度顺序
                new_transform = rasterio.Affine(
                    orig_transform.a,  # a
                    orig_transform.b,  # b
                    orig_transform.c,  # c
                    orig_transform.d,  # d
                    orig_transform.e,  # e
                    orig_transform.f   # f
                )
                
                print("原始变换矩阵:", orig_transform)
                print("修正后变换矩阵:", new_transform)
                
                # 获取图像边界
                bounds = src.bounds
                print("\n图像边界:")
                print(f"西经: {bounds.left:.6f}")
                print(f"东经: {bounds.right:.6f}")
                print(f"南纬: {bounds.bottom:.6f}")
                print(f"北纬: {bounds.top:.6f}")
                
                # 分块处理
                block_size = 640  # 模型输入大小
                overlap = 32      # 重叠区域大小
                pred_mask = np.zeros((height, width), dtype=np.uint8)
                
                for y in range(0, height, block_size - overlap):
                    for x in range(0, width, block_size - overlap):
                        # 提取块
                        y_end = min(y + block_size, height)
                        x_end = min(x + block_size, width)
                        block = img[y:y_end, x:x_end]
                        
                        print(f"\n处理块: ({x},{y}) -> ({x_end},{y_end})")
                        print(f"块原始大小: {block.shape}")
                        
                        # 调整块大小为640x640
                        if block.shape[:2] != (block_size, block_size):
                            block_resized = resize(
                                block,
                                (block_size, block_size, 3),
                                preserve_range=True
                            ).astype(np.uint8)
                        else:
                            block_resized = block
                            
                        print(f"调整后块大小: {block_resized.shape}")
                        
                        # 处理块
                        inputs = self.processor(
                            images=block_resized,
                            return_tensors="pt"
                        ).to(DEVICE)
                        
                        with torch.no_grad():
                            outputs = self.model(**inputs)
                            logits = outputs.logits
                            print(f"模型输出大小: {logits.shape}")
                            pred = logits.argmax(1).cpu().numpy()[0]
                            print(f"预测结果大小: {pred.shape}")
                        
                        # 将预测结果调整回原始块大小
                        if pred.shape != block.shape[:2]:
                            pred_resized = resize(
                                pred,
                                block.shape[:2],
                                preserve_range=True,
                                order=0  # 最近邻插值，保持标签值
                            ).astype(np.uint8)
                        else:
                            pred_resized = pred
                            
                        print(f"调整回原始大小: {pred_resized.shape}")
                        
                        # 计算有效区域
                        valid_h = min(block_size - overlap, y_end - y)
                        valid_w = min(block_size - overlap, x_end - x)
                        
                        # 确保尺寸匹配
                        write_h = min(valid_h, pred_resized.shape[0])
                        write_w = min(valid_w, pred_resized.shape[1])
                        
                        print(f"写入区域: ({write_h},{write_w})")
                        
                        # 将结果写回，只写入非重叠区域
                        pred_mask[y:y+write_h, x:x+write_w] = pred_resized[:write_h, :write_w]
                
                print(f"\n预测掩码最终大小: {pred_mask.shape}")
                
                # 保存结果
                meta = src.meta.copy()
                meta.update({
                    'count': 1,
                    'dtype': 'uint8',
                    'transform': new_transform,  # 使用修正后的变换矩阵
                    'crs': src.crs              # 保持原始坐标系统
                })
                
                with rasterio.open('pred_mask.tif', 'w', **meta) as dst:
                    dst.write(pred_mask, 1)
                
                return pred_mask, new_transform
                
        except Exception as e:
            print(f"推理失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None

class ResultProcessor:
    """结果处理器"""
    
    def __init__(self):
        # ADE20K数据集的类别映射
        self.class_mapping = {
            0: "背景",
            1: "建筑",
            2: "道路",
            3: "植被",
            4: "水体",
            
            # 可以根据需要添加更多类别
        }
    
    def mask_to_shapefile(self, mask, transform, output_path="prediction.shp"):
        """将预测掩码转换为shapefile
        
        Args:
            mask: 预测掩码
            transform: 地理变换矩阵
            output_path: 输出shapefile路径
        """
        try:
            print("\n=== 转换预测结果为Shapefile ===")
            print("变换矩阵:", transform)
            
            # 创建GeoDataFrame
            features = []
            for class_id in np.unique(mask):
                if class_id == 0:  # 跳过背景
                    continue
                    
                print(f"\n处理类别 {class_id} ({self.class_mapping.get(int(class_id), '未知')})")
                
                # 获取当前类别的掩码
                class_mask = mask == class_id
                
                # 提取轮廓
                shapes = rasterio.features.shapes(
                    class_mask.astype(np.uint8),
                    mask=class_mask,
                    transform=transform
                )
                
                # 转换为GeoJSON特征
                for geom, _ in shapes:
                    print("\n原始几何形状的第一个坐标:", geom['coordinates'][0][0])
                    
                    # 直接使用原始几何形状
                    new_geom = geom
                    
                    # 添加属性信息
                    feature = {
                        'geometry': new_geom,
                        'properties': {
                            'class_id': int(class_id),
                            'class_name': self.class_mapping.get(int(class_id), "未知")
                        }
                    }
                    features.append(feature)
                    
                    # 输出调试信息
                    coords = new_geom['coordinates'][0]
                    print("\n多边形坐标:")
                    for i, coord in enumerate(coords[:4]):  # 只显示前4个点
                        print(f"点{i+1}: ({coord[0]}, {coord[1]})")
            
            if not features:
                print("警告: 未找到任何有效地物")
                return False
            
            # 创建GeoDataFrame
            gdf = gpd.GeoDataFrame.from_features(features)
            
            # 确保使用正确的坐标系统
            gdf.crs = 'EPSG:4326'
            
            # 输出更多调试信息
            print("\n第一个要素的信息:")
            first_geom = gdf.geometry.iloc[0]
            print(f"类型: {first_geom.geom_type}")
            print(f"坐标系统: {gdf.crs}")
            print(f"边界框: {first_geom.bounds}")
            print(f"第一个坐标: {list(first_geom.exterior.coords)[0]}")
            print(f"最后一个坐标: {list(first_geom.exterior.coords)[-1]}")
            
            # 保存为shapefile
            gdf.to_file(output_path)
            
            # 输出统计信息
            print("\n地物统计:")
            for class_id, group in gdf.groupby('class_id'):
                class_name = self.class_mapping.get(int(class_id), "未知")
                feature_count = len(group)
                print(f"类别 {class_id} ({class_name}):")
                print(f"  - 要素数量: {feature_count}")
            
            print(f"\nShapefile已保存至: {output_path}")
            return True
            
        except Exception as e:
            print(f"转换Shapefile失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    # 1. 读取坐标
    filename='/teamspace/studios/this_studio/selected_area_coords_20250215_132421.txt'
    coords = CoordinateReader.read_coords(filename)
    if coords is None:
        return
    
    # 2. 准备数据
    if not ImageProcessor.prepare_inference_data(coords):
        return
    
    # 3. 初始化模型
    model = ModelInference()
    if not model.setup_model():
        return
    
    # 4. 运行推理
    pred_mask, transform = model.run_inference()
    if pred_mask is None:
        return
        
    # 5. 转换为shapefile
    processor = ResultProcessor()
    if not processor.mask_to_shapefile(pred_mask, transform):
        return
    
    print("处理完成")

if __name__ == "__main__":
    main()
