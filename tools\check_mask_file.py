#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
掩膜文件检查工具
检查mask文件的完整性和格式
"""

import os
import rasterio
import numpy as np
from pathlib import Path
import traceback

def check_mask_file(file_path):
    """
    检查掩膜文件的详细信息
    """
    print(f"检查掩膜文件: {Path(file_path).name}")
    print("-" * 50)
    
    try:
        with rasterio.open(file_path) as src:
            print(f"✅ 文件可以正常打开")
            print(f"📏 尺寸: {src.height} x {src.width}")
            print(f"📊 波段数: {src.count}")
            print(f"🔢 数据类型: {src.dtypes[0]}")
            print(f"🗺️  坐标系: {src.crs}")
            print(f"📍 地理变换: {src.transform}")
            print(f"🚫 NoData值: {src.nodata}")
            
            # 读取数据
            mask_data = src.read(1)  # 读取第一个波段
            
            print(f"\n数据统计:")
            print(f"  数据形状: {mask_data.shape}")
            print(f"  数据类型: {mask_data.dtype}")
            print(f"  数据范围: [{mask_data.min()}, {mask_data.max()}]")
            
            # 检查唯一值
            unique_values = np.unique(mask_data)
            print(f"  唯一值: {unique_values}")
            print(f"  唯一值数量: {len(unique_values)}")
            
            # 统计每个值的像素数
            for val in unique_values:
                count = np.sum(mask_data == val)
                percentage = count / mask_data.size * 100
                print(f"    值 {val}: {count:,} 像素 ({percentage:.1f}%)")
            
            # 检查NaN和无穷值
            nan_count = np.isnan(mask_data).sum()
            inf_count = np.isinf(mask_data).sum()
            
            if nan_count > 0:
                print(f"  ⚠️  NaN值: {nan_count:,} 像素")
            if inf_count > 0:
                print(f"  ⚠️  无穷值: {inf_count:,} 像素")
            
            # 检查是否是有效的二值掩膜
            if len(unique_values) <= 2 and all(val in [0, 1] for val in unique_values):
                print(f"  ✅ 有效的二值掩膜")
            elif len(unique_values) <= 10:
                print(f"  ℹ️  多类别掩膜")
            else:
                print(f"  ⚠️  掩膜值过多，可能不是标准掩膜格式")
            
            return True
            
    except rasterio.errors.RasterioIOError as e:
        print(f"❌ Rasterio IO错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        traceback.print_exc()
        return False

def convert_mask_to_standard(input_path, output_path):
    """
    将掩膜转换为标准格式
    """
    print(f"\n转换掩膜文件...")
    print(f"输入: {input_path}")
    print(f"输出: {output_path}")
    
    try:
        with rasterio.open(input_path) as src:
            mask_data = src.read(1)
            profile = src.profile.copy()
            
            print(f"原始数据范围: [{mask_data.min()}, {mask_data.max()}]")
            
            # 处理掩膜数据
            processed_mask = mask_data.copy()
            
            # 处理NaN值
            if np.isnan(processed_mask).any():
                print("处理NaN值...")
                processed_mask[np.isnan(processed_mask)] = 0
            
            # 处理无穷值
            if np.isinf(processed_mask).any():
                print("处理无穷值...")
                processed_mask[np.isinf(processed_mask)] = 0
            
            # 确保是二值掩膜
            unique_vals = np.unique(processed_mask)
            print(f"唯一值: {unique_vals}")
            
            if len(unique_vals) > 2:
                print("转换为二值掩膜...")
                # 将非零值转换为1
                processed_mask = (processed_mask > 0).astype(np.uint8)
            else:
                # 确保数据类型为uint8
                processed_mask = processed_mask.astype(np.uint8)
            
            print(f"处理后数据范围: [{processed_mask.min()}, {processed_mask.max()}]")
            print(f"处理后唯一值: {np.unique(processed_mask)}")
            
            # 更新profile
            profile.update({
                'dtype': 'uint8',
                'count': 1,
                'compress': 'lzw',
                'tiled': True,
                'blockxsize': 512,
                'blockysize': 512,
                'nodata': None  # 移除nodata值
            })
            
            # 保存处理后的掩膜
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with rasterio.open(output_path, 'w', **profile) as dst:
                dst.write(processed_mask, 1)
            
            print(f"✅ 转换完成")
            
            # 验证转换后的文件
            print(f"\n验证转换后的文件...")
            return check_mask_file(output_path)
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    mask_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted\mask"
    
    print("="*60)
    print("掩膜文件检查工具")
    print("="*60)
    
    mask_path = Path(mask_folder)
    if not mask_path.exists():
        print(f"❌ 掩膜文件夹不存在: {mask_folder}")
        return
    
    # 查找掩膜文件
    mask_files = list(mask_path.glob("*.tif")) + list(mask_path.glob("*.tiff"))
    
    if not mask_files:
        print(f"❌ 未找到掩膜文件")
        return
    
    print(f"找到 {len(mask_files)} 个掩膜文件\n")
    
    for mask_file in mask_files:
        success = check_mask_file(mask_file)
        
        if not success:
            print(f"\n尝试修复掩膜文件...")
            
            # 创建修复后的文件路径
            fixed_folder = mask_path / "fixed"
            fixed_file = fixed_folder / f"fixed_{mask_file.name}"
            
            convert_success = convert_mask_to_standard(mask_file, fixed_file)
            
            if convert_success:
                print(f"\n✅ 修复成功！修复后的文件: {fixed_file}")
                print(f"建议使用修复后的文件进行训练")
            else:
                print(f"\n❌ 修复失败")
        
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    main()
