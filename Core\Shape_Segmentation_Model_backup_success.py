import os
import cv2
import numpy as np
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import random
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.utils import to_categorical, Sequence
import segmentation_models as sm
from tensorflow.keras import mixed_precision
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import ReduceLROnPlateau
from tensorflow.keras.losses import BinaryCrossentropy

# 设置混合精度
mixed_precision.set_global_policy('mixed_float16')

# 定义 DataGenerator 类
class DataGenerator(keras.utils.Sequence):
    def __init__(self, x_set, y_set, batch_size):
        self.x, self.y = x_set, y_set
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.x) / float(self.batch_size)))

    def __getitem__(self, idx):
        batch_x = self.x[idx * self.batch_size:(idx + 1) * self.batch_size]
        batch_y = self.y[idx * self.batch_size:(idx + 1) * self.batch_size]
        return batch_x, batch_y

# Monitor GPU memory
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

# 强制使用 CPU
#os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# 设置数据集路径
dataset_path = r'D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset'
tif_file = os.path.join(dataset_path, 'image','City.tif')
shp_file = os.path.join(dataset_path, 'shape','Big_Obstacles.shp')

# 读取tif文件
with rasterio.open(tif_file) as src:
    image = src.read().transpose((1, 2, 0))
    transform = src.transform
    crs = src.crs

# 确保图像是3波段的
if image.shape[2] > 3:
    print(f"原始图像有 {image.shape[2]} 个波段，正在转换为3波段...")
    image = image[:, :, :3]
    print(f"转换后的图像形状: {image.shape}")

# 读取shp文件
gdf = gpd.read_file(shp_file)

# 创建掩码
mask = rasterize(
    [(shape, 1) for shape in gdf.geometry],
    out_shape=image.shape[:2],
    transform=transform,
    fill=0,
    dtype='uint8'
)

# 初始化MinMaxScaler
minmaxscaler = MinMaxScaler()

# 归一化图像数据
image = minmaxscaler.fit_transform(image.reshape(-1, image.shape[-1])).reshape(image.shape)

# 将掩码扩展为与图像相同的维度
mask = np.expand_dims(mask, axis=-1)

print("Image shape:", image.shape)
print("Mask shape:", mask.shape)

# 设置图像分块大小
image_patch_size = 128

# 创建图像块
def create_patches(img, mask, size=image_patch_size, stride=image_patch_size//2):
    patches_img = patchify(img, (size, size, img.shape[-1]), step=stride)
    patches_mask = patchify(mask, (size, size, 1), step=stride)
    
    patches_img = patches_img.reshape(-1, size, size, img.shape[-1])
    patches_mask = patches_mask.reshape(-1, size, size, 1)
    
    return patches_img, patches_mask

image_dataset, mask_dataset = create_patches(image, mask)

print("Image patches shape:", image_dataset.shape)
print("Mask patches shape:", mask_dataset.shape)

# 获取总的类别数量 (背景和建筑物)
total_classes = 2

# 将标签转换为分类格式
labels_categorical_dataset = to_categorical(mask_dataset, num_classes=total_classes)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(image_dataset, labels_categorical_dataset, test_size=0.2, random_state=42)

# 获取图像的高度、宽度、通道数和总类别数
image_height = X_train.shape[1]
image_width = X_train.shape[2]
image_channels = X_train.shape[3]

# 定义Jaccard系数
def jaccard_coef(y_true, y_pred):
    y_true_flatten = keras.backend.flatten(y_true)
    y_pred_flatten = keras.backend.flatten(y_pred)
    intersection = keras.backend.sum(y_true_flatten * y_pred_flatten)
    union = keras.backend.sum(y_true_flatten) + keras.backend.sum(y_pred_flatten) - intersection
    return (intersection + 1.0) / (union + 1.0)

# 在数据预处理部分添加
# 检查数据中是否有 NaN 值
print("Input data contains NaN:", np.isnan(X_train).any())
print("Label data contains NaN:", np.isnan(y_train).any())

# 简化模型结构
def simple_unet_model(n_classes=2, image_height=256, image_width=256, image_channels=3):
    inputs = keras.layers.Input((image_height, image_width, image_channels))
    
    # Encoder
    c1 = keras.layers.Conv2D(16, (3, 3), activation="relu", kernel_initializer="he_normal", padding="same")(inputs)
    p1 = keras.layers.MaxPooling2D((2, 2))(c1)
    
    c2 = keras.layers.Conv2D(32, (3, 3), activation="relu", kernel_initializer="he_normal", padding="same")(p1)
    p2 = keras.layers.MaxPooling2D((2, 2))(c2)
    
    c3 = keras.layers.Conv2D(64, (3, 3), activation="relu", kernel_initializer="he_normal", padding="same")(p2)
    
    # Decoder
    u4 = keras.layers.Conv2DTranspose(32, (2, 2), strides=(2, 2), padding="same")(c3)
    u4 = keras.layers.concatenate([u4, c2])
    c4 = keras.layers.Conv2D(32, (3, 3), activation="relu", kernel_initializer="he_normal", padding="same")(u4)
    
    u5 = keras.layers.Conv2DTranspose(16, (2, 2), strides=(2, 2), padding="same")(c4)
    u5 = keras.layers.concatenate([u5, c1])
    c5 = keras.layers.Conv2D(16, (3, 3), activation="relu", kernel_initializer="he_normal", padding="same")(u5)
    
    outputs = keras.layers.Conv2D(n_classes, (1, 1), activation="softmax")(c5)
    
    model = keras.models.Model(inputs=[inputs], outputs=[outputs])
    return model

# 使用简化的模型
model = simple_unet_model(n_classes=total_classes, image_height=image_height, image_width=image_width, image_channels=image_channels)

# 使用二元交叉熵损失
loss = BinaryCrossentropy()

# 使用更小的学习率和梯度裁剪
optimizer = Adam(learning_rate=0.0001, clipvalue=1.0)

model.compile(optimizer=optimizer, loss=loss, metrics=["accuracy", jaccard_coef])

# 打印模型摘要
model.summary()

# 定义回调函数，用于绘制训练过程中的损失和评估指标
class PlotLoss(keras.callbacks.Callback):
    def on_train_begin(self, logs={}):
        self.losses = []
        self.val_losses = []
        self.jaccard_coef = []
        self.val_jaccard_coef = []

    def on_epoch_end(self, epoch, logs={}):
        self.losses.append(logs.get('loss'))
        self.val_losses.append(logs.get('val_loss'))
        self.jaccard_coef.append(logs.get('jaccard_coef'))
        self.val_jaccard_coef.append(logs.get('val_jaccard_coef'))
        
        plt.figure(figsize=(12, 4))
        plt.subplot(121)
        plt.plot(self.losses, label='Training Loss')
        plt.plot(self.val_losses, label='Validation Loss')
        plt.title('Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        plt.subplot(122)
        plt.plot(self.jaccard_coef, label='Training IoU')
        plt.plot(self.val_jaccard_coef, label='Validation IoU')
        plt.title('Model IoU')
        plt.xlabel('Epoch')
        plt.ylabel('IoU')
        plt.legend()
        
        plt.tight_layout()
        plt.show()

plot_loss = PlotLoss()

# Create data generators
train_generator = DataGenerator(X_train, y_train, batch_size=4)
val_generator = DataGenerator(X_test, y_test, batch_size=4)

# 添加学习率调度器
reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=5, min_lr=0.00001)

# 在模型训练之前添加以下代码
import logging
logging.basicConfig(level=logging.INFO)

# 修改模型训练部分
try:
    model_history = model.fit(
        train_generator, 
        validation_data=val_generator,
        epochs=20,
        callbacks=[plot_loss, reduce_lr],
        verbose=1
    )
except Exception as e:
    logging.error(f"训练过程中发生错误: {str(e)}")
    raise

# 在训练后添加
logging.info("模型训练完成")

# 保存模型
model.save("building_segmentation_model.h5")

# 预测测试集
y_pred = model.predict(X_test)
y_pred_argmax = np.argmax(y_pred, axis=3)
y_test_argmax = np.argmax(y_test, axis=3)

# 随机选择一个测试图像进行可视化
test_image_number = random.randint(0, len(X_test))
test_image = X_test[test_image_number]
ground_truth_image = y_test_argmax[test_image_number]
test_image_input = np.expand_dims(test_image, 0)
prediction = model.predict(test_image_input)
predicted_image = np.argmax(prediction, axis=3)
predicted_image = predicted_image[0, :, :]

plt.figure(figsize=(12, 4))
plt.subplot(131)
plt.title("Original Image")
plt.imshow(test_image)
plt.subplot(132)
plt.title("Ground Truth")
plt.imshow(ground_truth_image, cmap='gray')
plt.subplot(133)
plt.title("Prediction")
plt.imshow(predicted_image, cmap='gray')
plt.tight_layout()
plt.show()

# 在训练后添加损失图绘制
plt.figure(figsize=(12, 4))
plt.subplot(121)
plt.plot(model_history.history['loss'], label='Training Loss')
plt.plot(model_history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

plt.subplot(122)
plt.plot(model_history.history['jaccard_coef'], label='Training IoU')
plt.plot(model_history.history['val_jaccard_coef'], label='Validation IoU')
plt.title('Model IoU')
plt.xlabel('Epoch')
plt.ylabel('IoU')
plt.legend()

plt.tight_layout()
plt.show()

print("模型训练和评估完成。")