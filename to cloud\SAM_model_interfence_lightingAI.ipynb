{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ls \"/teamspace/studios/this_studio/\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 安装依赖\n", "!pip install -q mercantile pyproj pillow requests rasterio transformers torch geopandas huggingface_hub leafmap"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 安装依赖\n", "!pip install -q numpy segment-anything opencv-python shapely"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda uninstall numpy pandas --yes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda install numpy pandas --yes\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功读取多边形坐标，顶点数: 8\n", "\n", "=== 下载瓦片调试信息 ===\n", "输入边界 (minlon,minlat,maxlon,maxlat): [43.493971824646, 29.62622010372746, 43.51049423217774, 29.640805290903497]\n", "\n", "转换后的边界:\n", "西经: 43.493972\n", "南纬: 29.626220\n", "东经: 43.510494\n", "北纬: 29.640805\n", "\n", "需要下载的瓦片数量: 169\n", "拼接图像大小: 3328x3328\n", "\n", "下载瓦片: z=18 x=162743 y=108455\n", "位置: (0,0)\n", "\n", "下载瓦片: z=18 x=162743 y=108456\n", "位置: (0,256)\n", "\n", "下载瓦片: z=18 x=162743 y=108457\n", "位置: (0,512)\n", "\n", "下载瓦片: z=18 x=162743 y=108458\n", "位置: (0,768)\n", "\n", "下载瓦片: z=18 x=162743 y=108459\n", "位置: (0,1024)\n", "\n", "下载瓦片: z=18 x=162743 y=108460\n", "位置: (0,1280)\n", "\n", "下载瓦片: z=18 x=162743 y=108461\n", "位置: (0,1536)\n", "\n", "下载瓦片: z=18 x=162743 y=108462\n", "位置: (0,1792)\n", "\n", "下载瓦片: z=18 x=162743 y=108463\n", "位置: (0,2048)\n", "\n", "下载瓦片: z=18 x=162743 y=108464\n", "位置: (0,2304)\n", "\n", "下载瓦片: z=18 x=162743 y=108465\n", "位置: (0,2560)\n", "\n", "下载瓦片: z=18 x=162743 y=108466\n", "位置: (0,2816)\n", "\n", "下载瓦片: z=18 x=162743 y=108467\n", "位置: (0,3072)\n", "\n", "下载瓦片: z=18 x=162744 y=108455\n", "位置: (256,0)\n", "\n", "下载瓦片: z=18 x=162744 y=108456\n", "位置: (256,256)\n", "\n", "下载瓦片: z=18 x=162744 y=108457\n", "位置: (256,512)\n", "\n", "下载瓦片: z=18 x=162744 y=108458\n", "位置: (256,768)\n", "\n", "下载瓦片: z=18 x=162744 y=108459\n", "位置: (256,1024)\n", "\n", "下载瓦片: z=18 x=162744 y=108460\n", "位置: (256,1280)\n", "\n", "下载瓦片: z=18 x=162744 y=108461\n", "位置: (256,1536)\n", "\n", "下载瓦片: z=18 x=162744 y=108462\n", "位置: (256,1792)\n", "\n", "下载瓦片: z=18 x=162744 y=108463\n", "位置: (256,2048)\n", "\n", "下载瓦片: z=18 x=162744 y=108464\n", "位置: (256,2304)\n", "\n", "下载瓦片: z=18 x=162744 y=108465\n", "位置: (256,2560)\n", "\n", "下载瓦片: z=18 x=162744 y=108466\n", "位置: (256,2816)\n", "\n", "下载瓦片: z=18 x=162744 y=108467\n", "位置: (256,3072)\n", "\n", "下载瓦片: z=18 x=162745 y=108455\n", "位置: (512,0)\n", "\n", "下载瓦片: z=18 x=162745 y=108456\n", "位置: (512,256)\n", "\n", "下载瓦片: z=18 x=162745 y=108457\n", "位置: (512,512)\n", "\n", "下载瓦片: z=18 x=162745 y=108458\n", "位置: (512,768)\n", "\n", "下载瓦片: z=18 x=162745 y=108459\n", "位置: (512,1024)\n", "\n", "下载瓦片: z=18 x=162745 y=108460\n", "位置: (512,1280)\n", "\n", "下载瓦片: z=18 x=162745 y=108461\n", "位置: (512,1536)\n", "\n", "下载瓦片: z=18 x=162745 y=108462\n", "位置: (512,1792)\n", "\n", "下载瓦片: z=18 x=162745 y=108463\n", "位置: (512,2048)\n", "\n", "下载瓦片: z=18 x=162745 y=108464\n", "位置: (512,2304)\n", "\n", "下载瓦片: z=18 x=162745 y=108465\n", "位置: (512,2560)\n", "\n", "下载瓦片: z=18 x=162745 y=108466\n", "位置: (512,2816)\n", "\n", "下载瓦片: z=18 x=162745 y=108467\n", "位置: (512,3072)\n", "\n", "下载瓦片: z=18 x=162746 y=108455\n", "位置: (768,0)\n", "\n", "下载瓦片: z=18 x=162746 y=108456\n", "位置: (768,256)\n", "\n", "下载瓦片: z=18 x=162746 y=108457\n", "位置: (768,512)\n", "\n", "下载瓦片: z=18 x=162746 y=108458\n", "位置: (768,768)\n", "\n", "下载瓦片: z=18 x=162746 y=108459\n", "位置: (768,1024)\n", "\n", "下载瓦片: z=18 x=162746 y=108460\n", "位置: (768,1280)\n", "\n", "下载瓦片: z=18 x=162746 y=108461\n", "位置: (768,1536)\n", "\n", "下载瓦片: z=18 x=162746 y=108462\n", "位置: (768,1792)\n", "\n", "下载瓦片: z=18 x=162746 y=108463\n", "位置: (768,2048)\n", "\n", "下载瓦片: z=18 x=162746 y=108464\n", "位置: (768,2304)\n", "\n", "下载瓦片: z=18 x=162746 y=108465\n", "位置: (768,2560)\n", "\n", "下载瓦片: z=18 x=162746 y=108466\n", "位置: (768,2816)\n", "\n", "下载瓦片: z=18 x=162746 y=108467\n", "位置: (768,3072)\n", "\n", "下载瓦片: z=18 x=162747 y=108455\n", "位置: (1024,0)\n", "\n", "下载瓦片: z=18 x=162747 y=108456\n", "位置: (1024,256)\n", "\n", "下载瓦片: z=18 x=162747 y=108457\n", "位置: (1024,512)\n", "\n", "下载瓦片: z=18 x=162747 y=108458\n", "位置: (1024,768)\n", "\n", "下载瓦片: z=18 x=162747 y=108459\n", "位置: (1024,1024)\n", "\n", "下载瓦片: z=18 x=162747 y=108460\n", "位置: (1024,1280)\n", "\n", "下载瓦片: z=18 x=162747 y=108461\n", "位置: (1024,1536)\n", "\n", "下载瓦片: z=18 x=162747 y=108462\n", "位置: (1024,1792)\n", "\n", "下载瓦片: z=18 x=162747 y=108463\n", "位置: (1024,2048)\n", "\n", "下载瓦片: z=18 x=162747 y=108464\n", "位置: (1024,2304)\n", "\n", "下载瓦片: z=18 x=162747 y=108465\n", "位置: (1024,2560)\n", "\n", "下载瓦片: z=18 x=162747 y=108466\n", "位置: (1024,2816)\n", "\n", "下载瓦片: z=18 x=162747 y=108467\n", "位置: (1024,3072)\n", "\n", "下载瓦片: z=18 x=162748 y=108455\n", "位置: (1280,0)\n", "\n", "下载瓦片: z=18 x=162748 y=108456\n", "位置: (1280,256)\n", "\n", "下载瓦片: z=18 x=162748 y=108457\n", "位置: (1280,512)\n", "\n", "下载瓦片: z=18 x=162748 y=108458\n", "位置: (1280,768)\n", "\n", "下载瓦片: z=18 x=162748 y=108459\n", "位置: (1280,1024)\n", "\n", "下载瓦片: z=18 x=162748 y=108460\n", "位置: (1280,1280)\n", "\n", "下载瓦片: z=18 x=162748 y=108461\n", "位置: (1280,1536)\n", "\n", "下载瓦片: z=18 x=162748 y=108462\n", "位置: (1280,1792)\n", "\n", "下载瓦片: z=18 x=162748 y=108463\n", "位置: (1280,2048)\n", "\n", "下载瓦片: z=18 x=162748 y=108464\n", "位置: (1280,2304)\n", "\n", "下载瓦片: z=18 x=162748 y=108465\n", "位置: (1280,2560)\n", "\n", "下载瓦片: z=18 x=162748 y=108466\n", "位置: (1280,2816)\n", "\n", "下载瓦片: z=18 x=162748 y=108467\n", "位置: (1280,3072)\n", "\n", "下载瓦片: z=18 x=162749 y=108455\n", "位置: (1536,0)\n", "\n", "下载瓦片: z=18 x=162749 y=108456\n", "位置: (1536,256)\n", "\n", "下载瓦片: z=18 x=162749 y=108457\n", "位置: (1536,512)\n", "\n", "下载瓦片: z=18 x=162749 y=108458\n", "位置: (1536,768)\n", "\n", "下载瓦片: z=18 x=162749 y=108459\n", "位置: (1536,1024)\n", "\n", "下载瓦片: z=18 x=162749 y=108460\n", "位置: (1536,1280)\n", "\n", "下载瓦片: z=18 x=162749 y=108461\n", "位置: (1536,1536)\n", "\n", "下载瓦片: z=18 x=162749 y=108462\n", "位置: (1536,1792)\n", "\n", "下载瓦片: z=18 x=162749 y=108463\n", "位置: (1536,2048)\n", "\n", "下载瓦片: z=18 x=162749 y=108464\n", "位置: (1536,2304)\n", "\n", "下载瓦片: z=18 x=162749 y=108465\n", "位置: (1536,2560)\n", "\n", "下载瓦片: z=18 x=162749 y=108466\n", "位置: (1536,2816)\n", "\n", "下载瓦片: z=18 x=162749 y=108467\n", "位置: (1536,3072)\n", "\n", "下载瓦片: z=18 x=162750 y=108455\n", "位置: (1792,0)\n", "\n", "下载瓦片: z=18 x=162750 y=108456\n", "位置: (1792,256)\n", "\n", "下载瓦片: z=18 x=162750 y=108457\n", "位置: (1792,512)\n", "\n", "下载瓦片: z=18 x=162750 y=108458\n", "位置: (1792,768)\n", "\n", "下载瓦片: z=18 x=162750 y=108459\n", "位置: (1792,1024)\n", "\n", "下载瓦片: z=18 x=162750 y=108460\n", "位置: (1792,1280)\n", "\n", "下载瓦片: z=18 x=162750 y=108461\n", "位置: (1792,1536)\n", "\n", "下载瓦片: z=18 x=162750 y=108462\n", "位置: (1792,1792)\n", "\n", "下载瓦片: z=18 x=162750 y=108463\n", "位置: (1792,2048)\n", "\n", "下载瓦片: z=18 x=162750 y=108464\n", "位置: (1792,2304)\n", "\n", "下载瓦片: z=18 x=162750 y=108465\n", "位置: (1792,2560)\n", "\n", "下载瓦片: z=18 x=162750 y=108466\n", "位置: (1792,2816)\n", "\n", "下载瓦片: z=18 x=162750 y=108467\n", "位置: (1792,3072)\n", "\n", "下载瓦片: z=18 x=162751 y=108455\n", "位置: (2048,0)\n", "\n", "下载瓦片: z=18 x=162751 y=108456\n", "位置: (2048,256)\n", "\n", "下载瓦片: z=18 x=162751 y=108457\n", "位置: (2048,512)\n", "\n", "下载瓦片: z=18 x=162751 y=108458\n", "位置: (2048,768)\n", "\n", "下载瓦片: z=18 x=162751 y=108459\n", "位置: (2048,1024)\n", "\n", "下载瓦片: z=18 x=162751 y=108460\n", "位置: (2048,1280)\n", "\n", "下载瓦片: z=18 x=162751 y=108461\n", "位置: (2048,1536)\n", "\n", "下载瓦片: z=18 x=162751 y=108462\n", "位置: (2048,1792)\n", "\n", "下载瓦片: z=18 x=162751 y=108463\n", "位置: (2048,2048)\n", "\n", "下载瓦片: z=18 x=162751 y=108464\n", "位置: (2048,2304)\n", "\n", "下载瓦片: z=18 x=162751 y=108465\n", "位置: (2048,2560)\n", "\n", "下载瓦片: z=18 x=162751 y=108466\n", "位置: (2048,2816)\n", "\n", "下载瓦片: z=18 x=162751 y=108467\n", "位置: (2048,3072)\n", "\n", "下载瓦片: z=18 x=162752 y=108455\n", "位置: (2304,0)\n", "\n", "下载瓦片: z=18 x=162752 y=108456\n", "位置: (2304,256)\n", "\n", "下载瓦片: z=18 x=162752 y=108457\n", "位置: (2304,512)\n", "\n", "下载瓦片: z=18 x=162752 y=108458\n", "位置: (2304,768)\n", "\n", "下载瓦片: z=18 x=162752 y=108459\n", "位置: (2304,1024)\n", "\n", "下载瓦片: z=18 x=162752 y=108460\n", "位置: (2304,1280)\n", "\n", "下载瓦片: z=18 x=162752 y=108461\n", "位置: (2304,1536)\n", "\n", "下载瓦片: z=18 x=162752 y=108462\n", "位置: (2304,1792)\n", "\n", "下载瓦片: z=18 x=162752 y=108463\n", "位置: (2304,2048)\n", "\n", "下载瓦片: z=18 x=162752 y=108464\n", "位置: (2304,2304)\n", "\n", "下载瓦片: z=18 x=162752 y=108465\n", "位置: (2304,2560)\n", "\n", "下载瓦片: z=18 x=162752 y=108466\n", "位置: (2304,2816)\n", "\n", "下载瓦片: z=18 x=162752 y=108467\n", "位置: (2304,3072)\n", "\n", "下载瓦片: z=18 x=162753 y=108455\n", "位置: (2560,0)\n", "\n", "下载瓦片: z=18 x=162753 y=108456\n", "位置: (2560,256)\n", "\n", "下载瓦片: z=18 x=162753 y=108457\n", "位置: (2560,512)\n", "\n", "下载瓦片: z=18 x=162753 y=108458\n", "位置: (2560,768)\n", "\n", "下载瓦片: z=18 x=162753 y=108459\n", "位置: (2560,1024)\n", "\n", "下载瓦片: z=18 x=162753 y=108460\n", "位置: (2560,1280)\n", "\n", "下载瓦片: z=18 x=162753 y=108461\n", "位置: (2560,1536)\n", "\n", "下载瓦片: z=18 x=162753 y=108462\n", "位置: (2560,1792)\n", "\n", "下载瓦片: z=18 x=162753 y=108463\n", "位置: (2560,2048)\n", "\n", "下载瓦片: z=18 x=162753 y=108464\n", "位置: (2560,2304)\n", "\n", "下载瓦片: z=18 x=162753 y=108465\n", "位置: (2560,2560)\n", "\n", "下载瓦片: z=18 x=162753 y=108466\n", "位置: (2560,2816)\n", "\n", "下载瓦片: z=18 x=162753 y=108467\n", "位置: (2560,3072)\n", "\n", "下载瓦片: z=18 x=162754 y=108455\n", "位置: (2816,0)\n", "\n", "下载瓦片: z=18 x=162754 y=108456\n", "位置: (2816,256)\n", "\n", "下载瓦片: z=18 x=162754 y=108457\n", "位置: (2816,512)\n", "\n", "下载瓦片: z=18 x=162754 y=108458\n", "位置: (2816,768)\n", "\n", "下载瓦片: z=18 x=162754 y=108459\n", "位置: (2816,1024)\n", "\n", "下载瓦片: z=18 x=162754 y=108460\n", "位置: (2816,1280)\n", "\n", "下载瓦片: z=18 x=162754 y=108461\n", "位置: (2816,1536)\n", "\n", "下载瓦片: z=18 x=162754 y=108462\n", "位置: (2816,1792)\n", "\n", "下载瓦片: z=18 x=162754 y=108463\n", "位置: (2816,2048)\n", "\n", "下载瓦片: z=18 x=162754 y=108464\n", "位置: (2816,2304)\n", "\n", "下载瓦片: z=18 x=162754 y=108465\n", "位置: (2816,2560)\n", "\n", "下载瓦片: z=18 x=162754 y=108466\n", "位置: (2816,2816)\n", "\n", "下载瓦片: z=18 x=162754 y=108467\n", "位置: (2816,3072)\n", "\n", "下载瓦片: z=18 x=162755 y=108455\n", "位置: (3072,0)\n", "\n", "下载瓦片: z=18 x=162755 y=108456\n", "位置: (3072,256)\n", "\n", "下载瓦片: z=18 x=162755 y=108457\n", "位置: (3072,512)\n", "\n", "下载瓦片: z=18 x=162755 y=108458\n", "位置: (3072,768)\n", "\n", "下载瓦片: z=18 x=162755 y=108459\n", "位置: (3072,1024)\n", "\n", "下载瓦片: z=18 x=162755 y=108460\n", "位置: (3072,1280)\n", "\n", "下载瓦片: z=18 x=162755 y=108461\n", "位置: (3072,1536)\n", "\n", "下载瓦片: z=18 x=162755 y=108462\n", "位置: (3072,1792)\n", "\n", "下载瓦片: z=18 x=162755 y=108463\n", "位置: (3072,2048)\n", "\n", "下载瓦片: z=18 x=162755 y=108464\n", "位置: (3072,2304)\n", "\n", "下载瓦片: z=18 x=162755 y=108465\n", "位置: (3072,2560)\n", "\n", "下载瓦片: z=18 x=162755 y=108466\n", "位置: (3072,2816)\n", "\n", "下载瓦片: z=18 x=162755 y=108467\n", "位置: (3072,3072)\n", "\n", "=== 下载完成 ===\n", "\n", "=== 处理图像 ===\n", "\n", "=== 初始化SAM模型 ===\n", "SAM模型已加载 (设备: cpu)\n", "\n", "=== 执行图像分割 ===\n", "图像分割完成\n", "\n", "=== 转换预测结果为Shapefile ===\n", "变换矩阵: | 0.00, 0.00, 43.49|\n", "| 0.00,-0.00, 29.64|\n", "| 0.00, 0.00, 1.00|\n", "\n", "处理类别 1 (建筑)\n", "\n", "原始几何形状的第一个坐标: (43.50241672104369, 29.633893980754273)\n", "\n", "多边形坐标:\n", "点1: (43.50241672104369, 29.633893980754273)\n", "点2: (43.50241672104369, 29.633889598186013)\n", "点3: (43.502396862380785, 29.633889598186013)\n", "点4: (43.502396862380785, 29.63388521561775)\n", "\n", "第一个要素的信息:\n", "类型: Polygon\n", "坐标系统: EPSG:4326\n", "边界框: (43.5021784170889, 29.633245360651493, 43.502903258284704, 29.633893980754273)\n", "第一个坐标: (43.50241672104369, 29.633893980754273)\n", "最后一个坐标: (43.50241672104369, 29.633893980754273)\n", "\n", "地物统计:\n", "类别 1 (建筑):\n", "  - 要素数量: 1\n", "\n", "Shapefile已保存至: prediction.shp\n", "处理完成\n"]}], "source": ["import os\n", "import math\n", "import random\n", "import numpy as np\n", "import rasterio\n", "import mercantile\n", "import requests\n", "import rasterio.features\n", "import geopandas as gpd\n", "from PIL import Image\n", "from io import BytesIO\n", "from shapely.geometry import Polygon\n", "import torch\n", "from segment_anything import sam_model_registry, SamPredictor\n", "import cv2\n", "from requests.adapters import HTTPAdapter\n", "from urllib3.util.retry import Retry\n", "\n", "class CoordinateReader:\n", "    \"\"\"坐标文件读取器\"\"\"\n", "    @staticmethod\n", "    def read_coords(filename):\n", "        \"\"\"读取坐标文件\"\"\"\n", "        try:\n", "            with open(filename, 'r') as f:\n", "                coords = []\n", "                for line in f:\n", "                    line = line.strip()\n", "                    if not line or line.startswith('#'):\n", "                        continue\n", "                    try:\n", "                        lon, lat = map(float, line.split(','))\n", "                        coords.append((lon, lat))\n", "                    except ValueError:\n", "                        print(f\"跳过无效行: {line}\")\n", "                \n", "                if len(coords) < 3:\n", "                    raise ValueError(\"至少需要3个坐标点\")\n", "                \n", "                # 确保多边形闭合\n", "                if coords[0] != coords[-1]:\n", "                    coords.append(coords[0])\n", "                \n", "                print(f\"成功读取多边形坐标，顶点数: {len(coords)}\")\n", "                return coords\n", "        except Exception as e:\n", "            print(f\"读取坐标文件失败: {e}\")\n", "            return None\n", "\n", "class SatelliteDownloader:\n", "    \"\"\"卫星图像下载器\"\"\"\n", "    def __init__(self):\n", "        \"\"\"初始化下载器\"\"\"\n", "        self.zoom = 18  # 缩放级别\n", "        self.tile_size = 256\n", "        self.session = self._configure_session()\n", "        self.tile_url_template = \"https://mt{}.google.com/vt/lyrs=s&x={}&y={}&z={}\"\n", "    \n", "    def _configure_session(self):\n", "        \"\"\"配置请求会话\"\"\"\n", "        session = requests.Session()\n", "        retry = Retry(\n", "            total=5,\n", "            backoff_factor=0.5,\n", "            status_forcelist=[429, 500, 502, 503, 504]\n", "        )\n", "        session.mount('https://', HTTPAdapter(max_retries=retry))\n", "        return session\n", "    \n", "    def download_satellite_image(self, bounds):\n", "        \"\"\"下载卫星图像\n", "        \n", "        Args:\n", "            bounds: 边界坐标 (min_lon, min_lat, max_lon, max_lat)\n", "        \n", "        Returns:\n", "            numpy.ndarray: 拼接后的图像\n", "        \"\"\"\n", "        try:\n", "            print(\"\\n=== 下载瓦片调试信息 ===\")\n", "            print(\"输入边界 (minlon,minlat,maxlon,maxlat):\", bounds)\n", "            \n", "            # 打印转换后的边界\n", "            print(\"\\n转换后的边界:\")\n", "            print(f\"西经: {bounds[0]:.6f}\")\n", "            print(f\"南纬: {bounds[1]:.6f}\")\n", "            print(f\"东经: {bounds[2]:.6f}\")\n", "            print(f\"北纬: {bounds[3]:.6f}\")\n", "            \n", "            # 计算需要下载的瓦片\n", "            west, south, east, north = bounds\n", "            tiles = list(mercantile.tiles(\n", "                west=west,\n", "                south=south,\n", "                east=east,\n", "                north=north,\n", "                zooms=[self.zoom]\n", "            ))\n", "            \n", "            print(f\"\\n需要下载的瓦片数量: {len(tiles)}\")\n", "            \n", "            # 下载并拼接瓦片\n", "            tile_size = self.tile_size\n", "            rows = set(tile.y for tile in tiles)\n", "            cols = set(tile.x for tile in tiles)\n", "            \n", "            total_width = len(cols) * tile_size\n", "            total_height = len(rows) * tile_size\n", "            \n", "            print(f\"拼接图像大小: {total_width}x{total_height}\")\n", "            mosaic = np.zeros((total_height, total_width, 3), dtype=np.uint8)\n", "            \n", "            min_row = min(rows)\n", "            min_col = min(cols)\n", "            \n", "            for tile in tiles:\n", "                x = (tile.x - min_col) * tile_size\n", "                y = (tile.y - min_row) * tile_size\n", "                \n", "                server = random.randint(0, 3)\n", "                url = self.tile_url_template.format(server, tile.x, tile.y, tile.z)\n", "                \n", "                print(f\"\\n下载瓦片: z={tile.z} x={tile.x} y={tile.y}\")\n", "                print(f\"位置: ({x},{y})\")\n", "                \n", "                try:\n", "                    response = self.session.get(url)\n", "                    if response.status_code == 200:\n", "                        img = Image.open(BytesIO(response.content))\n", "                        img_array = np.array(img)\n", "                        \n", "                        if img_array.shape[:2] == (tile_size, tile_size):\n", "                            mosaic[y:y+tile_size, x:x+tile_size] = img_array\n", "                        else:\n", "                            print(f\"警告: 瓦片图像格式不正确\")\n", "                    else:\n", "                        print(f\"警告: 下载瓦片失败，状态码: {response.status_code}\")\n", "                except Exception as e:\n", "                    print(f\"警告: 下载瓦片出错: {e}\")\n", "                    continue\n", "            \n", "            # 保存拼接后的图像\n", "            with rasterio.open(\n", "                'raw.tif',\n", "                'w',\n", "                driver='GTiff',\n", "                height=total_height,\n", "                width=total_width,\n", "                count=3,\n", "                dtype=mosaic.dtype,\n", "                crs='EPSG:4326',  # WGS84\n", "                transform=rasterio.transform.from_bounds(\n", "                    west=bounds[0],   # 最小经度\n", "                    south=bounds[1],  # 最小纬度\n", "                    east=bounds[2],   # 最大经度\n", "                    north=bounds[3],  # 最大纬度\n", "                    width=total_width,\n", "                    height=total_height\n", "                )\n", "            ) as dst:\n", "                # 写入RGB通道\n", "                for i in range(3):\n", "                    dst.write(mosaic[:, :, i], i + 1)\n", "            \n", "            print(\"\\n=== 下载完成 ===\")\n", "            return mosaic\n", "            \n", "        except Exception as e:\n", "            print(f\"下载瓦片失败: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            return None\n", "\n", "class ImageProcessor:\n", "    \"\"\"图像处理器\"\"\"\n", "    def __init__(self):\n", "        \"\"\"初始化图像处理器\"\"\"\n", "        pass\n", "    \n", "    def process_image(self, image):\n", "        \"\"\"处理图像\n", "        \n", "        Args:\n", "            image: 输入图像\n", "        \n", "        Returns:\n", "            numpy.ndarray: 处理后的图像\n", "        \"\"\"\n", "        try:\n", "            print(\"\\n=== 处理图像 ===\")\n", "            # 转换为RGB格式\n", "            if len(image.shape) == 2:\n", "                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)\n", "            elif image.shape[2] == 4:\n", "                image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)\n", "            \n", "            return image\n", "            \n", "        except Exception as e:\n", "            print(f\"处理图像失败: {e}\")\n", "            return None\n", "\n", "class ModelInference:\n", "    \"\"\"模型推理\"\"\"\n", "    def __init__(self, model_type=\"vit_h\"):\n", "        \"\"\"初始化模型\n", "        \n", "        Args:\n", "            model_type: SAM模型类型，可选 'vit_h', 'vit_l', 'vit_b'\n", "        \"\"\"\n", "        try:\n", "            print(\"\\n=== 初始化SAM模型 ===\")\n", "            \n", "            # 检查模型文件是否存在\n", "            model_path = f\"sam_{model_type}.pth\"\n", "            if not os.path.exists(model_path):\n", "                print(f\"请下载SAM模型文件并保存为 {model_path}\")\n", "                return\n", "            \n", "            # 初始化SAM模型\n", "            device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "            sam = sam_model_registry[model_type](checkpoint=model_path)\n", "            sam.to(device=device)\n", "            \n", "            self.predictor = SamPredictor(sam)\n", "            print(f\"SAM模型已加载 (设备: {device})\")\n", "            \n", "        except Exception as e:\n", "            print(f\"初始化模型失败: {e}\")\n", "    \n", "    def predict(self, image):\n", "        \"\"\"执行图像分割\n", "        \n", "        Args:\n", "            image: 输入图像\n", "        \n", "        Returns:\n", "            numpy.ndarray: 分割掩码\n", "        \"\"\"\n", "        try:\n", "            print(\"\\n=== 执行图像分割 ===\")\n", "            \n", "            # 设置图像\n", "            self.predictor.set_image(image)\n", "            \n", "            # 生成图像中心点作为提示点\n", "            h, w = image.shape[:2]\n", "            input_point = np.array([[w//2, h//2]])\n", "            input_label = np.array([1])\n", "            \n", "            # 执行分割\n", "            masks, scores, logits = self.predictor.predict(\n", "                point_coords=input_point,\n", "                point_labels=input_label,\n", "                multimask_output=True\n", "            )\n", "            \n", "            # 选择最佳掩码\n", "            best_mask = masks[np.argmax(scores)]\n", "            \n", "            # 将掩码转换为分类结果\n", "            result = np.zeros_like(best_mask, dtype=np.uint8)\n", "            result[best_mask] = 1  # 1表示建筑物\n", "            \n", "            print(\"图像分割完成\")\n", "            return result\n", "            \n", "        except Exception as e:\n", "            print(f\"执行分割失败: {e}\")\n", "            return None\n", "\n", "class ResultProcessor:\n", "    \"\"\"结果处理器\"\"\"\n", "    def __init__(self):\n", "        \"\"\"初始化结果处理器\"\"\"\n", "        self.class_mapping = {\n", "            1: \"建筑\",\n", "            2: \"道路\",\n", "            3: \"植被\",\n", "            4: \"水体\"\n", "        }\n", "    \n", "    def mask_to_shapefile(self, mask, transform, output_path=\"prediction.shp\"):\n", "        \"\"\"将预测掩码转换为shapefile\n", "        \n", "        Args:\n", "            mask: 预测掩码\n", "            transform: 地理变换矩阵\n", "            output_path: 输出shapefile路径\n", "        \"\"\"\n", "        try:\n", "            print(\"\\n=== 转换预测结果为Shapefile ===\")\n", "            print(\"变换矩阵:\", transform)\n", "            \n", "            # 创建GeoDataFrame\n", "            features = []\n", "            for class_id in np.unique(mask):\n", "                if class_id == 0:  # 跳过背景\n", "                    continue\n", "                    \n", "                print(f\"\\n处理类别 {class_id} ({self.class_mapping.get(int(class_id), '未知')})\")\n", "                \n", "                # 获取当前类别的掩码\n", "                class_mask = mask == class_id\n", "                \n", "                # 提取轮廓\n", "                shapes = rasterio.features.shapes(\n", "                    class_mask.astype(np.uint8),\n", "                    mask=class_mask,\n", "                    transform=transform\n", "                )\n", "                \n", "                # 转换为GeoJSON特征\n", "                for geom, _ in shapes:\n", "                    print(\"\\n原始几何形状的第一个坐标:\", geom['coordinates'][0][0])\n", "                    \n", "                    # 直接使用原始几何形状\n", "                    new_geom = geom\n", "                    \n", "                    # 添加属性信息\n", "                    feature = {\n", "                        'geometry': new_geom,\n", "                        'properties': {\n", "                            'class_id': int(class_id),\n", "                            'class_name': self.class_mapping.get(int(class_id), \"未知\")\n", "                        }\n", "                    }\n", "                    features.append(feature)\n", "                    \n", "                    # 输出调试信息\n", "                    coords = new_geom['coordinates'][0]\n", "                    print(\"\\n多边形坐标:\")\n", "                    for i, coord in enumerate(coords[:4]):  # 只显示前4个点\n", "                        print(f\"点{i+1}: ({coord[0]}, {coord[1]})\")\n", "            \n", "            if not features:\n", "                print(\"警告: 未找到任何有效地物\")\n", "                return False\n", "            \n", "            # 创建GeoDataFrame\n", "            gdf = gpd.GeoDataFrame.from_features(features)\n", "            \n", "            # 确保使用正确的坐标系统\n", "            gdf.crs = 'EPSG:4326'\n", "            \n", "            # 输出更多调试信息\n", "            print(\"\\n第一个要素的信息:\")\n", "            first_geom = gdf.geometry.iloc[0]\n", "            print(f\"类型: {first_geom.geom_type}\")\n", "            print(f\"坐标系统: {gdf.crs}\")\n", "            print(f\"边界框: {first_geom.bounds}\")\n", "            print(f\"第一个坐标: {list(first_geom.exterior.coords)[0]}\")\n", "            print(f\"最后一个坐标: {list(first_geom.exterior.coords)[-1]}\")\n", "            \n", "            # 保存为shapefile\n", "            gdf.to_file(output_path)\n", "            \n", "            # 输出统计信息\n", "            print(\"\\n地物统计:\")\n", "            for class_id, group in gdf.groupby('class_id'):\n", "                class_name = self.class_mapping.get(int(class_id), \"未知\")\n", "                feature_count = len(group)\n", "                print(f\"类别 {class_id} ({class_name}):\")\n", "                print(f\"  - 要素数量: {feature_count}\")\n", "            \n", "            print(f\"\\nShapefile已保存至: {output_path}\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"转换Shapefile失败: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            return False\n", "\n", "def main():\n", "    \"\"\"主函数\"\"\"\n", "    # 1. 读取坐标\n", "    filename = '/teamspace/studios/this_studio/selected_area_coords_20250403_101054.txt'\n", "    coords = CoordinateReader.read_coords(filename)\n", "    if coords is None:\n", "        return\n", "    \n", "    # 2. 准备推理数据\n", "    # 计算边界框\n", "    lons = [p[0] for p in coords]  # 经度\n", "    lats = [p[1] for p in coords]  # 纬度\n", "    bounds = [\n", "        min(lons),  # 最小经度\n", "        min(lats),  # 最小纬度\n", "        max(lons),  # 最大经度\n", "        max(lats)   # 最大纬度\n", "    ]\n", "    \n", "    # 3. 下载卫星图像\n", "    downloader = SatelliteDownloader()\n", "    image = downloader.download_satellite_image(bounds)\n", "    if image is None:\n", "        return\n", "    \n", "    # 4. 处理图像\n", "    processor = ImageProcessor()\n", "    processed_image = processor.process_image(image)\n", "    if processed_image is None:\n", "        return\n", "    \n", "    # 5. 初始化模型并推理\n", "    model = ModelInference()\n", "    mask = model.predict(processed_image)\n", "    if mask is None:\n", "        return\n", "    \n", "    # 6. 将结果转换为shapefile\n", "    with rasterio.open('raw.tif') as src:\n", "        transform = src.transform\n", "    \n", "    result_processor = ResultProcessor()\n", "    if not result_processor.mask_to_shapefile(mask, transform):\n", "        return\n", "    \n", "    print(\"处理完成\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}