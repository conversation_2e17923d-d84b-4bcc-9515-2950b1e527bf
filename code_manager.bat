@echo off
chcp 65001
setlocal enabledelayedexpansion

title Code Manager

:menu
cls
echo =====================================
echo         Code Manager Menu
echo =====================================
echo.
echo  1. Backup Code
echo  2. Restore Code
echo  3. View History
echo  4. Delete Backup
echo  5. Exit
echo.
echo =====================================

set /p choice=Please select (1-5): 

if "!choice!"=="1" (
    python backup_code.py
    goto menu
)
if "!choice!"=="2" (
    python restore_code.py
    goto menu
)
if "!choice!"=="3" (
    python view_history.py
    goto menu
)
if "!choice!"=="4" (
    python delete_backup.py
    goto menu
)
if "!choice!"=="5" (
    exit /b
)

echo.
echo Invalid choice, please try again!
timeout /t 2 >nul
goto menu