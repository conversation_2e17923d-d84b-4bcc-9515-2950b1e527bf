"""
Segformer模型推理封装
用于调用satellite_segmentation.py的功能进行推理
"""

import os
import sys
import torch
from PIL import Image
import numpy as np
from pathlib import Path

# 添加satellite_segmentation.py所在目录到系统路径
to_cloud_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "to cloud")
sys.path.append(to_cloud_dir)

from satellite_segmentation import (
    CoordinateReader, 
    SatelliteDownloader,
    ModelInference,
    ResultProcessor
)

class SegformerInferenceWrapper:
    """Segformer模型推理包装器"""
    
    def __init__(self, cache_dir=None):
        """初始化推理包装器
        
        Args:
            cache_dir: HuggingFace模型缓存目录
        """
        self.cache_dir = cache_dir
        self.model_inference = ModelInference()
        self.result_processor = ResultProcessor()
        
    def run_inference(self, coords_file, output_dir):
        """运行推理
        
        Args:
            coords_file: 坐标文件路径
            output_dir: 输出目录
            
        Returns:
            dict: 包含以下键的字典：
                - shape_file: 生成的shape文件路径
                - class_counts: 各类别的像素计数
        """
        try:
            # 1. 读取坐标
            coord_reader = CoordinateReader()
            coords = coord_reader.read_coords(coords_file)
            if not coords:
                raise ValueError("无法读取坐标文件")
                
            # 2. 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 3. 下载卫星图像
            downloader = SatelliteDownloader()
            bounds = self._get_bounds(coords)
            image_data = downloader.download_tiles(bounds)
            
            if image_data is None:
                raise ValueError("下载卫星图像失败")
            
            # 保存图像数据为raw.tif
            import rasterio
            raw_tif = os.path.join(output_dir, "raw.tif")
            height, width = image_data.shape[:2]
            transform = self._compute_transform(bounds, (height, width))
            
            with rasterio.open(
                raw_tif,
                'w',
                driver='GTiff',
                height=height,
                width=width,
                count=3,
                dtype=image_data.dtype,
                crs='EPSG:4326',
                transform=transform,
            ) as dst:
                # 写入RGB通道
                for i in range(3):
                    dst.write(image_data[:, :, i], i + 1)
                
            # 4. 运行模型推理
            self.model_inference.setup_model()
            # 切换到输出目录，因为satellite_segmentation.py会在当前目录寻找raw.tif
            current_dir = os.getcwd()
            os.chdir(output_dir)
            try:
                inference_result = self.model_inference.run_inference()
            finally:
                os.chdir(current_dir)
            
            if inference_result is None or len(inference_result) != 2:
                raise ValueError("模型推理失败")
                
            pred_mask, pred_transform = inference_result
            
            if pred_mask is None:
                raise ValueError("模型推理未生成有效的掩码")
                
            # 5. 处理结果
            shape_file = os.path.join(output_dir, "segmentation_result.shp")
            self.result_processor.mask_to_shapefile(pred_mask, pred_transform, shape_file)
            
            # 6. 统计各类别像素数
            class_counts = self._count_classes(pred_mask)
            
            return {
                "shape_file": shape_file,
                "class_counts": class_counts
            }
            
        except Exception as e:
            print(f"推理过程出错: {str(e)}")
            raise
            
    def _get_bounds(self, coords):
        """计算坐标的边界框
        
        Args:
            coords: 坐标点列表 [(lon1, lat1), (lon2, lat2), ...]
            
        Returns:
            tuple: (minlon, minlat, maxlon, maxlat)
        """
        lons, lats = zip(*coords)
        return (min(lons), min(lats), max(lons), max(lats))
        
    def _compute_transform(self, bounds, shape):
        """计算地理变换矩阵
        
        Args:
            bounds: (minlon, minlat, maxlon, maxlat)
            shape: 掩码的形状 (height, width)
            
        Returns:
            affine.Affine: 变换矩阵
        """
        from rasterio.transform import from_bounds
        return from_bounds(*bounds, width=shape[1], height=shape[0])
        
    def _count_classes(self, mask):
        """统计各类别的像素数量
        
        Args:
            mask: 预测掩码
            
        Returns:
            dict: 类别计数字典
        """
        unique, counts = np.unique(mask, return_counts=True)
        return {self.result_processor.class_mapping.get(int(cls), f"类别{cls}"): int(count) 
                for cls, count in zip(unique, counts)}
