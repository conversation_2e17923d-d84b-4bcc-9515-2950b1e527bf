# 🚀 UNet Python脚本快速开始指南

5分钟快速上手UNet卫星图像分割训练Python脚本！

## ⚡ 快速开始（5分钟）

### 第1步：环境准备（2分钟）

```bash
# 创建conda环境
conda create -n unet_training python=3.9 -y
conda activate unet_training

# 安装核心依赖
pip install tensorflow==2.15.0 rasterio geopandas opencv-python scikit-learn matplotlib psutil
```

### 第2步：获取脚本（30秒）

脚本文件：`unet_training_script.py` 已经创建在 `to_cloud/` 文件夹中

### 第3步：配置路径（1分钟）

编辑 `unet_training_script.py`，修改路径配置：

```python
# 找到这个函数并修改路径
def detect_platform_and_configure_paths():
    return {
        'platform': 'local',
        'image_folder': './data/converted/image',  # 你的图像文件夹
        'mask_folder': './data/converted/mask',    # 你的掩膜文件夹
        'output_folder': './models'                # 输出文件夹
    }
```

### 第4步：运行训练（1.5分钟设置）

```bash
python unet_training_script.py
```

## 📁 数据格式要求

确保你的数据结构如下：

```
project/
├── data/converted/
│   ├── image/
│   │   ├── tile_0_0_merged.tif
│   │   └── tile_0_1_merged.tif
│   └── mask/
│       ├── tile_0_0_mask.tif
│       └── tile_0_1_mask.tif
├── models/  # 自动创建
└── unet_training_script.py
```

## ⚙️ 快速配置调整

### 内存不足？
```python
# 在脚本中找到TRAINING_CONFIG，修改：
TRAINING_CONFIG = {
    'batch_size': 4,        # 从8改为4
    'patch_size': 224,      # 从256改为224
    'epochs': 20,           # 减少训练轮数
    # ... 其他保持不变
}
```

### GPU内存不足？
```python
TRAINING_CONFIG = {
    'batch_size': 2,        # 最小批次
    'patch_size': 224,      # 小图像块
    'enable_mixed_precision': True,  # 启用混合精度
    # ... 其他保持不变
}
```

### 想要更好效果？
```python
TRAINING_CONFIG = {
    'batch_size': 16,       # 增加批次（如果内存允许）
    'patch_size': 512,      # 更大图像块
    'epochs': 50,           # 更多训练轮数
    # ... 其他保持不变
}
```

## 🎯 云端平台快速配置

### Google Colab
```python
# 在Colab中运行前，先挂载Drive
from google.colab import drive
drive.mount('/content/drive')

# 修改路径为Drive路径
def detect_platform_and_configure_paths():
    return {
        'platform': 'colab',
        'image_folder': '/content/drive/MyDrive/dataset/converted/image',
        'mask_folder': '/content/drive/MyDrive/dataset/converted/mask',
        'output_folder': '/content/drive/MyDrive/models'
    }
```

### Kaggle
```python
def detect_platform_and_configure_paths():
    return {
        'platform': 'kaggle',
        'image_folder': '/kaggle/input/your-dataset/converted/image',
        'mask_folder': '/kaggle/input/your-dataset/converted/mask',
        'output_folder': '/kaggle/working/models'
    }
```

## 📊 预期输出

训练完成后，你将得到：

```
models/
├── best_unet_model.h5              # 最佳模型（推荐使用）
├── final_unet_model_full_data.h5   # 最终模型
├── training_history_full_data.json # 训练数据
└── training_curves.png             # 训练曲线图
```

## 🔧 常见问题1分钟解决

### Q: 提示"图像文件夹不存在"
**A**: 检查路径是否正确
```python
import os
print("当前目录:", os.getcwd())
print("文件夹存在:", os.path.exists('你的路径'))
```

### Q: 内存不足崩溃
**A**: 立即减少批次大小
```python
# 在TRAINING_CONFIG中设置
'batch_size': 2,
'patch_size': 224,
```

### Q: GPU不可用
**A**: 检查TensorFlow GPU支持
```python
import tensorflow as tf
print("GPU可用:", tf.config.list_physical_devices('GPU'))
```

### Q: 训练太慢
**A**: 减少数据量或启用优化
```python
TRAINING_CONFIG = {
    'epochs': 10,                    # 减少轮数
    'enable_mixed_precision': True,  # 启用加速
    'use_all_data': False,          # 限制数据量
    'max_patches_per_file': 100,    # 限制每文件块数
}
```

## 🎉 成功标志

看到以下输出说明训练成功：

```
🎉 训练完成！
✅ 最终模型已保存: ./models/final_unet_model_full_data.h5
✅ 训练历史已保存: ./models/training_history_full_data.json
📊 训练曲线已保存: ./models/training_curves.png

🎯 训练成功完成！
📁 模型文件保存在: ./models
```

## 📈 下一步

1. **查看训练曲线**: 打开 `training_curves.png`
2. **测试模型**: 使用 `best_unet_model.h5` 进行预测
3. **调优参数**: 根据结果调整配置重新训练

## 💡 专业提示

- **首次运行**: 使用小数据集测试（2-3个文件对）
- **参数调优**: 先用少量epochs测试，再增加
- **内存监控**: 关注训练过程中的内存使用情况
- **备份模型**: 定期备份训练好的模型

## 🔄 与Notebook的区别

| 特性 | Python脚本 | Jupyter Notebook |
|------|------------|------------------|
| **运行方式** | `python script.py` | 逐个cell执行 |
| **调试** | 命令行输出 | 交互式调试 |
| **自动化** | 完全自动化 | 需要手动执行 |
| **资源管理** | 自动清理 | 需要手动管理 |
| **适用场景** | 生产环境、批量处理 | 实验、原型开发 |

## 🚀 高级用法

### 批量训练多个配置
```python
# 创建配置文件
configs = [
    {'batch_size': 4, 'patch_size': 256, 'epochs': 20},
    {'batch_size': 8, 'patch_size': 384, 'epochs': 30},
    {'batch_size': 16, 'patch_size': 512, 'epochs': 40},
]

for i, config in enumerate(configs):
    print(f"开始训练配置 {i+1}/{len(configs)}")
    # 更新配置并训练
    update_config(**config)
    main()
```

### 命令行参数支持
```python
# 可以扩展脚本支持命令行参数
# python unet_training_script.py --batch_size 8 --epochs 30
```

---

**🚀 现在开始你的UNet训练之旅吧！**

如果遇到问题，请查看详细的 `ENVIRONMENT_SETUP.md` 文档。
