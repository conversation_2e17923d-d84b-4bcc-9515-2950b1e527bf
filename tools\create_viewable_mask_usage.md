# 📖 create_viewable_mask.py 使用指南

## 🎯 功能说明

`create_viewable_mask.py` 是一个用于将大型GeoTIFF掩膜文件转换为普通图像查看器可以打开的格式的工具。

**主要功能：**
- 批量处理文件夹下的所有掩膜文件
- 创建预览图（适合查看的尺寸）
- 生成PNG格式版本
- 创建统计信息图表
- 移除地理信息以兼容普通查看器

## 🚀 使用方法

### 方法1：批量处理（推荐）

**默认路径模式：**
```bash
python create_viewable_mask.py
```
- 使用代码中设置的默认路径
- 处理默认文件夹下的所有TIFF�件

**指定文件夹模式：**
```bash
python create_viewable_mask.py "D:/path/to/mask/folder"
```
- 处理指定文件夹下的所有TIFF文件
- 在该文件夹下创建viewable子文件夹

**完全自定义模式：**
```bash
python create_viewable_mask.py "D:/path/to/mask/folder" "D:/path/to/output/folder"
```
- 处理指定文件夹下的所有TIFF文件
- 输出到指定的文件夹

### 方法2：修改代码中的默认路径

编辑 `create_viewable_mask.py` 文件，修改以下行：
```python
default_mask_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted\mask"
```
改为您的实际路径，然后运行：
```bash
python create_viewable_mask.py
```

## 📁 输入文件夹结构

确保您的掩膜文件夹结构如下：
```
mask_folder/
├── tile_0_0_mask.tif
├── tile_1_1_mask.tif
├── building_mask.tif
└── ... (其他.tif或.tiff文件)
```

## 📂 输出文件夹结构

批量处理后，输出结构如下：
```
output_folder/
├── tile_0_0_mask/
│   ├── preview_tile_0_0_mask.png      # 预览图（推荐查看）
│   ├── tile_0_0_mask.png              # 完整PNG版本
│   ├── standard_tile_0_0_mask.tif     # 标准TIFF
│   └── stats_tile_0_0_mask.png        # 统计信息图
├── tile_1_1_mask/
│   ├── preview_tile_1_1_mask.png
│   ├── tile_1_1_mask.png
│   ├── standard_tile_1_1_mask.tif
│   └── stats_tile_1_1_mask.png
└── ... (每个掩膜文件都有独立的文件夹)
```

## 🎨 输出文件说明

### 1. 预览图 (`preview_*.png`)
- **用途**：快速查看掩膜内容
- **特点**：缩放到合适的查看尺寸（最大2048像素）
- **推荐**：首先查看这个文件

### 2. 完整PNG (`*.png`)
- **用途**：完整尺寸的PNG格式
- **特点**：保持原始分辨率，无地理信息
- **适用**：需要完整尺寸时使用

### 3. 标准TIFF (`standard_*.tif`)
- **用途**：标准TIFF格式，无地理信息
- **特点**：3通道RGB格式
- **适用**：需要TIFF格式但要兼容普通查看器

### 4. 统计图 (`stats_*.png`)
- **用途**：掩膜数据的统计信息可视化
- **内容**：像素值分布、缩略图、统计信息、比例饼图
- **适用**：了解掩膜数据质量和分布

## ✅ 使用示例

### 示例1：处理默认路径
```bash
cd tools
python create_viewable_mask.py
```

### 示例2：处理指定文件夹
```bash
cd tools
python create_viewable_mask.py "D:/GIS_Data/masks"
```

### 示例3：自定义输出路径
```bash
cd tools
python create_viewable_mask.py "D:/GIS_Data/masks" "D:/Output/viewable_masks"
```

## 📊 处理结果示例

```
============================================================
批量创建可查看的掩膜文件
============================================================
本工具将处理指定文件夹下的所有TIFF掩膜文件

📁 掩膜文件夹: D:\GIS Data\masks
📁 输出文件夹: D:\GIS Data\masks\viewable
📊 找到 3 个掩膜文件

[1/3] 处理: tile_0_0_mask.tif
--------------------------------------------------
转换: tile_0_0_mask.tif -> preview_tile_0_0_mask.png
  原始数据类型: uint8
  原始数据范围: [0, 1]
  图像尺寸: (15626, 22265)
✅ 标准TIFF已保存: standard_tile_0_0_mask.tif
✅ PNG版本已保存: tile_0_0_mask.png
✅ 预览图已保存: preview_tile_0_0_mask.png (尺寸: 1437x2048)
✅ 统计图已保存: stats_tile_0_0_mask.png
✅ 处理成功

============================================================
批量处理完成
============================================================
📊 总文件数: 3
✅ 成功处理: 3
❌ 处理失败: 0

🎉 成功处理 3 个掩膜文件！
📁 输出文件保存在: D:\GIS Data\masks\viewable
```

## 🔧 常见问题

### 问题1：找不到掩膜文件
**症状**：`❌ 在文件夹中未找到TIFF掩膜文件`
**解决**：
- 检查文件夹路径是否正确
- 确认文件扩展名是.tif或.tiff
- 检查文件权限

### 问题2：内存不足
**症状**：处理大文件时内存溢出
**解决**：
- 一次处理较少的文件
- 关闭其他占用内存的程序
- 使用更小的预览尺寸

### 问题3：输出文件无法打开
**症状**：生成的PNG文件损坏
**解决**：
- 检查原始掩膜文件是否完整
- 确认有足够的磁盘空间
- 重新运行处理

## 💡 使用建议

1. **首次使用**：先处理1-2个文件测试效果
2. **大批量处理**：确保有足够的磁盘空间
3. **查看顺序**：预览图 → 统计图 → 完整PNG
4. **文件管理**：定期清理不需要的输出文件

## 🔄 与其他工具的配合

- **数据检查**：配合 `dataset_diagnostic.py` 使用
- **数据转换**：配合 `convert_dataset.py` 使用
- **掩膜检查**：配合 `check_mask_file.py` 使用

---

**最后更新：** 2025-07-17  
**兼容性：** Python 3.7+, rasterio, PIL, opencv-python
