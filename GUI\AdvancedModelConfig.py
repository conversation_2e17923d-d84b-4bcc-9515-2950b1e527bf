from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QFormLayout, 
                            QLineEdit, QCheckBox, QSpinBox,
                            QDialogButtonBox)

class AdvancedModelConfig(QDialog):
    def __init__(self, config, parent=None):
        super().__init__(parent)
        self.config = config
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form = QFormLayout()
        # 动态生成表单字段
        self.widgets = {}
        for key, value in self.config['model']['args'].items():
            if isinstance(value, bool):
                widget = QCheckBox()
                widget.setChecked(value)
            elif isinstance(value, int):
                widget = QSpinBox()
                widget.setValue(value)
            else:
                widget = QLineEdit(str(value))
            form.addRow(key, widget)
            self.widgets[key] = widget
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        
        layout.addLayout(form)
        layout.addWidget(buttons) 