#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Shape文件边界的数据集下载器

功能：
1. 读取Shape文件的整体边界范围
2. 下载该边界范围的大图像（类似区域选择下载）
3. 基于大图像和Shape文件生成配对的image和mask切片
4. 确保image和mask完全匹配，适合训练

与原有shape_based_dataset_downloader的区别：
- 原版：按每个几何体分别下载瓦片，可能导致不同时间的数据混合
- 新版：按整体边界下载一张大图，然后切片，确保数据一致性
"""

import time
import requests
import numpy as np
from pathlib import Path
from datetime import datetime
from PIL import Image
import mercantile
import geopandas as gpd
from rasterio.features import rasterize
from rasterio.transform import from_bounds
import rasterio
from osgeo import gdal, osr
from io import BytesIO


class ShapeBoundaryDatasetDownloader:
    """基于Shape文件边界的数据集下载器"""

    def __init__(self, output_dir="dataset"):
        """
        初始化下载器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.image_dir = self.output_dir / "image"
        self.mask_dir = self.output_dir / "mask"
        self.viewable_mask_dir = self.mask_dir / "viewable_mask"

        # 创建输出目录
        for dir_path in [self.image_dir, self.mask_dir, self.viewable_mask_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 下载统计
        self.download_stats = {
            'start_time': None,
            'end_time': None,
            'total_tiles': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'generated_images': 0
        }

        # 下载延迟（秒）
        self.download_delay = 0.1

    def set_download_delay(self, delay):
        """设置下载延迟"""
        self.download_delay = delay

    def load_shapefile(self, shapefile_path):
        """
        加载Shape文件

        Args:
            shapefile_path: Shape文件路径

        Returns:
            GeoDataFrame: 加载的地理数据
        """
        try:
            print(f"🔄 加载Shape文件: {shapefile_path}")
            gdf = gpd.read_file(shapefile_path)

            # 确保坐标系为WGS84
            if gdf.crs != 'EPSG:4326':
                print(f"🔄 转换坐标系从 {gdf.crs} 到 EPSG:4326")
                gdf = gdf.to_crs('EPSG:4326')

            print(f"✅ 成功加载 {len(gdf)} 个要素")
            print(f"📊 边界范围: {gdf.total_bounds}")

            return gdf

        except Exception as e:
            print(f"❌ 加载Shape文件失败: {e}")
            return None

    def get_shapefile_bounds(self, gdf):
        """
        获取Shape文件的整体边界

        Args:
            gdf: GeoDataFrame

        Returns:
            tuple: (min_lng, min_lat, max_lng, max_lat)
        """
        bounds = gdf.total_bounds  # [minx, miny, maxx, maxy]
        return bounds[0], bounds[1], bounds[2], bounds[3]

    def download_tile_with_fallback(self, x, y, zoom_level, session, primary_source="google"):
        """
        使用主备源下载瓦片（复用Main_Window中的逻辑）
        """
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.arcgis.com/'
        }

        # 获取主备源URL
        url_sources = self.get_tile_url_with_fallback(x, y, zoom_level, primary_source)

        for url, source_name in url_sources:
            try:
                response = session.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                # 成功下载
                image = Image.open(BytesIO(response.content))
                return image

            except Exception as e:
                print(f"⚠️ {source_name}源下载失败: {e}")
                continue

        print(f"❌ 所有源都无法下载瓦片 {x}/{y}/{zoom_level}")
        return None

    def get_tile_url_with_fallback(self, x, y, zoom, primary_source):
        """
        获取瓦片URL的主备源列表
        """
        urls = []

        if primary_source == "google":
            # Google主源
            server = (x + y) % 4  # 负载均衡
            urls.append((f"https://mt{server}.google.com/vt/lyrs=s&x={x}&y={y}&z={zoom}", "Google"))
            # ArcGIS备源
            urls.append((f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom}/{y}/{x}", "ArcGIS"))
        elif primary_source == "arcgis":
            # ArcGIS主源
            urls.append((f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom}/{y}/{x}", "ArcGIS"))
            # Google备源
            server = (x + y) % 4
            urls.append((f"https://mt{server}.google.com/vt/lyrs=s&x={x}&y={y}&z={zoom}", "Google"))

        return urls

    def download_large_image(self, bounds, zoom_level=18, tile_source="google"):
        """
        下载指定边界的大图像

        Args:
            bounds: 边界 (min_lng, min_lat, max_lng, max_lat)
            zoom_level: 缩放级别
            tile_source: 瓦片源

        Returns:
            tuple: (image_array, geotransform) 或 (None, None)
        """
        min_lng, min_lat, max_lng, max_lat = bounds
        print(f"🌍 下载边界: lng[{min_lng:.6f}, {max_lng:.6f}], lat[{min_lat:.6f}, {max_lat:.6f}]")

        try:
            # 计算瓦片范围
            min_tile = mercantile.tile(min_lng, min_lat, zoom_level)
            max_tile = mercantile.tile(max_lng, max_lat, zoom_level)

            # 确保正确的顺序
            x_start = min(min_tile.x, max_tile.x)
            x_end = max(min_tile.x, max_tile.x)
            y_start = min(min_tile.y, max_tile.y)
            y_end = max(min_tile.y, max_tile.y)

            # 计算总瓦片数和图像大小
            tile_width = x_end - x_start + 1
            tile_height = y_end - y_start + 1
            total_width = tile_width * 256
            total_height = tile_height * 256

            print(f"📊 瓦片范围: {tile_width}×{tile_height} = {tile_width * tile_height}个瓦片")
            print(f"🖼️ 最终图像大小: {total_width}×{total_height} 像素")

            # 创建大图
            merged_image = Image.new('RGB', (total_width, total_height))

            # 下载和拼接瓦片
            session = requests.Session()
            successful_downloads = 0
            failed_downloads = 0
            total_tiles = tile_width * tile_height
            current_tile = 0

            for y in range(y_start, y_end + 1):
                for x in range(x_start, x_end + 1):
                    try:
                        # 更新进度条
                        current_tile += 1
                        progress = (current_tile / total_tiles) * 100
                        bar_length = 30
                        filled_length = int(bar_length * current_tile // total_tiles)
                        bar = '█' * filled_length + '░' * (bar_length - filled_length)
                        print(f"\r📥 下载进度: [{bar}] {progress:.1f}% ({current_tile}/{total_tiles})", end='', flush=True)

                        # 下载瓦片
                        tile_img = self.download_tile_with_fallback(x, y, zoom_level, session, tile_source)
                        if tile_img is None:
                            failed_downloads += 1
                            continue

                        # 计算瓦片在大图中的位置
                        paste_x = (x - x_start) * 256
                        paste_y = (y - y_start) * 256

                        # 将瓦片粘贴到大图上
                        merged_image.paste(tile_img, (paste_x, paste_y))
                        successful_downloads += 1

                        # 添加延迟
                        time.sleep(self.download_delay)

                    except Exception as e:
                        failed_downloads += 1
                        continue

            # 完成进度条
            print()  # 换行

            print(f"📊 瓦片下载完成: 成功 {successful_downloads} 个, 失败 {failed_downloads} 个")

            # 转换为numpy数组
            image_array = np.array(merged_image, dtype=np.uint8)

            # 计算地理变换参数
            ul_bounds = mercantile.bounds(x_start, y_start, zoom_level)
            lr_bounds = mercantile.bounds(x_end, y_end, zoom_level)

            x_res = (lr_bounds.east - ul_bounds.west) / total_width
            y_res = (ul_bounds.north - lr_bounds.south) / total_height

            geotransform = (
                ul_bounds.west,    # 左上角x坐标
                x_res,             # 水平分辨率
                0,                 # 旋转
                ul_bounds.north,   # 左上角y坐标
                0,                 # 旋转
                -y_res            # 垂直分辨率（负值）
            )

            self.download_stats['total_tiles'] = tile_width * tile_height
            self.download_stats['successful_downloads'] = successful_downloads
            self.download_stats['failed_downloads'] = failed_downloads

            return image_array, geotransform

        except Exception as e:
            print(f"❌ 下载大图像失败: {e}")
            return None, None

    def create_mask_from_shapefile(self, gdf, image_bounds, image_size):
        """
        从Shape文件创建mask

        Args:
            gdf: GeoDataFrame
            image_bounds: 图像边界 (west, south, east, north)
            image_size: 图像尺寸 (width, height)

        Returns:
            numpy.ndarray: mask数组
        """
        try:
            print(f"🎭 创建mask，图像尺寸: {image_size}")

            # 创建仿射变换
            transform = from_bounds(*image_bounds, image_size[0], image_size[1])

            # 准备几何体
            geometries = []
            for idx, row in gdf.iterrows():
                if row.geometry.is_valid:
                    geometries.append((row.geometry, 1))

            if not geometries:
                print("⚠️ 没有有效的几何体")
                return np.zeros(image_size[::-1], dtype=np.uint8)

            # 栅格化
            mask = rasterize(
                geometries,
                out_shape=image_size[::-1],  # (height, width)
                transform=transform,
                fill=0,
                dtype=np.uint8,
                all_touched=True
            )

            print(f"✅ Mask创建完成，前景像素: {np.sum(mask > 0)}/{mask.size}")
            return mask

        except Exception as e:
            print(f"❌ 创建mask失败: {e}")
            return np.zeros(image_size[::-1], dtype=np.uint8)

    def save_georeferenced_image(self, image_array, geotransform, output_path):
        """
        保存带地理参考的图像

        Args:
            image_array: 图像数组
            geotransform: 地理变换参数
            output_path: 输出路径
        """
        try:
            # 设置GDAL异常处理，消除警告
            gdal.UseExceptions()

            height, width = image_array.shape[:2]

            # 创建GDAL数据集
            driver = gdal.GetDriverByName('GTiff')

            if len(image_array.shape) == 3:
                # RGB图像
                dataset = driver.Create(str(output_path), width, height, 3, gdal.GDT_Byte)
                for i in range(3):
                    band = dataset.GetRasterBand(i + 1)
                    band.WriteArray(image_array[:, :, i])
            else:
                # 单波段图像（mask）
                dataset = driver.Create(str(output_path), width, height, 1, gdal.GDT_Byte)
                band = dataset.GetRasterBand(1)
                band.WriteArray(image_array)

            # 设置地理变换参数
            dataset.SetGeoTransform(geotransform)

            # 设置投影（WGS84）
            srs = osr.SpatialReference()
            srs.ImportFromEPSG(4326)
            dataset.SetProjection(srs.ExportToWkt())

            # 关闭数据集
            dataset = None
            print(f"✅ 保存图像: {output_path}")

        except Exception as e:
            print(f"❌ 保存图像失败: {e}")

    def create_viewable_mask(self, mask_path):
        """
        创建用户可查看的mask文件

        Args:
            mask_path: mask文件路径
        """
        try:
            print(f"🎨 创建可视化mask: {mask_path.name}")

            # 读取mask
            with rasterio.open(mask_path) as src:
                mask = src.read(1)

            base_name = mask_path.stem

            # 1. 创建标准灰度PNG (0->黑色, 1->白色)
            mask_255 = (mask * 255).astype(np.uint8)
            gray_image = Image.fromarray(mask_255, mode='L')
            gray_path = self.viewable_mask_dir / f"{base_name}_gray.png"
            gray_image.save(gray_path)

            # 2. 创建彩色PNG (更容易区分)
            colored_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
            colored_mask[mask == 0] = [25, 25, 112]   # 深蓝色 - 背景
            colored_mask[mask == 1] = [255, 165, 0]   # 橙色 - 建筑

            colored_image = Image.fromarray(colored_mask, mode='RGB')
            colored_path = self.viewable_mask_dir / f"{base_name}_colored.png"
            colored_image.save(colored_path)

            # 3. 创建对比图
            import matplotlib
            matplotlib.use('Agg')  # 使用非GUI后端
            import matplotlib.pyplot as plt

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

            # 原始灰度
            ax1.imshow(mask, cmap='gray', vmin=0, vmax=1)
            ax1.set_title('Original Mask (Grayscale)')
            ax1.axis('off')

            # 彩色版本
            ax2.imshow(colored_mask)
            ax2.set_title('Enhanced Color Version')
            ax2.axis('off')

            # 添加说明
            fig.suptitle(f'Mask Comparison: {base_name}', fontsize=14, fontweight='bold')

            # 统计信息
            total_pixels = mask.size
            building_pixels = np.sum(mask == 1)
            background_pixels = np.sum(mask == 0)

            stats_text = f"""
Statistics:
• Image Size: {mask.shape[0]} x {mask.shape[1]}
• Total Pixels: {total_pixels:,}
• Background: {background_pixels:,} ({background_pixels/total_pixels:.1%})
• Buildings: {building_pixels:,} ({building_pixels/total_pixels:.1%})

Color Legend:
Blue = Background
Orange = Buildings
            """

            plt.figtext(0.02, 0.02, stats_text, fontsize=9,
                       verticalalignment='bottom', fontfamily='monospace',
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

            plt.tight_layout()
            comparison_path = self.viewable_mask_dir / f"{base_name}_comparison.png"
            plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
            plt.close()

            print(f"✅ 可视化mask创建完成:")
            print(f"   - 灰度版: {gray_path.name}")
            print(f"   - 彩色版: {colored_path.name}")
            print(f"   - 对比图: {comparison_path.name}")

        except Exception as e:
            print(f"❌ 创建可视化mask失败: {e}")

    def process_shapefile_boundary(self, shapefile_path, zoom_level=18, tile_source="google"):
        """
        处理Shape文件边界，生成训练数据集

        Args:
            shapefile_path: Shape文件路径
            zoom_level: 缩放级别
            tile_source: 瓦片源

        Returns:
            bool: 是否成功
        """
        print("🚀 开始处理Shape文件边界")
        print("="*60)

        self.download_stats['start_time'] = datetime.now()

        try:
            # 1. 加载Shape文件
            gdf = self.load_shapefile(shapefile_path)
            if gdf is None:
                return False

            # 2. 获取整体边界
            bounds = self.get_shapefile_bounds(gdf)
            print(f"📏 Shape文件边界: {bounds}")

            # 3. 下载大图像
            print("\n📥 下载大图像...")
            image_array, geotransform = self.download_large_image(
                bounds, zoom_level, tile_source
            )
            if image_array is None:
                print("❌ 下载大图像失败")
                return False

            # 4. 创建mask
            print("\n🎭 创建mask...")
            image_bounds = (
                geotransform[0],  # west
                geotransform[3] + geotransform[5] * image_array.shape[0],  # south
                geotransform[0] + geotransform[1] * image_array.shape[1],  # east
                geotransform[3]   # north
            )

            mask_array = self.create_mask_from_shapefile(
                gdf, image_bounds, (image_array.shape[1], image_array.shape[0])
            )

            # 5. 保存image和mask
            print("\n💾 保存image和mask...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存到正式的image和mask文件夹（使用相同的基础名称）
            base_filename = f"satellite_{timestamp}"
            image_filename = f"{base_filename}.tif"
            mask_filename = f"{base_filename}.tif"

            final_image_path = self.image_dir / image_filename
            final_mask_path = self.mask_dir / mask_filename

            self.save_georeferenced_image(image_array, geotransform, final_image_path)
            self.save_georeferenced_image(mask_array, geotransform, final_mask_path)

            print(f"✅ image已保存: {image_filename}")
            print(f"✅ mask已保存: {mask_filename}")

            # 6. 创建可视化mask
            print("\n🎨 创建可视化mask...")
            self.create_viewable_mask(final_mask_path)

            # 更新统计
            self.download_stats['generated_images'] = 1
            self.download_stats['end_time'] = datetime.now()

            print("\n🎉 处理完成！")
            return True

        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False




def main():
    """命令行入口"""
    import argparse

    parser = argparse.ArgumentParser(description="基于Shape文件边界的数据集下载器")
    parser.add_argument("shapefile", help="Shape文件路径")
    parser.add_argument("-o", "--output", default="dataset", help="输出目录")
    parser.add_argument("-z", "--zoom", type=int, default=18, help="缩放级别")
    parser.add_argument("--source", default="google", choices=["google", "arcgis"], help="瓦片源")

    args = parser.parse_args()

    # 创建下载器
    downloader = ShapeBoundaryDatasetDownloader(args.output)

    # 开始处理
    success = downloader.process_shapefile_boundary(
        args.shapefile,
        zoom_level=args.zoom,
        tile_source=args.source
    )

    if success:
        print("\n🎉 处理完成！")
    else:
        print("\n❌ 处理失败！")


if __name__ == "__main__":
    main()
