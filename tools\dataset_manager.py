#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集管理工具

功能：
1. 数据集下载管理
2. 数据集格式转换
3. 数据集质量检查
4. 数据集统计分析

使用方法：
从GUI界面调用，或直接运行脚本
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
import numpy as np
import cv2
import rasterio
import geopandas as gpd
from typing import Dict, List, Tuple, Optional

class DatasetManager:
    """数据集管理器"""

    def __init__(self, base_dir="dataset"):
        """
        初始化数据集管理器

        Args:
            base_dir: 数据集基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)

        # 标准目录结构
        self.image_dir = self.base_dir / "image"
        self.mask_dir = self.base_dir / "mask"
        self.metadata_dir = self.base_dir / "metadata"

        # 创建目录
        for dir_path in [self.image_dir, self.mask_dir, self.metadata_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 元数据文件
        self.metadata_file = self.metadata_dir / "dataset_info.json"
        self.load_metadata()

    def load_metadata(self):
        """加载数据集元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            except:
                self.metadata = self.create_default_metadata()
        else:
            self.metadata = self.create_default_metadata()

    def create_default_metadata(self):
        """创建默认元数据"""
        return {
            "dataset_name": "Satellite Dataset",
            "created_date": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "version": "1.0",
            "description": "卫星图像语义分割数据集",
            "image_count": 0,
            "mask_count": 0,
            "image_format": "GeoTIFF",
            "mask_format": "GeoTIFF",
            "coordinate_system": "EPSG:4326",
            "tile_size": 256,
            "classes": {
                "0": "背景",
                "1": "建筑物/障碍物"
            },
            "statistics": {},
            "download_history": []
        }

    def save_metadata(self):
        """保存元数据"""
        self.metadata["last_updated"] = datetime.now().isoformat()

        # 转换不兼容JSON的数据类型
        metadata_copy = self._convert_for_json(self.metadata)

        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_copy, f, indent=2, ensure_ascii=False)

    def add_download_record(self, source_type, source_path, tile_count, success_count):
        """
        添加下载记录

        Args:
            source_type: 数据源类型 ('shapefile', 'region')
            source_path: 数据源路径
            tile_count: 瓦片总数
            success_count: 成功下载数
        """
        record = {
            "timestamp": datetime.now().isoformat(),
            "source_type": source_type,
            "source_path": str(source_path),
            "tile_count": tile_count,
            "success_count": success_count,
            "success_rate": success_count / tile_count if tile_count > 0 else 0
        }

        self.metadata["download_history"].append(record)
        self.save_metadata()

    def scan_dataset(self):
        """扫描数据集，更新统计信息"""
        print("🔍 扫描数据集...")

        # 扫描图像文件
        image_files = list(self.image_dir.glob("*.tif")) + list(self.image_dir.glob("*.tiff"))
        mask_files = list(self.mask_dir.glob("*.tif")) + list(self.mask_dir.glob("*.tiff"))

        self.metadata["image_count"] = len(image_files)
        self.metadata["mask_count"] = len(mask_files)

        print(f"📊 找到 {len(image_files)} 个图像文件")
        print(f"📊 找到 {len(mask_files)} 个mask文件")

        # 计算统计信息
        if image_files:
            self.calculate_dataset_statistics(image_files, mask_files)

        self.save_metadata()
        return len(image_files), len(mask_files)

    def calculate_dataset_statistics(self, image_files, mask_files):
        """
        计算数据集统计信息

        Args:
            image_files: 图像文件列表
            mask_files: mask文件列表
        """
        print("📊 计算数据集统计信息...")

        stats = {
            "image_sizes": [],
            "mask_pixel_distribution": {"background": 0, "foreground": 0},
            "file_sizes": {"images": [], "masks": []},
            "coordinate_bounds": {"min_x": float('inf'), "min_y": float('inf'),
                                "max_x": float('-inf'), "max_y": float('-inf')}
        }

        # 分析前几个文件作为样本
        sample_size = min(10, len(image_files))

        for i, image_file in enumerate(image_files[:sample_size]):
            try:
                # 读取图像信息
                with rasterio.open(image_file) as src:
                    stats["image_sizes"].append([src.width, src.height])
                    stats["file_sizes"]["images"].append(image_file.stat().st_size)

                    # 更新坐标边界
                    bounds = src.bounds
                    stats["coordinate_bounds"]["min_x"] = min(stats["coordinate_bounds"]["min_x"], bounds.left)
                    stats["coordinate_bounds"]["min_y"] = min(stats["coordinate_bounds"]["min_y"], bounds.bottom)
                    stats["coordinate_bounds"]["max_x"] = max(stats["coordinate_bounds"]["max_x"], bounds.right)
                    stats["coordinate_bounds"]["max_y"] = max(stats["coordinate_bounds"]["max_y"], bounds.top)

                # 分析对应的mask文件
                mask_file = self.find_corresponding_mask(image_file, mask_files)
                if mask_file:
                    with rasterio.open(mask_file) as src:
                        mask_data = src.read(1)
                        background_pixels = np.sum(mask_data == 0)
                        foreground_pixels = np.sum(mask_data == 1)

                        stats["mask_pixel_distribution"]["background"] += background_pixels
                        stats["mask_pixel_distribution"]["foreground"] += foreground_pixels
                        stats["file_sizes"]["masks"].append(mask_file.stat().st_size)

                # 显示美观的进度条
                progress = (i + 1) / sample_size
                self._print_progress_bar(progress, i + 1, sample_size, "分析数据集")

            except Exception as e:
                print(f"⚠️ 分析文件失败 {image_file}: {e}")

        # 计算平均值和总结
        if stats["image_sizes"]:
            avg_width = np.mean([size[0] for size in stats["image_sizes"]])
            avg_height = np.mean([size[1] for size in stats["image_sizes"]])
            stats["average_image_size"] = [int(avg_width), int(avg_height)]

        if stats["file_sizes"]["images"]:
            stats["average_image_file_size"] = int(np.mean(stats["file_sizes"]["images"]))

        if stats["file_sizes"]["masks"]:
            stats["average_mask_file_size"] = int(np.mean(stats["file_sizes"]["masks"]))

        # 计算前景背景比例
        total_pixels = (stats["mask_pixel_distribution"]["background"] +
                       stats["mask_pixel_distribution"]["foreground"])
        if total_pixels > 0:
            stats["foreground_ratio"] = stats["mask_pixel_distribution"]["foreground"] / total_pixels
            stats["background_ratio"] = stats["mask_pixel_distribution"]["background"] / total_pixels

        self.metadata["statistics"] = stats
        print("✅ 统计信息计算完成")

    def _print_progress_bar(self, progress, current, total, task_name="处理"):
        """打印美观的进度条"""
        try:
            import sys

            # 计算进度条长度
            bar_length = 30
            filled_length = int(bar_length * progress)

            # 创建进度条
            bar = '█' * filled_length + '░' * (bar_length - filled_length)

            # 计算百分比
            percent = progress * 100

            # 格式化输出
            progress_text = f"\r📊 {task_name}: [{bar}] {percent:6.1f}% ({current}/{total})"

            # 输出进度条（覆盖上一行）
            sys.stdout.write(progress_text)
            sys.stdout.flush()

            # 如果完成，换行
            if progress >= 1.0:
                print()  # 换行

        except Exception as e:
            # 如果进度条显示失败，回退到简单显示
            print(f"  {task_name}进度: {current}/{total} ({percent:.1f}%)")

    def find_corresponding_mask(self, image_file, mask_files):
        """
        查找对应的mask文件

        Args:
            image_file: 图像文件路径
            mask_files: mask文件列表

        Returns:
            Path or None: 对应的mask文件路径
        """
        image_stem = image_file.stem

        # 1. 精确匹配（完全相同的文件名）
        for mask_file in mask_files:
            if mask_file.stem == image_stem:
                return mask_file

        # 2. 时间戳匹配（提取时间戳部分进行匹配）
        import re

        # 提取图像文件的时间戳（格式：YYYYMMDD_HHMMSS）
        timestamp_pattern = r'(\d{8}_\d{6})'
        image_timestamp_match = re.search(timestamp_pattern, image_stem)

        if image_timestamp_match:
            image_timestamp = image_timestamp_match.group(1)

            for mask_file in mask_files:
                mask_timestamp_match = re.search(timestamp_pattern, mask_file.stem)
                if mask_timestamp_match and mask_timestamp_match.group(1) == image_timestamp:
                    return mask_file

        # 3. 传统命名模式匹配（兼容老版本）
        possible_names = [
            f"{image_stem}_mask.tif",
            f"{image_stem}_mask.tiff"
        ]

        for mask_file in mask_files:
            if mask_file.name in possible_names:
                return mask_file

        return None

    def _detect_dataset_type(self, image_files):
        """
        检测数据集类型

        Args:
            image_files: 图像文件列表

        Returns:
            str: "large_images" 或 "small_patches"
        """
        if not image_files:
            return "unknown"

        # 检查文件大小和命名模式
        large_image_indicators = 0
        small_patch_indicators = 0

        for image_file in image_files[:5]:  # 检查前5个文件
            try:
                # 检查文件大小
                file_size_mb = image_file.stat().st_size / (1024 * 1024)

                # 检查文件名模式
                filename = image_file.stem.lower()

                # 大图像的特征
                if (file_size_mb > 10 or  # 文件大于10MB
                    'satellite' in filename or  # 包含satellite关键词
                    len(image_files) <= 5):  # 文件数量很少
                    large_image_indicators += 1

                # 小切片的特征
                if (file_size_mb < 1 or  # 文件小于1MB
                    'tile' in filename or  # 包含tile关键词
                    'patch' in filename or  # 包含patch关键词
                    len(image_files) > 20):  # 文件数量很多
                    small_patch_indicators += 1

            except Exception:
                continue

        # 根据指标判断类型
        if large_image_indicators > small_patch_indicators:
            return "large_images"
        else:
            return "small_patches"

    def validate_dataset(self):
        """
        验证数据集完整性

        Returns:
            dict: 验证结果
        """
        print("🔍 验证数据集完整性...")

        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "image_count": 0,
            "mask_count": 0,
            "paired_count": 0
        }

        # 获取文件列表
        image_files = list(self.image_dir.glob("*.tif")) + list(self.image_dir.glob("*.tiff"))
        mask_files = list(self.mask_dir.glob("*.tif")) + list(self.mask_dir.glob("*.tiff"))

        validation_result["image_count"] = len(image_files)
        validation_result["mask_count"] = len(mask_files)

        # 检查配对
        paired_count = 0
        for image_file in image_files:
            mask_file = self.find_corresponding_mask(image_file, mask_files)
            if mask_file:
                paired_count += 1

                # 检查文件完整性
                try:
                    with rasterio.open(image_file) as img_src, rasterio.open(mask_file) as mask_src:
                        if img_src.shape != mask_src.shape:
                            validation_result["warnings"].append(
                                f"尺寸不匹配: {image_file.name} vs {mask_file.name}"
                            )
                except Exception as e:
                    validation_result["errors"].append(f"文件损坏: {image_file.name} - {e}")
            else:
                validation_result["warnings"].append(f"缺少对应mask: {image_file.name}")

        validation_result["paired_count"] = paired_count

        # 检查是否有足够的数据
        if paired_count == 0:
            validation_result["valid"] = False
            validation_result["errors"].append("没有找到有效的图像-mask配对")
        else:
            # 检测数据集类型
            dataset_type = self._detect_dataset_type(image_files)

            if dataset_type == "large_images":
                # 大图像模式：1-5个大图像是正常的
                if paired_count >= 1:
                    validation_result["info"] = f"检测到大图像数据集，共{paired_count}个配对文件，训练时将自动切片"
                else:
                    validation_result["warnings"].append("建议至少有1个大图像用于训练")
            else:
                # 切片模式：需要较多的小切片
                if paired_count < 10:
                    validation_result["warnings"].append("切片数据量较少，可能影响训练效果")

        # 打印验证结果
        print(f"📊 验证结果:")
        print(f"  图像文件: {validation_result['image_count']}")
        print(f"  Mask文件: {validation_result['mask_count']}")
        print(f"  有效配对: {validation_result['paired_count']}")

        if validation_result["errors"]:
            print(f"❌ 错误 ({len(validation_result['errors'])}):")
            for error in validation_result["errors"]:
                print(f"    {error}")

        if validation_result.get("info"):
            print(f"ℹ️ {validation_result['info']}")

        if validation_result["warnings"]:
            print(f"⚠️ 警告 ({len(validation_result['warnings'])}):")
            for warning in validation_result["warnings"]:
                print(f"    {warning}")

        if validation_result["valid"] and not validation_result["errors"]:
            print("✅ 数据集验证通过")

        return validation_result

    def get_dataset_info(self):
        """
        获取数据集信息

        Returns:
            dict: 数据集信息
        """
        self.scan_dataset()
        return self.metadata

    def export_dataset_report(self, output_path=None):
        """
        导出数据集报告

        Args:
            output_path: 输出路径
        """
        if output_path is None:
            output_path = self.metadata_dir / f"dataset_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # 获取最新信息
        info = self.get_dataset_info()
        validation = self.validate_dataset()

        report = {
            "dataset_info": info,
            "validation_result": validation,
            "export_time": datetime.now().isoformat()
        }

        # 转换不兼容JSON的数据类型
        report_copy = self._convert_for_json(report)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_copy, f, indent=2, ensure_ascii=False)

        print(f"📄 数据集报告已导出: {output_path}")
        return output_path

    def _convert_for_json(self, obj):
        """递归转换对象中不兼容JSON的数据类型"""
        if isinstance(obj, dict):
            return {key: self._convert_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'item'):  # numpy标量
            return obj.item()
        else:
            return obj

def main():
    """主函数 - 用于测试"""
    import argparse

    parser = argparse.ArgumentParser(description='数据集管理工具')
    parser.add_argument('--dataset-dir', '-d', default='dataset', help='数据集目录')
    parser.add_argument('--action', '-a', choices=['scan', 'validate', 'report'],
                       default='scan', help='执行的操作')

    args = parser.parse_args()

    # 创建管理器
    manager = DatasetManager(args.dataset_dir)

    if args.action == 'scan':
        manager.scan_dataset()
    elif args.action == 'validate':
        manager.validate_dataset()
    elif args.action == 'report':
        manager.export_dataset_report()

if __name__ == "__main__":
    main()
