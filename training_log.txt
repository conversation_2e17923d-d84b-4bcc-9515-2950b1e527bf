[2025-09-24 20:31:44] === 开始训练 DeepLabV3+ 分割模型 ===
[2025-09-24 20:31:44] 使用配置: D:\Satellite_Image_Auto_Offset\Autooffset_Test\config\deeplabv3+_config.json
[2025-09-24 20:31:44] 启用多分类模式，类别数量: 4
[2025-09-24 20:31:44] 类别名称: ['背景', '建筑物', '道路', '植被']
[2025-09-24 20:31:44] 数据模式: image+shape
[2025-09-24 20:31:44] 图像文件: D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250924_144816.tif
[2025-09-24 20:31:44] 形状文件: D:/Satellite_Image_Auto_Offset/arar_multiclass/arar_multiclass.shp
[2025-09-24 20:31:44] 正在读取图像数据...
[2025-09-24 20:31:44] 图像形状: (3072, 5120, 3)
[2025-09-24 20:31:44] 图像数据类型: uint8
[2025-09-24 20:31:44] 图像值范围: 0 - 255
[2025-09-24 20:31:44] 正在读取形状数据...
[2025-09-24 20:31:44] 形状数据包含 67 个要素
[2025-09-24 20:31:44] 转换CRS: EPSG:3857 -> EPSG:4326
[2025-09-24 20:31:44] CRS转换完成
[2025-09-24 20:31:44] 正在创建掩码...
[2025-09-24 20:31:44] 掩码形状: (3072, 5120)
[2025-09-24 20:31:44]   背景 (ID:0): 13631189 像素
[2025-09-24 20:31:44]   建筑物 (ID:1): 581706 像素
[2025-09-24 20:31:44]   道路 (ID:2): 774715 像素
[2025-09-24 20:31:44]   植被 (ID:3): 741030 像素
[2025-09-24 20:31:44] 开始图像切片，切片大小: 256, 重叠: 64
[2025-09-24 20:31:45] 生成了 258 个图像块
[2025-09-24 20:31:45] 最终训练样本数: 206, 测试样本数: 52
[2025-09-24 20:31:45] 应用backbone预处理，使用: resnet50
[2025-09-24 20:31:45] 使用手动ImageNet预处理（ResNet）
[2025-09-24 20:31:45] 预处理前图像范围: [0.0000, 1.0000]
[2025-09-24 20:31:45] 预处理后图像范围: [-2.1179, -1.7870]
[2025-09-24 20:31:45] 创建DeepLabV3+模型，骨干网络: resnet50, 输出步长: 16
[2025-09-24 20:31:48] 多分类类别权重: {0: 0.32036254691389715, 1: 4.043721576287951, 2: 3.1291687301654276, 3: 3.2085645277106862}
[2025-09-24 20:31:48]   背景 (ID:0): 13194651 个像素
[2025-09-24 20:31:48]   建筑物 (ID:1): 1045342 个像素
[2025-09-24 20:31:48]   道路 (ID:2): 1350861 个像素
[2025-09-24 20:31:48]   植被 (ID:3): 1317434 个像素
[2025-09-24 20:31:48] 优化器配置: lr=1.0e-04, clipnorm=1.0, clipvalue=0.5
[2025-09-24 20:31:48] 使用标准分类交叉熵损失 + 改进的优化器配置
[2025-09-24 20:31:48] 模型参数总数: 40,431,684
[2025-09-24 20:31:48] 模型将保存到: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models/deeplabv3+_model.h5
[2025-09-24 20:31:48] 开始训练模型...
[2025-09-24 20:35:02] 最终模型已保存到: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models/deeplabv3+_model_final.h5
[2025-09-24 20:35:02] 最佳模型已保存到: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models/deeplabv3+_model.h5
[2025-09-24 20:35:02] 正在生成训练报告...
[2025-09-24 20:35:03] 📊 训练报告已生成:
[2025-09-24 20:35:03]    - Markdown报告: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models\training_reports\DeepLabV3Plus_20250924_203148\training_report_20250924_203148.md
[2025-09-24 20:35:03]    - HTML报告: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models\training_reports\DeepLabV3Plus_20250924_203148\training_report_20250924_203148.html
[2025-09-24 20:35:03]    - 训练曲线: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models\training_reports\DeepLabV3Plus_20250924_203148\training_curves_20250924_203148.png
[2025-09-24 20:35:03]    - 训练历史: D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models\training_reports\DeepLabV3Plus_20250924_203148\training_history_20250924_203148.json
[2025-09-24 20:35:03] === DeepLabV3+ 训练完成 ===
