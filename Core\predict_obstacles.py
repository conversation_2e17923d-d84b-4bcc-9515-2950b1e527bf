import os
import numpy as np
import rasterio
from rasterio.merge import merge
from rasterio.transform import Affine
from tensorflow.keras.models import load_model
import tensorflow as tf

# 定义自定义损失函数和指标（与训练时保持一致）
def dice_loss(y_true, y_pred, smooth=1e-5):
    """Dice损失函数"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)
    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f)
    dice_coef = (2. * intersection + smooth) / (union + smooth)
    dice_coef = tf.clip_by_value(dice_coef, 1e-7, 1.0 - 1e-7)
    return tf.cast(1 - dice_coef, tf.float32)

def focal_loss(gamma=2., alpha=.25):
    """Focal损失函数"""
    def focal_loss_fixed(y_true, y_pred):
        epsilon = tf.keras.backend.epsilon()
        y_pred = tf.clip_by_value(y_pred, epsilon, 1. - epsilon)
        p_t = tf.where(tf.equal(y_true, 1), y_pred, 1 - y_pred)
        alpha_factor = tf.ones_like(y_true) * alpha
        alpha_t = tf.where(tf.equal(y_true, 1), alpha_factor, 1 - alpha_factor)
        cross_entropy = -tf.math.log(p_t)
        weight = alpha_t * tf.pow((1 - p_t), gamma)
        focal_loss_val = weight * cross_entropy
        focal_loss_val = tf.reduce_mean(tf.reduce_sum(focal_loss_val, axis=-1))
        focal_loss_val = tf.where(tf.math.is_finite(focal_loss_val), focal_loss_val, tf.constant(0.0))
        return tf.cast(focal_loss_val, tf.float32)
    return focal_loss_fixed

def combined_loss(y_true, y_pred):
    """组合损失函数"""
    dice = dice_loss(y_true, y_pred)
    focal = focal_loss(gamma=2., alpha=.25)(y_true, y_pred)
    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))
    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))
    combined = dice + focal
    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))
    return tf.cast(combined, tf.float32)

def iou_coef(y_true, y_pred, smooth=1e-5):
    """IoU系数"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)
    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection
    iou = (intersection + smooth) / (union + smooth)
    iou = tf.clip_by_value(iou, 1e-7, 1.0 - 1e-7)
    return tf.cast(iou, tf.float32)
from tqdm import tqdm
import matplotlib.pyplot as plt
import shutil
from scipy import ndimage
from shapely.geometry import Polygon, MultiPolygon
import geopandas as gpd

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

# 设置路径 - 修改为相对路径，避免硬编码
MODEL_PATH = r'trained_models/unet_model.h5'  # 默认模型路径
INPUT_IMAGE_PATH = r'dataset/image/test.tif'  # 默认输入图像路径
OUTPUT_DIR = r'prediction_patches'
MERGED_OUTPUT_PATH = r'Shape/Output/merged_prediction.tif'
POLYGON_OUTPUT_PATH = r'Shape/Output/obstacles_polygons.shp'
 
# 默认分块大小和步长
DEFAULT_PATCH_SIZE = 256
DEFAULT_STRIDE = 128
BATCH_SIZE = 16

def preprocess_image(image):
    """
    预处理图像 - 使用与训练时相同的MinMaxScaler方法
    """
    from sklearn.preprocessing import MinMaxScaler

    # 使用与训练时相同的预处理方法
    minmaxscaler = MinMaxScaler()
    original_shape = image.shape
    image_reshaped = image.reshape(-1, image.shape[-1])
    image_normalized = minmaxscaler.fit_transform(image_reshaped)
    return image_normalized.reshape(original_shape)

def get_model_input_size(model):
    """获取模型的输入尺寸"""
    try:
        # 对于TensorFlow/Keras模型
        if hasattr(model, 'input_shape'):
            input_shape = model.input_shape
            if isinstance(input_shape, list):
                input_shape = input_shape[0]  # 如果有多个输入，取第一个

            # input_shape格式通常是 (None, height, width, channels)
            if len(input_shape) >= 3:
                height, width = input_shape[1], input_shape[2]
                if height is not None and width is not None:
                    return height, width

        # 如果无法获取，返回默认值
        print("警告：无法获取模型输入尺寸，使用默认值256x256")
        return DEFAULT_PATCH_SIZE, DEFAULT_PATCH_SIZE

    except Exception as e:
        print(f"获取模型输入尺寸时出错: {str(e)}，使用默认值256x256")
        return DEFAULT_PATCH_SIZE, DEFAULT_PATCH_SIZE

def create_patches(image, patch_size=DEFAULT_PATCH_SIZE, stride=DEFAULT_STRIDE):
    height, width, _ = image.shape
    for y in range(0, height - patch_size + 1, stride):
        for x in range(0, width - patch_size + 1, stride):
            yield (image[y:y+patch_size, x:x+patch_size, :3], (y, x))

def predict_and_save_patches(model, image, output_dir, src_transform, src_crs):
    # 动态获取模型输入尺寸
    patch_height, patch_width = get_model_input_size(model)
    patch_size = patch_height  # 假设是正方形
    stride = patch_size // 2  # 步长设为patch大小的一半

    print(f"模型输入尺寸: {patch_height}x{patch_width}")
    print(f"使用patch大小: {patch_size}x{patch_size}, 步长: {stride}")

    patches_generator = create_patches(image, patch_size=patch_size, stride=stride)
    total_patches = sum(1 for _ in create_patches(image, patch_size=patch_size, stride=stride))

    print("开始分块预测...")
    progress_bar = tqdm(total=total_patches, desc="预测进度")

    batch_patches = []
    batch_positions = []

    for i, (patch, position) in enumerate(patches_generator):
        batch_patches.append(patch)
        batch_positions.append(position)

        if len(batch_patches) == BATCH_SIZE or (i + 1) == total_patches:
            predictions = model.predict(np.array(batch_patches), batch_size=BATCH_SIZE, verbose=0)

            for j, (pred, (y, x)) in enumerate(zip(predictions, batch_positions)):
                output_file = os.path.join(output_dir, f'pred_{y}_{x}.tif')
                patch_transform = Affine(src_transform.a, src_transform.b, src_transform.c + src_transform.a * x,
                                         src_transform.d, src_transform.e, src_transform.f + src_transform.e * y)

                with rasterio.open(output_file, 'w', driver='GTiff',
                                   height=patch_size, width=patch_size,
                                   count=1, dtype=rasterio.float32,
                                   crs=src_crs, transform=patch_transform) as dst:
                    dst.write(pred[:,:,1], 1)

            progress_bar.update(len(batch_patches))
            batch_patches = []
            batch_positions = []

    progress_bar.close()

def merge_predictions(input_dir, output_file, original_shape, src_transform, src_crs):
    prediction_files = [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f.endswith('.tif')]
    
    print(f"找到 {len(prediction_files)} 个预测文件")
    print("开始合并预测结果...")
    
    merged_array = np.zeros(original_shape, dtype=np.float32)
    
    for f in tqdm(prediction_files, desc="合并进度"):
        filename = os.path.basename(f)
        parts = filename.split('_')
        y = int(parts[1])
        x = int(parts[2].split('.')[0])
        
        with rasterio.open(f) as src:
            prediction = src.read(1)
            height, width = prediction.shape
            
            row_end = min(y + height, original_shape[0])
            col_end = min(x + width, original_shape[1])
            merged_array[y:row_end, x:col_end] = prediction[:row_end-y, :col_end-x]
    
    with rasterio.open(output_file, "w", driver="GTiff",
                       height=original_shape[0], width=original_shape[1],
                       count=1, dtype=rasterio.float32,
                       crs=src_crs, transform=src_transform) as dest:
        dest.write(merged_array, 1)
    
    print(f"合并预测结果保存到 {output_file}")

from skimage.measure import label, regionprops
from shapely.geometry import Polygon
import geopandas as gpd

def mask_to_polygons(mask, transform, min_area, simplify_tolerance, min_hole_area):
    """将掩码转换为多边形"""
    label_image = label(mask)
    regions = regionprops(label_image)

    polygons = []
    for region in regions:
        if region.area >= min_area:
            # 获取区域的多边形坐标
            coords = region.coords
            poly = Polygon([transform * (c[1], c[0]) for c in coords])
            
            # 简化多边形
            poly = poly.simplify(simplify_tolerance, preserve_topology=True)
            
            # 处理空洞
            if poly.interiors:
                poly = Polygon(poly.exterior, 
                               [i for i in poly.interiors if Polygon(i).area >= min_hole_area])
            
            polygons.append(poly)

    return polygons

def raster_to_polygon(raster_path, output_path, threshold=0.2, min_area=10, simplify_tolerance=1.0, min_hole_area=10):
    print("\n开始将栅格转换为多边形...")
    
    with rasterio.open(raster_path) as src:
        mask = src.read(1)  # 读取第一个波段
        transform = src.transform
    
    # 应用阈值
    binary_mask = (mask > threshold).astype(np.uint8)
    
    # 使用形态学操作填充小空洞和平滑边缘
    binary_mask = ndimage.binary_closing(binary_mask, structure=np.ones((3,3)), iterations=2)
    binary_mask = ndimage.binary_opening(binary_mask, structure=np.ones((3,3)), iterations=1)
    
    # 填充内部空洞
    binary_mask = ndimage.binary_fill_holes(binary_mask)
    
    # 将掩码转换为多边形
    polygons = mask_to_polygons(binary_mask, transform, min_area, simplify_tolerance, min_hole_area)
    
    # 将多边形保存为 GeoDataFrame
    gdf = gpd.GeoDataFrame(geometry=polygons, crs=src.crs)
    
    # 保存为 shapefile
    gdf.to_file(output_path)
    
    print(f"生成的多边形数量: {len(polygons)}")
    print(f"多边形结果已保存到 {output_path}")

def main(model_path=None, input_image_path=None, output_dir=None):
    """
    主函数 - 支持自定义路径参数
    """
    # 使用传入的参数或默认值
    model_path = model_path or MODEL_PATH
    input_image_path = input_image_path or INPUT_IMAGE_PATH
    output_dir = output_dir or OUTPUT_DIR

    print("开始执行主函数...")
    print(f"模型路径: {model_path}")
    print(f"输入图像路径: {input_image_path}")
    print(f"输出目录: {output_dir}")

    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在: {model_path}")
        return False

    if not os.path.exists(input_image_path):
        print(f"错误：输入图像文件不存在: {input_image_path}")
        return False

    # 清空并重新创建 OUTPUT_DIR
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    print(f"已清空并重新创建目录: {output_dir}")

    # 读取输入图像
    with rasterio.open(input_image_path) as src:
        image = src.read().transpose((1, 2, 0))
        src_transform = src.transform
        src_crs = src.crs
        print(f"图像形状: {image.shape}")
        print(f"图像数据类型: {image.dtype}")
        print(f"图像值范围: {image.min()} - {image.max()}")

    # 确保图像是3波段
    if image.shape[2] > 3:
        image = image[:, :, :3]
        print(f"转换为3波段，新形状: {image.shape}")

    # 预处理图像
    print("开始预处理图像...")
    image = preprocess_image(image)
    print(f"预处理后图像值范围: {image.min()} - {image.max()}")

    # 加载模型
    print(f"加载模型: {model_path}")

    # 定义自定义对象字典
    custom_objects = {
        'combined_loss': combined_loss,
        'dice_loss': dice_loss,
        'focal_loss': focal_loss,
        'iou_coef': iou_coef
    }

    try:
        model = load_model(model_path, custom_objects=custom_objects, compile=False)
        print("✅ 成功加载UNet模型（包含自定义损失函数）")
    except Exception as e:
        print(f"⚠️ 使用自定义对象加载失败，尝试不编译加载: {e}")
        try:
            model = load_model(model_path, compile=False)
            print("✅ 成功加载UNet模型（不编译）")
        except Exception as e2:
            print(f"❌ 模型加载失败: {e2}")
            return False

    print(f"模型输入形状: {model.input_shape}")
    print(f"模型输出形状: {model.output_shape}")

    # 预测并保存结果
    predict_and_save_patches(model, image, output_dir, src_transform, src_crs)

    # 合并预测结果
    merged_output_path = os.path.join(os.path.dirname(output_dir), 'merged_prediction.tif')
    merge_predictions(output_dir, merged_output_path, image.shape[:2], src_transform, src_crs)

    # 将预测结果转换为多边形
    polygon_output_path = os.path.join(os.path.dirname(output_dir), 'obstacles_polygons.shp')
    raster_to_polygon(merged_output_path, polygon_output_path, threshold=0.2, min_area=10, simplify_tolerance=1.0, min_hole_area=10)

    print("\n主函数执行完毕")
    return True

if __name__ == "__main__":
    main()