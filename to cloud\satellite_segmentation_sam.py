import os
import math
import numpy as np
import rasterio
import mercantile
import requests
import geopandas as gpd
from PIL import Image
from io import BytesIO
from shapely.geometry import Polygon
import torch
from segment_anything import sam_model_registry, SamPredictor
import cv2

class CoordinateReader:
    """坐标读取器"""
    def __init__(self, coordinate_file):
        """初始化坐标读取器
        
        Args:
            coordinate_file: 坐标文件路径
        """
        self.coordinate_file = coordinate_file
    
    def read_coordinates(self):
        """读取坐标文件
        
        Returns:
            tuple: (min_lon, min_lat, max_lon, max_lat)
        """
        try:
            with open(self.coordinate_file, 'r') as f:
                # 假设文件格式为：min_lon,min_lat,max_lon,max_lat
                coords = f.read().strip().split(',')
                return tuple(map(float, coords))
        except Exception as e:
            print(f"读取坐标文件失败: {e}")
            return None

class SatelliteDownloader:
    """卫星图像下载器"""
    def __init__(self, api_key=None):
        """初始化下载器
        
        Args:
            api_key: Google Maps API密钥（可选）
        """
        self.api_key = api_key
        self.zoom = 18  # 缩放级别
        self.tile_size = 256
    
    def download_satellite_image(self, bounds):
        """下载卫星图像
        
        Args:
            bounds: 边界坐标 (min_lon, min_lat, max_lon, max_lat)
        
        Returns:
            numpy.ndarray: 拼接后的图像
        """
        try:
            print("\n=== 下载卫星图像 ===")
            min_lon, min_lat, max_lon, max_lat = bounds
            
            # 获取瓦片范围
            min_tile = mercantile.tile(min_lon, max_lat, self.zoom)
            max_tile = mercantile.tile(max_lon, min_lat, self.zoom)
            
            # 计算需要下载的瓦片数量
            tiles_x = max_tile.x - min_tile.x + 1
            tiles_y = max_tile.y - min_tile.y + 1
            
            print(f"瓦片范围: {tiles_x}x{tiles_y}")
            
            # 创建空白图像
            total_width = int(tiles_x * self.tile_size)
            total_height = int(tiles_y * self.tile_size)
            mosaic = np.zeros((total_height, total_width, 3), dtype=np.uint8)
            
            # 下载并拼接瓦片
            for i in range(int(tiles_x)):
                for j in range(int(tiles_y)):
                    x = min_tile.x + i
                    y = min_tile.y + j
                    
                    # 构建URL
                    url = f"http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={self.zoom}"
                    if self.api_key:
                        url += f"&key={self.api_key}"
                    
                    try:
                        response = requests.get(url)
                        img = Image.open(BytesIO(response.content))
                        img_array = np.array(img)
                        
                        # 将瓦片放入正确位置
                        y_start = j * self.tile_size
                        y_end = (j + 1) * self.tile_size
                        x_start = i * self.tile_size
                        x_end = (i + 1) * self.tile_size
                        mosaic[y_start:y_end, x_start:x_end] = img_array
                        
                    except Exception as e:
                        print(f"下载瓦片失败 ({x}, {y}): {e}")
            
            # 保存拼接后的图像
            with rasterio.open(
                'raw.tif',
                'w',
                driver='GTiff',
                height=total_height,
                width=total_width,
                count=3,
                dtype=mosaic.dtype,
                crs='EPSG:4326',  # WGS84
                transform=rasterio.transform.from_bounds(
                    south=bounds[1],  # 最小纬度
                    west=bounds[0],   # 最小经度
                    north=bounds[3],  # 最大纬度
                    east=bounds[2],   # 最大经度
                    width=total_width,
                    height=total_height
                )
            ) as dst:
                # 写入RGB通道
                for i in range(3):
                    dst.write(mosaic[:, :, i], i + 1)
            
            print("卫星图像下载完成")
            return mosaic
            
        except Exception as e:
            print(f"下载卫星图像失败: {e}")
            return None

class ImageProcessor:
    """图像处理器"""
    def __init__(self):
        """初始化图像处理器"""
        pass
    
    def process_image(self, image):
        """处理图像
        
        Args:
            image: 输入图像
        
        Returns:
            numpy.ndarray: 处理后的图像
        """
        try:
            print("\n=== 处理图像 ===")
            # 转换为RGB格式
            if len(image.shape) == 2:
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            elif image.shape[2] == 4:
                image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
            
            return image
            
        except Exception as e:
            print(f"处理图像失败: {e}")
            return None

class ModelInference:
    """模型推理"""
    def __init__(self, model_type="vit_h"):
        """初始化模型
        
        Args:
            model_type: SAM模型类型，可选 'vit_h', 'vit_l', 'vit_b'
        """
        try:
            print("\n=== 初始化SAM模型 ===")
            
            # 检查模型文件是否存在
            model_path = f"sam_{model_type}.pth"
            if not os.path.exists(model_path):
                print(f"请下载SAM模型文件并保存为 {model_path}")
                return
            
            # 初始化SAM模型
            device = "cuda" if torch.cuda.is_available() else "cpu"
            sam = sam_model_registry[model_type](checkpoint=model_path)
            sam.to(device=device)
            
            self.predictor = SamPredictor(sam)
            print(f"SAM模型已加载 (设备: {device})")
            
        except Exception as e:
            print(f"初始化模型失败: {e}")
    
    def predict(self, image):
        """执行图像分割
        
        Args:
            image: 输入图像
        
        Returns:
            numpy.ndarray: 分割掩码
        """
        try:
            print("\n=== 执行图像分割 ===")
            
            # 设置图像
            self.predictor.set_image(image)
            
            # 生成图像中心点作为提示点
            h, w = image.shape[:2]
            input_point = np.array([[w//2, h//2]])
            input_label = np.array([1])
            
            # 执行分割
            masks, scores, logits = self.predictor.predict(
                point_coords=input_point,
                point_labels=input_label,
                multimask_output=True
            )
            
            # 选择最佳掩码
            best_mask = masks[np.argmax(scores)]
            
            # 将掩码转换为分类结果
            # 这里我们将掩码转换为与原始代码相同的类别格式
            result = np.zeros_like(best_mask, dtype=np.uint8)
            result[best_mask] = 1  # 1表示建筑物
            
            print("图像分割完成")
            return result
            
        except Exception as e:
            print(f"执行分割失败: {e}")
            return None

class ResultProcessor:
    """结果处理器"""
    def __init__(self):
        """初始化结果处理器"""
        self.class_mapping = {
            1: "建筑",
            2: "道路",
            3: "植被",
            4: "水体"
        }
    
    def mask_to_shapefile(self, mask, transform, output_path="prediction.shp"):
        """将预测掩码转换为shapefile
        
        Args:
            mask: 预测掩码
            transform: 地理变换矩阵
            output_path: 输出shapefile路径
        """
        try:
            print("\n=== 转换预测结果为Shapefile ===")
            print("变换矩阵:", transform)
            
            # 创建GeoDataFrame
            features = []
            for class_id in np.unique(mask):
                if class_id == 0:  # 跳过背景
                    continue
                    
                print(f"\n处理类别 {class_id} ({self.class_mapping.get(int(class_id), '未知')})")
                
                # 获取当前类别的掩码
                class_mask = mask == class_id
                
                # 提取轮廓
                shapes = rasterio.features.shapes(
                    class_mask.astype(np.uint8),
                    mask=class_mask,
                    transform=transform
                )
                
                # 转换为GeoJSON特征
                for geom, _ in shapes:
                    print("\n原始几何形状的第一个坐标:", geom['coordinates'][0][0])
                    
                    # 直接使用原始几何形状
                    new_geom = geom
                    
                    # 添加属性信息
                    feature = {
                        'geometry': new_geom,
                        'properties': {
                            'class_id': int(class_id),
                            'class_name': self.class_mapping.get(int(class_id), "未知")
                        }
                    }
                    features.append(feature)
                    
                    # 输出调试信息
                    coords = new_geom['coordinates'][0]
                    print("\n多边形坐标:")
                    for i, coord in enumerate(coords[:4]):  # 只显示前4个点
                        print(f"点{i+1}: ({coord[0]}, {coord[1]})")
            
            if not features:
                print("警告: 未找到任何有效地物")
                return False
            
            # 创建GeoDataFrame
            gdf = gpd.GeoDataFrame.from_features(features)
            
            # 确保使用正确的坐标系统
            gdf.crs = 'EPSG:4326'
            
            # 输出更多调试信息
            print("\n第一个要素的信息:")
            first_geom = gdf.geometry.iloc[0]
            print(f"类型: {first_geom.geom_type}")
            print(f"坐标系统: {gdf.crs}")
            print(f"边界框: {first_geom.bounds}")
            print(f"第一个坐标: {list(first_geom.exterior.coords)[0]}")
            print(f"最后一个坐标: {list(first_geom.exterior.coords)[-1]}")
            
            # 保存为shapefile
            gdf.to_file(output_path)
            
            # 输出统计信息
            print("\n地物统计:")
            for class_id, group in gdf.groupby('class_id'):
                class_name = self.class_mapping.get(int(class_id), "未知")
                feature_count = len(group)
                print(f"类别 {class_id} ({class_name}):")
                print(f"  - 要素数量: {feature_count}")
            
            print(f"\nShapefile已保存至: {output_path}")
            return True
            
        except Exception as e:
            print(f"转换Shapefile失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    try:
        # 初始化各个组件
        coord_reader = CoordinateReader("coordinates.txt")
        downloader = SatelliteDownloader()
        processor = ImageProcessor()
        model = ModelInference()
        result_processor = ResultProcessor()
        
        # 读取坐标
        bounds = coord_reader.read_coordinates()
        if not bounds:
            return
        
        # 下载卫星图像
        image = downloader.download_satellite_image(bounds)
        if image is None:
            return
        
        # 处理图像
        processed_image = processor.process_image(image)
        if processed_image is None:
            return
        
        # 执行分割
        mask = model.predict(processed_image)
        if mask is None:
            return
        
        # 获取变换矩阵
        with rasterio.open('raw.tif') as src:
            transform = src.transform
        
        # 转换结果为shapefile
        result_processor.mask_to_shapefile(mask, transform)
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
