#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 UNet云端训练快速启动示例

这个脚本展示了如何使用 unet_training_cloud.py 进行初训和增量训练
"""

# 导入主训练脚本
exec(open('unet_training_cloud.py').read())

def example_initial_training():
    """示例：初始训练（推荐用于您的情况）"""
    print("🚀 示例：初始训练配置")
    print("=" * 50)
    
    # 1. 设置混合数据集配置
    quick_setup_for_datasets('mixed')
    
    # 2. 设置初始训练参数
    setup_initial_training(
        batch_size=6,                    # 适合云端GPU
        epochs=25,                       # 充分训练
        learning_rate=5e-5,              # 保守学习率
        model_save_name='unet_model_final.h5',
        early_stopping_patience=10,      # 早停耐心
        lr_reduce_patience=5             # 学习率衰减耐心
    )
    
    # 3. 更新数据路径（需要根据实际情况修改）
    print("\n⚠️  请根据您的实际情况修改以下路径：")
    print("update_paths(")
    print("    image_folder='/path/to/combined/images',")
    print("    mask_folder='/path/to/combined/masks',")
    print("    output_folder='/path/to/output'")
    print(")")
    
    # 4. 显示当前配置
    print("\n📋 当前训练配置：")
    for key, value in TRAINING_CONFIG.items():
        if key in ['training_mode', 'batch_size', 'epochs', 'learning_rate', 'model_save_name']:
            print(f"  {key}: {value}")
    
    print("\n✅ 配置完成！运行 main() 开始训练")

def example_incremental_training():
    """示例：增量训练"""
    print("🔄 示例：增量训练配置")
    print("=" * 50)
    
    # 1. 设置混合数据集配置
    quick_setup_for_datasets('mixed')
    
    # 2. 设置增量训练参数
    setup_incremental_training(
        pretrained_model_path='/path/to/unet_model_v3.h5',  # 需要修改为实际路径
        batch_size=6,
        epochs=15,
        incremental_learning_rate=1e-5,   # 更小的学习率
        model_save_name='unet_model_v4.h5',
        create_backup=True,               # 创建备份
        early_stopping_patience=8
    )
    
    # 3. 更新数据路径
    print("\n⚠️  请根据您的实际情况修改以下路径：")
    print("update_paths(")
    print("    image_folder='/path/to/mixed/images',")
    print("    mask_folder='/path/to/mixed/masks',")
    print("    output_folder='/path/to/output'")
    print(")")
    
    # 4. 显示当前配置
    print("\n📋 当前训练配置：")
    for key, value in TRAINING_CONFIG.items():
        if key in ['training_mode', 'pretrained_model_path', 'batch_size', 'epochs', 'incremental_learning_rate']:
            print(f"  {key}: {value}")
    
    print("\n✅ 配置完成！运行 main() 开始训练")

def recommended_for_your_case():
    """针对您情况的推荐配置"""
    print("🎯 针对您情况的推荐配置")
    print("=" * 50)
    print("基于您的训练历史：")
    print("  第一次：数据集1，IoU=93.02%（优秀）")
    print("  第二次：数据集2，13轮中断（未完成）")
    print("  第三次：数据集3，效果不佳")
    print("\n推荐策略：从第一次优秀模型重新开始，使用混合数据集")
    
    # 推荐配置
    quick_setup_for_datasets('mixed')
    
    setup_initial_training(
        batch_size=6,                    # 云端GPU友好
        epochs=25,                       # 充分训练
        learning_rate=5e-5,              # 保守但有效的学习率
        model_save_name='unet_model_final_mixed.h5',
        early_stopping_patience=10,
        lr_reduce_patience=5
    )
    
    print("\n📋 推荐配置已设置：")
    print(f"  训练模式: {TRAINING_CONFIG['training_mode']}")
    print(f"  批次大小: {TRAINING_CONFIG['batch_size']}")
    print(f"  训练轮数: {TRAINING_CONFIG['epochs']}")
    print(f"  学习率: {TRAINING_CONFIG['learning_rate']}")
    print(f"  混合精度: {TRAINING_CONFIG['enable_mixed_precision']}")
    
    print("\n🎯 预期效果：IoU ≥ 93.5%（超过第一次训练）")
    print("\n⚠️  接下来需要：")
    print("1. 合并三个数据集到同一目录")
    print("2. 使用 update_paths() 设置正确的路径")
    print("3. 运行 main() 开始训练")

def show_all_options():
    """显示所有可用的配置选项"""
    print("📋 所有可用的配置选项")
    print("=" * 50)
    
    print("🔧 快速数据集配置：")
    print("  quick_setup_for_datasets('single')   # 单数据集")
    print("  quick_setup_for_datasets('mixed')    # 混合数据集（推荐）")
    print("  quick_setup_for_datasets('large')    # 大数据集")
    
    print("\n🚀 初始训练设置：")
    print("  setup_initial_training(")
    print("      batch_size=8,")
    print("      epochs=50,")
    print("      learning_rate=1e-4,")
    print("      model_save_name='model.h5'")
    print("  )")
    
    print("\n🔄 增量训练设置：")
    print("  setup_incremental_training(")
    print("      pretrained_model_path='/path/to/model.h5',")
    print("      batch_size=6,")
    print("      epochs=25,")
    print("      incremental_learning_rate=2e-5")
    print("  )")
    
    print("\n📁 路径设置：")
    print("  update_paths(")
    print("      image_folder='/path/to/images',")
    print("      mask_folder='/path/to/masks',")
    print("      output_folder='/path/to/output'")
    print("  )")
    
    print("\n🎯 开始训练：")
    print("  main()")

if __name__ == "__main__":
    print("🛰️ UNet云端训练快速启动示例")
    print("=" * 60)
    
    print("\n选择一个示例运行：")
    print("1. example_initial_training()      # 初始训练示例")
    print("2. example_incremental_training()  # 增量训练示例")
    print("3. recommended_for_your_case()     # 针对您情况的推荐配置")
    print("4. show_all_options()              # 显示所有配置选项")
    
    print("\n🎯 推荐：直接运行 recommended_for_your_case()")
    
    # 自动运行推荐配置
    recommended_for_your_case()