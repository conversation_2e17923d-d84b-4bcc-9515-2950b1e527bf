# 数据集下载和管理工具使用说明

## 概述

本工具集提供了基于Shape文件的数据集下载和管理功能，解决了使用GEE数据集训练和预测不一致的问题。通过使用已有的建筑物Shape文件，可以生成高质量的image+mask训练数据集。

## 功能特点

### 🎯 核心优势
- **统一数据源**：使用相同的卫星图像源进行训练和推理
- **精确标注**：基于真实的建筑物Shape文件生成mask
- **自动化流程**：一键完成从Shape文件到训练数据集的转换
- **质量保证**：内置数据验证和统计分析功能

### 📋 主要功能
1. **Shape文件范围下载**：根据Shape文件中的多边形范围下载卫星图像
2. **自动mask生成**：将Shape文件转换为训练用的mask文件
3. **数据集管理**：统一管理、验证和分析数据集
4. **质量控制**：自动验证数据完整性和配对关系

## 菜单结构

### 工具菜单
```
工具
├── 数据集下载
│   ├── 选择区域下载数据集    # 原有功能，从模型训练菜单移动
│   ├── 生成数据集           # 原有功能，从模型训练菜单移动
│   ├── ─────────────────
│   └── Shape文件范围下载    # 新功能
├── ─────────────────────
└── 数据集管理              # 新功能
```

## 使用流程

### 方案一：Shape文件边界下载（推荐）

基于Shape文件的整体边界范围，下载一张大图像，然后生成配对的image+mask训练切片。确保数据一致性和质量。

#### 主要特点
- 🎯 **边界下载**：根据Shape文件整体边界下载大图像
- 🖼️ **完美配对**：确保image和mask完全匹配，无时间差异
- 📊 **训练友好**：生成大图像，由训练代码自动切片，避免重复操作
- 🔄 **格式标准**：输出标准的GeoTIFF格式，带地理参考
- 👁️ **可视化支持**：自动生成可视化mask文件，方便用户查看

#### 1. 准备Shape文件
- 确保Shape文件包含建筑物或障碍物的多边形
- 坐标系统建议使用WGS84 (EPSG:4326)
- 文件完整性：确保.shp、.shx、.dbf等文件齐全

#### 2. 启动下载工具
1. 打开主程序
2. 点击菜单：**工具 → 数据集下载 → Shape文件范围下载**
3. 在对话框中配置参数

#### 3. 配置下载参数

**基本设置**：
- **Shape文件**：选择包含建筑物的Shape文件
- **输出目录**：设置数据集保存位置（默认：dataset文件夹）
- **数据源**：推荐Google卫星图像

**高级设置**：
- **自动验证**：下载完成后自动验证数据集
- **生成报告**：创建详细的统计报告
- **下载延迟**：控制下载速度（避免被限制）

#### 4. 开始下载
1. 点击"开始下载"按钮
2. 监控下载进度和日志
3. 等待下载完成

#### 5. 验证结果
下载完成后，会在输出目录生成：
```
dataset/
├── image/                    # 图像文件
│   └── satellite_image_xxx.tif
└── mask/                     # mask文件
    ├── satellite_mask_xxx.tif
    └── viewable_mask/        # 可视化mask文件
        ├── satellite_mask_xxx_gray.png      # 灰度版本
        ├── satellite_mask_xxx_colored.png   # 彩色版本
        └── satellite_mask_xxx_comparison.png # 对比图
```

**文件说明**：
- **图像文件**：可直接用于训练，训练代码会自动切片
- **mask文件**：与图像完全匹配的标注文件
- **可视化mask**：方便用户查看标注效果的PNG文件

### 方案二：数据集管理

#### 1. 打开数据集管理
- 点击菜单：**工具 → 数据集管理**

#### 2. 扫描现有数据集
1. 选择数据集目录
2. 点击"扫描数据集"
3. 查看统计信息和验证结果

#### 3. 管理功能
- **数据集信息**：查看基本信息和统计数据
- **文件列表**：浏览所有图像和mask文件
- **下载管理**：启动新的下载任务
- **导出报告**：生成详细的数据集报告

## 配置说明

### 瓦片大小选择
- **256像素**：适合小目标检测，下载速度快
- **512像素**：适合大目标检测，图像质量高
- **1024像素**：适合高精度应用，文件较大

### 瓦片源选择
- **Google**：图像质量高，更新频率快（推荐）
- **Bing**：覆盖范围广，需要API密钥
- **OpenStreetMap**：开源免费，图像质量一般

### 下载控制
- **最大瓦片数**：建议设置为50-200，避免单个区域下载过多
- **下载延迟**：建议100-500ms，避免被服务器限制
- **批量处理**：大范围区域建议分批下载

## 数据质量保证

### 自动验证
- **文件完整性**：检查图像和mask文件是否损坏
- **配对关系**：验证每个图像都有对应的mask
- **尺寸一致性**：确保图像和mask尺寸匹配
- **数据分布**：分析前景背景比例

### 质量指标
- **配对率**：image-mask配对的百分比
- **前景比例**：障碍物像素占总像素的比例
- **文件大小**：平均文件大小统计
- **坐标范围**：数据集的地理覆盖范围

## 常见问题

### Q1: 下载速度很慢怎么办？
**A**: 
- 增加下载延迟时间
- 选择不同的瓦片源
- 减少最大瓦片数限制
- 检查网络连接

### Q2: 生成的mask文件全是黑色？
**A**: 
- 检查Shape文件是否包含有效的多边形
- 确认坐标系统是否正确
- 验证Shape文件与卫星图像的地理范围是否重叠

### Q3: 训练时提示数据集格式错误？
**A**: 
- 使用数据集管理工具验证数据集
- 检查image和mask文件夹结构
- 确认文件命名是否一致

### Q4: 如何提高标注质量？
**A**: 
- 使用高质量的Shape文件
- 选择合适的瓦片大小
- 验证Shape文件的几何精度
- 定期更新Shape文件数据

## 技术支持

### 文件结构
```
tools/
├── shape_based_dataset_downloader.py  # 核心下载功能
├── dataset_manager.py                 # 数据集管理功能
└── dataset_tools_usage.md            # 使用说明

GUI/
├── ShapeBasedDownloadDialog.py       # 下载对话框
└── DatasetManagementDialog.py        # 管理对话框
```

### 依赖库
- `geopandas`: Shape文件处理
- `rasterio`: 地理图像处理
- `requests`: 网络下载
- `mercantile`: 瓦片坐标转换
- `shapely`: 几何计算
- `numpy`: 数值计算
- `opencv-python`: 图像处理

### 日志和调试
- 下载过程中的详细日志会显示在界面中
- 错误信息会保存在数据集的metadata文件夹中
- 可以通过数据集管理工具查看历史记录

## 最佳实践

1. **数据准备**：使用高质量、最新的建筑物Shape文件
2. **参数设置**：根据应用场景选择合适的瓦片大小
3. **质量控制**：每次下载后都进行数据验证
4. **版本管理**：为不同版本的数据集创建不同的文件夹
5. **备份策略**：定期备份重要的数据集和配置文件

通过这套工具，你可以轻松地从Shape文件生成高质量的训练数据集，解决GEE数据集的不一致问题，提高模型的训练效果和实际应用性能。
