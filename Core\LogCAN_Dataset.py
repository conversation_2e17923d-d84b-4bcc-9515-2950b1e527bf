"""
LogCAN数据集处理模块
支持image+shape和image+mask两种数据模式
包括数据加载、预处理、增强等功能
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2
from sklearn.model_selection import train_test_split
import warnings

warnings.filterwarnings("ignore")

class LogCANDataProcessor:
    """LogCAN数据处理器"""
    
    def __init__(self, config):
        self.config = config
        self.slice_size = config.data_config['slice_size']
        self.overlap = config.data_config['overlap']
        self.min_foreground_ratio = config.data_config['min_foreground_ratio']
        self.use_augmentation = config.data_config['use_augmentation']
        
        # 设置数据增强
        self.train_transform = self._get_train_transforms()
        self.val_transform = self._get_val_transforms()
    
    def _get_train_transforms(self):
        """获取训练时的数据增强"""
        if not self.use_augmentation:
            return A.Compose([
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
        
        return A.Compose([
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.5),
            A.RandomRotate90(p=0.5),
            A.OneOf([
                A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=1.0),
                A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=1.0),
            ], p=0.3),
            A.OneOf([
                A.GaussNoise(var_limit=(10.0, 50.0), p=1.0),
                A.GaussianBlur(blur_limit=3, p=1.0),
            ], p=0.2),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    
    def _get_val_transforms(self):
        """获取验证时的数据变换"""
        return A.Compose([
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    
    def load_image_shape_data(self, image_path, shape_path):
        """加载image+shape模式的数据"""
        print(f"加载图像: {image_path}")
        print(f"加载形状文件: {shape_path}")
        
        # 加载图像
        with rasterio.open(image_path) as src:
            image = src.read().transpose((1, 2, 0))
            transform = src.transform
            crs = src.crs
            
            # 获取图像信息
            print(f"图像尺寸: {image.shape}")
            print(f"图像数据类型: {image.dtype}")
        
        # 确保图像是3通道
        if image.shape[2] == 1:
            image = np.repeat(image, 3, axis=2)
        elif image.shape[2] > 3:
            image = image[:, :, :3]
        
        # 归一化到0-255
        if image.dtype != np.uint8:
            image = self._normalize_image(image)
        
        # 加载形状文件并生成掩码
        gdf = gpd.read_file(shape_path)
        print(f"形状文件包含 {len(gdf)} 个几何体")
        
        # 确保CRS匹配
        if gdf.crs != crs:
            print(f"转换CRS: {gdf.crs} -> {crs}")
            gdf = gdf.to_crs(crs)
        
        # 处理多分类情况
        num_classes = self.config.model_config['num_classes']
        if num_classes > 2:
            mask = self._generate_multiclass_mask(gdf, image.shape[:2], transform, num_classes)
        else:
            mask = self._generate_binary_mask(gdf, image.shape[:2], transform)
        
        print(f"掩码尺寸: {mask.shape}")
        print(f"掩码唯一值: {np.unique(mask)}")
        
        return image, mask
    
    def _normalize_image(self, image):
        """归一化图像到0-255"""
        # 处理每个通道
        normalized = np.zeros_like(image, dtype=np.uint8)
        for i in range(image.shape[2]):
            channel = image[:, :, i]
            if channel.max() > channel.min():
                channel_norm = ((channel - channel.min()) / (channel.max() - channel.min()) * 255)
                normalized[:, :, i] = channel_norm.astype(np.uint8)
            else:
                normalized[:, :, i] = 0
        return normalized
    
    def _generate_binary_mask(self, gdf, shape, transform):
        """生成二值掩码"""
        mask = rasterize(
            gdf.geometry,
            out_shape=shape,
            transform=transform,
            fill=0,
            default_value=1,
            dtype=np.uint8
        )
        return mask
    
    def _generate_multiclass_mask(self, gdf, shape, transform, num_classes):
        """生成多分类掩码"""
        print(f"🚀 _generate_multiclass_mask 开始执行")
        print(f"   gdf形状: {gdf.shape}")
        print(f"   num_classes: {num_classes}")

        mask = np.zeros(shape, dtype=np.uint8)

        # 检查是否有类别字段
        class_field = self.config.model_config.get('class_field', 'class')
        class_mapping = self.config.model_config.get('class_mapping', {})

        print(f"🔍 配置信息:")
        print(f"   class_field: {class_field}")
        print(f"   class_mapping: {class_mapping}")
        print(f"   class_mapping类型: {type(class_mapping)}")
        print(f"   gdf列名: {list(gdf.columns)}")

        if class_field in gdf.columns:
            print(f"✅ 找到类别字段: {class_field}")

            # 显示字段详细信息
            unique_values = gdf[class_field].unique()
            print(f"   字段唯一值: {unique_values}")
            value_counts = gdf[class_field].value_counts()
            print(f"   值分布: {dict(value_counts)}")

            # 如果有类别映射，使用映射逻辑
            if class_mapping:
                print(f"使用类别映射: {class_mapping}")
                shapes_by_class = []
                for _, row in gdf.iterrows():
                    if row.geometry is not None:
                        original_value = row[class_field]

                        # 尝试多种映射方式
                        class_id = None
                        if original_value in class_mapping:
                            class_id = class_mapping[original_value]
                        elif str(original_value) in class_mapping:
                            class_id = class_mapping[str(original_value)]
                        elif isinstance(original_value, (int, float)) and 1 <= original_value <= 3:
                            class_id = int(original_value)
                        else:
                            class_id = 1

                        shapes_by_class.append((row.geometry, class_id))

                # 输出类别映射统计
                class_stats = {}
                for _, class_id in shapes_by_class:
                    class_stats[class_id] = class_stats.get(class_id, 0) + 1
                print(f"类别映射统计: {class_stats}")
            else:
                # 没有类别映射，直接使用数字类别
                unique_classes = gdf[class_field].unique()
                print(f"发现类别: {unique_classes}")

                shapes_by_class = []

                # 检查是否是数字类别（如1,2,3）
                if all(isinstance(cls, (int, float)) for cls in unique_classes):
                    print("检测到数字类别，直接使用原始类别ID")
                    for _, row in gdf.iterrows():
                        if row.geometry is not None:
                            original_class = int(row[class_field])
                            if 1 <= original_class <= 3:  # 支持类别1、2、3
                                shapes_by_class.append((row.geometry, original_class))
                            else:
                                print(f"警告: 类别 {original_class} 超出范围 [1, 3]，跳过")
                else:
                    # 字符串类别，按顺序分配ID
                    for class_idx, class_name in enumerate(sorted(unique_classes), 1):
                        if class_idx >= num_classes:
                            print(f"警告: 类别 {class_name} 超出num_classes限制，跳过")
                            continue

                        class_gdf = gdf[gdf[class_field] == class_name]
                        for _, row in class_gdf.iterrows():
                            if row.geometry is not None:
                                shapes_by_class.append((row.geometry, class_idx))

                # 输出类别映射统计
                class_stats = {}
                for _, class_id in shapes_by_class:
                    class_stats[class_id] = class_stats.get(class_id, 0) + 1
                print(f"自动类别映射统计: {class_stats}")

            # 栅格化
            if shapes_by_class:
                mask = rasterize(
                    shapes_by_class,
                    out_shape=shape,
                    transform=transform,
                    fill=0,
                    dtype='uint8',
                    all_touched=True
                )
            else:
                # 没有类别映射，直接使用数字类别
                unique_classes = gdf[class_field].unique()
                print(f"发现类别: {unique_classes}")

                if all(isinstance(cls, (int, float)) for cls in unique_classes):
                    # 数字类别，直接使用
                    shapes_by_class = []
                    for _, row in gdf.iterrows():
                        if row.geometry is not None:
                            class_id = int(row[class_field])
                            if 1 <= class_id < num_classes:
                                shapes_by_class.append((row.geometry, class_id))

                    if shapes_by_class:
                        mask = rasterize(
                            shapes_by_class,
                            out_shape=shape,
                            transform=transform,
                            fill=0,
                            dtype='uint8',
                            all_touched=True
                        )
                else:
                    # 字符串类别，按范围分配
                    for class_id in range(1, num_classes):
                        class_gdf = gdf[gdf[class_field] == class_id]
                        if len(class_gdf) > 0:
                            class_mask = rasterize(
                                class_gdf.geometry,
                                out_shape=shape,
                                transform=transform,
                                fill=0,
                                default_value=class_id,
                                dtype=np.uint8
                            )
                            mask = np.maximum(mask, class_mask)
        else:
            # 如果没有类别字段，所有几何体都标记为类别1
            mask = rasterize(
                gdf.geometry,
                out_shape=shape,
                transform=transform,
                fill=0,
                default_value=1,
                dtype=np.uint8
            )
        
        return mask
    
    def load_image_mask_pairs(self, image_folder, mask_folder):
        """加载image+mask模式的文件对，支持多种数据集结构"""
        print(f"扫描图像文件夹: {image_folder}")
        print(f"扫描掩码文件夹: {mask_folder}")

        # 检查路径是否有效
        if not image_folder or not mask_folder:
            raise ValueError("图像文件夹和掩码文件夹路径不能为空")

        if not os.path.exists(image_folder):
            raise ValueError(f"图像文件夹不存在: {image_folder}")

        if not os.path.exists(mask_folder):
            raise ValueError(f"掩码文件夹不存在: {mask_folder}")

        # 检查是否是单个大文件模式
        if self._is_single_file_mode(image_folder, mask_folder):
            print("检测到单个大文件模式，将进行自动切片处理")
            # 找到单个图像和掩码文件
            image_file = self._find_single_image_file(image_folder)
            mask_file = self._find_single_mask_file(mask_folder)

            if not image_file or not mask_file:
                raise ValueError("在单文件模式下未找到图像或掩码文件")

            print(f"图像文件: {image_file}")
            print(f"掩码文件: {mask_file}")
            print("将自动进行切片分割并分配训练/验证集")

            # 返回特殊标记，表示这是单文件模式
            return "SINGLE_FILE_MODE", (image_file, mask_file)

        # 检查是否有预分割的训练/验证集结构
        train_image_folder = os.path.join(image_folder, 'train')
        val_image_folder = os.path.join(image_folder, 'val')
        train_mask_folder = os.path.join(mask_folder, 'train')
        val_mask_folder = os.path.join(mask_folder, 'val')

        has_train_val_structure = (
            os.path.exists(train_image_folder) and
            os.path.exists(val_image_folder) and
            os.path.exists(train_mask_folder) and
            os.path.exists(val_mask_folder)
        )

        if has_train_val_structure:
            print("检测到预分割的训练/验证集结构")
            # 使用预分割的结构
            train_pairs = self._scan_folder_pairs(train_image_folder, train_mask_folder)
            val_pairs = self._scan_folder_pairs(val_image_folder, val_mask_folder)

            print(f"训练集: {len(train_pairs)} 对文件")
            print(f"验证集: {len(val_pairs)} 对文件")

            if len(train_pairs) == 0:
                raise ValueError("训练集中没有找到匹配的图像-掩码对")

            return train_pairs, val_pairs
        else:
            print("检测到多文件夹结构，将自动分割训练/验证集")
            # 扫描多文件夹
            all_pairs = self._scan_folder_pairs(image_folder, mask_folder)

            print(f"找到 {len(all_pairs)} 对文件")

            if len(all_pairs) == 0:
                raise ValueError("未找到匹配的图像-掩码文件对")

            # 返回所有文件对，让调用者决定如何分割
            return all_pairs

    def _is_single_file_mode(self, image_folder, mask_folder):
        """检查是否是单个大文件模式"""
        # 获取文件夹中的文件
        image_files = [f for f in os.listdir(image_folder)
                      if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))]
        mask_files = [f for f in os.listdir(mask_folder)
                     if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))]

        # 如果每个文件夹只有一个文件，认为是单文件模式
        return len(image_files) == 1 and len(mask_files) == 1

    def _find_single_image_file(self, image_folder):
        """找到单个图像文件"""
        image_files = [f for f in os.listdir(image_folder)
                      if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))]
        if len(image_files) == 1:
            return os.path.join(image_folder, image_files[0])
        return None

    def _find_single_mask_file(self, mask_folder):
        """找到单个掩码文件"""
        mask_files = [f for f in os.listdir(mask_folder)
                     if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))]
        if len(mask_files) == 1:
            return os.path.join(mask_folder, mask_files[0])
        return None

    def _scan_folder_pairs(self, image_folder, mask_folder):
        """扫描文件夹中的图像-掩码对"""
        # 获取文件列表
        image_files = sorted([f for f in os.listdir(image_folder)
                             if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))])
        mask_files = sorted([f for f in os.listdir(mask_folder)
                            if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))])

        # 匹配文件对
        matched_pairs = []
        for img_file in image_files:
            img_name = os.path.splitext(img_file)[0]
            for mask_file in mask_files:
                mask_name = os.path.splitext(mask_file)[0]
                if img_name == mask_name:
                    matched_pairs.append((
                        os.path.join(image_folder, img_file),
                        os.path.join(mask_folder, mask_file)
                    ))
                    break

        return matched_pairs
    
    def split_train_val(self, file_pairs):
        """分割训练和验证集"""
        train_pairs, val_pairs = train_test_split(
            file_pairs, 
            train_size=self.config.data_config['train_ratio'],
            random_state=42
        )
        
        print(f"训练集: {len(train_pairs)} 对文件")
        print(f"验证集: {len(val_pairs)} 对文件")
        
        return train_pairs, val_pairs
    
    def load_file_pair(self, img_path, mask_path):
        """加载单个图像-掩码文件对"""
        # 加载图像
        if img_path.lower().endswith(('.tif', '.tiff')):
            with rasterio.open(img_path) as src:
                image = src.read().transpose((1, 2, 0))
        else:
            image = np.array(Image.open(img_path).convert('RGB'))
        
        # 加载掩码
        if mask_path.lower().endswith(('.tif', '.tiff')):
            with rasterio.open(mask_path) as src:
                mask = src.read(1)
        else:
            mask = np.array(Image.open(mask_path).convert('L'))
        
        # 确保掩码格式正确
        mask = self._process_mask(mask)
        
        return image, mask
    
    def _process_mask(self, mask):
        """处理掩码格式"""
        num_classes = self.config.model_config['num_classes']
        
        if num_classes == 2:
            # 二分类：确保掩码是0和1
            mask = (mask > 0).astype(np.uint8)
        else:
            # 多分类：确保掩码值在有效范围内
            mask = np.clip(mask, 0, num_classes - 1).astype(np.uint8)
        
        return mask
    
    def create_patches(self, image, mask):
        """创建图像切片"""
        h, w = image.shape[:2]
        step = self.slice_size - self.overlap
        patches = []
        
        for y in range(0, h - self.slice_size + 1, step):
            for x in range(0, w - self.slice_size + 1, step):
                img_patch = image[y:y+self.slice_size, x:x+self.slice_size]
                mask_patch = mask[y:y+self.slice_size, x:x+self.slice_size]
                
                # 检查前景像素比例
                if self._is_valid_patch(mask_patch):
                    patches.append({
                        'image': img_patch,
                        'mask': mask_patch
                    })
        
        return patches
    
    def _is_valid_patch(self, mask_patch):
        """检查切片是否有效"""
        # 计算前景像素比例
        foreground_pixels = np.sum(mask_patch > 0)
        total_pixels = mask_patch.size
        foreground_ratio = foreground_pixels / total_pixels
        
        return foreground_ratio >= self.min_foreground_ratio

class LogCANDataset(Dataset):
    """LogCAN数据集类"""
    
    def __init__(self, config, mode='train', file_pairs=None, single_files=None):
        self.config = config
        self.mode = mode
        self.processor = LogCANDataProcessor(config)

        self.samples = []
        self._load_data(file_pairs, single_files)
    
    def _load_data(self, file_pairs=None, single_files=None):
        """加载数据"""
        data_mode = self.config.data_config['data_mode']

        if data_mode == 'image+shape':
            self._load_image_shape_data()
        elif data_mode == 'image+mask':
            if single_files is not None:
                self._load_single_large_files(single_files)
            else:
                self._load_image_mask_data(file_pairs)
        else:
            raise ValueError(f"不支持的数据模式: {data_mode}")
    
    def _load_image_shape_data(self):
        """加载image+shape模式的数据"""
        image_path = self.config.paths['image_path']
        shape_path = self.config.paths['shape_path']
        
        if not image_path or not shape_path:
            raise ValueError("image+shape模式需要提供image_path和shape_path")
        
        image, mask = self.processor.load_image_shape_data(image_path, shape_path)
        patches = self.processor.create_patches(image, mask)
        self.samples.extend(patches)
        
        print(f"生成了 {len(self.samples)} 个有效切片")
    
    def _load_image_mask_data(self, file_pairs):
        """加载image+mask模式的数据"""
        if file_pairs is None:
            image_folder = self.config.paths['image_folder']
            mask_folder = self.config.paths['mask_folder']
            
            if not image_folder or not mask_folder:
                raise ValueError("image+mask模式需要提供image_folder和mask_folder")
            
            all_pairs = self.processor.load_image_mask_pairs(image_folder, mask_folder)
            train_pairs, val_pairs = self.processor.split_train_val(all_pairs)
            
            file_pairs = train_pairs if self.mode == 'train' else val_pairs
        
        # 处理每对文件
        for img_path, mask_path in file_pairs:
            image, mask = self.processor.load_file_pair(img_path, mask_path)
            patches = self.processor.create_patches(image, mask)
            self.samples.extend(patches)
        
        print(f"{self.mode}模式: 生成了 {len(self.samples)} 个有效切片")

    def _load_single_large_files(self, single_files):
        """加载单个大文件模式的数据"""
        image_file, mask_file = single_files
        print(f"加载{self.mode}数据: 单个大文件模式")
        print(f"图像文件: {image_file}")
        print(f"掩码文件: {mask_file}")

        # 加载大文件
        image, mask = self.processor.load_file_pair(image_file, mask_file)

        # 生成所有切片
        all_patches = self.processor.create_patches(image, mask)

        # 根据训练/验证模式分割数据
        train_ratio = self.config.data_config.get('train_ratio', 0.8)

        # 随机打乱但保持可重现性
        import random
        random.seed(42)
        shuffled_patches = all_patches.copy()
        random.shuffle(shuffled_patches)

        if self.mode == 'train':
            # 取前80%作为训练集
            num_train = int(len(shuffled_patches) * train_ratio)
            self.samples = shuffled_patches[:num_train]
        else:
            # 取后20%作为验证集
            num_train = int(len(shuffled_patches) * train_ratio)
            self.samples = shuffled_patches[num_train:]

        print(f"从{len(all_patches)}个总切片中分配{len(self.samples)}个{self.mode}切片样本")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        image = sample['image']
        mask = sample['mask']
        
        # 应用数据变换
        if self.mode == 'train':
            transformed = self.processor.train_transform(image=image, mask=mask)
        else:
            transformed = self.processor.val_transform(image=image, mask=mask)
        
        return transformed['image'], transformed['mask'].long()
