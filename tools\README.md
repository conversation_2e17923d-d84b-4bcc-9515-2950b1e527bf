# 数据集检查与转换工具集

本文件夹包含了一套完整的数据集质量检查和格式转换工具，专门用于处理从Google Earth Engine (GEE) 下载的卫星图像数据。

## 📁 工具概览

| 工具名称 | 功能描述 | 适用场景 |
|---------|---------|---------|
| `dataset_diagnostic.py` | 数据集完整性诊断 | 检查TIFF文件是否损坏、可读性验证 |
| `analyze_nan_data.py` | NaN值分析工具 | 分析图像中的无效值分布和数据质量 |
| `convert_dataset.py` | 数据格式转换器 | 将float64图像转换为标准uint8格式 |
| `check_mask_file.py` | 掩膜文件检查器 | 专门检查和修复掩膜文件 |
| `create_viewable_mask.py` | 掩膜可视化工具 | **批量**创建可在普通查看器中打开的掩膜文件 |
| `test_viewable_mask.py` | 测试工具 | 测试掩膜可视化工具的功能 |

## 🚀 快速开始

### 1. 数据集完整性检查

当您从GEE下载数据后，首先运行完整性检查：

```bash
python dataset_diagnostic.py
```

**功能：**
- 检查所有TIFF文件的可读性
- 验证文件格式和元数据
- 统计文件大小和数量
- 识别损坏的文件

**输出示例：**
```
📊 总文件数: 4
✅ 正常文件: 4
❌ 损坏文件: 0
📏 总大小: 2.09 GB
```

### 2. NaN值质量分析

如果发现数据问题，运行NaN分析：

```bash
python analyze_nan_data.py
```

**功能：**
- 分析每个波段的NaN值分布
- 统计有效数据比例
- 识别数据质量问题
- 自动创建处理后的数据

**输出示例：**
```
波段 1:
  总像素数: 1,089,936
  NaN像素数: 1,410 (0.1%)
  有效像素数: 1,088,526 (99.9%)
  有效数据范围: [1045.00, 7852.00]
```

### 3. 图像格式转换

将GEE的float64格式转换为标准格式：

```bash
python convert_dataset.py
```

**功能：**
- float64 → uint8 转换
- 智能处理NaN值
- 百分位数裁剪增强对比度
- 大幅减少文件大小

**转换效果：**
- 原始：2.09 GB → 转换后：~618 MB
- 数据范围：1000-8000 → 0-255
- 格式：不可查看 → 标准图像格式

### 4. 掩膜文件检查

检查掩膜文件的完整性：

```bash
python check_mask_file.py
```

**功能：**
- 验证掩膜文件格式
- 检查二值掩膜的有效性
- 统计类别分布
- 自动修复问题

### 5. 批量创建可查看的掩膜 ⭐ **已升级**

为大型掩膜文件批量创建可视化版本：

```bash
# 方法1: 使用默认路径（推荐）
python create_viewable_mask.py

# 方法2: 指定掩膜文件夹
python create_viewable_mask.py "D:/path/to/mask/folder"

# 方法3: 指定输入和输出文件夹
python create_viewable_mask.py "D:/path/to/mask/folder" "D:/path/to/output/folder"
```

**新功能：**
- ✅ **批量处理**：一次处理文件夹下所有掩膜文件
- ✅ **命令行支持**：灵活指定输入输出路径
- ✅ **自动组织**：每个掩膜文件独立输出文件夹
- ✅ **进度显示**：实时显示处理进度和结果

**输出内容：**
- 创建预览图（适合查看的尺寸）
- 生成PNG格式版本
- 创建统计信息图表
- 移除地理信息以兼容普通查看器

**输出结构：**
```
output_folder/
├── mask1/
│   ├── preview_mask1.png      # 预览图
│   ├── mask1.png              # 完整PNG
│   ├── standard_mask1.tif     # 标准TIFF
│   └── stats_mask1.png        # 统计图
├── mask2/
│   └── ... (同样的文件结构)
└── ...
```

## 📋 使用流程

### 完整的数据处理流程：

```mermaid
graph TD
    A[GEE下载数据] --> B[运行 dataset_diagnostic.py]
    B --> C{文件是否正常?}
    C -->|否| D[检查下载过程]
    C -->|是| E[运行 analyze_nan_data.py]
    E --> F{数据质量如何?}
    F -->|有问题| G[重新下载或处理]
    F -->|良好| H[运行 convert_dataset.py]
    H --> I[运行 check_mask_file.py]
    I --> J[运行 create_viewable_mask.py]
    J --> K[开始模型训练]
```

### 推荐执行顺序：

1. **第一步：基础检查**
   ```bash
   python dataset_diagnostic.py
   ```

2. **第二步：质量分析**
   ```bash
   python analyze_nan_data.py
   ```

3. **第三步：格式转换**
   ```bash
   python convert_dataset.py
   ```

4. **第四步：掩膜检查**
   ```bash
   python check_mask_file.py
   ```

5. **第五步：可视化**
   ```bash
   python create_viewable_mask.py
   ```

## ⚙️ 配置说明

### 默认路径配置

所有工具都使用以下默认路径，可在脚本中修改：

```python
# 输入路径
input_image_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\image"
input_mask_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\mask"

# 输出路径
output_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted"
```

### 自定义参数

可以通过命令行参数指定路径：

```bash
python dataset_diagnostic.py "自定义图像路径" "自定义掩膜路径"
```

## 🔧 常见问题解决

### 问题1：文件"损坏"无法打开

**原因：** GEE下载的文件是float64格式的GeoTIFF，普通查看器无法处理

**解决：** 运行 `convert_dataset.py` 转换为标准格式

### 问题2：训练效果差

**原因：** 数据包含NaN值或数据范围不标准

**解决：** 
1. 运行 `analyze_nan_data.py` 分析问题
2. 运行 `convert_dataset.py` 标准化数据

### 问题3：验证集loss为0

**原因：** 验证数据包含大量NaN值

**解决：** 使用转换后的干净数据重新训练

### 问题4：掩膜文件无法查看

**原因：** 文件太大或格式不兼容

**解决：** 运行 `create_viewable_mask.py` 创建可查看版本

## 📊 输出文件说明

### convert_dataset.py 输出：
```
converted/
├── image/          # 转换后的图像文件
│   ├── tile_0_0_img-*.tif
└── mask/           # 复制的掩膜文件
    └── tile_0_0_mask.tif
```

### create_viewable_mask.py 输出：
```
viewable/
├── preview_tile_0_0_mask.png      # 预览图（推荐查看）
├── tile_0_0_mask.png              # 完整PNG版本
├── standard_tile_0_0_mask.tif     # 标准TIFF
└── stats_tile_0_0_mask.png        # 统计信息图
```

## 🆕 工具升级说明

### create_viewable_mask.py 升级 (v2.0)
- ✅ **批量处理**：支持文件夹级别的批量处理
- ✅ **命令行参数**：支持灵活的路径配置
- ✅ **自动组织**：每个文件独立输出文件夹
- ✅ **向后兼容**：保留单文件处理功能
- ✅ **测试工具**：提供专门的测试脚本

**升级优势：**
- 无需每次修改代码中的文件名
- 一次处理多个掩膜文件
- 更好的文件组织结构
- 支持命令行批量操作

## 💡 最佳实践

1. **数据下载后立即检查**：避免在训练时才发现数据问题
2. **保留原始数据**：转换后的数据用于训练，原始数据作为备份
3. **查看统计信息**：了解数据分布有助于调整训练参数
4. **定期清理**：删除不需要的临时文件和转换文件
5. **批量处理优先**：优先使用批量处理工具，提高效率

## 🔄 更新训练配置

转换完成后，更新训练配置文件：

```json
{
    "model": {
        "args": {
            "image_folder": "D:/Satellite_Image_Auto_Offset/GIS Data from GEE/converted/image",
            "mask_folder": "D:/Satellite_Image_Auto_Offset/GIS Data from GEE/converted/mask"
        }
    }
}
```

## 📞 技术支持

如果遇到问题：
1. 检查文件路径是否正确
2. 确认Python环境包含所需依赖
3. 查看工具输出的错误信息
4. 检查磁盘空间是否充足

---

*最后更新：2025-07-17*
