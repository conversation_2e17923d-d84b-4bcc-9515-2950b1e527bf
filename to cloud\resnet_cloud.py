import os
import cv2
import numpy as np
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import random
import tensorflow as tf
import keras
from keras.utils import to_categorical, Sequence
import segmentation_models as sm
from keras.optimizers import Adam
from keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard
from datetime import datetime
import json

# 设置混合精度
tf.keras.mixed_precision.set_global_policy('mixed_float16')

# 配置参数
CONFIG = {
    'dataset_path': './dataset',  # 数据集路径
    'model': {
        'args': {
            'input_shape': (256, 256, 3),
            'num_classes': 2,
            'backbone': 'resnet50'
        }
    },
    'trainer': {
        'batch_size': 8,
        'epochs': 100,
        'learning_rate': 0.001,
        'slice_size': 256,
        'overlap': 0.2,
        'save_dir': './models'  # 模型保存路径
    }
}

class DataGenerator(keras.utils.Sequence):
    def __init__(self, x_set, y_set, batch_size):
        self.x, self.y = x_set, y_set
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.x) / float(self.batch_size)))

    def __getitem__(self, idx):
        batch_x = self.x[idx * self.batch_size:(idx + 1) * self.batch_size]
        batch_y = self.y[idx * self.batch_size:(idx + 1) * self.batch_size]
        return batch_x, batch_y

def create_resnet_segmentation_model(input_shape, num_classes, backbone='resnet50'):
    """创建基于ResNet的分割模型"""
    base_model = sm.Unet(
        backbone_name=backbone,
        input_shape=input_shape,
        classes=num_classes,
        activation='softmax',
        encoder_weights='imagenet'
    )

    # 添加自定义层
    x = base_model.output
    x = keras.layers.Conv2D(num_classes, (1, 1), padding='same')(x)
    x = keras.layers.BatchNormalization()(x)
    x = keras.layers.Activation('softmax', name='final_output')(x)

    model = keras.models.Model(inputs=base_model.input, outputs=x)
    return model

def train_resnet(config):
    """训练ResNet分割模型"""
    print("=== 开始训练 ResNet 分割模型 ===")

    # 获取训练参数
    model_args = config['model']['args']
    trainer_args = config['trainer']

    # 设置训练参数
    batch_size = trainer_args['batch_size']
    epochs = trainer_args['epochs']
    learning_rate = trainer_args['learning_rate']
    slice_size = trainer_args['slice_size']
    overlap = trainer_args['overlap']
    save_dir = trainer_args['save_dir']

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 读取数据
    with rasterio.open('test.tif') as src:  # 根据实际上传的文件名修改
        image = src.read().transpose((1, 2, 0))

    gdf = gpd.read_file('Buildings.shp')  # 根据实际上传的文件名修改

    # 确保图像是3波段
    if image.shape[2] > 3:
        image = image[:, :, :3]

    # 创建掩码
    mask = rasterize(
        [(shape, 1) for shape in gdf.geometry],
        out_shape=image.shape[:2],
        transform=src.transform,
        fill=0,
        dtype='uint8',
        all_touched=True  # 🔑 添加all_touched参数
    )

    # 数据预处理
    minmaxscaler = MinMaxScaler()
    image = minmaxscaler.fit_transform(image.reshape(-1, image.shape[-1])).reshape(image.shape)
    mask = np.expand_dims(mask, axis=-1)

    # 创建图像块
    def create_patches(img, mask, size=slice_size, stride=slice_size-overlap):
        patches_img = patchify(img, (size, size, img.shape[-1]), step=stride)
        patches_mask = patchify(mask, (size, size, 1), step=stride)

        patches_img = patches_img.reshape(-1, size, size, img.shape[-1])
        patches_mask = patches_mask.reshape(-1, size, size, 1)

        return patches_img, patches_mask

    image_patches, mask_patches = create_patches(image, mask)
    print(f"创建了 {len(image_patches)} 个图像块")

    # 转换标签为分类格式
    mask_categorical = to_categorical(mask_patches, num_classes=2)

    # 划分训练集和验证集
    X_train, X_val, y_train, y_val = train_test_split(
        image_patches, mask_categorical,
        test_size=0.2, random_state=42
    )

    # 创建模型
    model = create_resnet_segmentation_model(**model_args)

    # 配置优化器和损失函数
    optimizer = Adam(learning_rate=learning_rate)
    loss = sm.losses.CategoricalCELoss()
    metrics = [
        sm.metrics.IOUScore(threshold=0.5),
        sm.metrics.FScore(threshold=0.5),
        'accuracy'
    ]

    model.compile(optimizer, loss, metrics)

    # 创建回调函数
    callbacks = [
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-6
        ),
        ModelCheckpoint(
            os.path.join(save_dir, 'best_model.h5'),
            monitor='val_iou_score',
            mode='max',
            save_best_only=True
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True
        ),
        TensorBoard(log_dir=os.path.join(save_dir, 'logs'))
    ]

    # 创建数据生成器
    train_gen = DataGenerator(X_train, y_train, batch_size)
    val_gen = DataGenerator(X_val, y_val, batch_size)

    # 训练模型
    print("开始训练...")
    history = model.fit(
        train_gen,
        validation_data=val_gen,
        epochs=epochs,
        callbacks=callbacks,
        verbose=1
    )

    return history

if __name__ == "__main__":
    # 使用内置配置进行训练
    history = train_resnet(CONFIG)

    # 绘制训练历史
    plt.figure(figsize=(15, 5))

    # 绘制损失
    plt.subplot(1, 3, 1)
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    # 绘制IoU
    plt.subplot(1, 3, 2)
    plt.plot(history.history['iou_score'], label='Training IoU')
    plt.plot(history.history['val_iou_score'], label='Validation IoU')
    plt.title('Model IoU')
    plt.xlabel('Epoch')
    plt.ylabel('IoU')
    plt.legend()

    # 绘制准确率
    plt.subplot(1, 3, 3)
    plt.plot(history.history['accuracy'], label='Training Accuracy')
    plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
    plt.title('Model Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()

    plt.tight_layout()
    plt.show()
