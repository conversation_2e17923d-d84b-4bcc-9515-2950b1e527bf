import os
import subprocess
from datetime import datetime

def run_command(command):
    """安全执行 Git 命令"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"错误: {e.stderr.strip()}")
        return None

def check_git_repo():
    """初始化 Git 仓库"""
    try:
        repo_path = r'D:\Satellite_Image_Auto_Offset\Autooffset_Test'
        os.chdir(repo_path)
        
        # 强制设置用户身份
        run_command('git config user.name "AutoOffset"')
        run_command('git config user.email "<EMAIL>"')
        
        # 初始化仓库（如果不存在）
        if not os.path.exists('.git'):
            print("初始化 Git 仓库...")
            run_command('git init')
        
        # 修复分离头指针
        current_branch = run_command('git rev-parse --abbrev-ref HEAD')
        if not current_branch or "HEAD" in current_branch:
            print("正在关联到 master 分支...")
            run_command('git checkout -B master')
        
        # 配置备份仓库
        backup_path = r'D:\Satellite_Image_Auto_Offset\backup\autooffset_backup.git'
        if not os.path.exists(backup_path):
            print("创建备份仓库...")
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            run_command(f'git init --bare "{backup_path}"')
        
        # 强制更新远程仓库配置
        run_command('git remote remove backup')
        run_command(f'git remote add backup "{backup_path}"')
        
        return True
    except Exception as e:
        print(f"仓库初始化失败: {str(e)}")
        return False

def backup_code():
    """执行备份"""
    try:
        if not check_git_repo():
            return
        
        # 添加所有更改
        run_command('git add --all')
        status = run_command('git status --porcelain')
        if not status:
            print("没有需要备份的更改")
            return
        
        print("\n检测到以下变更:")
        print(status)
        
        # 用户输入
        version = input("\n版本号 (例如 v1.0.0): ").strip()
        description = input("版本描述: ").strip()
        
        # 提交
        run_command(f'git commit -m "{version}: {description}"')
        run_command(f'git tag -a {version} -m "版本 {version} - {description}"')
        
        # 推送
        print("正在推送至备份仓库...")
        run_command('git push backup master --force')
        run_command('git push backup --tags --force')
        
        print("\n备份成功！")
    except Exception as e:
        print(f"备份失败: {str(e)}")

if __name__ == '__main__':
    backup_code()
    input("\n按回车键退出...")