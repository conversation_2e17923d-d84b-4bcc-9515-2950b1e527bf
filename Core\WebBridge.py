from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal

class WebBridge(QObject):
    locationChanged = pyqtSignal(float, float)
    pointCreated = pyqtSignal(float, float)
    polygonFinished = pyqtSignal()
    drawingCanceled = pyqtSignal()
    coordinatesChanged = pyqtSignal(float, float, float)
    areaSelected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
    
    @pyqtSlot(float, float)
    def handlePointAdded(self, lat, lng):
        """当在JavaScript中添加点时发出信号"""
        self.pointCreated.emit(lat, lng)
    
    @pyqtSlot()
    def handlePolygonCompleted(self):
        """当在JavaScript中完成多边形绘制时发出信号"""
        self.polygonFinished.emit()
    
    @pyqtSlot()
    def handleDrawingCanceled(self):
        """当绘制被取消时发出信号"""
        self.drawingCanceled.emit()
    
    @pyqtSlot(float, float)
    def handleLocationUpdated(self, lat, lng):
        """当获取到用户位置时发出信号"""
        self.locationChanged.emit(lat, lng)
        
    @pyqtSlot(float, float, float)
    def handleCoordinatesUpdated(self, lat, lng, zoom):
        """当鼠标移动时发出信号"""
        self.coordinatesChanged.emit(lat, lng, zoom)
    
    @pyqtSlot(str)
    def saveSelectedArea(self, points_json):
        """当在JavaScript中完成区域选择时发出信号"""
        print(f"已保存选定区域坐标：{points_json}")  # 添加调试信息
        self.areaSelected.emit(points_json)
        
    @pyqtSlot()
    def clearSelection(self):
        """清除地图上的选择区域"""
        # 清除矩形选择区域
        self.page().runJavaScript("""
            if (window.rectangleLayer) { 
                map.removeLayer(window.rectangleLayer); 
                window.rectangleLayer = null; 
            }
            if (window.selectedBounds) {
                window.selectedBounds = null;
            }
            if (window.drawControl) {
                window.drawControl.disable();
            }
        """)