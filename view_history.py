import os
import subprocess
from datetime import datetime

def run_command(command):
    """安全执行 Git 命令"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"错误: {e.stderr.strip()}")
        return None
    except Exception as e:
        print(f"执行命令失败: {str(e)}")
        return None

def format_date(date_str):
    """格式化日期"""
    try:
        # 处理可能的时区格式（如 2023-10-01 12:00:00 +0800）
        if '+' in date_str:
            date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S %z')
        else:
            date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        return date.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        print(f"日期解析失败: {date_str}")
        return date_str

def view_history():
    """查看备份历史"""
    try:
        repo_path = r'D:\Satellite_Image_Auto_Offset\Autooffset_Test'
        if not os.path.exists(repo_path):
            print("错误: 仓库路径不存在")
            return
        os.chdir(repo_path)
        
        print("\n=== 备份历史 ===\n")
        
        tags_output = run_command('git tag --list --sort=-creatordate')
        if not tags_output:
            print("没有找到任何备份记录")
            return
        tags = tags_output.split('\n')
        
        for tag in tags:
            tag = tag.strip()
            if not tag:
                continue
            
            date = run_command(f'git for-each-ref --format="%(taggerdate:iso)" "refs/tags/{tag}"')
            if not date:
                continue
            
            message = run_command(f'git show {tag} --no-patch --format="%(contents:subject)"')
            message = message if message else "（无描述）"
            
            print(f"版本: {tag}")
            print(f"时间: {format_date(date)}")
            print(f"描述: {message}")
            print("-" * 50)
        
        total_tags = len(tags)
        print(f"\n总计: {total_tags} 个备份")
        
        if total_tags > 0:
            sorted_tags = run_command('git tag --list --sort=creatordate').split('\n')
            earliest_tag = sorted_tags[0].strip() if sorted_tags else None
            latest_tag = sorted_tags[-1].strip() if sorted_tags else None
            
            if earliest_tag and latest_tag:
                earliest_date = run_command(f'git for-each-ref --format="%(taggerdate:iso)" "refs/tags/{earliest_tag}"')
                latest_date = run_command(f'git for-each-ref --format="%(taggerdate:iso)" "refs/tags/{latest_tag}"')
                if earliest_date and latest_date:
                    print(f"最早备份: {format_date(earliest_date)}")
                    print(f"最新备份: {format_date(latest_date)}")
    
    except Exception as e:
        print(f"查看历史失败: {str(e)}")

if __name__ == '__main__':
    try:
        view_history()
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
    
    input("\n按回车键返回主菜单...")