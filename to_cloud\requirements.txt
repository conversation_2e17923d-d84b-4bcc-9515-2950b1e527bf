# UNet卫星图像分割训练环境依赖包
# 使用方法: pip install -r requirements.txt

# 深度学习框架
tensorflow==2.15.0

# 地理数据处理
rasterio>=1.3.0
geopandas>=0.12.0
GDAL>=3.4.0
shapely>=1.8.0
pyproj>=3.3.0

# 图像处理
opencv-python>=4.5.0
Pillow>=8.0.0
patchify>=0.2.3

# 科学计算
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 系统工具
psutil>=5.8.0
tqdm>=4.62.0

# 其他工具
pyyaml>=6.0
pathlib2>=2.3.0

# 可选：Jupyter支持（如果需要在notebook中使用）
# jupyter>=1.0.0
# ipykernel>=6.0.0

# 可选：GPU加速（如果使用GPU）
# tensorflow[and-cuda]==2.15.0  # 替换上面的tensorflow行
