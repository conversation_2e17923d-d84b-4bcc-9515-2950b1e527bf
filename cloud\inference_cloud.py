import os
import torch
import numpy as np
from PIL import Image
from transformers import pipeline
from segment_anything import sam_model_registry, SamPredictor
import argparse

def load_image(image_path):
    """加载图像文件"""
    return Image.open(image_path)

def run_hf_inference(image_path, model_name="facebook/detr-resnet-50-panoptic"):
    """使用HuggingFace模型进行推理"""
    # 初始化模型
    model = pipeline(
        "image-segmentation",
        model=model_name,
        device=0 if torch.cuda.is_available() else -1
    )
    
    # 加载图像
    image = load_image(image_path)
    
    # 执行推理
    results = model(image)
    
    # 保存结果
    output_path = image_path.replace(".tif", "_hf_segmentation.png")
    save_results(results, output_path)
    
    return output_path

def run_sam_inference(image_path, checkpoint_path):
    """使用SAM模型进行推理"""
    # 初始化SAM模型
    sam = sam_model_registry["vit_h"](checkpoint=checkpoint_path)
    predictor = SamPredictor(sam)
    
    # 加载图像
    image = np.array(load_image(image_path))
    predictor.set_image(image)
    
    # 生成自动掩码
    masks = predictor.generate()
    
    # 保存结果
    output_path = image_path.replace(".tif", "_sam_segmentation.png")
    save_results(masks, output_path)
    
    return output_path

def save_results(results, output_path):
    """保存分割结果"""
    # 根据不同模型的输出格式保存结果
    if isinstance(results, list):  # HuggingFace模型结果
        # 合并所有分割掩码
        combined_mask = np.zeros_like(results[0]["mask"])
        for r in results:
            combined_mask = np.logical_or(combined_mask, r["mask"])
        
        # 保存为PNG
        Image.fromarray((combined_mask * 255).astype(np.uint8)).save(output_path)
    
    else:  # SAM模型结果
        # 保存第一个掩码
        Image.fromarray((results[0] * 255).astype(np.uint8)).save(output_path)

def main():
    parser = argparse.ArgumentParser(description="云端图像分割推理")
    parser.add_argument("--image_path", required=True, help="输入图像路径")
    parser.add_argument("--model_type", choices=["hf", "sam"], required=True, help="模型类型")
    parser.add_argument("--model_path", help="模型检查点路径（用于SAM）")
    parser.add_argument("--model_name", default="facebook/detr-resnet-50-panoptic", help="HuggingFace模型名称")
    
    args = parser.parse_args()
    
    if args.model_type == "hf":
        output_path = run_hf_inference(args.image_path, args.model_name)
    else:
        output_path = run_sam_inference(args.image_path, args.model_path)
    
    print(f"分割结果已保存至: {output_path}")

if __name__ == "__main__":
    main()
