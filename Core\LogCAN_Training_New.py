"""
新的LogCAN训练代码架构
支持二分类和多分类，兼容image+shape和image+mask两种数据模式
参考UNet数据集使用方式，简化配置和使用流程
"""

import os
import sys
import json
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torch.nn.functional as F
from datetime import datetime
import argparse
from pathlib import Path
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from PIL import Image
import warnings

# 抑制警告
warnings.filterwarnings("ignore")

class LogCANConfig:
    """LogCAN训练配置类"""
    
    def __init__(self):
        # 默认配置
        self.model_config = {
            'num_classes': 2,
            'transform_channel': 128,
            'backbone': 'resnet50',
            'pretrained': True
        }
        
        self.training_config = {
            'epochs': 30,
            'batch_size': 2,
            'learning_rate': 1e-3,
            'weight_decay': 1e-4,
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'save_interval': 10,  # 增加保存间隔，减少常规checkpoint数量
            'val_interval': 1,
            'save_best_only': True,  # 新增：只保存最佳模型
            'keep_last_n_checkpoints': 3  # 新增：只保留最近3个checkpoint
        }
        
        self.data_config = {
            'data_mode': 'image+mask',  # 'image+shape' or 'image+mask'
            'slice_size': 256,
            'overlap': 32,
            'train_ratio': 0.8,
            'use_augmentation': True,
            'min_foreground_ratio': 0.01
        }
        
        self.paths = {
            'image_path': None,
            'shape_path': None,
            'image_folder': None,
            'mask_folder': None,
            'save_dir': './trained_models/logcan'
        }
    
    def from_dict(self, config_dict):
        """从字典加载配置"""
        if 'model' in config_dict:
            self.model_config.update(config_dict['model'])

            # 特殊处理class_mapping：将字符串格式转换为字典格式
            if 'class_mapping' in config_dict['model']:
                class_mapping = config_dict['model']['class_mapping']
                if isinstance(class_mapping, str) and class_mapping:
                    # 解析字符串格式：如 "建筑物:1,道路:2,植被:3"
                    try:
                        mapping_dict = {}
                        for item in class_mapping.split(','):
                            if ':' in item:
                                key, value = item.strip().split(':', 1)
                                mapping_dict[key.strip()] = int(value.strip())
                        self.model_config['class_mapping'] = mapping_dict
                        print(f"类别映射转换: {class_mapping} -> {mapping_dict}")
                    except Exception as e:
                        print(f"警告: 类别映射解析失败: {e}")
                        self.model_config['class_mapping'] = {}
                elif isinstance(class_mapping, dict):
                    self.model_config['class_mapping'] = class_mapping
                else:
                    self.model_config['class_mapping'] = {}

        if 'training' in config_dict:
            self.training_config.update(config_dict['training'])
        if 'data' in config_dict:
            self.data_config.update(config_dict['data'])
        if 'paths' in config_dict:
            self.paths.update(config_dict['paths'])

        # 兼容旧格式：处理直接在根级别的路径配置
        for key in ['image_path', 'shape_path', 'image_folder', 'mask_folder', 'save_dir']:
            if key in config_dict:
                self.paths[key] = config_dict[key]

    def from_file(self, config_path):
        """从文件加载配置"""
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        self.from_dict(config_dict)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'model': self.model_config,
            'training': self.training_config,
            'data': self.data_config,
            'paths': self.paths
        }

class LogCANDataset(Dataset):
    """LogCAN数据集类，支持image+shape和image+mask两种模式"""
    
    def __init__(self, config, mode='train'):
        self.config = config
        self.mode = mode
        self.data_mode = config.data_config['data_mode']
        self.slice_size = config.data_config['slice_size']
        self.overlap = config.data_config['overlap']
        self.min_foreground_ratio = config.data_config['min_foreground_ratio']

        # 多分类配置
        self.num_classes = config.model_config.get('num_classes', 2)
        self.class_mapping = config.model_config.get('class_mapping', {})
        self.class_field = config.model_config.get('class_field', 'ClassValue')

        self.samples = []
        self.class_weights = None
        self.load_data()
    
    def load_data(self):
        """根据数据模式加载数据"""
        if self.data_mode == 'image+shape':
            self._load_image_shape_data()
        elif self.data_mode == 'image+mask':
            self._load_image_mask_data()
        else:
            raise ValueError(f"不支持的数据模式: {self.data_mode}")
    
    def _load_image_shape_data(self):
        """加载image+shape模式的数据"""
        image_path = self.config.paths['image_path']
        shape_path = self.config.paths['shape_path']
        
        if not image_path or not shape_path:
            raise ValueError("image+shape模式需要提供image_path和shape_path")
        
        print(f"加载图像: {image_path}")
        print(f"加载形状文件: {shape_path}")
        
        # 加载图像
        with rasterio.open(image_path) as src:
            image = src.read().transpose((1, 2, 0))
            transform = src.transform
            crs = src.crs
        
        # 确保图像是3通道
        if image.shape[2] == 1:
            image = np.repeat(image, 3, axis=2)
        elif image.shape[2] > 3:
            image = image[:, :, :3]
        
        # 归一化到0-255
        if image.dtype != np.uint8:
            image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
        
        # 加载形状文件并生成掩码
        gdf = gpd.read_file(shape_path)
        
        # 确保CRS匹配
        if gdf.crs != crs:
            gdf = gdf.to_crs(crs)
        
        # 生成多分类掩码
        mask = self._create_multiclass_mask(gdf, image.shape[:2], transform)

        # 切片处理
        self._create_patches(image, mask)

        # 计算类别权重
        self._compute_class_weights()

    def _create_multiclass_mask(self, gdf, image_shape, transform):
        """创建多分类掩码，借鉴DeepLab的类别映射机制"""
        mask = np.zeros(image_shape, dtype=np.uint8)

        # 检查是否有类别字段
        if self.class_field in gdf.columns:
            print(f"使用类别字段: {self.class_field}")

            # 如果有类别映射配置，使用映射
            if self.class_mapping:
                print(f"使用类别映射: {self.class_mapping}")
                shapes_by_class = []
                for _, row in gdf.iterrows():
                    if row.geometry is not None:
                        original_value = row[self.class_field]

                        # 尝试多种映射方式
                        class_id = None

                        # 1. 直接使用原始值作为键（数字或字符串）
                        if original_value in self.class_mapping:
                            class_id = self.class_mapping[original_value]
                        # 2. 将原始值转换为字符串后查找
                        elif str(original_value) in self.class_mapping:
                            class_id = self.class_mapping[str(original_value)]
                        # 3. 如果原始值是数字，直接使用（保持原始类别ID）
                        elif isinstance(original_value, (int, float)) and 1 <= original_value <= 3:
                            class_id = int(original_value)
                        else:
                            # 默认为类别1
                            class_id = 1
                            print(f"警告: 无法映射类别值 {original_value}，使用默认类别1")

                        shapes_by_class.append((row.geometry, class_id))

                # 输出类别映射统计
                class_stats = {}
                for _, class_id in shapes_by_class:
                    class_stats[class_id] = class_stats.get(class_id, 0) + 1
                print(f"类别映射统计: {class_stats}")
            else:
                # 自动生成类别映射
                unique_classes = gdf[self.class_field].unique()
                print(f"自动生成类别映射，发现类别: {unique_classes}")

                shapes_by_class = []

                # 检查是否是数字类别（如1,2,3）
                if all(isinstance(cls, (int, float)) for cls in unique_classes):
                    print("检测到数字类别，直接使用原始类别ID")
                    for _, row in gdf.iterrows():
                        if row.geometry is not None:
                            original_class = int(row[self.class_field])
                            if 1 <= original_class <= 3:  # 支持类别1、2、3
                                shapes_by_class.append((row.geometry, original_class))
                            else:
                                print(f"警告: 类别 {original_class} 超出范围 [1, 3]，跳过")
                else:
                    # 字符串类别，按顺序分配ID
                    for class_idx, class_name in enumerate(sorted(unique_classes), 1):
                        if class_idx >= self.num_classes:
                            print(f"警告: 类别 {class_name} 超出num_classes限制，跳过")
                            continue

                        class_gdf = gdf[gdf[self.class_field] == class_name]
                        for _, row in class_gdf.iterrows():
                            if row.geometry is not None:
                                shapes_by_class.append((row.geometry, class_idx))

                # 输出类别映射统计
                class_stats = {}
                for _, class_id in shapes_by_class:
                    class_stats[class_id] = class_stats.get(class_id, 0) + 1
                print(f"自动类别映射统计: {class_stats}")

            # 栅格化
            if shapes_by_class:
                mask = rasterize(
                    shapes_by_class,
                    out_shape=image_shape,
                    transform=transform,
                    fill=0,
                    dtype='uint8',
                    all_touched=True
                )
        else:
            print(f"未找到类别字段 {self.class_field}，使用二分类模式")
            # 所有几何体标记为类别1
            mask = rasterize(
                gdf.geometry,
                out_shape=image_shape,
                transform=transform,
                fill=0,
                default_value=1,
                dtype=np.uint8
            )

        # 验证类别分布
        unique_values = np.unique(mask)
        print(f"掩码中的类别: {unique_values}")
        for value in unique_values:
            count = np.sum(mask == value)
            print(f"  类别 {value}: {count} 像素 ({count/mask.size*100:.2f}%)")

        return mask

    def _compute_class_weights(self):
        """计算类别权重，借鉴DeepLab的平衡策略"""
        if not self.samples:
            return

        # 收集所有标签
        all_labels = []
        for sample in self.samples:
            mask = sample['mask']
            labels = mask[mask != 255]  # 排除ignore_index
            all_labels.extend(labels.flatten())

        all_labels = np.array(all_labels)
        unique_labels = np.unique(all_labels)

        if len(unique_labels) <= 1:
            print("只有一个类别，不计算类别权重")
            return

        # 计算平衡权重
        from sklearn.utils.class_weight import compute_class_weight
        class_weights = compute_class_weight(
            'balanced',
            classes=unique_labels,
            y=all_labels
        )

        # 创建权重字典
        self.class_weights = {}
        for label, weight in zip(unique_labels, class_weights):
            self.class_weights[int(label)] = float(weight)

        print(f"计算得到的类别权重: {self.class_weights}")

    def _load_image_mask_data(self):
        """加载image+mask模式的数据"""
        image_folder = self.config.paths['image_folder']
        mask_folder = self.config.paths['mask_folder']
        
        if not image_folder or not mask_folder:
            raise ValueError("image+mask模式需要提供image_folder和mask_folder")
        
        print(f"加载图像文件夹: {image_folder}")
        print(f"加载掩码文件夹: {mask_folder}")
        
        # 获取匹配的图像和掩码文件
        image_files = sorted([f for f in os.listdir(image_folder) 
                             if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))])
        mask_files = sorted([f for f in os.listdir(mask_folder) 
                            if f.lower().endswith(('.jpg', '.png', '.tif', '.tiff'))])
        
        print(f"找到 {len(image_files)} 个图像文件，{len(mask_files)} 个掩码文件")
        
        # 匹配文件对
        matched_pairs = []
        for img_file in image_files:
            img_name = os.path.splitext(img_file)[0]
            for mask_file in mask_files:
                mask_name = os.path.splitext(mask_file)[0]
                if img_name == mask_name:
                    matched_pairs.append((
                        os.path.join(image_folder, img_file),
                        os.path.join(mask_folder, mask_file)
                    ))
                    break
        
        print(f"匹配到 {len(matched_pairs)} 对图像-掩码文件")
        
        if len(matched_pairs) == 0:
            raise ValueError("未找到匹配的图像-掩码文件对")
        
        # 分割训练和验证集
        train_pairs, val_pairs = train_test_split(
            matched_pairs, 
            train_size=self.config.data_config['train_ratio'],
            random_state=42
        )
        
        pairs = train_pairs if self.mode == 'train' else val_pairs
        print(f"{self.mode}模式: 使用 {len(pairs)} 对文件")
        
        # 处理每对文件
        for img_path, mask_path in pairs:
            self._process_file_pair(img_path, mask_path)
    
    def _process_file_pair(self, img_path, mask_path):
        """处理单个图像-掩码文件对"""
        # 加载图像
        if img_path.lower().endswith(('.tif', '.tiff')):
            with rasterio.open(img_path) as src:
                image = src.read().transpose((1, 2, 0))
        else:
            image = np.array(Image.open(img_path).convert('RGB'))
        
        # 加载掩码
        if mask_path.lower().endswith(('.tif', '.tiff')):
            with rasterio.open(mask_path) as src:
                mask = src.read(1)
        else:
            mask = np.array(Image.open(mask_path).convert('L'))
        
        # 确保掩码是二值的
        if mask.max() > 1:
            mask = (mask > 0).astype(np.uint8)
        
        # 切片处理
        self._create_patches(image, mask)
    
    def _create_patches(self, image, mask):
        """创建图像切片"""
        h, w = image.shape[:2]
        step = self.slice_size - self.overlap
        
        for y in range(0, h - self.slice_size + 1, step):
            for x in range(0, w - self.slice_size + 1, step):
                img_patch = image[y:y+self.slice_size, x:x+self.slice_size]
                mask_patch = mask[y:y+self.slice_size, x:x+self.slice_size]
                
                # 检查前景像素比例
                foreground_ratio = np.sum(mask_patch > 0) / (self.slice_size * self.slice_size)
                
                if foreground_ratio >= self.min_foreground_ratio:
                    self.samples.append({
                        'image': img_patch,
                        'mask': mask_patch
                    })
        
        print(f"生成了 {len(self.samples)} 个有效切片")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # 转换为tensor
        image = torch.from_numpy(sample['image']).permute(2, 0, 1).float() / 255.0
        mask = torch.from_numpy(sample['mask']).long()
        
        return image, mask

class ResNetBackbone(nn.Module):
    """ResNet骨干网络"""
    
    def __init__(self, pretrained=True):
        super().__init__()
        import torchvision.models as models
        
        resnet = models.resnet50(pretrained=pretrained)
        
        # 提取特征层
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool
        
        self.layer1 = resnet.layer1  # 256 channels
        self.layer2 = resnet.layer2  # 512 channels
        self.layer3 = resnet.layer3  # 1024 channels
        self.layer4 = resnet.layer4  # 2048 channels
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        feat1 = self.layer1(x)  # 1/4
        feat2 = self.layer2(feat1)  # 1/8
        feat3 = self.layer3(feat2)  # 1/16
        feat4 = self.layer4(feat3)  # 1/32
        
        return [feat1, feat2, feat3, feat4]

class LogCANHead(nn.Module):
    """LogCAN分割头部"""

    def __init__(self, in_channels=[256, 512, 1024, 2048], transform_channel=128, num_classes=2):
        super().__init__()
        self.transform_channel = transform_channel
        self.num_classes = num_classes

        # 特征变换层
        self.bottlenecks = nn.ModuleList([
            self._make_bottleneck(in_ch, transform_channel)
            for in_ch in in_channels
        ])

        # 分类器
        self.classifier = nn.Conv2d(transform_channel, num_classes, kernel_size=1)

        # 上采样层
        self.upsample = nn.Upsample(scale_factor=4, mode='bilinear', align_corners=False)

    def _make_bottleneck(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, features):
        # 处理最高层特征
        feat = self.bottlenecks[-1](features[-1])

        # 逐层融合特征
        for i in range(len(features) - 2, -1, -1):
            feat = F.interpolate(feat, scale_factor=2, mode='bilinear', align_corners=False)
            feat = feat + self.bottlenecks[i](features[i])

        # 分类
        out = self.classifier(feat)
        out = self.upsample(out)

        return out

class LogCANModel(nn.Module):
    """完整的LogCAN模型"""

    def __init__(self, num_classes=2, transform_channel=128, pretrained=True):
        super().__init__()
        self.backbone = ResNetBackbone(pretrained=pretrained)
        self.head = LogCANHead(
            in_channels=[256, 512, 1024, 2048],
            transform_channel=transform_channel,
            num_classes=num_classes
        )

    def forward(self, x):
        features = self.backbone(x)
        out = self.head(features)
        return out

class LogCANTrainer:
    """LogCAN训练器"""

    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.training_config['device'])

        # 创建模型
        self.model = LogCANModel(
            num_classes=config.model_config['num_classes'],
            transform_channel=config.model_config['transform_channel'],
            pretrained=config.model_config['pretrained']
        ).to(self.device)

        # 创建优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config.training_config['learning_rate'],
            weight_decay=config.training_config['weight_decay']
        )

        # 创建损失函数 - 支持类别权重
        self.class_weights = None
        self.criterion = nn.CrossEntropyLoss(ignore_index=255)

    def set_class_weights(self, class_weights):
        """设置类别权重，借鉴DeepLab的权重平衡策略"""
        if class_weights is None:
            return

        self.class_weights = class_weights

        # 创建权重张量
        weight_tensor = torch.ones(self.config.model_config['num_classes'])
        for class_id, weight in class_weights.items():
            if 0 <= class_id < len(weight_tensor):
                weight_tensor[class_id] = weight

        # 更新损失函数
        self.criterion = nn.CrossEntropyLoss(
            weight=weight_tensor.to(self.device),
            ignore_index=255
        )

        print(f"已设置类别权重: {class_weights}")

        # 创建学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.training_config['epochs'],
            eta_min=1e-6
        )

        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_accuracies = []

    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = len(train_loader)

        for batch_idx, (images, masks) in enumerate(train_loader):
            images = images.to(self.device)
            masks = masks.to(self.device)

            # 前向传播
            outputs = self.model(images)
            loss = self.criterion(outputs, masks)

            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()

            # 显示进度
            if batch_idx % 10 == 0:
                print(f"  Batch {batch_idx}/{num_batches}, Loss: {loss.item():.4f}")

        avg_loss = total_loss / num_batches
        self.train_losses.append(avg_loss)
        return avg_loss

    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0

        with torch.no_grad():
            for images, masks in val_loader:
                images = images.to(self.device)
                masks = masks.to(self.device)

                outputs = self.model(images)
                loss = self.criterion(outputs, masks)

                total_loss += loss.item()

                # 计算准确率
                pred = outputs.argmax(dim=1)
                correct += (pred == masks).sum().item()
                total += masks.numel()

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        self.val_losses.append(avg_loss)
        self.val_accuracies.append(accuracy)

        return avg_loss, accuracy

    def save_model(self, save_path, epoch, best=False):
        """保存模型"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'val_accuracies': self.val_accuracies,
            'config': self.config.to_dict()
        }

        if best:
            save_path = save_path.replace('.pth', '_best.pth')

        torch.save(checkpoint, save_path)
        print(f"模型已保存: {save_path}")

def train_logcan_new(config_path, verbose=True):
    """新的LogCAN训练主函数"""
    print("=" * 60)
    print("LogCAN 训练系统 - 新版本")
    print("=" * 60)

    # 导入必要的模块
    import sys
    import os

    # 确保Core目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from LogCAN_Dataset import LogCANDataset
    from LogCAN_Model import create_logcan_model
    from LogCAN_Trainer import LogCANTrainer

    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config_dict = json.load(f)

    config = LogCANConfig()
    config.from_dict(config_dict)

    # 自动补充多分类配置（如果缺失）
    if config.data_config['data_mode'] == 'image+shape' and config.model_config['num_classes'] > 2:
        # 检查是否缺少多分类配置
        if 'class_field' not in config.model_config or not config.model_config['class_field']:
            print("⚠️  检测到缺少多分类配置，尝试自动检测...")

            # 尝试自动检测shapefile中的类别字段和值
            shape_path = config.paths.get('shape_path', '')
            if shape_path and os.path.exists(shape_path):
                try:
                    import geopandas as gpd
                    import pandas as pd
                    gdf = gpd.read_file(shape_path)
                    print(f"📊 分析shapefile: {shape_path}")
                    print(f"   字段列表: {list(gdf.columns)}")

                    # 尝试找到可能的类别字段
                    possible_fields = []
                    for col in gdf.columns:
                        if col.lower() in ['class', 'classvalue', 'type', 'category', 'label']:
                            possible_fields.append(col)
                        elif gdf[col].dtype in ['int64', 'int32', 'object'] and gdf[col].nunique() <= 10:
                            # 数值型或字符型，且唯一值不超过10个
                            possible_fields.append(col)

                    if possible_fields:
                        # 使用第一个可能的字段
                        class_field = possible_fields[0]
                        unique_values = sorted(gdf[class_field].unique())

                        print(f"✅ 自动检测到类别字段: {class_field}")
                        print(f"   唯一值: {unique_values}")

                        # 生成自动映射
                        if all(isinstance(v, (int, float)) for v in unique_values):
                            # 数值型：直接使用数值作为类别ID
                            class_mapping = {f"类别{int(v)}": int(v) for v in unique_values if not pd.isna(v)}
                        else:
                            # 字符型：按字母顺序分配ID
                            class_mapping = {str(v): i+1 for i, v in enumerate(unique_values) if not pd.isna(v)}

                        config.model_config['class_field'] = class_field
                        config.model_config['class_mapping'] = class_mapping
                        config.model_config['use_class_weights'] = True

                        print(f"✅ 已设置自动检测的多分类配置:")
                        print(f"   - class_field: {class_field}")
                        print(f"   - class_mapping: {class_mapping}")
                        print(f"   - 检测到 {len(class_mapping)} 个类别")

                        # 保存自动检测的配置到文件
                        try:
                            config_dict['model']['class_field'] = class_field
                            config_dict['model']['class_mapping'] = class_mapping
                            config_dict['model']['use_class_weights'] = True

                            with open(config_path, 'w', encoding='utf-8') as f:
                                json.dump(config_dict, f, indent=4, ensure_ascii=False)
                            print(f"💾 已保存自动检测的配置到: {config_path}")
                        except Exception as e:
                            print(f"⚠️  保存配置失败: {e}")

                    else:
                        # 找不到合适的字段，使用默认配置
                        print("⚠️  无法自动检测类别字段，使用默认配置")
                        config.model_config['class_field'] = 'ClassValue'
                        config.model_config['class_mapping'] = {'建筑物': 1, '道路': 2, '植被': 3}
                        config.model_config['use_class_weights'] = True
                        print(f"✅ 已设置默认多分类配置:")
                        print(f"   - class_field: ClassValue")
                        print(f"   - class_mapping: {config.model_config['class_mapping']}")

                except Exception as e:
                    print(f"❌ 自动检测失败: {e}")
                    print("⚠️  使用默认配置")
                    config.model_config['class_field'] = 'ClassValue'
                    config.model_config['class_mapping'] = {'建筑物': 1, '道路': 2, '植被': 3}
                    config.model_config['use_class_weights'] = True
            else:
                print("⚠️  shapefile路径无效，使用默认配置")
                config.model_config['class_field'] = 'ClassValue'
                config.model_config['class_mapping'] = {'建筑物': 1, '道路': 2, '植被': 3}
                config.model_config['use_class_weights'] = True

    if verbose:
        print(f"配置加载完成:")
        print(f"- 数据模式: {config.data_config['data_mode']}")
        print(f"- 类别数量: {config.model_config['num_classes']}")
        print(f"- 训练轮数: {config.training_config['epochs']}")
        print(f"- 批次大小: {config.training_config['batch_size']}")
        print(f"- 设备: {config.training_config['device']}")

    # 创建保存目录
    save_dir = config.paths['save_dir']
    os.makedirs(save_dir, exist_ok=True)

    # 创建数据集
    print("\n📊 创建数据集...")
    try:
        if config.data_config['data_mode'] == 'image+mask':
            # image+mask模式：分别创建训练和验证数据集
            from LogCAN_Dataset import LogCANDataProcessor
            processor = LogCANDataProcessor(config)

            # 获取文件对
            pairs_result = processor.load_image_mask_pairs(
                config.paths['image_folder'],
                config.paths['mask_folder']
            )

            # 检查返回结果的类型
            if isinstance(pairs_result, tuple) and pairs_result[0] == "SINGLE_FILE_MODE":
                # 单个大文件模式，使用特殊处理
                print("检测到单个大文件，使用单文件切片模式")
                _, (image_file, mask_file) = pairs_result

                # 创建数据集，传递单文件信息
                train_dataset = LogCANDataset(config, mode='train', single_files=(image_file, mask_file))
                val_dataset = LogCANDataset(config, mode='val', single_files=(image_file, mask_file))

            elif isinstance(pairs_result, tuple) and len(pairs_result) == 2 and pairs_result[0] != "SINGLE_FILE_MODE":
                # 预分割的训练/验证集
                train_pairs, val_pairs = pairs_result
                print("使用预分割的训练/验证集")

                # 创建数据集
                train_dataset = LogCANDataset(config, mode='train', file_pairs=train_pairs)
                val_dataset = LogCANDataset(config, mode='val', file_pairs=val_pairs)
            else:
                # 多文件夹，需要自动分割
                all_pairs = pairs_result
                train_pairs, val_pairs = processor.split_train_val(all_pairs)
                print("自动分割训练/验证集")

                # 创建数据集
                train_dataset = LogCANDataset(config, mode='train', file_pairs=train_pairs)
                val_dataset = LogCANDataset(config, mode='val', file_pairs=val_pairs)
        else:
            # image+shape模式：使用单个数据集
            train_dataset = LogCANDataset(config, mode='train')

            # 创建验证集（使用训练集的一部分）
            total_samples = len(train_dataset.samples)
            val_size = int(total_samples * (1 - config.data_config['train_ratio']))

            if val_size > 0:
                val_samples = train_dataset.samples[-val_size:]
                train_dataset.samples = train_dataset.samples[:-val_size]

                val_dataset = LogCANDataset(config, mode='val')
                val_dataset.samples = val_samples
            else:
                val_dataset = train_dataset

        print(f"✅ 训练集: {len(train_dataset)} 个样本")
        print(f"✅ 验证集: {len(val_dataset)} 个样本")

    except Exception as e:
        print(f"❌ 数据集创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

    # 创建数据加载器
    print("\n🔄 创建数据加载器...")
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training_config['batch_size'],
        shuffle=True,
        num_workers=0,  # Windows兼容性
        pin_memory=True if config.training_config['device'] == 'cuda' else False
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training_config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True if config.training_config['device'] == 'cuda' else False
    )

    # 创建模型
    print("\n🏗️  创建模型...")
    try:
        model = create_logcan_model(config)
        print(f"✅ 模型创建成功")
        print(f"   - 类别数量: {config.model_config['num_classes']}")
        print(f"   - 骨干网络: {config.model_config.get('backbone', 'resnet50')}")
        print(f"   - 变换通道: {config.model_config.get('transform_channel', 128)}")
    except Exception as e:
        print(f"❌ 模型创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

    # 创建训练器
    print("\n⚙️  创建训练器...")
    try:
        trainer = LogCANTrainer(model, config)

        # 设置类别权重（如果训练数据集有计算出权重）
        if hasattr(train_dataset, 'class_weights') and train_dataset.class_weights:
            trainer.set_class_weights(train_dataset.class_weights)
        print(f"✅ 训练器创建成功")
        print(f"   - 优化器: {config.training_config.get('optimizer', {}).get('type', 'Adam')}")
        print(f"   - 学习率: {config.training_config['learning_rate']}")
        print(f"   - 设备: {config.training_config['device']}")
    except Exception as e:
        print(f"❌ 训练器创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

    # 开始训练
    print(f"\n🚀 开始训练")
    print(f"   保存目录: {save_dir}")
    print("=" * 60)

    epochs = config.training_config['epochs']
    val_interval = config.training_config['val_interval']
    save_interval = config.training_config['save_interval']

    try:
        for epoch in range(epochs):
            print(f"\n📅 Epoch {epoch+1}/{epochs}")
            print("=" * 60)

            # 训练阶段
            train_loss, train_metrics = trainer.train_epoch(train_loader, epoch, epochs)

            # 验证阶段
            is_best = False
            if (epoch + 1) % val_interval == 0:
                val_loss, val_metrics, is_best = trainer.validate(val_loader, epoch)

            # 保存模型 - 优化保存策略
            save_best_only = config.training_config.get('save_best_only', False)
            keep_last_n = config.training_config.get('keep_last_n_checkpoints', 3)

            should_save = False
            if save_best_only:
                # 只保存最佳模型
                should_save = is_best
            else:
                # 按间隔保存或最佳模型
                should_save = (epoch + 1) % save_interval == 0 or is_best

            if should_save:
                model_path = os.path.join(save_dir, f'logcan_epoch_{epoch+1}.pth')
                trainer.save_checkpoint(model_path, epoch+1, is_best)

                # 清理旧的checkpoint文件，只保留最近的N个
                if not is_best and keep_last_n > 0:
                    import glob
                    pattern = os.path.join(save_dir, 'logcan_epoch_*.pth')
                    checkpoint_files = glob.glob(pattern)
                    # 排除_best.pth文件
                    checkpoint_files = [f for f in checkpoint_files if '_best.pth' not in f]
                    checkpoint_files.sort(key=lambda x: os.path.getctime(x))

                    # 删除多余的文件
                    while len(checkpoint_files) > keep_last_n:
                        old_file = checkpoint_files.pop(0)
                        try:
                            os.remove(old_file)
                            print(f"  🗑️  删除旧checkpoint: {os.path.basename(old_file)}")
                        except:
                            pass

            # 更新学习率
            if val_interval > 0 and (epoch + 1) % val_interval == 0:
                trainer.update_learning_rate(val_metrics['mean_iou'] if 'val_metrics' in locals() else None)
            else:
                trainer.update_learning_rate()

            # 显示当前最佳结果
            if hasattr(trainer, 'best_val_miou'):
                print(f"\n🏆 当前最佳: Epoch {trainer.best_epoch+1}, mIoU: {trainer.best_val_miou:.4f}")

        # 训练完成
        print("\n" + "=" * 60)
        print("🎉 训练完成！")
        print(f"🏆 最佳结果: Epoch {trainer.best_epoch+1}, mIoU: {trainer.best_val_miou:.4f}")

        # 创建按时间命名的训练报告文件夹
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        reports_base_dir = os.path.join(project_root, 'trained_models', 'training_reports')
        report_dir = os.path.join(reports_base_dir, f'LoGCAN_{timestamp}')

        # 确保报告目录存在
        os.makedirs(report_dir, exist_ok=True)

        # 保存训练曲线和报告到时间命名的文件夹
        curves_path = os.path.join(report_dir, f'training_curves_{timestamp}.png')
        trainer.plot_training_curves(curves_path)

        # 保存训练历史数据（详细的JSON）
        history_path = os.path.join(report_dir, f'training_history_{timestamp}.json')
        trainer.save_training_history(history_path)

        # 生成Markdown格式的训练报告
        md_report_path = os.path.join(report_dir, f'training_report_{timestamp}.md')
        trainer.save_markdown_report(md_report_path, timestamp)

        # 生成HTML格式的训练报告
        html_report_path = os.path.join(report_dir, f'training_report_{timestamp}.html')
        trainer.save_html_report(html_report_path, timestamp)

        print(f"📊 训练报告已保存到: {report_dir}")
        print(f"   📈 训练曲线: training_curves_{timestamp}.png")
        print(f"   📄 Markdown报告: training_report_{timestamp}.md")
        print(f"   🌐 HTML报告: training_report_{timestamp}.html")
        print(f"   📊 训练历史: training_history_{timestamp}.json")

        # 更新配置文件，保存最新的训练路径
        try:
            config_path = os.path.join(project_root, 'config', 'logcan_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 更新路径信息
                if 'paths' not in config_data:
                    config_data['paths'] = {}

                config_data['paths'].update(config.paths)

                # 保存更新后的配置
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=4, ensure_ascii=False)

                print(f"✅ 配置文件已更新: {config_path}")

        except Exception as e:
            print(f"⚠️  更新配置文件失败: {e}")

        print("=" * 60)

        return trainer

    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        # 保存当前状态
        interrupt_path = os.path.join(save_dir, f'logcan_interrupted_epoch_{epoch+1}.pth')
        trainer.save_checkpoint(interrupt_path, epoch+1)
        print(f"💾 已保存中断状态: {interrupt_path}")
        return trainer

    except Exception as e:
        print(f"\n❌ 训练过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

        # 尝试保存当前状态
        try:
            error_path = os.path.join(save_dir, f'logcan_error_epoch_{epoch+1}.pth')
            trainer.save_checkpoint(error_path, epoch+1)
            print(f"💾 已保存错误状态: {error_path}")
        except:
            pass

        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='LogCAN训练 - 新版本')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    try:
        config = train_logcan_new(args.config, args.verbose)
        print("训练配置验证成功！")
    except Exception as e:
        print(f"训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
