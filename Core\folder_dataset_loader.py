#!/usr/bin/env python3
"""
文件夹数据集加载器
支持从image文件夹和mask文件夹加载配对的训练数据
"""

import os
import numpy as np
import rasterio
from pathlib import Path
import cv2
from sklearn.preprocessing import MinMaxScaler
from patchify import patchify
import tensorflow as tf
from tensorflow import keras
try:
    from .image_merger import ImageMerger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    from image_merger import ImageMerger


class FolderDatasetLoader:
    """文件夹数据集加载器"""

    def __init__(self, image_folder, mask_folder, slice_size=256, overlap=32,
                 auto_merge=True, temp_folder=None):
        """
        初始化文件夹数据集加载器

        Args:
            image_folder: 图像文件夹路径
            mask_folder: 掩膜文件夹路径
            slice_size: 切片大小
            overlap: 重叠大小
            auto_merge: 是否自动合并分块图像
            temp_folder: 临时文件夹（用于存储合并后的图像）
        """
        self.image_folder = Path(image_folder)
        self.mask_folder = Path(mask_folder)
        self.slice_size = slice_size
        self.overlap = overlap
        self.auto_merge = auto_merge
        self.temp_folder = Path(temp_folder) if temp_folder else Path("temp_merged")

        # 验证文件夹存在
        if not self.image_folder.exists():
            raise FileNotFoundError(f"图像文件夹不存在: {image_folder}")
        if not self.mask_folder.exists():
            raise FileNotFoundError(f"掩膜文件夹不存在: {mask_folder}")

        # 查找配对的文件
        self.paired_files = self._find_paired_files()

        if not self.paired_files:
            raise ValueError("未找到配对的图像和掩膜文件")

        print(f"找到 {len(self.paired_files)} 对配对文件")
        for img_path, mask_path in self.paired_files:
            print(f"  图像: {img_path.name} <-> 掩膜: {mask_path.name}")

        # 移动尺寸检查到这里，在找到配对文件后进行
        self._validate_dataset_sizes()

    def _validate_dataset_sizes(self):
        """验证数据集中图像和掩膜的尺寸匹配"""
        print("🔍 验证数据集尺寸匹配...")

        if not hasattr(self, 'paired_files') or not self.paired_files:
            return  # 如果还没有配对文件，跳过验证

        size_mismatches = []
        sample_count = min(10, len(self.paired_files))  # 检查前10个文件

        for i, (img_path, mask_path) in enumerate(self.paired_files[:sample_count]):
            try:
                # 读取图像尺寸
                with rasterio.open(img_path) as src:
                    img_shape = (src.height, src.width)

                # 读取掩膜尺寸
                with rasterio.open(mask_path) as src:
                    mask_shape = (src.height, src.width)

                if img_shape != mask_shape:
                    size_mismatches.append({
                        'image': img_path.name,
                        'mask': mask_path.name,
                        'img_size': img_shape,
                        'mask_size': mask_shape
                    })
            except Exception as e:
                print(f"⚠️  检查文件 {img_path.name} 时出错: {e}")

        if size_mismatches:
            print(f"❌ 发现 {len(size_mismatches)} 个尺寸不匹配的文件对:")
            for mismatch in size_mismatches[:3]:  # 只显示前3个
                print(f"   {mismatch['image']} ({mismatch['img_size']}) <-> {mismatch['mask']} ({mismatch['mask_size']})")

            if len(size_mismatches) > 3:
                print(f"   ... 还有 {len(size_mismatches) - 3} 个不匹配")

            print(f"\n💡 建议解决方案:")
            print(f"   1. 运行修复脚本: python fix_dataset_size_mismatch.py")
            print(f"   2. 或者重新下载数据集确保参数一致")

            # 询问是否继续
            response = input("\n是否继续训练? (y/n): ").lower().strip()
            if response not in ['y', 'yes']:
                raise ValueError("数据集尺寸不匹配，训练已取消")
            else:
                print("⚠️  继续训练，但可能影响训练效果")
        else:
            print("✅ 数据集尺寸检查通过")

    def _find_paired_files(self):
        """查找配对的图像和掩膜文件"""
        # 支持的图像格式
        image_exts = ['.tif', '.tiff', '.png', '.jpg', '.jpeg']
        mask_exts = ['.tif', '.tiff', '.png']

        # 获取图像文件
        image_files = []
        for ext in image_exts:
            image_files.extend(self.image_folder.glob(f"*{ext}"))

        # 获取掩膜文件
        mask_files = []
        for ext in mask_exts:
            mask_files.extend(self.mask_folder.glob(f"*{ext}"))

        if not image_files:
            raise FileNotFoundError(f"在图像文件夹中未找到图像文件: {self.image_folder}")
        if not mask_files:
            raise FileNotFoundError(f"在掩膜文件夹中未找到掩膜文件: {self.mask_folder}")

        # 如果启用自动合并，先处理分块图像
        if self.auto_merge:
            image_files = self._merge_fragmented_images(image_files)

        # 按文件名匹配
        paired_files = []

        for img_file in image_files:
            img_base = img_file.stem

            # 查找匹配的掩膜文件
            mask_file = None
            for mask in mask_files:
                mask_base = mask.stem

                # 使用统一的匹配函数
                if self._is_matching_pair(img_base, mask_base):
                    mask_file = mask
                    break

            if mask_file:
                paired_files.append((img_file, mask_file))
                print(f"配对成功: {img_file.name} <-> {mask_file.name}")
            else:
                print(f"警告: 未找到图像 {img_file.name} 对应的掩膜文件")
                # 打印调试信息
                print(f"  可用的掩膜文件:")
                for mask in mask_files:
                    print(f"    - {mask.name}")
                print(f"  图像基础名称: {img_base}")
                print(f"  图像瓦片名称: {self._extract_tile_base_name(img_base)}")

        return paired_files

    def _clean_filename(self, filename):
        """清理文件名，去除常见的后缀"""
        # 去除常见的后缀
        suffixes = ['_merged', '_mask', '_label', '_gt', '_image', '_img']

        clean_name = filename.lower()
        for suffix in suffixes:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)]

        return clean_name

    def _extract_tile_base_name(self, filename):
        """提取瓦片的基础名称，如从 tile_0_0_img-0000000000_merged 提取 tile_0_0"""
        import re

        # 匹配 tile_x_y 模式
        tile_pattern = r'(tile_\d+_\d+)'
        match = re.search(tile_pattern, filename.lower())
        if match:
            return match.group(1)

        # 如果没有匹配到tile模式，返回清理后的文件名
        return self._clean_filename(filename)

    def _is_matching_pair(self, img_name, mask_name):
        """判断图像文件和掩膜文件是否匹配"""
        img_lower = img_name.lower()
        mask_lower = mask_name.lower()

        # 1. 精确匹配
        if img_name == mask_name:
            return True

        # 2. 瓦片基础名称匹配
        img_tile = self._extract_tile_base_name(img_name)
        mask_tile = self._extract_tile_base_name(mask_name)
        if img_tile == mask_tile:
            return True

        # 3. 包含关系匹配
        if img_tile in mask_lower or mask_tile in img_lower:
            return True

        # 4. 清理后的文件名匹配
        img_clean = self._clean_filename(img_name)
        mask_clean = self._clean_filename(mask_name)
        if img_clean == mask_clean:
            return True

        # 5. 特殊情况：img和mask关键词匹配
        img_no_suffix = img_lower.replace('_img', '').replace('_image', '').replace('_merged', '')
        mask_no_suffix = mask_lower.replace('_mask', '').replace('_label', '').replace('_gt', '')
        if img_no_suffix == mask_no_suffix:
            return True

        return False

    def _merge_fragmented_images(self, image_files):
        """合并分块的图像文件"""
        if not image_files:
            return image_files

        print("检查是否需要合并分块图像...")

        # 使用ImageMerger查找相关图像
        merger = ImageMerger()
        try:
            grouped_files = merger.find_related_images(self.image_folder)

            # 检查是否有需要合并的组
            needs_merge = any(len(files) > 1 for files in grouped_files.values())

            if not needs_merge:
                print("未发现需要合并的分块图像")
                return image_files

            print("发现分块图像，开始合并...")

            # 创建临时文件夹
            self.temp_folder.mkdir(exist_ok=True)

            # 合并图像
            merged_results = merger.merge_folder_images(
                self.image_folder,
                self.temp_folder
            )

            # 返回合并后的文件列表
            merged_files = [Path(path) for path in merged_results.values()]
            print(f"图像合并完成，共 {len(merged_files)} 个文件")

            # 打印合并后的文件名，用于调试
            for merged_file in merged_files:
                print(f"  合并后文件: {merged_file.name}")

            return merged_files

        except Exception as e:
            print(f"图像合并失败，使用原始文件: {e}")
            return image_files

    def load_paired_data(self, index):
        """加载指定索引的配对数据"""
        if index >= len(self.paired_files):
            raise IndexError(f"索引超出范围: {index} >= {len(self.paired_files)}")

        img_path, mask_path = self.paired_files[index]

        # 加载图像
        with rasterio.open(img_path) as src:
            image = src.read().transpose((1, 2, 0))
            transform = src.transform
            crs = src.crs

        # 确保图像是3通道
        if image.shape[2] == 1:
            image = np.repeat(image, 3, axis=2)
        elif image.shape[2] > 3:
            image = image[:, :, :3]

        # 加载掩膜
        with rasterio.open(mask_path) as src:
            mask = src.read(1)  # 读取第一个波段

        # 确保掩膜是二值的
        if mask.max() > 1:
            mask = (mask > 0).astype(np.uint8)

        # 检查并修复掩膜中的无效值
        if np.isnan(mask).any():
            print(f"  警告: 掩膜数据包含NaN值，已修复")
            mask = np.nan_to_num(mask, nan=0.0).astype(np.uint8)

        if np.isinf(mask).any():
            print(f"  警告: 掩膜数据包含Inf值，已修复")
            mask = np.nan_to_num(mask, posinf=1.0, neginf=0.0).astype(np.uint8)

        # 确保掩膜值在0-1范围内
        mask = np.clip(mask, 0, 1).astype(np.uint8)

        return image, mask, transform, crs

    def create_training_dataset(self, normalize=True, filter_empty=True,
                              min_foreground_ratio=0.001):
        """
        创建训练数据集

        Args:
            normalize: 是否归一化图像
            filter_empty: 是否过滤空白图像块
            min_foreground_ratio: 最小前景比例

        Returns:
            tuple: (image_patches, mask_patches)
        """
        all_image_patches = []
        all_mask_patches = []

        print(f"开始创建训练数据集，切片大小: {self.slice_size}, 重叠: {self.overlap}")

        for i, (img_path, mask_path) in enumerate(self.paired_files):
            print(f"处理文件对 {i+1}/{len(self.paired_files)}: {img_path.name}")

            # 加载数据
            image, mask, _, _ = self.load_paired_data(i)

            # 归一化图像
            if normalize:
                if image.dtype != np.uint8:
                    # 如果不是uint8，先归一化到0-255
                    scaler = MinMaxScaler(feature_range=(0, 255))
                    image = scaler.fit_transform(image.reshape(-1, image.shape[-1])).reshape(image.shape)
                    image = image.astype(np.uint8)

                # 归一化到0-1
                image = image.astype(np.float32) / 255.0

                # 检查并修复NaN/Inf值
                if np.isnan(image).any():
                    print(f"  警告: 图像数据包含NaN值，已修复")
                    image = np.nan_to_num(image, nan=0.0)

                if np.isinf(image).any():
                    print(f"  警告: 图像数据包含Inf值，已修复")
                    image = np.nan_to_num(image, posinf=1.0, neginf=0.0)

                # 确保数据在合理范围内
                image = np.clip(image, 0.0, 1.0)

            # 创建图像块
            stride = self.slice_size - self.overlap

            # 图像块
            img_patches = patchify(image, (self.slice_size, self.slice_size, image.shape[-1]), step=stride)
            img_patches = img_patches.reshape(-1, self.slice_size, self.slice_size, image.shape[-1])

            # 掩膜块
            mask_expanded = np.expand_dims(mask, axis=-1)
            mask_patches = patchify(mask_expanded, (self.slice_size, self.slice_size, 1), step=stride)
            mask_patches = mask_patches.reshape(-1, self.slice_size, self.slice_size, 1)

            # 确保图像块和掩膜块数量一致
            min_patches = min(len(img_patches), len(mask_patches))
            if len(img_patches) != len(mask_patches):
                print(f"  警告: 图像块数量({len(img_patches)})与掩膜块数量({len(mask_patches)})不一致")
                print(f"  调整为相同数量: {min_patches}")
                img_patches = img_patches[:min_patches]
                mask_patches = mask_patches[:min_patches]

            print(f"  生成 {len(img_patches)} 个图像块")

            # 过滤空白图像块
            if filter_empty:
                print(f"  过滤前: 图像块数量={len(img_patches)}, 掩膜块数量={len(mask_patches)}")

                valid_indices = []
                total_patches = len(mask_patches)

                for j in range(total_patches):
                    foreground_ratio = np.sum(mask_patches[j]) / (self.slice_size * self.slice_size)
                    if foreground_ratio >= min_foreground_ratio:
                        valid_indices.append(j)

                if valid_indices:
                    # 确保索引不超出范围
                    max_index = min(len(img_patches), len(mask_patches)) - 1
                    valid_indices = [idx for idx in valid_indices if idx <= max_index]

                    if valid_indices:
                        img_patches = img_patches[valid_indices]
                        mask_patches = mask_patches[valid_indices]
                        print(f"  保留 {len(valid_indices)}/{total_patches} 个有效图像块")
                    else:
                        print(f"  警告: 所有有效索引都超出范围")
                        continue
                else:
                    print(f"  警告: 所有图像块都被过滤掉了")
                    continue

            all_image_patches.append(img_patches)
            all_mask_patches.append(mask_patches)

        if not all_image_patches:
            raise ValueError("没有生成有效的训练数据")

        # 合并所有图像块
        final_image_patches = np.concatenate(all_image_patches, axis=0)
        final_mask_patches = np.concatenate(all_mask_patches, axis=0)

        print(f"数据集创建完成:")
        print(f"  总图像块数: {len(final_image_patches)}")
        print(f"  图像块形状: {final_image_patches.shape}")
        print(f"  掩膜块形状: {final_mask_patches.shape}")

        return final_image_patches, final_mask_patches

    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_folder.exists():
            import shutil
            shutil.rmtree(self.temp_folder)
            print(f"清理临时文件: {self.temp_folder}")


if __name__ == "__main__":
    # 测试代码
    import sys

    if len(sys.argv) < 3:
        print("用法: python folder_dataset_loader.py <image_folder> <mask_folder>")
        sys.exit(1)

    image_folder = sys.argv[1]
    mask_folder = sys.argv[2]

    try:
        loader = FolderDatasetLoader(image_folder, mask_folder)
        image_patches, mask_patches = loader.create_training_dataset()
        print(f"✅ 数据集加载成功: {len(image_patches)} 个训练样本")
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
