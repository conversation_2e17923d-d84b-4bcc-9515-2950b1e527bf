#!/usr/bin/env python3
"""
内存监控工具
"""

import psutil
import os
import time
import threading
from datetime import datetime

class MemoryMonitor:
    """内存监控类"""
    
    def __init__(self, interval=5):
        """
        初始化内存监控器
        
        Args:
            interval: 监控间隔（秒）
        """
        self.interval = interval
        self.monitoring = False
        self.monitor_thread = None
        self.log_file = f"memory_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
    def get_memory_info(self):
        """获取当前内存信息"""
        # 系统内存信息
        memory = psutil.virtual_memory()
        
        # 当前进程内存信息
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()
        
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'system_total_gb': memory.total / (1024**3),
            'system_available_gb': memory.available / (1024**3),
            'system_used_gb': memory.used / (1024**3),
            'system_percent': memory.percent,
            'process_rss_gb': process_memory.rss / (1024**3),
            'process_vms_gb': process_memory.vms / (1024**3)
        }
    
    def log_memory_info(self, info):
        """记录内存信息到文件"""
        log_line = (
            f"[{info['timestamp']}] "
            f"系统: {info['system_used_gb']:.2f}/{info['system_total_gb']:.2f}GB "
            f"({info['system_percent']:.1f}%) | "
            f"进程: RSS={info['process_rss_gb']:.2f}GB, "
            f"VMS={info['process_vms_gb']:.2f}GB"
        )
        
        print(log_line)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_line + '\n')
    
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                info = self.get_memory_info()
                self.log_memory_info(info)
                
                # 检查内存使用是否过高
                if info['system_percent'] > 90:
                    warning = f"[警告] 系统内存使用率过高: {info['system_percent']:.1f}%"
                    print(warning)
                    with open(self.log_file, 'a', encoding='utf-8') as f:
                        f.write(warning + '\n')
                
                time.sleep(self.interval)
                
            except Exception as e:
                print(f"监控过程中发生错误: {e}")
                break
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            print("监控已经在运行中")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        print(f"开始内存监控，日志文件: {self.log_file}")
        print(f"监控间隔: {self.interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=self.interval + 1)
        print("内存监控已停止")
    
    def get_current_status(self):
        """获取当前状态"""
        info = self.get_memory_info()
        print("\n当前内存状态:")
        print(f"  系统总内存: {info['system_total_gb']:.2f} GB")
        print(f"  系统可用内存: {info['system_available_gb']:.2f} GB")
        print(f"  系统已用内存: {info['system_used_gb']:.2f} GB ({info['system_percent']:.1f}%)")
        print(f"  当前进程内存: {info['process_rss_gb']:.2f} GB")
        return info

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='内存监控工具')
    parser.add_argument('--interval', type=int, default=5, help='监控间隔（秒）')
    parser.add_argument('--status', action='store_true', help='只显示当前状态')
    
    args = parser.parse_args()
    
    monitor = MemoryMonitor(interval=args.interval)
    
    if args.status:
        monitor.get_current_status()
        return
    
    try:
        monitor.start_monitoring()
        print("按 Ctrl+C 停止监控")
        
        # 保持程序运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
