import os
import subprocess
from datetime import datetime

def run_command(command):
    """安全执行 Git 命令"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',  # 显式指定编码
            errors='replace'   # 处理特殊字符
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"错误: {e.stderr.strip()}")
        return None
    except Exception as e:
        print(f"执行命令失败: {str(e)}")
        return None

def format_date(date_str):
    """格式化日期（兼容带时区）"""
    try:
        if '+' in date_str:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S %z').strftime('%Y-%m-%d %H:%M:%S')
        else:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
    except:
        return date_str

def delete_backup():
    """删除指定版本备份"""
    try:
        # 修正路径至E盘
        repo_path = r'D:\Satellite_Image_Auto_Offset\Autooffset_Test'
        if not os.path.exists(repo_path):
            print("错误: 仓库路径不存在")
            return
        os.chdir(repo_path)
        
        # 同步远程标签
        print("正在获取最新备份列表...")
        run_command('git fetch backup --tags --force')
        
        # 获取按时间排序的标签列表
        tags = run_command('git tag --list --sort=-creatordate')
        if not tags:
            print("\n当前没有备份记录")
            return
        
        tag_list = tags.split('\n')
        print("\n=== 备份删除菜单 ===")
        print("ID | 版本号            | 时间                | 描述")
        print("-" * 80)
        
        # 显示带序号的备份列表
        for idx, tag in enumerate(tag_list, 1):
            tag = tag.strip()
            date = run_command(f'git for-each-ref --format="%(taggerdate:iso)" "refs/tags/{tag}"')
            message = run_command(f'git show {tag} --no-patch --format="%(contents:subject)"')
            date_str = format_date(date) if date else "未知时间"
            desc = message if message else "（无描述）"
            print(f"{idx:2} | {tag:15} | {date_str:19} | {desc}")
        
        # 用户输入处理
        print("\n操作选项:")
        print("1. 输入单个/多个ID (例如: 1 或 2,4,5)")
        print("2. 输入 'all' 删除全部")
        print("3. 输入 'q' 退出")
        choice = input("\n请选择要删除的备份ID: ").strip().lower()
        
        if choice == 'q':
            return
        
        # 删除全部备份
        if choice == 'all':
            confirm = input("\n确定要删除所有备份？此操作不可逆！(y/n): ").lower()
            if confirm == 'y':
                print("正在删除所有备份...")
                run_command('git tag -d $(git tag -l)')  # 批量删除本地标签
                run_command('git push backup --delete $(git tag -l)')  # 批量删除远程标签
                print("所有备份已清除")
            return
        
        # 处理多选删除
        try:
            indices = [int(x.strip()) for x in choice.replace(',', ' ').split()]
            if not indices:
                raise ValueError
            if any(i < 1 or i > len(tag_list) for i in indices):
                print("错误: 包含无效的ID")
                return
            
            # 获取待删除标签列表
            tags_to_delete = [tag_list[i-1].strip() for i in indices]
            print("\n即将删除以下备份：")
            for tag in tags_to_delete:
                print(f"- {tag}")
            
            confirm = input("\n确定要永久删除这些备份？(y/n): ").lower()
            if confirm != 'y':
                print("取消操作")
                return
            
            # 执行删除操作
            for tag in tags_to_delete:
                run_command(f'git tag -d {tag}')  # 删除本地标签
                run_command(f'git push backup :refs/tags/{tag}')  # 删除远程标签
            print("已成功删除选定备份")
            
        except ValueError:
            print("错误: 请输入有效数字ID")

    except Exception as e:
        print(f"删除操作失败: {str(e)}")
    finally:
        input("\n按回车键返回主菜单...")

if __name__ == '__main__':
    delete_backup()