import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QComboBox, QDialogButtonBox, QGroupBox,
                            QRadioButton, QMessageBox, QDoubleSpinBox, QSpinBox)
from PyQt5.QtCore import Qt
import geopandas as gpd
from shapely.geometry import Polygon, Point
import numpy as np
import traceback

class GridGeneratorDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("生成网格")
        self.setMinimumWidth(400)
        self.parent = parent
        self.settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'grid_settings.json')
        self.parameters_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'grid_parameters.json')
        self.init_ui()
        self.load_settings()
        self.load_previous_parameters()

    def init_ui(self):
        layout = QVBoxLayout(self)

        # 原点坐标组
        origin_group = QGroupBox("原点坐标")
        origin_layout = QHBoxLayout()
        
        # 东坐标
        self.origin_easting_label = QLabel("东坐标:")
        self.origin_easting_edit = QLineEdit()
        self.origin_easting_edit.setPlaceholderText("输入东坐标")
        origin_layout.addWidget(self.origin_easting_label)
        origin_layout.addWidget(self.origin_easting_edit)
        
        # 北坐标
        self.origin_northing_label = QLabel("北坐标:")
        self.origin_northing_edit = QLineEdit()
        self.origin_northing_edit.setPlaceholderText("输入北坐标")
        origin_layout.addWidget(self.origin_northing_label)
        origin_layout.addWidget(self.origin_northing_edit)
        
        origin_group.setLayout(origin_layout)
        layout.addWidget(origin_group)

        # 网格参数组
        grid_group = QGroupBox("网格参数")
        grid_layout = QVBoxLayout()
        
        # 网格方向
        self.azimuth_label = QLabel("网格方向(度):")
        self.azimuth_edit = QLineEdit()
        self.azimuth_edit.setPlaceholderText("输入网格方向角度")
        grid_layout.addWidget(self.azimuth_label)
        grid_layout.addWidget(self.azimuth_edit)
        
        # 网格间距
        spacing_layout = QHBoxLayout()
        self.track_spacing_label = QLabel("测线间距:")
        self.track_spacing_edit = QLineEdit()
        self.track_spacing_edit.setPlaceholderText("输入测线间距")
        spacing_layout.addWidget(self.track_spacing_label)
        spacing_layout.addWidget(self.track_spacing_edit)
        
        self.bin_spacing_label = QLabel("道间距:")
        self.bin_spacing_edit = QLineEdit()
        self.bin_spacing_edit.setPlaceholderText("输入道间距")
        spacing_layout.addWidget(self.bin_spacing_label)
        spacing_layout.addWidget(self.bin_spacing_edit)
        grid_layout.addLayout(spacing_layout)

        # 网格数量
        number_layout = QHBoxLayout()
        self.num_tracks_label = QLabel("测线数量:")
        self.num_tracks_edit = QLineEdit()
        self.num_tracks_edit.setPlaceholderText("输入测线数量")
        number_layout.addWidget(self.num_tracks_label)
        number_layout.addWidget(self.num_tracks_edit)
        
        self.num_bins_label = QLabel("道数量:")
        self.num_bins_edit = QLineEdit()
        self.num_bins_edit.setPlaceholderText("输入道数量")
        number_layout.addWidget(self.num_bins_label)
        number_layout.addWidget(self.num_bins_edit)
        grid_layout.addLayout(number_layout)

        # 起始编号和步长
        start_number_layout = QHBoxLayout()
        # 起始线号
        self.start_line_label = QLabel("起始线号:")
        self.start_line_edit = QLineEdit()
        self.start_line_edit.setPlaceholderText("输入起始线号")
        start_number_layout.addWidget(self.start_line_label)
        start_number_layout.addWidget(self.start_line_edit)
        
        # 线号步长
        self.line_increment_label = QLabel("线号步长:")
        self.line_increment_edit = QLineEdit()
        self.line_increment_edit.setPlaceholderText("输入线号步长")
        self.line_increment_edit.setText("1")  # 默认值
        start_number_layout.addWidget(self.line_increment_label)
        start_number_layout.addWidget(self.line_increment_edit)
        grid_layout.addLayout(start_number_layout)

        # 起始桩号和步长
        stake_number_layout = QHBoxLayout()
        # 起始桩号
        self.start_stake_label = QLabel("起始桩号:")
        self.start_stake_edit = QLineEdit()
        self.start_stake_edit.setPlaceholderText("输入起始桩号")
        stake_number_layout.addWidget(self.start_stake_label)
        stake_number_layout.addWidget(self.start_stake_edit)
        
        # 桩号步长
        self.stake_increment_label = QLabel("桩号步长:")
        self.stake_increment_edit = QLineEdit()
        self.stake_increment_edit.setPlaceholderText("输入桩号步长")
        self.stake_increment_edit.setText("1")  # 默认值
        stake_number_layout.addWidget(self.stake_increment_label)
        stake_number_layout.addWidget(self.stake_increment_edit)
        grid_layout.addLayout(stake_number_layout)

        grid_group.setLayout(grid_layout)
        layout.addWidget(grid_group)

        # 坐标系统选择
        crs_layout = QHBoxLayout()
        self.crs_label = QLabel("坐标系统:")
        self.crs_combo = QComboBox()
        self.init_crs_combo()
        crs_layout.addWidget(self.crs_label)
        crs_layout.addWidget(self.crs_combo)
        layout.addLayout(crs_layout)

        # 生成方向选择
        direction_group = QGroupBox("生成方向")
        direction_layout = QHBoxLayout()
        self.left_radio = QRadioButton("向左")
        self.right_radio = QRadioButton("向右")
        self.left_radio.setChecked(True)
        direction_layout.addWidget(self.left_radio)
        direction_layout.addWidget(self.right_radio)
        direction_group.setLayout(direction_layout)
        layout.addWidget(direction_group)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def init_crs_combo(self):
        """初始化坐标系统下拉列表"""
        if hasattr(self.parent, 'coordinate_systems'):
            # 添加UTM投影坐标系统
            utm_systems = {
                "UTM Zone 37N (Int 1924)": "EPSG:20437",  # International 1924 UTM Zone 37N
                "UTM Zone 38N (Int 1924)": "EPSG:20438",  # International 1924 UTM Zone 38N
                "UTM Zone 39N (Int 1924)": "EPSG:20439",  # International 1924 UTM Zone 39N
            }
            
            # 添加所有坐标系统
            for name, code in utm_systems.items():
                self.crs_combo.addItem(name, code)
        else:
            # 如果父窗口没有坐标系统列表，使用默认值
            default_crs = {
                "UTM Zone 38N (Int 1924)": "EPSG:20438",  # International 1924 UTM Zone 38N
                "WGS 84": "EPSG:4326",
                "Web Mercator": "EPSG:3857"
            }
            for name, code in default_crs.items():
                self.crs_combo.addItem(name, code)

    def save_settings(self):
        """保存当前设置到文件"""
        settings = {
            'origin_easting': self.origin_easting_edit.text(),
            'origin_northing': self.origin_northing_edit.text(),
            'azimuth': self.azimuth_edit.text(),
            'track_spacing': self.track_spacing_edit.text(),
            'bin_spacing': self.bin_spacing_edit.text(),
            'num_tracks': self.num_tracks_edit.text(),
            'num_bins': self.num_bins_edit.text(),
            'start_line': self.start_line_edit.text(),
            'line_increment': self.line_increment_edit.text(),
            'start_stake': self.start_stake_edit.text(),
            'stake_increment': self.stake_increment_edit.text(),
            'direction': 'left' if self.left_radio.isChecked() else 'right',
            'crs_index': self.crs_combo.currentIndex()
        }
        
        # 确保config目录存在
        os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
        
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
                
            # 同时保存网格参数到grid_parameters.json
            self.save_grid_parameters()
        except Exception as e:
            QMessageBox.warning(self, "保存设置失败", f"无法保存设置：{str(e)}")

    def save_grid_parameters(self):
        """保存网格参数到单独的配置文件，供PointOffsetManager使用"""
        try:
            # 创建网格参数字典
            grid_params = {
                'origin_easting': float(self.origin_easting_edit.text()),
                'origin_northing': float(self.origin_northing_edit.text()),
                'azimuth': float(self.azimuth_edit.text()),
                'track_spacing': float(self.track_spacing_edit.text()),
                'bin_spacing': float(self.bin_spacing_edit.text()),
                'start_line': int(self.start_line_edit.text()),
                'start_stake': int(self.start_stake_edit.text()),
                'line_increment': int(self.line_increment_edit.text()),
                'stake_increment': int(self.stake_increment_edit.text()),
                'direction': 'right' if self.right_radio.isChecked() else 'left',
                'crs': self.crs_combo.currentData(),
                'num_tracks': int(self.num_tracks_edit.text()) if self.num_tracks_edit.text() else 0,
                'num_bins': int(self.num_bins_edit.text()) if self.num_bins_edit.text() else 0,
                'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 确保config目录存在
            os.makedirs(os.path.dirname(self.parameters_file), exist_ok=True)
            
            # 保存到JSON文件
            with open(self.parameters_file, 'w') as f:
                json.dump(grid_params, f, indent=4)
                
            print(f"已保存网格参数到: {self.parameters_file}")
        except Exception as e:
            print(f"保存网格参数时出错: {str(e)}")
            traceback.print_exc()
            QMessageBox.warning(self, "保存网格参数失败", f"无法保存网格参数：{str(e)}")

    def load_settings(self):
        """从文件加载设置"""
        if not os.path.exists(self.settings_file):
            return
            
        try:
            with open(self.settings_file, 'r') as f:
                settings = json.load(f)
                
            self.origin_easting_edit.setText(settings.get('origin_easting', ''))
            self.origin_northing_edit.setText(settings.get('origin_northing', ''))
            self.azimuth_edit.setText(settings.get('azimuth', ''))
            self.track_spacing_edit.setText(settings.get('track_spacing', ''))
            self.bin_spacing_edit.setText(settings.get('bin_spacing', ''))
            self.num_tracks_edit.setText(settings.get('num_tracks', ''))
            self.num_bins_edit.setText(settings.get('num_bins', ''))
            self.start_line_edit.setText(settings.get('start_line', ''))
            self.line_increment_edit.setText(settings.get('line_increment', '1'))
            self.start_stake_edit.setText(settings.get('start_stake', ''))
            self.stake_increment_edit.setText(settings.get('stake_increment', '1'))
            
            if settings.get('direction') == 'right':
                self.right_radio.setChecked(True)
            else:
                self.left_radio.setChecked(True)
                
            crs_index = settings.get('crs_index', 0)
            if crs_index < self.crs_combo.count():
                self.crs_combo.setCurrentIndex(crs_index)
                
        except Exception as e:
            QMessageBox.warning(self, "加载设置失败", f"无法加载设置：{str(e)}")

    def load_previous_parameters(self):
        """从grid_parameters.json加载上一次的网格参数"""
        if not os.path.exists(self.parameters_file):
            return
            
        try:
            with open(self.parameters_file, 'r') as f:
                grid_params = json.load(f)
                
            print(f"已加载上一次的网格参数: {grid_params}")
            
            # 检查并设置参数
            if 'origin_easting' in grid_params:
                self.origin_easting_edit.setText(str(grid_params['origin_easting']))
            if 'origin_northing' in grid_params:
                self.origin_northing_edit.setText(str(grid_params['origin_northing']))
            if 'azimuth' in grid_params:
                self.azimuth_edit.setText(str(grid_params['azimuth']))
            if 'track_spacing' in grid_params:
                self.track_spacing_edit.setText(str(grid_params['track_spacing']))
            if 'bin_spacing' in grid_params:
                self.bin_spacing_edit.setText(str(grid_params['bin_spacing']))
            if 'start_line' in grid_params:
                self.start_line_edit.setText(str(grid_params['start_line']))
            if 'start_stake' in grid_params:
                self.start_stake_edit.setText(str(grid_params['start_stake']))
            if 'line_increment' in grid_params:
                self.line_increment_edit.setText(str(grid_params['line_increment']))
            if 'stake_increment' in grid_params:
                self.stake_increment_edit.setText(str(grid_params['stake_increment']))
            
            # 设置方向
            if 'direction' in grid_params:
                if grid_params['direction'] == 'right':
                    self.right_radio.setChecked(True)
                else:
                    self.left_radio.setChecked(True)
                
        except Exception as e:
            print(f"加载上一次的网格参数时出错: {str(e)}")

    def accept(self):
        """当点击确定按钮时保存设置并关闭对话框"""
        self.save_settings()
        super().accept()

    def get_values(self):
        """获取对话框中的值"""
        try:
            values = {
                'origin_easting': float(self.origin_easting_edit.text()),
                'origin_northing': float(self.origin_northing_edit.text()),
                'azimuth': float(self.azimuth_edit.text()),
                'track_spacing': float(self.track_spacing_edit.text()),
                'bin_spacing': float(self.bin_spacing_edit.text()),
                'num_tracks': int(self.num_tracks_edit.text()),
                'num_bins': int(self.num_bins_edit.text()),
                'start_line': int(self.start_line_edit.text()),
                'line_increment': int(self.line_increment_edit.text()),
                'start_stake': int(self.start_stake_edit.text()),
                'stake_increment': int(self.stake_increment_edit.text()),
                'direction': 'right' if self.right_radio.isChecked() else 'left',
                'crs': self.crs_combo.currentData()
            }
            return values
        except ValueError as e:
            QMessageBox.critical(self, "输入错误", f"请确保所有字段都填写正确：{str(e)}")
            return None

    def generate_grid(self, values):
        """生成网格和点
        网格：相邻格网中心点之间的距离等于道距
        点：沿测线方向间距为道距，垂直测线方向间距为线距
        """
        origin_x = values['origin_easting']
        origin_y = values['origin_northing']
        azimuth = np.radians(values['azimuth'])
        track_spacing = values['track_spacing']  # 测线间距（地距）
        bin_spacing = values['bin_spacing']      # 道间距（道距）
        num_tracks = values['num_tracks']
        num_bins = values['num_bins']
        direction = values['direction']
        crs = values['crs']
        start_line = values['start_line']
        line_increment = values['line_increment']
        start_stake = values['start_stake']
        stake_increment = values['stake_increment']

        # 计算方向向量
        dx = np.cos(azimuth)
        dy = np.sin(azimuth)
        
        # 计算垂直方向向量
        perpendicular_dx = -dy
        perpendicular_dy = dx
        
        if direction == 'right':
            perpendicular_dx = -perpendicular_dx
            perpendicular_dy = -perpendicular_dy

        # 生成网格和点
        grid_cells = []
        grid_points = []
        line_numbers = []
        stake_numbers = []
        point_line_numbers = []
        point_stake_numbers = []
        
        # 使用道距作为网格单元的边长
        cell_size = bin_spacing
        
        # 1. 先生成点（使用原来的逻辑）
        for i in range(num_tracks):
            current_line = start_line + i * line_increment
            
            # 对于点：使用线距作为间距
            point_base_x = origin_x + i * track_spacing * perpendicular_dx
            point_base_y = origin_y + i * track_spacing * perpendicular_dy
            
            for j in range(num_bins):
                current_stake = start_stake + j * stake_increment
                
                # 生成点 -------------------------
                point_x = point_base_x + j * bin_spacing * dx
                point_y = point_base_y + j * bin_spacing * dy
                
                grid_points.append(Point(point_x, point_y))
                point_line_numbers.append(current_line)
                point_stake_numbers.append(current_stake)
        
        # 2. 再生成格网（使用新的密集逻辑）
        grids_between_points = int(track_spacing/bin_spacing + 1)  # 加1是因为要包括起始点线
        num_grid_rows = num_tracks * grids_between_points + 1  # 最后加1是因为要包括最后一条点线
        
        for i in range(num_grid_rows):
            point_line_index = i // grids_between_points
            grid_position = i % grids_between_points
            
            current_line = start_line + point_line_index * line_increment + (grid_position * bin_spacing / track_spacing) * line_increment
            
            # 计算网格基准点
            grid_base_x = origin_x + (point_line_index * track_spacing + grid_position * bin_spacing) * perpendicular_dx
            grid_base_y = origin_y + (point_line_index * track_spacing + grid_position * bin_spacing) * perpendicular_dy
            
            for j in range(num_bins):
                current_stake = start_stake + j * stake_increment
                
                # 生成网格 -------------------------
                # 计算网格中心点
                center_x = grid_base_x + j * bin_spacing * dx
                center_y = grid_base_y + j * bin_spacing * dy
                
                # 计算网格的四个角点
                corners = []
                # 左上角
                corners.append((
                    center_x - cell_size/2 * dx - cell_size/2 * perpendicular_dx,
                    center_y - cell_size/2 * dy - cell_size/2 * perpendicular_dy
                ))
                # 右上角
                corners.append((
                    center_x + cell_size/2 * dx - cell_size/2 * perpendicular_dx,
                    center_y + cell_size/2 * dy - cell_size/2 * perpendicular_dy
                ))
                # 右下角
                corners.append((
                    center_x + cell_size/2 * dx + cell_size/2 * perpendicular_dx,
                    center_y + cell_size/2 * dy + cell_size/2 * perpendicular_dy
                ))
                # 左下角
                corners.append((
                    center_x - cell_size/2 * dx + cell_size/2 * perpendicular_dx,
                    center_y - cell_size/2 * dy + cell_size/2 * perpendicular_dy
                ))
                # 闭合多边形
                corners.append(corners[0])
                
                # 创建网格单元
                cell = Polygon(corners)
                grid_cells.append(cell)
                line_numbers.append(current_line)
                stake_numbers.append(current_stake)

        # 创建网格的GeoDataFrame
        grid_gdf = gpd.GeoDataFrame({
            'geometry': grid_cells,
            'line_number': line_numbers,
            'stake_number': stake_numbers
        }, crs=crs)
        
        # 设置网格样式
        grid_gdf['style'] = {
            'color': '#A0A0A0',  # 浅灰色
            'linestyle': '--',   # 虚线
            'linewidth': 0.5,    # 线宽
            'alpha': 0.7         # 透明度
        }
        
        # 创建点的GeoDataFrame
        point_gdf = gpd.GeoDataFrame({
            'geometry': grid_points,
            'line_number': point_line_numbers,
            'stake_number': point_stake_numbers
        }, crs=crs)
        
        return grid_gdf, point_gdf
