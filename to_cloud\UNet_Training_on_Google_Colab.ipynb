{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🛰️ UNet卫星图像建筑物分割训练 - Google Colab版\n", "\n", "本notebook用于在Google Colab上训练UNet模型，处理从Google Earth Engine导出的卫星图像数据。\n", "\n", "## 📋 使用前准备\n", "1. 确保GEE数据已导出到Google Drive的`GEE_Exports`文件夹\n", "2. 运行时类型选择：**GPU**\n", "3. 挂载Google Drive\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🔧 环境设置和依赖安装"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# 安装必要的依赖包\n", "!pip install rasterio geopandas patchify scikit-learn matplotlib seaborn\n", "!pip install tensorflow-gpu  # 如果需要特定版本的TensorFlow\n", "\n", "# 导入基础库\n", "import os\n", "import sys\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 检查GPU可用性\n", "import tensorflow as tf\n", "print(f\"TensorFlow版本: {tf.__version__}\")\n", "print(f\"GPU可用: {tf.config.list_physical_devices('GPU')}\")\n", "print(f\"CUDA可用: {tf.test.is_built_with_cuda()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "mount_drive"}, "source": ["## 📁 挂载Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount"}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# 设置数据路径\n", "DRIVE_ROOT = '/content/drive/MyDrive'\n", "GEE_EXPORTS_PATH = f'{DRIVE_ROOT}/GEE_Exports'\n", "WORK_DIR = f'{DRIVE_ROOT}/UNet_Training'\n", "\n", "# 创建工作目录\n", "os.makedirs(WORK_DIR, exist_ok=True)\n", "os.makedirs(f'{WORK_DIR}/models', exist_ok=True)\n", "os.makedirs(f'{WORK_DIR}/logs', exist_ok=True)\n", "os.makedirs(f'{WORK_DIR}/processed_data', exist_ok=True)\n", "\n", "print(f\"✅ 工作目录创建完成: {WORK_DIR}\")\n", "print(f\"📁 GEE数据路径: {GEE_EXPORTS_PATH}\")\n", "\n", "# 检查GEE数据是否存在\n", "if os.path.exists(GEE_EXPORTS_PATH):\n", "    files = os.listdir(GEE_EXPORTS_PATH)\n", "    print(f\"📊 发现 {len(files)} 个文件/文件夹\")\n", "    for f in files[:10]:  # 显示前10个\n", "        print(f\"  - {f}\")\n", "    if len(files) > 10:\n", "        print(f\"  ... 还有 {len(files)-10} 个文件\")\n", "else:\n", "    print(\"❌ 未找到GEE_Exports文件夹，请检查数据是否已导出\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_analysis"}, "source": ["## 🔍 数据集分析和预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analyze_data"}, "outputs": [], "source": ["import rasterio\n", "import geopandas as gpd\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "def analyze_gee_data(data_path):\n", "    \"\"\"分析GEE导出的数据\"\"\"\n", "    print(\"🔍 分析GEE导出数据...\")\n", "    \n", "    # 查找图像和掩膜文件\n", "    image_files = []\n", "    mask_files = []\n", "    \n", "    for root, dirs, files in os.walk(data_path):\n", "        for file in files:\n", "            if file.endswith(('.tif', '.tiff')):\n", "                full_path = os.path.join(root, file)\n", "                if 'mask' in file.lower() or 'label' in file.lower():\n", "                    mask_files.append(full_path)\n", "                else:\n", "                    image_files.append(full_path)\n", "    \n", "    print(f\"📊 找到 {len(image_files)} 个图像文件\")\n", "    print(f\"🎭 找到 {len(mask_files)} 个掩膜文件\")\n", "    \n", "    # 分析第一个图像文件\n", "    if image_files:\n", "        sample_image = image_files[0]\n", "        print(f\"\\n📋 分析样本图像: {os.path.basename(sample_image)}\")\n", "        \n", "        with rasterio.open(sample_image) as src:\n", "            print(f\"  尺寸: {src.height} x {src.width}\")\n", "            print(f\"  波段数: {src.count}\")\n", "            print(f\"  数据类型: {src.dtypes[0]}\")\n", "            print(f\"  坐标系: {src.crs}\")\n", "            \n", "            # 读取小样本检查数据质量\n", "            sample_data = src.read(1, window=rasterio.windows.Window(0, 0, 1000, 1000))\n", "            nan_ratio = np.isnan(sample_data).sum() / sample_data.size\n", "            print(f\"  NaN比例: {nan_ratio:.1%}\")\n", "            \n", "            if nan_ratio < 0.5:\n", "                valid_data = sample_data[~np.isnan(sample_data)]\n", "                if len(valid_data) > 0:\n", "                    print(f\"  数据范围: [{valid_data.min():.2f}, {valid_data.max():.2f}]\")\n", "    \n", "    return image_files, mask_files\n", "\n", "# 分析数据\n", "image_files, mask_files = analyze_gee_data(GEE_EXPORTS_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "preprocess_data"}, "outputs": [], "source": ["def convert_and_merge_images(image_files, output_dir):\n", "    \"\"\"转换和合并图像文件\"\"\"\n", "    print(\"🔄 开始图像预处理...\")\n", "    \n", "    processed_images = []\n", "    \n", "    for i, img_file in enumerate(image_files):\n", "        print(f\"处理 {i+1}/{len(image_files)}: {os.path.basename(img_file)}\")\n", "        \n", "        try:\n", "            with rasterio.open(img_file) as src:\n", "                # 读取图像数据\n", "                image_data = src.read()\n", "                profile = src.profile.copy()\n", "                \n", "                # 处理每个波段\n", "                processed_bands = []\n", "                for band_idx in range(image_data.shape[0]):\n", "                    band_data = image_data[band_idx].copy()\n", "                    \n", "                    # 处理NaN值\n", "                    valid_mask = ~np.isnan(band_data)\n", "                    if valid_mask.sum() > 0:\n", "                        valid_data = band_data[valid_mask]\n", "                        \n", "                        # 百分位数裁剪\n", "                        p2, p98 = np.percentile(valid_data, [2, 98])\n", "                        clipped_data = np.clip(valid_data, p2, p98)\n", "                        \n", "                        # 归一化到0-255\n", "                        if p98 > p2:\n", "                            normalized = ((clipped_data - p2) / (p98 - p2) * 255)\n", "                        else:\n", "                            normalized = np.zeros_like(clipped_data)\n", "                        \n", "                        # 创建输出波段\n", "                        output_band = np.zeros_like(band_data, dtype=np.uint8)\n", "                        output_band[valid_mask] = normalized.astype(np.uint8)\n", "                        processed_bands.append(output_band)\n", "                    else:\n", "                        processed_bands.append(np.zeros_like(band_data, dtype=np.uint8))\n", "                \n", "                # 保存处理后的图像\n", "                output_path = os.path.join(output_dir, f\"processed_{os.path.basename(img_file)}\")\n", "                \n", "                profile.update({\n", "                    'dtype': 'uint8',\n", "                    'compress': 'lzw'\n", "                })\n", "                \n", "                with rasterio.open(output_path, 'w', **profile) as dst:\n", "                    for i, band in enumerate(processed_bands, 1):\n", "                        dst.write(band, i)\n", "                \n", "                processed_images.append(output_path)\n", "                print(f\"  ✅ 已保存: {os.path.basename(output_path)}\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  ❌ 处理失败: {e}\")\n", "    \n", "    return processed_images\n", "\n", "# 预处理图像\n", "if image_files:\n", "    processed_images = convert_and_merge_images(image_files, f'{WORK_DIR}/processed_data')\n", "    print(f\"\\n✅ 图像预处理完成，共处理 {len(processed_images)} 个文件\")\n", "else:\n", "    print(\"❌ 未找到图像文件\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_definition"}, "source": ["## 🧠 UNet模型定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "unet_model"}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers, Model\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau\n", "from tensorflow.keras.utils import to_categorical\n", "from sklearn.utils.class_weight import compute_class_weight\n", "\n", "def create_complete_unet(input_shape=(256, 256, 3), num_classes=2):\n", "    \"\"\"创建完整的UNet模型\"\"\"\n", "    inputs = keras.Input(shape=input_shape)\n", "    \n", "    # 编码器 (下采样路径)\n", "    # Block 1\n", "    c1 = layers.Conv2D(64, (3, 3), padding='same')(inputs)\n", "    c1 = layers.BatchNormalization()(c1)\n", "    c1 = layers.Activation('relu')(c1)\n", "    c1 = layers.Conv2D(64, (3, 3), padding='same')(c1)\n", "    c1 = layers.BatchNormalization()(c1)\n", "    c1 = layers.Activation('relu')(c1)\n", "    c1 = layers.Dropout(0.1)(c1)\n", "    p1 = layers.MaxPooling2D((2, 2))(c1)\n", "    \n", "    # Block 2\n", "    c2 = layers.Conv2D(128, (3, 3), padding='same')(p1)\n", "    c2 = layers.BatchNormalization()(c2)\n", "    c2 = layers.Activation('relu')(c2)\n", "    c2 = layers.Conv2D(128, (3, 3), padding='same')(c2)\n", "    c2 = layers.BatchNormalization()(c2)\n", "    c2 = layers.Activation('relu')(c2)\n", "    c2 = layers.Dropout(0.1)(c2)\n", "    p2 = layers.MaxPooling2D((2, 2))(c2)\n", "    \n", "    # Block 3\n", "    c3 = layers.Conv2D(256, (3, 3), padding='same')(p2)\n", "    c3 = layers.BatchNormalization()(c3)\n", "    c3 = layers.Activation('relu')(c3)\n", "    c3 = layers.Conv2D(256, (3, 3), padding='same')(c3)\n", "    c3 = layers.BatchNormalization()(c3)\n", "    c3 = layers.Activation('relu')(c3)\n", "    c3 = layers.Dropout(0.2)(c3)\n", "    p3 = layers.MaxPooling2D((2, 2))(c3)\n", "    \n", "    # Block 4\n", "    c4 = layers.Conv2D(512, (3, 3), padding='same')(p3)\n", "    c4 = layers.BatchNormalization()(c4)\n", "    c4 = layers.Activation('relu')(c4)\n", "    c4 = layers.Conv2D(512, (3, 3), padding='same')(c4)\n", "    c4 = layers.BatchNormalization()(c4)\n", "    c4 = layers.Activation('relu')(c4)\n", "    c4 = layers.Dropout(0.2)(c4)\n", "    p4 = layers.MaxPooling2D((2, 2))(c4)\n", "    \n", "    # 瓶颈层\n", "    c5 = layers.Conv2D(1024, (3, 3), padding='same')(p4)\n", "    c5 = layers.BatchNormalization()(c5)\n", "    c5 = layers.Activation('relu')(c5)\n", "    c5 = layers.Conv2D(1024, (3, 3), padding='same')(c5)\n", "    c5 = layers.BatchNormalization()(c5)\n", "    c5 = layers.Activation('relu')(c5)\n", "    c5 = layers.Dropout(0.3)(c5)\n", "    \n", "    # 解码器 (上采样路径)\n", "    # Block 6\n", "    u6 = layers.Conv2DTranspose(512, (2, 2), strides=(2, 2), padding='same')(c5)\n", "    u6 = layers.concatenate([u6, c4])\n", "    c6 = layers.Conv2D(512, (3, 3), padding='same')(u6)\n", "    c6 = layers.BatchNormalization()(c6)\n", "    c6 = layers.Activation('relu')(c6)\n", "    c6 = layers.Conv2D(512, (3, 3), padding='same')(c6)\n", "    c6 = layers.BatchNormalization()(c6)\n", "    c6 = layers.Activation('relu')(c6)\n", "    c6 = layers.Dropout(0.2)(c6)\n", "    \n", "    # Block 7\n", "    u7 = layers.Conv2DTranspose(256, (2, 2), strides=(2, 2), padding='same')(c6)\n", "    u7 = layers.concatenate([u7, c3])\n", "    c7 = layers.Conv2D(256, (3, 3), padding='same')(u7)\n", "    c7 = layers.BatchNormalization()(c7)\n", "    c7 = layers.Activation('relu')(c7)\n", "    c7 = layers.Conv2D(256, (3, 3), padding='same')(c7)\n", "    c7 = layers.BatchNormalization()(c7)\n", "    c7 = layers.Activation('relu')(c7)\n", "    c7 = layers.Dropout(0.2)(c7)\n", "    \n", "    # Block 8\n", "    u8 = layers.Conv2DTranspose(128, (2, 2), strides=(2, 2), padding='same')(c7)\n", "    u8 = layers.concatenate([u8, c2])\n", "    c8 = layers.Conv2D(128, (3, 3), padding='same')(u8)\n", "    c8 = layers.BatchNormalization()(c8)\n", "    c8 = layers.Activation('relu')(c8)\n", "    c8 = layers.Conv2D(128, (3, 3), padding='same')(c8)\n", "    c8 = layers.BatchNormalization()(c8)\n", "    c8 = layers.Activation('relu')(c8)\n", "    c8 = layers.Dropout(0.1)(c8)\n", "    \n", "    # Block 9\n", "    u9 = layers.Conv2DTranspose(64, (2, 2), strides=(2, 2), padding='same')(c8)\n", "    u9 = layers.concatenate([u9, c1])\n", "    c9 = layers.Conv2D(64, (3, 3), padding='same')(u9)\n", "    c9 = layers.BatchNormalization()(c9)\n", "    c9 = layers.Activation('relu')(c9)\n", "    c9 = layers.Conv2D(64, (3, 3), padding='same')(c9)\n", "    c9 = layers.BatchNormalization()(c9)\n", "    c9 = layers.Activation('relu')(c9)\n", "    c9 = layers.Dropout(0.1)(c9)\n", "    \n", "    # 输出层\n", "    outputs = layers.Conv2D(num_classes, (1, 1), activation='softmax', name='output')(c9)\n", "    \n", "    model = Model(inputs=[inputs], outputs=[outputs], name='Complete_UNet')\n", "    return model\n", "\n", "# 创建模型\n", "print(\"🧠 创建UNet模型...\")\n", "model = create_complete_unet(input_shape=(256, 256, 3), num_classes=2)\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {"id": "loss_functions"}, "source": ["## 📊 损失函数和指标定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "loss_metrics"}, "outputs": [], "source": ["def dice_loss(y_true, y_pred, smooth=1e-5):\n", "    \"\"\"Dice损失函数\"\"\"\n", "    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)\n", "    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)\n", "    \n", "    # 裁剪预测值以避免极值\n", "    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)\n", "    \n", "    intersection = tf.reduce_sum(y_true_f * y_pred_f)\n", "    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f)\n", "    \n", "    dice_coef = (2. * intersection + smooth) / (union + smooth)\n", "    dice_coef = tf.clip_by_value(dice_coef, 1e-7, 1.0 - 1e-7)\n", "    \n", "    return tf.cast(1 - dice_coef, tf.float32)\n", "\n", "def focal_loss(gamma=2., alpha=.25):\n", "    \"\"\"Focal损失函数\"\"\"\n", "    def focal_loss_fixed(y_true, y_pred):\n", "        y_true = tf.cast(y_true, tf.float32)\n", "        y_pred = tf.cast(y_pred, tf.float32)\n", "        \n", "        epsilon = 1e-7\n", "        y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)\n", "        \n", "        pt_1 = tf.where(tf.equal(y_true, 1), y_pred, tf.ones_like(y_pred))\n", "        pt_0 = tf.where(tf.equal(y_true, 0), y_pred, tf.zeros_like(y_pred))\n", "        \n", "        pt_1 = tf.clip_by_value(pt_1, epsilon, 1.0 - epsilon)\n", "        pt_0 = tf.clip_by_value(pt_0, epsilon, 1.0 - epsilon)\n", "        \n", "        loss1 = alpha * tf.pow(1. - pt_1, gamma) * tf.math.log(pt_1)\n", "        loss0 = (1 - alpha) * tf.pow(pt_0, gamma) * tf.math.log(1. - pt_0)\n", "        \n", "        focal_loss_val = -tf.reduce_mean(loss1) - tf.reduce_mean(loss0)\n", "        focal_loss_val = tf.where(tf.math.is_finite(focal_loss_val), focal_loss_val, tf.constant(0.0))\n", "        \n", "        return tf.cast(focal_loss_val, tf.float32)\n", "    return focal_loss_fixed\n", "\n", "def combined_loss(y_true, y_pred):\n", "    \"\"\"组合损失函数\"\"\"\n", "    dice = dice_loss(y_true, y_pred)\n", "    focal = focal_loss(gamma=2., alpha=.25)(y_true, y_pred)\n", "    \n", "    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))\n", "    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))\n", "    \n", "    combined = dice + focal\n", "    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))\n", "    \n", "    return tf.cast(combined, tf.float32)\n", "\n", "def iou_coef(y_true, y_pred, smooth=1e-6):\n", "    \"\"\"IoU系数\"\"\"\n", "    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)\n", "    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)\n", "    \n", "    intersection = tf.reduce_sum(y_true_f * y_pred_f)\n", "    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection\n", "    \n", "    iou = (intersection + smooth) / (union + smooth)\n", "    return tf.cast(iou, tf.float32)\n", "\n", "print(\"📊 损失函数和指标定义完成\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## 📦 数据加载和预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data_loader"}, "outputs": [], "source": ["from patchify import patchify\n", "from sklearn.model_selection import train_test_split\n", "\n", "class DataGenerator(keras.utils.Sequence):\n", "    \"\"\"数据生成器\"\"\"\n", "    def __init__(self, image_dataset, label_dataset, indices, batch_size, augment=False):\n", "        self.image_dataset = image_dataset\n", "        self.label_dataset = label_dataset\n", "        self.indices = indices\n", "        self.batch_size = batch_size\n", "        self.augment = augment\n", "\n", "    def __len__(self):\n", "        return int(np.ceil(len(self.indices) / float(self.batch_size)))\n", "    \n", "    def _augment_data(self, images, masks):\n", "        \"\"\"数据增强\"\"\"\n", "        augmented_images = []\n", "        augmented_masks = []\n", "        \n", "        for img, mask in zip(images, masks):\n", "            # 随机水平翻转\n", "            if np.random.random() > 0.5:\n", "                img = np.fliplr(img)\n", "                mask = np.fliplr(mask)\n", "            \n", "            # 随机垂直翻转\n", "            if np.random.random() > 0.5:\n", "                img = np.flipud(img)\n", "                mask = np.flipud(mask)\n", "            \n", "            # 随机旋转90度\n", "            if np.random.random() > 0.5:\n", "                k = np.random.randint(1, 4)\n", "                img = np.rot90(img, k)\n", "                mask = np.rot90(mask, k)\n", "            \n", "            augmented_images.append(img)\n", "            augmented_masks.append(mask)\n", "        \n", "        return np.array(augmented_images), np.array(augmented_masks)\n", "\n", "    def __getitem__(self, idx):\n", "        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]\n", "        batch_x = self.image_dataset[batch_indices]\n", "        batch_y = self.label_dataset[batch_indices]\n", "        \n", "        if self.augment:\n", "            batch_x, batch_y = self._augment_data(batch_x, batch_y)\n", "\n", "        return batch_x, batch_y\n", "\n", "def create_patches(image, mask, patch_size=256, step=224):\n", "    \"\"\"创建图像块\"\"\"\n", "    print(f\"创建图像块: patch_size={patch_size}, step={step}\")\n", "    \n", "    # 图像块\n", "    img_patches = patchify(image, (patch_size, patch_size, image.shape[2]), step=step)\n", "    img_patches = img_patches.reshape(-1, patch_size, patch_size, image.shape[2])\n", "    \n", "    # 掩膜块\n", "    mask_expanded = np.expand_dims(mask, axis=-1)\n", "    mask_patches = patchify(mask_expanded, (patch_size, patch_size, 1), step=step)\n", "    mask_patches = mask_patches.reshape(-1, patch_size, patch_size, 1)\n", "    \n", "    print(f\"生成 {len(img_patches)} 个图像块\")\n", "    \n", "    # 过滤空白块\n", "    valid_indices = []\n", "    for i in range(len(mask_patches)):\n", "        foreground_ratio = np.sum(mask_patches[i]) / (patch_size * patch_size)\n", "        if foreground_ratio >= 0.001:  # 至少0.1%的前景\n", "            valid_indices.append(i)\n", "    \n", "    if valid_indices:\n", "        img_patches = img_patches[valid_indices]\n", "        mask_patches = mask_patches[valid_indices]\n", "        print(f\"过滤后保留 {len(img_patches)} 个有效图像块\")\n", "    \n", "    return img_patches, mask_patches\n", "\n", "def load_and_process_data(image_files, mask_files):\n", "    \"\"\"加载和处理数据\"\"\"\n", "    print(\"📦 开始加载和处理数据...\")\n", "    \n", "    all_image_patches = []\n", "    all_mask_patches = []\n", "    \n", "    # 处理每对图像和掩膜\n", "    for i, (img_file, mask_file) in enumerate(zip(image_files, mask_files)):\n", "        print(f\"\\n处理文件对 {i+1}/{len(image_files)}\")\n", "        print(f\"图像: {os.path.basename(img_file)}\")\n", "        print(f\"掩膜: {os.path.basename(mask_file)}\")\n", "        \n", "        try:\n", "            # 加载图像\n", "            with rasterio.open(img_file) as src:\n", "                image = src.read().transpose(1, 2, 0)  # HWC格式\n", "                image = image.astype(np.float32) / 255.0\n", "            \n", "            # 加载掩膜\n", "            with rasterio.open(mask_file) as src:\n", "                mask = src.read(1)  # 只读第一个波段\n", "                mask = (mask > 0).astype(np.uint8)  # 确保二值化\n", "            \n", "            print(f\"图像尺寸: {image.shape}\")\n", "            print(f\"掩膜尺寸: {mask.shape}\")\n", "            \n", "            # 创建图像块\n", "            img_patches, mask_patches = create_patches(image, mask)\n", "            \n", "            all_image_patches.append(img_patches)\n", "            all_mask_patches.append(mask_patches)\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 处理失败: {e}\")\n", "            continue\n", "    \n", "    if all_image_patches:\n", "        # 合并所有图像块\n", "        image_dataset = np.concatenate(all_image_patches, axis=0)\n", "        mask_dataset = np.concatenate(all_mask_patches, axis=0)\n", "        \n", "        print(f\"\\n📊 数据集统计:\")\n", "        print(f\"总图像块数: {len(image_dataset)}\")\n", "        print(f\"图像块形状: {image_dataset.shape}\")\n", "        print(f\"掩膜块形状: {mask_dataset.shape}\")\n", "        \n", "        # 转换标签为分类格式\n", "        labels_categorical = to_categorical(mask_dataset, num_classes=2)\n", "        print(f\"分类标签形状: {labels_categorical.shape}\")\n", "        \n", "        return image_dataset, labels_categorical\n", "    else:\n", "        raise ValueError(\"没有成功加载任何数据\")\n", "\n", "print(\"📦 数据加载函数定义完成\")"]}, {"cell_type": "markdown", "metadata": {"id": "training"}, "source": ["## 🚀 模型训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_model"}, "outputs": [], "source": ["# 配置GPU内存增长\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "if gpus:\n", "    try:\n", "        for gpu in gpus:\n", "            tf.config.experimental.set_memory_growth(gpu, True)\n", "        print(f\"✅ GPU配置完成: {len(gpus)} 个GPU\")\n", "    except RuntimeError as e:\n", "        print(f\"❌ GPU配置失败: {e}\")\n", "\n", "# 训练配置\n", "BATCH_SIZE = 8  # Colab GPU内存限制\n", "EPOCHS = 50\n", "LEARNING_RATE = 1e-4\n", "VALIDATION_SPLIT = 0.2\n", "\n", "print(f\"🔧 训练配置:\")\n", "print(f\"  批次大小: {BATCH_SIZE}\")\n", "print(f\"  训练轮数: {EPOCHS}\")\n", "print(f\"  学习率: {LEARNING_RATE}\")\n", "print(f\"  验证集比例: {VALIDATION_SPLIT}\")\n", "\n", "# 加载数据\n", "if processed_images and mask_files:\n", "    # 匹配处理后的图像和掩膜文件\n", "    matched_pairs = []\n", "    for proc_img in processed_images:\n", "        img_name = os.path.basename(proc_img).replace('processed_', '')\n", "        for mask_file in mask_files:\n", "            mask_name = os.path.basename(mask_file)\n", "            if 'tile_0_0' in img_name and 'tile_0_0' in mask_name:\n", "                matched_pairs.append((proc_img, mask_file))\n", "                break\n", "    \n", "    print(f\"\\n📊 找到 {len(matched_pairs)} 对匹配的文件\")\n", "    \n", "    if matched_pairs:\n", "        # 加载数据\n", "        image_dataset, labels_categorical = load_and_process_data(\n", "            [pair[0] for pair in matched_pairs],\n", "            [pair[1] for pair in matched_pairs]\n", "        )\n", "        \n", "        # 数据集划分\n", "        indices = np.arange(len(image_dataset))\n", "        train_indices, val_indices = train_test_split(\n", "            indices, test_size=VALIDATION_SPLIT, random_state=42\n", "        )\n", "        \n", "        print(f\"\\n📊 数据集划分:\")\n", "        print(f\"  训练集: {len(train_indices)} 样本\")\n", "        print(f\"  验证集: {len(val_indices)} 样本\")\n", "        \n", "        # 创建数据生成器\n", "        train_generator = DataGenerator(\n", "            image_dataset, labels_categorical, train_indices,\n", "            batch_size=BATCH_SIZE, augment=True\n", "        )\n", "        \n", "        val_generator = DataGenerator(\n", "            image_dataset, labels_categorical, val_indices,\n", "            batch_size=BATCH_SIZE, augment=False\n", "        )\n", "        \n", "        print(f\"\\n📦 数据生成器创建完成\")\n", "        print(f\"  训练批次数: {len(train_generator)}\")\n", "        print(f\"  验证批次数: {len(val_generator)}\")\n", "        \n", "    else:\n", "        print(\"❌ 未找到匹配的图像和掩膜文件\")\n", "else:\n", "    print(\"❌ 缺少处理后的图像或掩膜文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "compile_train"}, "outputs": [], "source": ["# 编译模型\n", "optimizer = Adam(\n", "    learning_rate=LEARNING_RATE,\n", "    clipnorm=1.0,\n", "    clipvalue=0.5\n", ")\n", "\n", "model.compile(\n", "    optimizer=optimizer,\n", "    loss=combined_loss,\n", "    metrics=['accuracy', iou_coef]\n", ")\n", "\n", "print(\"✅ 模型编译完成\")\n", "\n", "# 设置回调函数\n", "model_save_path = f'{WORK_DIR}/models/unet_model_colab.h5'\n", "\n", "callbacks = [\n", "    ModelCheckpoint(\n", "        model_save_path,\n", "        monitor='val_iou_coef',\n", "        mode='max',\n", "        save_best_only=True,\n", "        verbose=1\n", "    ),\n", "    EarlyStopping(\n", "        monitor='val_iou_coef',\n", "        mode='max',\n", "        patience=15,\n", "        restore_best_weights=True,\n", "        verbose=1\n", "    ),\n", "    ReduceLROnPlateau(\n", "        monitor='val_iou_coef',\n", "        mode='max',\n", "        factor=0.8,\n", "        patience=5,\n", "        min_lr=1e-6,\n", "        verbose=1\n", "    )\n", "]\n", "\n", "print(\"📋 回调函数设置完成\")\n", "\n", "# 开始训练\n", "if 'train_generator' in locals() and 'val_generator' in locals():\n", "    print(\"\\n🚀 开始训练...\")\n", "    \n", "    history = model.fit(\n", "        train_generator,\n", "        epochs=EPOCHS,\n", "        validation_data=val_generator,\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    print(\"\\n🎉 训练完成！\")\n", "    print(f\"模型已保存到: {model_save_path}\")\n", "    \n", "else:\n", "    print(\"❌ 数据生成器未创建，无法开始训练\")"]}, {"cell_type": "markdown", "metadata": {"id": "visualization"}, "source": ["## 📈 训练结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot_results"}, "outputs": [], "source": ["# 绘制训练历史\n", "if 'history' in locals():\n", "    plt.figure(figsize=(15, 5))\n", "    \n", "    # 损失曲线\n", "    plt.subplot(1, 3, 1)\n", "    plt.plot(history.history['loss'], label='训练损失')\n", "    plt.plot(history.history['val_loss'], label='验证损失')\n", "    plt.title('模型损失')\n", "    plt.xlabel('轮数')\n", "    plt.ylabel('损失')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    \n", "    # 准确率曲线\n", "    plt.subplot(1, 3, 2)\n", "    plt.plot(history.history['accuracy'], label='训练准确率')\n", "    plt.plot(history.history['val_accuracy'], label='验证准确率')\n", "    plt.title('模型准确率')\n", "    plt.xlabel('轮数')\n", "    plt.ylabel('准确率')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    \n", "    # IoU曲线\n", "    plt.subplot(1, 3, 3)\n", "    plt.plot(history.history['iou_coef'], label='训练IoU')\n", "    plt.plot(history.history['val_iou_coef'], label='验证IoU')\n", "    plt.title('模型IoU')\n", "    plt.xlabel('轮数')\n", "    plt.ylabel('IoU')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(f'{WORK_DIR}/training_history.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    # 打印最佳结果\n", "    best_epoch = np.argmax(history.history['val_iou_coef'])\n", "    best_val_iou = max(history.history['val_iou_coef'])\n", "    best_val_acc = history.history['val_accuracy'][best_epoch]\n", "    best_val_loss = history.history['val_loss'][best_epoch]\n", "    \n", "    print(f\"\\n🏆 最佳训练结果 (第 {best_epoch + 1} 轮):\")\n", "    print(f\"  验证IoU: {best_val_iou:.4f}\")\n", "    print(f\"  验证准确率: {best_val_acc:.4f}\")\n", "    print(f\"  验证损失: {best_val_loss:.4f}\")\n", "    \n", "else:\n", "    print(\"❌ 没有训练历史数据可供可视化\")"]}, {"cell_type": "markdown", "metadata": {"id": "prediction_demo"}, "source": ["## 🔮 预测演示"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "demo_prediction"}, "outputs": [], "source": ["# 加载最佳模型进行预测演示\n", "if os.path.exists(model_save_path):\n", "    print(\"🔮 加载最佳模型进行预测演示...\")\n", "    \n", "    # 加载模型\n", "    best_model = keras.models.load_model(\n", "        model_save_path,\n", "        custom_objects={\n", "            'combined_loss': combined_loss,\n", "            'iou_coef': iou_coef\n", "        }\n", "    )\n", "    \n", "    # 选择几个验证样本进行预测\n", "    if 'val_generator' in locals() and len(val_generator) > 0:\n", "        sample_batch_x, sample_batch_y = val_generator[0]\n", "        predictions = best_model.predict(sample_batch_x[:4])  # 预测前4个样本\n", "        \n", "        # 可视化预测结果\n", "        fig, axes = plt.subplots(4, 4, figsize=(16, 16))\n", "        \n", "        for i in range(4):\n", "            # 原始图像\n", "            axes[i, 0].imshow(sample_batch_x[i])\n", "            axes[i, 0].set_title('原始图像')\n", "            axes[i, 0].axis('off')\n", "            \n", "            # 真实掩膜\n", "            true_mask = np.argmax(sample_batch_y[i], axis=-1)\n", "            axes[i, 1].imshow(true_mask, cmap='gray')\n", "            axes[i, 1].set_title('真实掩膜')\n", "            axes[i, 1].axis('off')\n", "            \n", "            # 预测掩膜\n", "            pred_mask = np.argmax(predictions[i], axis=-1)\n", "            axes[i, 2].imshow(pred_mask, cmap='gray')\n", "            axes[i, 2].set_title('预测掩膜')\n", "            axes[i, 2].axis('off')\n", "            \n", "            # 叠加显示\n", "            overlay = sample_batch_x[i].copy()\n", "            overlay[:, :, 0] = np.where(pred_mask == 1, 1.0, overlay[:, :, 0])\n", "            axes[i, 3].imshow(overlay)\n", "            axes[i, 3].set_title('预测叠加')\n", "            axes[i, 3].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(f'{WORK_DIR}/prediction_demo.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "        \n", "        print(\"✅ 预测演示完成\")\n", "    else:\n", "        print(\"❌ 没有验证数据可供演示\")\n", "else:\n", "    print(\"❌ 未找到训练好的模型\")"]}, {"cell_type": "markdown", "metadata": {"id": "download_results"}, "source": ["## 📥 下载训练结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "download"}, "outputs": [], "source": ["from google.colab import files\n", "import zipfile\n", "\n", "# 创建结果压缩包\n", "results_zip_path = f'{WORK_DIR}/training_results.zip'\n", "\n", "print(\"📦 创建结果压缩包...\")\n", "\n", "with zipfile.ZipFile(results_zip_path, 'w') as zipf:\n", "    # 添加模型文件\n", "    if os.path.exists(model_save_path):\n", "        zipf.write(model_save_path, 'unet_model_colab.h5')\n", "        print(\"  ✅ 已添加模型文件\")\n", "    \n", "    # 添加训练历史图\n", "    history_plot = f'{WORK_DIR}/training_history.png'\n", "    if os.path.exists(history_plot):\n", "        zipf.write(history_plot, 'training_history.png')\n", "        print(\"  ✅ 已添加训练历史图\")\n", "    \n", "    # 添加预测演示图\n", "    demo_plot = f'{WORK_DIR}/prediction_demo.png'\n", "    if os.path.exists(demo_plot):\n", "        zipf.write(demo_plot, 'prediction_demo.png')\n", "        print(\"  ✅ 已添加预测演示图\")\n", "    \n", "    # 添加训练日志\n", "    if 'history' in locals():\n", "        log_path = f'{WORK_DIR}/training_log.json'\n", "        with open(log_path, 'w') as f:\n", "            json.dump(history.history, f, indent=2)\n", "        zipf.write(log_path, 'training_log.json')\n", "        print(\"  ✅ 已添加训练日志\")\n", "\n", "print(f\"\\n📥 压缩包创建完成: {results_zip_path}\")\n", "\n", "# 下载压缩包\n", "try:\n", "    files.download(results_zip_path)\n", "    print(\"✅ 开始下载训练结果...\")\n", "except Exception as e:\n", "    print(f\"❌ 下载失败: {e}\")\n", "    print(f\"您可以手动从以下路径下载文件: {results_zip_path}\")\n", "\n", "# 显示文件信息\n", "if os.path.exists(results_zip_path):\n", "    file_size = os.path.getsize(results_zip_path) / (1024 * 1024)\n", "    print(f\"\\n📊 压缩包信息:\")\n", "    print(f\"  文件大小: {file_size:.1f} MB\")\n", "    print(f\"  包含内容:\")\n", "    \n", "    with zipfile.ZipFile(results_zip_path, 'r') as zipf:\n", "        for file_info in zipf.filelist:\n", "            print(f\"    - {file_info.filename} ({file_info.file_size / 1024:.1f} KB)\")\n", "\n", "print(\"\\n🎉 训练完成！感谢使用UNet卫星图像分割训练系统！\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}