#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建可查看的掩膜文件
将大型GeoTIFF掩膜转换为普通图像查看器可以打开的格式

支持两种使用方式：
1. 批量处理文件夹下的所有掩膜文件（推荐）
2. 处理单个掩膜文件

使用方法：
python create_viewable_mask.py [mask_folder] [output_folder]
- mask_folder: 包含掩膜文件的文件夹路径（可选，默认使用代码中的路径）
- output_folder: 输出文件夹路径（可选，默认在mask_folder下创建viewable子文件夹）
"""

import os
import sys
import rasterio
import numpy as np
from pathlib import Path
from PIL import Image
import cv2

def create_viewable_mask(input_path, output_folder, create_preview=True):
    """
    创建可查看的掩膜文件
    """
    print(f"处理掩膜文件: {Path(input_path).name}")

    with rasterio.open(input_path) as src:
        mask_data = src.read(1)

        print(f"原始尺寸: {mask_data.shape}")
        print(f"数据范围: [{mask_data.min()}, {mask_data.max()}]")

        # 1. 创建标准TIFF（无地理信息）
        standard_tiff_path = Path(output_folder) / f"standard_{Path(input_path).stem}.tif"

        # 转换为3通道RGB，使用高对比度颜色便于区分
        if mask_data.max() <= 1:
            # 二值掩膜：使用高对比度颜色
            rgb_mask = np.zeros((mask_data.shape[0], mask_data.shape[1], 3), dtype=np.uint8)
            rgb_mask[mask_data == 0] = [34, 139, 34]    # 森林绿 - 背景（可布设区域）
            rgb_mask[mask_data == 1] = [220, 20, 60]    # 深红色 - 障碍物（不可布设区域）
        else:
            # 多值掩膜：使用彩色映射
            unique_vals = np.unique(mask_data)
            rgb_mask = np.zeros((mask_data.shape[0], mask_data.shape[1], 3), dtype=np.uint8)

            # 为不同值分配不同颜色
            colors = [
                [34, 139, 34],   # 森林绿 - 背景
                [220, 20, 60],   # 深红色 - 主要障碍物
                [255, 140, 0],   # 深橙色 - 次要障碍物
                [138, 43, 226],  # 蓝紫色 - 其他类别
                [255, 215, 0],   # 金色 - 其他类别
                [30, 144, 255],  # 道奇蓝 - 其他类别
                [255, 20, 147],  # 深粉色 - 其他类别
                [50, 205, 50],   # 酸橙绿 - 其他类别
            ]

            for i, val in enumerate(unique_vals):
                color = colors[i % len(colors)]
                rgb_mask[mask_data == val] = color

        # 保存为标准TIFF
        os.makedirs(output_folder, exist_ok=True)

        # 使用PIL保存（无地理信息）
        pil_image = Image.fromarray(rgb_mask)
        pil_image.save(standard_tiff_path, format='TIFF', compression='lzw')

        print(f"✅ 标准TIFF已保存: {standard_tiff_path}")

        # 2. 创建PNG版本
        png_path = Path(output_folder) / f"{Path(input_path).stem}.png"
        pil_image.save(png_path, format='PNG')
        print(f"✅ PNG版本已保存: {png_path}")

        # 3. 创建预览图（缩小版本）
        if create_preview:
            # 计算合适的预览尺寸
            max_size = 2048
            height, width = mask_data.shape

            if max(height, width) > max_size:
                if height > width:
                    new_height = max_size
                    new_width = int(width * max_size / height)
                else:
                    new_width = max_size
                    new_height = int(height * max_size / width)

                # 使用OpenCV调整大小
                preview_mask = cv2.resize(rgb_mask, (new_width, new_height),
                                        interpolation=cv2.INTER_NEAREST)

                # 保存预览图
                preview_path = Path(output_folder) / f"preview_{Path(input_path).stem}.png"
                preview_image = Image.fromarray(preview_mask)
                preview_image.save(preview_path, format='PNG')

                print(f"✅ 预览图已保存: {preview_path} (尺寸: {new_width}x{new_height})")
            else:
                print("原图尺寸适中，无需创建预览图")

        # 4. 创建对比图（原始vs彩色）
        create_comparison_image(mask_data, rgb_mask, Path(output_folder) / f"comparison_{Path(input_path).stem}.png")

        # 5. 创建统计信息图
        create_statistics_image(mask_data, Path(output_folder) / f"stats_{Path(input_path).stem}.png")

        return True

def create_comparison_image(mask_data, rgb_mask, output_path):
    """
    创建原始mask和彩色mask的对比图
    """
    import matplotlib.pyplot as plt

    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Mask对比图 - 更容易区分背景和障碍物', fontsize=16, fontweight='bold')

    # 1. 原始mask（灰度）
    im1 = axes[0].imshow(mask_data, cmap='gray', vmin=0, vmax=mask_data.max())
    axes[0].set_title('原始Mask\n(难以区分)', fontsize=12)
    axes[0].axis('off')

    # 添加颜色条
    cbar1 = plt.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)
    cbar1.set_label('像素值', rotation=270, labelpad=15)

    # 2. 彩色mask
    axes[1].imshow(rgb_mask)
    axes[1].set_title('彩色增强Mask\n(容易区分)', fontsize=12)
    axes[1].axis('off')

    # 3. 图例说明
    axes[2].axis('off')

    # 创建图例
    unique_vals = np.unique(mask_data)
    legend_text = "🎨 颜色说明:\n\n"

    if mask_data.max() <= 1:
        legend_text += "🟢 森林绿: 背景区域\n"
        legend_text += "   (数值=0, 可布设区域)\n\n"
        legend_text += "🔴 深红色: 障碍物区域\n"
        legend_text += "   (数值=1, 不可布设区域)\n\n"
    else:
        colors_desc = [
            "🟢 森林绿: 背景",
            "🔴 深红色: 主要障碍物",
            "🟠 深橙色: 次要障碍物",
            "🟣 蓝紫色: 其他类别",
            "🟡 金色: 其他类别",
            "🔵 道奇蓝: 其他类别",
            "🩷 深粉色: 其他类别",
            "🟢 酸橙绿: 其他类别"
        ]

        for i, val in enumerate(unique_vals):
            if i < len(colors_desc):
                legend_text += f"{colors_desc[i]}\n   (数值={val})\n\n"

    legend_text += "💡 使用建议:\n"
    legend_text += "• 使用彩色版本更容易识别\n"
    legend_text += "• 绿色区域 = 可以布设设备\n"
    legend_text += "• 红色区域 = 避免布设设备"

    axes[2].text(0.05, 0.95, legend_text, transform=axes[2].transAxes,
                fontsize=11, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ 对比图已保存: {output_path}")

def create_statistics_image(mask_data, output_path):
    """
    创建掩膜统计信息的可视化图像
    """
    import matplotlib.pyplot as plt

    # 创建统计图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('掩膜文件详细统计信息', fontsize=16, fontweight='bold')

    # 1. 数据分布直方图
    axes[0, 0].hist(mask_data.flatten(), bins=50, alpha=0.7, color='blue')
    axes[0, 0].set_title('像素值分布')
    axes[0, 0].set_xlabel('像素值')
    axes[0, 0].set_ylabel('频次')

    # 2. 缩略图
    if mask_data.shape[0] > 1000 or mask_data.shape[1] > 1000:
        # 缩小显示
        step = max(mask_data.shape[0] // 500, mask_data.shape[1] // 500, 1)
        thumbnail = mask_data[::step, ::step]
    else:
        thumbnail = mask_data

    im = axes[0, 1].imshow(thumbnail, cmap='gray', vmin=0, vmax=mask_data.max())
    axes[0, 1].set_title('掩膜缩略图')
    axes[0, 1].axis('off')
    plt.colorbar(im, ax=axes[0, 1])

    # 3. 统计信息文本
    unique_vals, counts = np.unique(mask_data, return_counts=True)
    stats_text = f"""
掩膜统计信息:
• 尺寸: {mask_data.shape[0]} × {mask_data.shape[1]}
• 数据类型: {mask_data.dtype}
• 数据范围: [{mask_data.min()}, {mask_data.max()}]
• 唯一值数量: {len(unique_vals)}

像素分布:
"""

    for val, count in zip(unique_vals, counts):
        percentage = count / mask_data.size * 100
        stats_text += f"• 值 {val}: {count:,} 像素 ({percentage:.1f}%)\n"

    axes[1, 0].text(0.05, 0.95, stats_text, transform=axes[1, 0].transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
    axes[1, 0].axis('off')

    # 4. 饼图显示比例
    if len(unique_vals) <= 10:  # 只有少量类别时显示饼图
        percentages = counts / mask_data.size * 100
        labels = [f'值 {val}\n({pct:.1f}%)' for val, pct in zip(unique_vals, percentages)]

        axes[1, 1].pie(counts, labels=labels, autopct='', startangle=90)
        axes[1, 1].set_title('像素值比例')
    else:
        axes[1, 1].text(0.5, 0.5, '类别过多\n无法显示饼图',
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].axis('off')

    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ 统计图已保存: {output_path}")

def process_mask_folder(mask_folder, output_folder=None, create_preview=True):
    """
    批量处理文件夹下的所有掩膜文件

    Args:
        mask_folder: 包含掩膜文件的文件夹路径
        output_folder: 输出文件夹路径，如果为None则在mask_folder下创建viewable子文件夹
        create_preview: 是否创建预览图
    """
    mask_path = Path(mask_folder)

    if not mask_path.exists():
        print(f"❌ 掩膜文件夹不存在: {mask_folder}")
        return False

    # 查找所有TIFF掩膜文件
    mask_files = []
    for ext in ['.tif', '.tiff']:
        mask_files.extend(mask_path.glob(f"*{ext}"))
        mask_files.extend(mask_path.glob(f"*{ext.upper()}"))

    if not mask_files:
        print(f"❌ 在文件夹中未找到TIFF掩膜文件: {mask_folder}")
        return False

    # 设置输出文件夹
    if output_folder is None:
        output_folder = mask_path / "viewable"
    else:
        output_folder = Path(output_folder)

    print(f"📁 掩膜文件夹: {mask_folder}")
    print(f"📁 输出文件夹: {output_folder}")
    print(f"📊 找到 {len(mask_files)} 个掩膜文件")
    print()

    # 批量处理所有掩膜文件
    success_count = 0
    failed_count = 0

    for i, mask_file in enumerate(mask_files, 1):
        print(f"[{i}/{len(mask_files)}] 处理: {mask_file.name}")
        print("-" * 50)

        try:
            # 为每个文件创建单独的输出子文件夹
            file_output_folder = output_folder / mask_file.stem
            success = create_viewable_mask(str(mask_file), str(file_output_folder), create_preview)

            if success:
                success_count += 1
                print(f"✅ 处理成功")
            else:
                failed_count += 1
                print(f"❌ 处理失败")

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理失败: {e}")

        print()

    # 处理结果总结
    print("=" * 60)
    print("批量处理完成")
    print("=" * 60)
    print(f"📊 总文件数: {len(mask_files)}")
    print(f"✅ 成功处理: {success_count}")
    print(f"❌ 处理失败: {failed_count}")

    if success_count > 0:
        print(f"\n🎉 成功处理 {success_count} 个掩膜文件！")
        print(f"📁 输出文件保存在: {output_folder}")
        print(f"\n💡 使用建议:")
        print(f"1. 每个掩膜文件都有独立的输出文件夹")
        print(f"2. 🎨 打开 comparison_*.png 查看彩色对比图（推荐！）")
        print(f"3. 📊 打开 stats_*.png 查看详细统计信息")
        print(f"4. 🖼️ 打开 preview_*.png 查看缩略图预览")
        print(f"5. 📁 使用 *.png 文件获得完整尺寸彩色图像")

        # 显示文件夹结构示例
        print(f"\n📂 输出文件夹结构:")
        print(f"{output_folder.name}/")
        for mask_file in mask_files[:3]:  # 显示前3个作为示例
            print(f"├── {mask_file.stem}/")
            print(f"│   ├── 🎨 comparison_{mask_file.stem}.png  (彩色对比图)")
            print(f"│   ├── 🖼️ preview_{mask_file.stem}.png     (缩略图)")
            print(f"│   ├── 📁 {mask_file.stem}.png           (完整彩色图)")
            print(f"│   ├── 📄 standard_{mask_file.stem}.tif   (标准格式)")
            print(f"│   └── 📊 stats_{mask_file.stem}.png      (统计信息)")
        if len(mask_files) > 3:
            print(f"└── ... (还有 {len(mask_files)-3} 个文件夹)")

    return success_count > 0

def process_single_mask(mask_file, output_folder=None, create_preview=True):
    """
    处理单个掩膜文件（向后兼容函数）

    Args:
        mask_file: 掩膜文件路径
        output_folder: 输出文件夹路径，如果为None则在mask文件同级目录创建viewable子文件夹
        create_preview: 是否创建预览图
    """
    mask_path = Path(mask_file)

    if not mask_path.exists():
        print(f"❌ 掩膜文件不存在: {mask_file}")
        return False

    # 设置输出文件夹
    if output_folder is None:
        output_folder = mask_path.parent / "viewable"
    else:
        output_folder = Path(output_folder)

    print("="*60)
    print("创建可查看的掩膜文件（单文件模式）")
    print("="*60)

    try:
        success = create_viewable_mask(str(mask_file), str(output_folder), create_preview)

        if success:
            print(f"\n🎉 转换完成！")
            print(f"可查看的文件保存在: {output_folder}")
            print(f"\n建议:")
            print(f"1. 打开 preview_*.png 查看掩膜预览")
            print(f"2. 打开 stats_*.png 查看统计信息")
            print(f"3. 如果需要完整尺寸，使用 *.png 文件")
            return True
        else:
            print(f"❌ 转换失败")
            return False

    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 默认路径配置 - 用户可以修改这些路径
    default_mask_folder = r"dataset\mask"  # 当前项目的掩码路径

    print("="*60)
    print("批量创建可查看的掩膜文件")
    print("="*60)
    print("本工具将处理指定文件夹下的所有TIFF掩膜文件")
    print("支持当前项目的dataset/mask文件夹")
    print()

    # 解析命令行参数
    if len(sys.argv) >= 2:
        mask_folder = sys.argv[1]
        print(f"📁 使用命令行指定的掩膜文件夹: {mask_folder}")
    else:
        mask_folder = default_mask_folder
        print(f"📁 使用默认掩膜文件夹: {mask_folder}")

    if len(sys.argv) >= 3:
        output_folder = sys.argv[2]
        print(f"📁 使用命令行指定的输出文件夹: {output_folder}")
    else:
        output_folder = None  # None表示在mask文件夹下创建viewable子文件夹
        print(f"📁 输出文件夹: 将在掩膜文件夹下创建viewable子文件夹")

    print()

    # 检查掩膜文件夹是否存在
    if not os.path.exists(mask_folder):
        print(f"❌ 掩膜文件夹不存在: {mask_folder}")
        print()
        print("💡 使用方法:")
        print("1. 确保在项目根目录运行此脚本")
        print("2. 或使用命令行参数: python create_viewable_mask.py <mask_folder> [output_folder]")
        print()
        print("示例:")
        print('python tools/create_viewable_mask.py')
        print('python tools/create_viewable_mask.py "dataset/mask"')
        print('python tools/create_viewable_mask.py "dataset/mask" "dataset/mask_viewable"')
        return

    try:
        success = process_mask_folder(
            mask_folder=mask_folder,
            output_folder=output_folder,
            create_preview=True
        )

        if not success:
            print("❌ 批量处理失败")

    except Exception as e:
        print(f"❌ 批量处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
