import os
import sys
import torch
import numpy as np
from PIL import Image
import warnings
import platform
import multiprocessing

# 抑制警告
warnings.filterwarnings("ignore")

# 注意：旧的rsseg框架已移除，LogCAN现在使用独立的推理系统
# 如需LogCAN推理，请使用Core/LogCAN_Training_New.py中的推理功能

# 检查是否在CPU或GPU模式下运行
if torch.cuda.is_available():
    device_name = torch.cuda.get_device_name(0)
    device_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
    print(f"LoGCAN模块: 检测到GPU: {device_name} ({device_memory:.2f} GB)")
    # 启用CUDA优化
    torch.backends.cudnn.benchmark = True
else:
    # CPU模式设置
    cpu_info = platform.processor()
    cpu_count = multiprocessing.cpu_count()
    print(f"LoGCAN模块: 未检测到GPU，将使用CPU: {cpu_info} ({cpu_count}核)")
    # 为CPU优化线程数
    torch.set_num_threads(multiprocessing.cpu_count())
    print(f"已设置PyTorch使用 {multiprocessing.cpu_count()} 个CPU线程进行计算")

# 旧的rsseg依赖已移除
# LogCAN现在使用独立的训练和推理系统，请参考Core/LogCAN_Training_New.py
print("注意：LogCAN已迁移到独立系统，不再依赖rsseg框架")

class LoGCANInferenceModel:
    """LoGCAN模型推理类，实现与主程序集成的推理功能"""
    
    def __init__(self, config_path, checkpoint_path):
        """
        初始化LoGCAN推理模型
        
        Args:
            config_path: 配置文件路径
            checkpoint_path: 模型检查点路径
        """
        self.config_path = config_path
        self.checkpoint_path = checkpoint_path
        
        # 检查文件是否存在
        for path, name in [(config_path, "配置文件"), (checkpoint_path, "模型检查点")]:
            if not os.path.exists(path):
                raise FileNotFoundError(f"找不到LoGCAN {name}: {path}")
        
        # 设备检测和配置
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if self.device.type == "cuda":
            device_name = torch.cuda.get_device_name(0)
            device_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"LoGCAN模型将使用GPU: {device_name} ({device_memory:.2f} GB)")
        else:
            print(f"LoGCAN模型将使用CPU进行推理")
        
        # 加载模型
        self.model = self._load_model()
        self.model.to(self.device)
        self.model.eval()
        print(f"LoGCAN模型已加载并设置为评估模式，使用设备: {self.device}")
        
        # LoGCAN的颜色映射
        self.color_map = {
            0: [132, 41, 246],  # Land
            1: [60, 16, 152],   # Building
            2: [110, 193, 228], # Road
            3: [254, 221, 58],  # Vegetation
            4: [226, 169, 41],  # Water
            5: [155, 155, 155], # Unlabeled
        }
        
    def _load_model(self):
        """加载LoGCAN模型"""
        print(f"正在加载LoGCAN模型: {self.checkpoint_path}")
        try:
            # 加载配置
            cfg = Config.fromfile(self.config_path)
            print(f"配置文件加载成功: {self.config_path}")
            
            # 创建模型类
            class InferenceModule(LightningModule):
                def __init__(self, config):
                    super().__init__()
                    self.cfg = config
                    self.net = build_model(config.model_config)
                    
                def forward(self, x):
                    pred = self.net(x)
                    return pred[0]  # 只返回主要预测结果
            
            # 初始化模型
            model = InferenceModule(cfg)
            
            # 加载权重
            try:
                print(f"从文件加载模型权重: {self.checkpoint_path}")
                checkpoint = torch.load(self.checkpoint_path, map_location="cpu")
                
                if "state_dict" in checkpoint:
                    model.load_state_dict(checkpoint["state_dict"])
                    print("模型权重从state_dict加载成功")
                else:
                    model.load_state_dict(checkpoint)
                    print("模型权重加载成功")
                
                # 检查模型完整性
                model_param_count = sum(p.numel() for p in model.parameters())
                print(f"模型参数总数: {model_param_count:,}")
                
                return model
            except Exception as e:
                import traceback
                traceback.print_exc()
                raise RuntimeError(f"加载模型权重失败: {str(e)}")
            
        except Exception as e:
            print(f"加载LoGCAN模型失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
        
    def preprocess_image(self, image_path):
        """预处理图像为模型输入格式"""
        try:
            print(f"预处理图像: {image_path}")
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"找不到图像文件: {image_path}")
                
            # 打开图像
            img = Image.open(image_path).convert("RGB")
            
            # 保存原始尺寸用于后续恢复
            self.original_size = img.size
            print(f"原始图像尺寸: {self.original_size}")
            
            # 调整尺寸到模型输入大小
            input_size = 256  # LoGCAN模型输入尺寸
            img = img.resize((input_size, input_size), Image.BILINEAR)
            img_np = np.array(img)
            
            # 标准化
            img_np = img_np / 255.0
            img_np = (img_np - np.array([0.485, 0.456, 0.406])) / np.array([0.229, 0.224, 0.225])
            
            # 转换为张量
            img_tensor = torch.from_numpy(img_np.transpose(2, 0, 1)).float()
            img_tensor = img_tensor.unsqueeze(0)  # 添加批次维度
            
            print(f"预处理后的图像张量形状: {img_tensor.shape}")
            return img_tensor
            
        except Exception as e:
            print(f"图像预处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
        
    def predict(self, image_path):
        """对单个图像进行推理并返回预测结果"""
        print(f"LoGCAN模型开始推理: {image_path}")
        
        try:
            # 预处理图像
            img_tensor = self.preprocess_image(image_path)
            img_tensor = img_tensor.to(self.device)
            
            # 记录内存使用情况
            if self.device.type == "cuda":
                mem_allocated_before = torch.cuda.memory_allocated(0) / (1024**3)
                print(f"推理前GPU内存使用: {mem_allocated_before:.2f} GB")
            
            # 使用适当的方式运行推理
            print("开始执行模型推理...")
            with torch.no_grad():
                # 在GPU上启用混合精度加速推理
                if self.device.type == "cuda" and hasattr(torch.cuda, 'amp') and hasattr(torch.cuda.amp, 'autocast'):
                    with torch.cuda.amp.autocast():
                        output = self.model(img_tensor)
                else:
                    output = self.model(img_tensor)
                
            # 获取推理后内存使用情况
            if self.device.type == "cuda":
                mem_allocated_after = torch.cuda.memory_allocated(0) / (1024**3)
                print(f"推理后GPU内存使用: {mem_allocated_after:.2f} GB")
                print(f"推理过程内存变化: {mem_allocated_after - mem_allocated_before:.2f} GB")
                
            # 获取预测类别
            _, predicted = torch.max(output, dim=1)
            predicted = predicted.cpu().numpy()[0]  # 取第一个批次
            
            # 记录类别分布统计
            unique_classes, counts = np.unique(predicted, return_counts=True)
            class_info = ", ".join([f"类别{c}: {n}像素" for c, n in zip(unique_classes, counts)])
            print(f"预测结果类别分布: {class_info}")
            
            # 恢复原始尺寸
            predicted_img = Image.fromarray(predicted.astype(np.uint8))
            predicted_img = predicted_img.resize(self.original_size, Image.NEAREST)
            predicted = np.array(predicted_img)
            
            print(f"LoGCAN推理完成，输出形状: {predicted.shape}")
            return predicted
            
        except Exception as e:
            print(f"推理过程发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def predict_and_save(self, image_path, output_path):
        """预测并直接保存结果为彩色图像"""
        # 获取预测结果
        prediction = self.predict(image_path)
        
        # 保存彩色结果
        self.save_colored_result(prediction, output_path)
        print(f"预测结果已保存: {output_path}")
        
        return output_path
        
    def save_colored_result(self, prediction, output_path):
        """将预测结果转换为彩色图像并保存"""
        # 创建彩色图像
        height, width = prediction.shape
        rgb_image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 填充颜色
        for label, color in self.color_map.items():
            mask = (prediction == label)
            rgb_image[mask] = color
            
        # 保存图像
        Image.fromarray(rgb_image).save(output_path)
        print(f"彩色分割结果已保存: {output_path}, 尺寸: {width}x{height}")
        
    def predict_batch(self, image_paths, output_dir=None):
        """批量推理多张图像"""
        if not output_dir:
            output_dir = os.path.dirname(image_paths[0])
            
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建输出目录: {output_dir}")
            
        results = []
        
        for i, img_path in enumerate(image_paths):
            print(f"处理图像 {i+1}/{len(image_paths)}: {img_path}")
            
            try:
                # 预测单张图像
                prediction = self.predict(img_path)
                results.append(prediction)
                
                # 保存结果
                base_name = os.path.basename(img_path)
                name_without_ext = os.path.splitext(base_name)[0]
                output_path = os.path.join(output_dir, f"{name_without_ext}_pred.png")
                self.save_colored_result(prediction, output_path)
                print(f"已保存结果图像 {i+1}/{len(image_paths)}: {output_path}")
            except Exception as e:
                print(f"处理图像 {img_path} 时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                
        return results
        
    def cleanup(self):
        """清理模型资源"""
        print("正在清理LoGCAN模型资源...")
        
        if hasattr(self, 'model') and self.model is not None:
            # 如果在GPU上，先移至CPU再删除
            if self.device.type == "cuda":
                self.model.cpu()
            del self.model
            self.model = None
            
            # 清理GPU缓存
            if self.device.type == "cuda":
                before_cleanup = torch.cuda.memory_allocated(0) / (1024**3)
                torch.cuda.empty_cache()
                after_cleanup = torch.cuda.memory_allocated(0) / (1024**3)
                print(f"GPU内存清理: {before_cleanup:.2f} GB -> {after_cleanup:.2f} GB")
        
        print("LoGCAN模型资源已清理完毕") 