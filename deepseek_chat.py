import os
import json
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, 
                           QLineEdit, QPushButton, QLabel, QSpinBox, 
                           QDoubleSpinBox, QToolButton, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon
import requests
from datetime import datetime

class SettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设置")
        self.setFixedSize(300, 200)
        
        layout = QVBoxLayout()
        
        # 温度设置
        temp_layout = QHBoxLayout()
        temp_label = QLabel("温度:")
        self.temp_spin = QDoubleSpinBox()
        self.temp_spin.setRange(0.0, 2.0)
        self.temp_spin.setSingleStep(0.1)
        self.temp_spin.setValue(0.7)
        temp_layout.addWidget(temp_label)
        temp_layout.addWidget(self.temp_spin)
        
        # 最大token设置
        token_layout = QHBoxLayout()
        token_label = QLabel("最大Token:")
        self.token_spin = QSpinBox()
        self.token_spin.setRange(1, 4096)
        self.token_spin.setValue(2048)
        token_layout.addWidget(token_label)
        token_layout.addWidget(self.token_spin)
        
        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.accept)
        
        layout.addLayout(temp_layout)
        layout.addLayout(token_layout)
        layout.addWidget(ok_button)
        
        self.setLayout(layout)

class ChatMessage(QWidget):
    def __init__(self, text, is_user=True, parent=None):
        super().__init__(parent)
        layout = QHBoxLayout()
        
        msg = QTextEdit()
        msg.setReadOnly(True)
        msg.setPlainText(text)
        msg.setStyleSheet(
            "QTextEdit { background-color: #DCF8C6; }" if is_user else
            "QTextEdit { background-color: #E8E8E8; }"
        )
        
        if is_user:
            layout.addStretch()
            layout.addWidget(msg)
        else:
            layout.addWidget(msg)
            layout.addStretch()
            
        self.setLayout(layout)

class DeepSeekChatDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("DeepSeek 聊天")
        self.resize(600, 800)
        
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        self.temperature = 0.7
        self.max_tokens = 2048
        self.total_cost = 0.0
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 顶部工具栏
        toolbar = QHBoxLayout()
        
        # API消费显示
        self.cost_label = QLabel(f"API消费: ${self.total_cost:.4f}")
        toolbar.addWidget(self.cost_label)
        
        # 设置按钮
        settings_btn = QToolButton()
        settings_btn.setIcon(QIcon("icons/settings.png"))
        settings_btn.clicked.connect(self.show_settings)
        toolbar.addWidget(settings_btn)
        
        layout.addLayout(toolbar)
        
        # 聊天记录区域
        self.chat_area = QVBoxLayout()
        chat_widget = QWidget()
        chat_widget.setLayout(self.chat_area)
        
        scroll_area = QTextEdit()
        scroll_area.setReadOnly(True)
        scroll_area.setWidget(chat_widget)
        layout.addWidget(scroll_area)
        
        # 输入区域
        input_layout = QHBoxLayout()
        self.input_edit = QLineEdit()
        self.input_edit.returnPressed.connect(self.send_message)
        send_btn = QPushButton("发送")
        send_btn.clicked.connect(self.send_message)
        
        input_layout.addWidget(self.input_edit)
        input_layout.addWidget(send_btn)
        
        layout.addLayout(input_layout)
        
        self.setLayout(layout)
    
    def show_settings(self):
        dialog = SettingsDialog(self)
        if dialog.exec_():
            self.temperature = dialog.temp_spin.value()
            self.max_tokens = dialog.token_spin.value()
    
    def add_message(self, text, is_user=True):
        message = ChatMessage(text, is_user)
        self.chat_area.addWidget(message)
    
    def send_message(self):
        user_input = self.input_edit.text().strip()
        if not user_input:
            return
            
        self.add_message(user_input, True)
        self.input_edit.clear()
        
        # 调用DeepSeek API
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "messages": [{"role": "user", "content": user_input}],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens
            }
            
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            response_data = response.json()
            bot_response = response_data["choices"][0]["message"]["content"]
            
            # 计算API消费
            prompt_tokens = response_data["usage"]["prompt_tokens"]
            completion_tokens = response_data["usage"]["completion_tokens"]
            cost = (prompt_tokens * 0.00002) + (completion_tokens * 0.00002)
            self.total_cost += cost
            self.cost_label.setText(f"API消费: ${self.total_cost:.4f}")
            
            self.add_message(bot_response, False)
            
        except Exception as e:
            self.add_message(f"错误: {str(e)}", False)
