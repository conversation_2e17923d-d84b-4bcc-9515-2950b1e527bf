from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSpinBox, QPushButton, QGroupBox, 
                            QFormLayout, QLineEdit, QFileDialog,
                            QMessageBox)
from PyQt5.QtCore import Qt
import os

class GenerateDatasetDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("生成数据集设置")
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 保存路径组
        save_group = QGroupBox("保存设置")
        save_layout = QFormLayout()

        # 保存目录
        dir_layout = QHBoxLayout()
        self.save_dir = QLineEdit()
        # 设置默认保存目录为当前目录下的 dataset 文件夹
        default_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dataset')
        self.save_dir.setText(default_dir)

        browse_dir_button = QPushButton("浏览...")
        browse_dir_button.clicked.connect(self.browse_save_dir)

        dir_layout.addWidget(self.save_dir)
        dir_layout.addWidget(browse_dir_button)
        save_layout.addRow("保存目录:", dir_layout)

        # 文件名
        self.filename = QLineEdit()
        # 设置默认文件名（带时间戳）
        from datetime import datetime
        default_filename = f"satellite_image_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.filename.setText(default_filename)
        self.filename.setPlaceholderText("输入文件名（不含扩展名）")
        save_layout.addRow("文件名:", self.filename)

        save_group.setLayout(save_layout)
        
        # 数据处理组
        data_group = QGroupBox("数据处理设置")
        data_layout = QFormLayout()
        
        self.slice_size = QSpinBox()
        self.slice_size.setRange(32, 4096)
        self.slice_size.setValue(256)
        self.slice_size.setSingleStep(32)
        data_layout.addRow("切片大小:", self.slice_size)
        
        self.overlap = QSpinBox()
        self.overlap.setRange(0, 100)
        self.overlap.setValue(20)
        self.overlap.setSuffix("%")
        data_layout.addRow("重叠比例:", self.overlap)
        
        data_group.setLayout(data_layout)
        
        # 添加所有组到主布局
        layout.addWidget(save_group)
        layout.addWidget(data_group)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("开始生成")
        self.cancel_button = QPushButton("取消")
        self.ok_button.clicked.connect(self.validate_and_accept)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

    def browse_save_dir(self):
        """打开文件夹选择对话框"""
        current_path = self.save_dir.text()
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "选择数据集保存目录",
            current_path,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )
        if folder_path:
            self.save_dir.setText(folder_path)

    def validate_and_accept(self):
        """验证保存设置并接受对话框"""
        save_dir = self.save_dir.text().strip()
        filename = self.filename.text().strip()

        # 检查目录是否为空
        if not save_dir:
            QMessageBox.critical(self, "错误", "请选择保存目录")
            return

        # 检查文件名是否为空
        if not filename:
            QMessageBox.critical(self, "错误", "请输入文件名")
            return

        # 检查文件名是否包含非法字符
        import re
        if not re.match(r'^[a-zA-Z0-9_\-\u4e00-\u9fff]+$', filename):
            QMessageBox.critical(self, "错误", "文件名只能包含字母、数字、下划线、连字符和中文字符")
            return

        # 检查目录是否存在，如果不存在则创建
        if not os.path.exists(save_dir):
            try:
                os.makedirs(save_dir)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法创建保存目录：{str(e)}")
                return

        # 检查目录是否可写
        if not os.access(save_dir, os.W_OK):
            QMessageBox.critical(self, "错误", "保存目录不可写，请选择其他目录")
            return

        # 检查文件是否已存在
        full_path = os.path.join(save_dir, f"{filename}.tif")
        if os.path.exists(full_path):
            reply = QMessageBox.question(
                self, "文件已存在",
                f"文件 '{filename}.tif' 已存在，是否覆盖？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return

        self.accept()
    
    def get_settings(self):
        """获取所有设置参数"""
        save_dir = self.save_dir.text().strip()
        filename = self.filename.text().strip()

        return {
            "save_dir": save_dir,
            "filename": filename,
            "save_path": os.path.join(save_dir, f"{filename}.tif"),  # 完整文件路径
            "slice_size": self.slice_size.value(),
            "overlap": self.overlap.value()
        }