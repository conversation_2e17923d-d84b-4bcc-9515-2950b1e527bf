{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🛰️ UNet卫星图像建筑物分割训练 - 全部按需加载模式\n", "\n", "本notebook使用**全部按需加载模式**进行训练，充分利用云端资源获得最佳训练效果\n", "\n", "## 📋 主要特点\n", "- **🚀 全部按需加载**: 使用所有可用的图像块，不限制数据量\n", "- **🔧 图像合并**: 自动检测并合并4个子瓦片为1个完整图像\n", "- **💾 数据生成器**: 使用数据生成器，按需加载数据\n", "- **🎯 智能配对**: 改进的image-mask文件配对逻辑\n", "- **⚡ 混合精度**: 支持混合精度训练加速\n", "- **🌐 云端优化**: 针对云端环境优化的参数配置\n", "- **📈 完整监控**: 包含多种训练指标\n", "\n", "## ⚙️ 训练模式说明\n", "- **use_all_data = True**: 使用所有可用的图像块\n", "- **max_patches_per_file = None**: 不限制每个文件的图像块数量\n", "- **max_total_patches = None**: 不限制总图像块数量\n", "- **适用场景**: 云端训练、追求最佳效果、GPU内存充足\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 环境设置和依赖安装"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 安装兼容的TensorFlow版本\n", "! pip uninstall tensorflow -y -q\n", "! pip install tensorflow==2.15.0 -q\n", "print(\"✅ 已安装TensorFlow 2.15.0\")\n", "\n", "# 安装必要的依赖包\n", "import sys\n", "\n", "required_packages = [\n", "    'rasterio', 'geopandas', 'patchify', 'scikit-learn', \n", "    'matplotlib', 'seaborn', 'tensorflow', 'opencv-python', 'psutil'\n", "]\n", "\n", "for package in required_packages:\n", "    try:\n", "        __import__(package.replace('-', '_'))\n", "    except ImportError:\n", "        print(f\"安装 {package}...\")\n", "        !{sys.executable} -m pip install {package}\n", "\n", "# 导入基础库\n", "import os\n", "import sys\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "import psutil\n", "import gc\n", "warnings.filterwarnings('ignore')\n", "\n", "# 检查TensorFlow和GPU\n", "import tensorflow as tf\n", "print(f\"TensorFlow版本: {tf.__version__}\")\n", "print(f\"GPU可用: {tf.config.list_physical_devices('GPU')}\")\n", "print(f\"CUDA可用: {tf.test.is_built_with_cuda()}\")\n", "\n", "# 配置GPU内存增长\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "if gpus:\n", "    try:\n", "        for gpu in gpus:\n", "            tf.config.experimental.set_memory_growth(gpu, True)\n", "        print(f\"✅ GPU配置完成: {len(gpus)} 个GPU\")\n", "    except RuntimeError as e:\n", "        print(f\"❌ GPU配置失败: {e}\")\n", "\n", "# 内存监控函数\n", "def monitor_memory():\n", "    \"\"\"监控内存使用情况\"\"\"\n", "    memory = psutil.virtual_memory()\n", "    print(f\"📊 内存状态:\")\n", "    print(f\"  总内存: {memory.total/1024**3:.1f} GB\")\n", "    print(f\"  已用内存: {memory.used/1024**3:.1f} GB\")\n", "    print(f\"  可用内存: {memory.available/1024**3:.1f} GB\")\n", "    print(f\"  使用率: {memory.percent:.1f}%\")\n", "    \n", "    if memory.percent > 85:\n", "        print(\"⚠️  内存使用率过高\")\n", "    elif memory.percent > 75:\n", "        print(\"🔶 内存使用率较高\")\n", "    else:\n", "        print(\"✅ 内存使用正常\")\n", "\n", "print(\"\\n✅ 环境设置完成\")\n", "monitor_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📁 数据路径配置 - **请根据您的平台修改此部分**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==========================================\n", "# 📍 数据路径配置 - 请根据您的平台修改\n", "# ==========================================\n", "\n", "# 🔧 平台检测和路径配置\n", "def detect_platform_and_configure_paths():\n", "    \"\"\"自动检测平台并配置路径\"\"\"\n", "    \n", "    # Kaggle平台检测\n", "    if '/kaggle/' in os.getcwd():\n", "        print(\"🔍 检测到Kaggle平台\")\n", "        return {\n", "            'platform': 'kaggle',\n", "            'image_folder': '/kaggle/input/your-dataset/converted/image',\n", "            'mask_folder': '/kaggle/input/your-dataset/converted/mask',\n", "            'output_folder': '/kaggle/working/models'\n", "        }\n", "    \n", "    # Google Colab检测\n", "    elif 'google.colab' in sys.modules:\n", "        print(\"🔍 检测到Google Colab平台\")\n", "        return {\n", "            'platform': 'colab',\n", "            'image_folder': '/content/drive/MyDrive/dataset/converted/image',\n", "            'mask_folder': '/content/drive/MyDrive/dataset/converted/mask',\n", "            'output_folder': '/content/drive/MyDrive/models'\n", "        }\n", "    \n", "    # Azure ML检测\n", "    elif 'AZUREML_' in os.environ:\n", "        print(\"🔍 检测到Azure ML平台\")\n", "        return {\n", "            'platform': 'azure',\n", "            'image_folder': './data/converted/image',\n", "            'mask_folder': './data/converted/mask',\n", "            'output_folder': './outputs/models'\n", "        }\n", "    \n", "    # 默认本地环境\n", "    else:\n", "        print(\"🔍 使用默认本地环境配置\")\n", "        return {\n", "            'platform': 'local',\n", "            'image_folder': './workspace/converted/image',\n", "            'mask_folder': './workspace/converted/mask',\n", "            'output_folder': '/workspace/training_output'\n", "        }\n", "\n", "# 获取平台配置\n", "config = detect_platform_and_configure_paths()\n", "\n", "# 📁 数据路径设置 - 请根据实际情况修改\n", "IMAGE_FOLDER = config['image_folder']\n", "MASK_FOLDER = config['mask_folder']\n", "OUTPUT_FOLDER = config['output_folder']\n", "\n", "# 🔧 如果自动检测不准确，请手动设置路径\n", "# IMAGE_FOLDER = '/your/actual/path/to/converted/image'\n", "# MASK_FOLDER = '/your/actual/path/to/converted/mask'\n", "# OUTPUT_FOLDER = '/your/actual/path/to/output'\n", "\n", "print(f\"\\n📁 数据路径配置:\")\n", "print(f\"  平台: {config['platform']}\")\n", "print(f\"  图像文件夹: {IMAGE_FOLDER}\")\n", "print(f\"  掩膜文件夹: {MASK_FOLDER}\")\n", "print(f\"  输出文件夹: {OUTPUT_FOLDER}\")\n", "\n", "# 创建输出文件夹\n", "os.makedirs(OUTPUT_FOLDER, exist_ok=True)\n", "\n", "# 验证路径存在\n", "if not os.path.exists(IMAGE_FOLDER):\n", "    print(f\"⚠️  警告: 图像文件夹不存在: {IMAGE_FOLDER}\")\n", "    print(\"请修改上面的路径配置\")\n", "if not os.path.exists(MASK_FOLDER):\n", "    print(f\"⚠️  警告: 掩膜文件夹不存在: {MASK_FOLDER}\")\n", "    print(\"请修改上面的路径配置\")\n", "\n", "print(\"\\n✅ 路径配置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 图像合并器 - 与本地代码一致"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==========================================\n", "# 🔧 图像合并器类 - 与本地代码一致\n", "# ==========================================\n", "import re\n", "import tempfile\n", "import shutil\n", "import rasterio\n", "from rasterio.merge import merge\n", "from pathlib import Path\n", "\n", "class ImageMerger:\n", "    \"\"\"图像合并器，用于合并GEE下载的分块图像\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.supported_formats = ['.tif', '.tiff']\n", "    \n", "    def find_related_images(self, image_folder, base_name=None):\n", "        \"\"\"查找相关的图像文件\"\"\"\n", "        image_folder = Path(image_folder)\n", "        if not image_folder.exists():\n", "            raise FileNotFoundError(f\"图像文件夹不存在: {image_folder}\")\n", "        \n", "        # 获取所有图像文件\n", "        image_files = []\n", "        for ext in self.supported_formats:\n", "            image_files.extend(image_folder.glob(f\"*{ext}\"))\n", "        \n", "        if not image_files:\n", "            raise FileNotFoundError(f\"在文件夹中未找到图像文件: {image_folder}\")\n", "        \n", "        # 按基础名称分组\n", "        grouped_files = {}\n", "        \n", "        for img_file in image_files:\n", "            # 提取基础名称（去除可能的分块后缀）\n", "            name = img_file.stem\n", "            \n", "            # 常见的GEE分块模式，优先匹配tile格式\n", "            patterns = [\n", "                r'(tile_\\d+_\\d+)_img-\\d+-\\d+$',  # tile_x_y_img-offset1-offset2\n", "                r'(.+)-\\d+-\\d+-\\d+$',            # name-0-0-0\n", "                r'(.+)_\\d+_\\d+$',                # name_1_2\n", "                r'(.+)-part\\d+$',                # name-part1\n", "                r'(.+)-\\d+$',                    # name-1\n", "                r'(.+)_\\d+$',                    # name_1\n", "            ]\n", "            \n", "            base = name\n", "            for pattern in patterns:\n", "                match = re.match(pattern, name)\n", "                if match:\n", "                    base = match.group(1)\n", "                    break\n", "            \n", "            if base not in grouped_files:\n", "                grouped_files[base] = []\n", "            grouped_files[base].append(img_file)\n", "        \n", "        return grouped_files\n", "    \n", "    def _extract_tile_base_name(self, filename):\n", "        \"\"\"提取瓦片的基础名称\"\"\"\n", "        # 匹配 tile_x_y 模式\n", "        tile_pattern = r'(tile_\\d+_\\d+)'\n", "        match = re.search(tile_pattern, filename.lower())\n", "        if match:\n", "            return match.group(1)\n", "        return filename\n", "    \n", "    def merge_images(self, image_files, output_path, method='first'):\n", "        \"\"\"合并多个图像文件\"\"\"\n", "        if not image_files:\n", "            raise ValueError(\"没有提供图像文件\")\n", "        \n", "        if len(image_files) == 1:\n", "            # 只有一个文件，直接复制\n", "            shutil.copy2(image_files[0], output_path)\n", "            print(f\"单个文件复制完成: {output_path}\")\n", "            return output_path\n", "        \n", "        print(f\"开始合并 {len(image_files)} 个图像文件...\")\n", "        \n", "        # 打开所有图像文件\n", "        src_files = []\n", "        try:\n", "            for img_file in image_files:\n", "                src = rasterio.open(img_file)\n", "                src_files.append(src)\n", "                print(f\"  - {img_file.name}\")\n", "            \n", "            # 合并图像\n", "            mosaic, out_trans = merge(src_files, method=method)\n", "            \n", "            # 获取元数据\n", "            out_meta = src_files[0].meta.copy()\n", "            out_meta.update({\n", "                \"driver\": \"<PERSON>iff\",\n", "                \"height\": mosaic.shape[1],\n", "                \"width\": mosaic.shape[2],\n", "                \"count\": mosaic.shape[0],\n", "                \"transform\": out_trans,\n", "                \"compress\": \"lzw\",\n", "                \"tiled\": True,\n", "                \"blockxsize\": 512,\n", "                \"blockysize\": 512,\n", "                \"interleave\": \"pixel\"\n", "            })\n", "            \n", "            # 保存合并后的图像\n", "            os.makedirs(os.path.dirname(output_path), exist_ok=True)\n", "            with rasterio.open(output_path, \"w\", **out_meta) as dest:\n", "                dest.write(mosaic)\n", "            \n", "            print(f\"图像合并完成: {os.path.basename(output_path)}\")\n", "            print(f\"输出图像尺寸: {mosaic.shape[1]} x {mosaic.shape[2]}\")\n", "            \n", "        finally:\n", "            # 关闭所有文件\n", "            for src in src_files:\n", "                src.close()\n", "        \n", "        return output_path\n", "    \n", "    def merge_folder_images(self, image_folder, output_folder, base_name=None):\n", "        \"\"\"合并文件夹中的所有相关图像\"\"\"\n", "        # 查找相关图像\n", "        grouped_files = self.find_related_images(image_folder, base_name)\n", "        \n", "        # 创建输出文件夹\n", "        os.makedirs(output_folder, exist_ok=True)\n", "        \n", "        results = {}\n", "        \n", "        for base, files in grouped_files.items():\n", "            if len(files) > 1:\n", "                # 需要合并\n", "                tile_base = self._extract_tile_base_name(base)\n", "                output_path = os.path.join(output_folder, f\"{tile_base}_merged.tif\")\n", "                merged_path = self.merge_images(files, output_path)\n", "                results[base] = merged_path\n", "                print(f\"✅ 合并完成: {base} -> {os.path.basename(merged_path)}\")\n", "            else:\n", "                # 单个文件，直接复制\n", "                tile_base = self._extract_tile_base_name(base)\n", "                output_path = os.path.join(output_folder, f\"{tile_base}.tif\")\n", "                shutil.copy2(files[0], output_path)\n", "                results[base] = output_path\n", "                print(f\"✅ 复制完成: {base} -> {os.path.basename(output_path)}\")\n", "        \n", "        return results\n", "\n", "print(\"✅ 图像合并器类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⚙️ 训练配置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==========================================\n", "# ⚙️ 训练配置 - 全部按需加载模式（云端优化）\n", "# ==========================================\n", "\n", "TRAINING_CONFIG = {\n", "    # 基础训练参数\n", "    'batch_size': 20,          # 批次大小（云端可以使用更大的批次）\n", "    'epochs': 10,            # 最大训练轮数\n", "    'learning_rate': 1e-4,    # 学习率\n", "    'validation_split': 0.2,  # 验证集比例\n", "    \n", "    # 图像处理参数\n", "    'patch_size': 512,        # 图像块大小\n", "    'patch_step': 512,        # 图像块步长（重叠32像素）\n", "    'min_foreground_ratio': 0.01,  # 前景像素最小比例（降低以包含更多数据）\n", "    \n", "    # 全部按需加载模式参数\n", "    'use_all_data': True,     # 启用全部按需加载模式\n", "    'max_patches_per_file': None,  # 不限制每个文件的图像块数量\n", "    'max_total_patches': None,     # 不限制总图像块数量\n", "    'use_data_augmentation': True, # 是否使用数据增强\n", "    'shuffle': True,          # 是否打乱数据\n", "    'prefetch_buffer': 4,     # 预取缓冲区大小（云端可以更大）\n", "    \n", "    # 云端优化参数\n", "    'use_all_files': True,    # 使用全部文件\n", "    'memory_limit_gb': 50,    # 内存限制（云端通常有更多内存）\n", "    'enable_mixed_precision': True,  # 启用混合精度训练\n", "}\n", "\n", "print(\"⚙️ 训练配置:\")\n", "for key, value in TRAINING_CONFIG.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "print(\"\\n✅ 训练配置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 数据生成器类 - 内存友好的数据加载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==========================================\n", "# 🔧 数据生成器类 - 内存友好的数据加载\n", "# ==========================================\n", "\n", "class DataGenerator(tf.keras.utils.Sequence):\n", "    \"\"\"数据生成器 - 避免一次性加载所有数据到内存\"\"\"\n", "    \n", "    def __init__(self, file_pairs, config, is_training=True, shuffle=True):\n", "        self.file_pairs = file_pairs\n", "        self.config = config\n", "        self.is_training = is_training\n", "        self.shuffle = shuffle\n", "        self.batch_size = config['batch_size']\n", "        \n", "        # 预处理所有文件，生成图像块索引\n", "        self.patch_indices = self._generate_patch_indices()\n", "        self.total_patches = len(self.patch_indices)\n", "        \n", "        print(f\"📊 数据生成器初始化完成:\")\n", "        print(f\"  文件对数量: {len(file_pairs)}\")\n", "        print(f\"  总图像块数: {self.total_patches}\")\n", "        print(f\"  批次大小: {self.batch_size}\")\n", "        print(f\"  批次数量: {len(self)}\")\n", "        \n", "        self.on_epoch_end()\n", "    \n", "    def _generate_patch_indices(self):\n", "        \"\"\"生成所有图像块的索引信息\"\"\"\n", "        print(\"🔍 生成图像块索引...\")\n", "        patch_indices = []\n", "        \n", "        for file_idx, (img_file, mask_file) in enumerate(self.file_pairs):\n", "            try:\n", "                # 读取图像尺寸信息（不加载实际数据）\n", "                with rasterio.open(img_file) as src:\n", "                    height, width = src.height, src.width\n", "                    bands = src.count\n", "                \n", "                # 计算可能的图像块位置\n", "                patch_size = self.config['patch_size']\n", "                step = self.config['patch_step']\n", "                \n", "                patches_per_file = 0\n", "                for y in range(0, height - patch_size + 1, step):\n", "                    for x in range(0, width - patch_size + 1, step):\n", "                        patch_indices.append({\n", "                            'file_idx': file_idx,\n", "                            'img_file': img_file,\n", "                            'mask_file': mask_file,\n", "                            'x': x,\n", "                            'y': y,\n", "                            'patch_size': patch_size\n", "                        })\n", "                        patches_per_file += 1\n", "                        \n", "                        # 全部按需加载模式：不限制图像块数量\n", "                        max_patches = self.config.get('max_patches_per_file', None)\n", "                        use_all_data = self.config.get('use_all_data', False)\n", "                        \n", "                        # 只有在非全部数据模式下才限制数量\n", "                        if not use_all_data and max_patches and patches_per_file >= max_patches:\n", "                            break\n", "                    # 只有在非全部数据模式下才限制数量\n", "                    max_patches = self.config.get('max_patches_per_file', None)\n", "                    use_all_data = self.config.get('use_all_data', False)\n", "                    if not use_all_data and max_patches and patches_per_file >= max_patches:\n", "                        break\n", "                \n", "                print(f\"  文件 {file_idx+1}: {os.path.basename(img_file)} -> {patches_per_file} 个图像块\")\n", "                \n", "            except Exception as e:\n", "                print(f\"  ❌ 处理文件失败: {img_file} - {e}\")\n", "                continue\n", "        \n", "        return patch_indices\n", "    \n", "    def __len__(self):\n", "        \"\"\"返回批次数量\"\"\"\n", "        return int(np.ceil(len(self.patch_indices) / float(self.batch_size)))\n", "    \n", "    def __getitem__(self, idx):\n", "        \"\"\"获取一个批次的数据\"\"\"\n", "        # 获取当前批次的索引\n", "        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]\n", "        \n", "        # 加载批次数据\n", "        batch_x = []\n", "        batch_y = []\n", "        \n", "        for i in batch_indices:\n", "            if i >= len(self.patch_indices):\n", "                continue\n", "                \n", "            patch_info = self.patch_indices[i]\n", "            \n", "            try:\n", "                # 加载图像块\n", "                img_patch, mask_patch = self._load_patch(patch_info)\n", "                \n", "                # 检查前景比例\n", "                foreground_ratio = np.sum(mask_patch > 0) / mask_patch.size\n", "                if foreground_ratio >= self.config['min_foreground_ratio']:\n", "                    batch_x.append(img_patch)\n", "                    batch_y.append(mask_patch)\n", "                else:\n", "                    # 如果当前块不符合要求，尝试下一个\n", "                    continue\n", "                    \n", "            except Exception as e:\n", "                # 如果加载失败，跳过这个块\n", "                continue\n", "        \n", "        # 如果批次数据不足，用零填充\n", "        while len(batch_x) < self.batch_size:\n", "            if len(batch_x) > 0:\n", "                batch_x.append(np.zeros_like(batch_x[0]))\n", "                batch_y.append(np.zeros_like(batch_y[0]))\n", "            else:\n", "                # 如果完全没有有效数据，创建默认形状\n", "                patch_size = self.config['patch_size']\n", "                batch_x.append(np.zeros((patch_size, patch_size, 3), dtype=np.float32))\n", "                batch_y.append(np.zeros((patch_size, patch_size, 1), dtype=np.float32))\n", "        \n", "        # 转换为numpy数组\n", "        X = np.array(batch_x[:self.batch_size], dtype=np.float32)\n", "        y = np.array(batch_y[:self.batch_size], dtype=np.float32)\n", "        \n", "        # 数据预处理\n", "        X = X / 255.0  # 归一化\n", "        y = (y > 0).astype(np.float32)  # 二值化\n", "        \n", "        # 转换为分类格式\n", "        y_categorical = np.zeros((*y.shape[:3], 2), dtype=np.float32)\n", "        y_categorical[..., 0] = (y[..., 0] == 0)  # 背景\n", "        y_categorical[..., 1] = (y[..., 0] == 1)  # 前景\n", "        \n", "        # 数据增强（仅训练时）\n", "        if self.is_training and self.config.get('use_data_augmentation', True):\n", "            X, y_categorical = self._augment_batch(X, y_categorical)\n", "        \n", "        return X, y_categorical\n", "    \n", "    def _load_patch(self, patch_info):\n", "        \"\"\"加载单个图像块\"\"\"\n", "        x, y = patch_info['x'], patch_info['y']\n", "        patch_size = patch_info['patch_size']\n", "        \n", "        # 读取图像块\n", "        with rasterio.open(patch_info['img_file']) as src:\n", "            # 读取指定区域\n", "            window = rasterio.windows.Window(x, y, patch_size, patch_size)\n", "            image = src.read(window=window).transpose(1, 2, 0)\n", "            if image.shape[2] > 3:\n", "                image = image[:, :, :3]\n", "        \n", "        # 读取掩膜块\n", "        with rasterio.open(patch_info['mask_file']) as src:\n", "            window = rasterio.windows.Window(x, y, patch_size, patch_size)\n", "            mask = src.read(1, window=window)\n", "            mask = np.expand_dims(mask, axis=-1)\n", "        \n", "        return image.astype(np.float32), mask.astype(np.float32)\n", "    \n", "    def _augment_batch(self, images, masks):\n", "        \"\"\"批次数据增强\"\"\"\n", "        augmented_images = []\n", "        augmented_masks = []\n", "        \n", "        for img, mask in zip(images, masks):\n", "            # 随机水平翻转\n", "            if np.random.random() > 0.5:\n", "                img = np.fliplr(img)\n", "                mask = np.fliplr(mask)\n", "            \n", "            # 随机垂直翻转\n", "            if np.random.random() > 0.5:\n", "                img = np.flipud(img)\n", "                mask = np.flipud(mask)\n", "            \n", "            # 随机旋转90度\n", "            if np.random.random() > 0.5:\n", "                k = np.random.randint(1, 4)\n", "                img = np.rot90(img, k)\n", "                mask = np.rot90(mask, k)\n", "            \n", "            augmented_images.append(img)\n", "            augmented_masks.append(mask)\n", "        \n", "        return np.array(augmented_images), np.array(augmented_masks)\n", "    \n", "    def on_epoch_end(self):\n", "        \"\"\"每个epoch结束时调用\"\"\"\n", "        self.indices = np.arange(len(self.patch_indices))\n", "        if self.shuffle:\n", "            np.random.shuffle(self.indices)\n", "\n", "print(\"✅ 数据生成器类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 数据准备函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_data_generators(image_folder, mask_folder, config):\n", "    \"\"\"准备数据生成器 - 集成图像合并功能\"\"\"\n", "    print(\"📦 开始准备数据生成器（集成图像合并）...\")\n", "    \n", "    # 获取原始文件列表\n", "    image_files = []\n", "    mask_files = []\n", "    \n", "    for ext in ['.tif', '.tiff']:\n", "        image_files.extend(Path(image_folder).glob(f\"*{ext}\"))\n", "        mask_files.extend(Path(mask_folder).glob(f\"*{ext}\"))\n", "    \n", "    image_files = [str(f) for f in image_files]\n", "    mask_files = [str(f) for f in mask_files]\n", "    \n", "    print(f\"找到 {len(image_files)} 个图像文件，{len(mask_files)} 个掩膜文件\")\n", "    \n", "    # 🔧 检查是否需要合并分块图像\n", "    print(\"\\n🔧 检查是否需要合并分块图像...\")\n", "    \n", "    merger = ImageMerger()\n", "    temp_folder = None\n", "    \n", "    try:\n", "        grouped_files = merger.find_related_images(image_folder)\n", "        \n", "        # 检查是否有需要合并的组\n", "        needs_merge = any(len(files) > 1 for files in grouped_files.values())\n", "        \n", "        if needs_merge:\n", "            print(\"✅ 发现分块图像，开始合并...\")\n", "            \n", "            # 创建临时文件夹\n", "            temp_folder = tempfile.mkdtemp(prefix='merged_images_')\n", "            print(f\"临时合并文件夹: {temp_folder}\")\n", "            \n", "            # 合并图像\n", "            merged_results = merger.merge_folder_images(image_folder, temp_folder)\n", "            \n", "            # 更新图像文件列表为合并后的文件\n", "            image_files = [path for path in merged_results.values()]\n", "            print(f\"\\n✅ 图像合并完成，共 {len(image_files)} 个文件\")\n", "            \n", "            # 打印合并后的文件名\n", "            for merged_file in image_files:\n", "                print(f\"  合并后文件: {os.path.basename(merged_file)}\")\n", "        else:\n", "            print(\"ℹ️  未发现需要合并的分块图像\")\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️  图像合并检查失败，使用原始文件: {e}\")\n", "    \n", "    # 🔧 改进的文件匹配逻辑\n", "    print(\"\\n🔧 开始智能文件配对...\")\n", "    matched_pairs = []\n", "    \n", "    for img_file in image_files:\n", "        img_name = os.path.basename(img_file)\n", "        \n", "        # 提取瓦片基础名称\n", "        tile_base = None\n", "        if 'tile_' in img_name.lower():\n", "            # 使用正则表达式提取tile_x_y\n", "            tile_pattern = r'(tile_\\d+_\\d+)'\n", "            match = re.search(tile_pattern, img_name.lower())\n", "            if match:\n", "                tile_base = match.group(1)\n", "        \n", "        if tile_base:\n", "            # 寻找匹配的掩膜文件\n", "            for mask_file in mask_files:\n", "                mask_name = os.path.basename(mask_file)\n", "                if tile_base in mask_name.lower():\n", "                    matched_pairs.append((img_file, mask_file))\n", "                    print(f\"✅ 配对成功: {os.path.basename(img_file)} <-> {os.path.basename(mask_file)}\")\n", "                    break\n", "        else:\n", "            print(f\"⚠️  无法提取瓦片信息: {img_name}\")\n", "    \n", "    print(f\"\\n📊 智能匹配结果: {len(matched_pairs)} 对文件\")\n", "    \n", "    if not matched_pairs:\n", "        raise ValueError(\"未找到任何匹配的图像-掩膜对！请检查文件命名和路径。\")\n", "    \n", "    # 🔧 分割训练和验证数据\n", "    from sklearn.model_selection import train_test_split\n", "    \n", "    train_pairs, val_pairs = train_test_split(\n", "        matched_pairs, \n", "        test_size=config['validation_split'], \n", "        random_state=42\n", "    )\n", "    \n", "    print(f\"\\n📊 数据分割结果:\")\n", "    print(f\"  训练文件对: {len(train_pairs)}\")\n", "    print(f\"  验证文件对: {len(val_pairs)}\")\n", "    \n", "    # 🔧 创建数据生成器\n", "    print(\"\\n🔧 创建数据生成器...\")\n", "    \n", "    train_generator = DataGenerator(\n", "        train_pairs, \n", "        config, \n", "        is_training=True, \n", "        shuffle=config.get('shuffle', True)\n", "    )\n", "    \n", "    val_generator = DataGenerator(\n", "        val_pairs, \n", "        config, \n", "        is_training=False, \n", "        shuffle=False\n", "    )\n", "    \n", "    print(\"\\n✅ 数据生成器创建完成\")\n", "    \n", "    # 返回生成器和临时文件夹路径（用于清理）\n", "    return train_generator, val_generator, temp_folder\n", "\n", "print(\"✅ 数据准备函数定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 UNet模型定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tensorflow import keras\n", "from tensorflow.keras import layers\n", "\n", "def conv_block(inputs, num_filters):\n", "    \"\"\"卷积块\"\"\"\n", "    x = layers.Conv2D(num_filters, 3, padding=\"same\")(inputs)\n", "    x = layers.BatchNormalization()(x)\n", "    x = layers.Activation(\"relu\")(x)\n", "    \n", "    x = layers.Conv2D(num_filters, 3, padding=\"same\")(x)\n", "    x = layers.BatchNormalization()(x)\n", "    x = layers.Activation(\"relu\")(x)\n", "    \n", "    return x\n", "\n", "def encoder_block(inputs, num_filters):\n", "    \"\"\"编码器块\"\"\"\n", "    x = conv_block(inputs, num_filters)\n", "    p = layers.MaxPool2D((2, 2))(x)\n", "    return x, p\n", "\n", "def decoder_block(inputs, skip_features, num_filters):\n", "    \"\"\"解码器块\"\"\"\n", "    x = layers.Conv2DTranspose(num_filters, (2, 2), strides=2, padding=\"same\")(inputs)\n", "    x = layers.Concatenate()([x, skip_features])\n", "    x = conv_block(x, num_filters)\n", "    return x\n", "\n", "def create_complete_unet(input_shape=(256, 256, 3), num_classes=2):\n", "    \"\"\"创建完整的UNet模型\"\"\"\n", "    inputs = layers.Input(input_shape)\n", "    \n", "    # 编码器\n", "    s1, p1 = encoder_block(inputs, 64)\n", "    s2, p2 = encoder_block(p1, 128)\n", "    s3, p3 = encoder_block(p2, 256)\n", "    s4, p4 = encoder_block(p3, 512)\n", "    \n", "    # 桥接\n", "    b1 = conv_block(p4, 1024)\n", "    \n", "    # 解码器\n", "    d1 = decoder_block(b1, s4, 512)\n", "    d2 = decoder_block(d1, s3, 256)\n", "    d3 = decoder_block(d2, s2, 128)\n", "    d4 = decoder_block(d3, s1, 64)\n", "    \n", "    # 输出层\n", "    outputs = layers.Conv2D(num_classes, 1, padding=\"same\", activation=\"softmax\")(d4)\n", "    \n", "    model = keras.Model(inputs, outputs, name=\"UNet\")\n", "    return model\n", "\n", "# 损失函数和指标\n", "def dice_coef(y_true, y_pred, smooth=1e-6):\n", "    \"\"\"Dice系数\"\"\"\n", "    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)\n", "    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)\n", "    \n", "    intersection = tf.reduce_sum(y_true_f * y_pred_f)\n", "    dice = (2. * intersection + smooth) / (tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) + smooth)\n", "    \n", "    return tf.cast(dice, tf.float32)\n", "\n", "def dice_loss(y_true, y_pred):\n", "    \"\"\"Dice损失\"\"\"\n", "    return 1 - dice_coef(y_true, y_pred)\n", "\n", "def focal_loss(y_true, y_pred, alpha=0.25, gamma=2.0):\n", "    \"\"\"Focal损失\"\"\"\n", "    epsilon = tf.keras.backend.epsilon()\n", "    y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)\n", "    \n", "    ce = -y_true * tf.math.log(y_pred)\n", "    weight = alpha * y_true * tf.pow((1 - y_pred), gamma)\n", "    focal = weight * ce\n", "    \n", "    return tf.reduce_mean(tf.reduce_sum(focal, axis=-1))\n", "\n", "def combined_loss(y_true, y_pred):\n", "    \"\"\"组合损失函数\"\"\"\n", "    dice = dice_loss(y_true, y_pred)\n", "    focal = focal_loss(y_true, y_pred)\n", "    \n", "    # 数值稳定性检查\n", "    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))\n", "    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))\n", "    \n", "    combined = dice + focal\n", "    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))\n", "    \n", "    return tf.cast(combined, tf.float32)\n", "\n", "def iou_coef(y_true, y_pred, smooth=1e-6):\n", "    \"\"\"IoU系数\"\"\"\n", "    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)\n", "    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)\n", "    \n", "    intersection = tf.reduce_sum(y_true_f * y_pred_f)\n", "    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection\n", "    \n", "    iou = (intersection + smooth) / (union + smooth)\n", "    return tf.cast(iou, tf.float32)\n", "\n", "# 创建模型\n", "print(\"🧠 创建UNet模型...\")\n", "model = create_complete_unet(input_shape=(256, 256, 3), num_classes=2)\n", "print(f\"✅ 模型创建完成，参数数量: {model.count_params():,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 执行训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 开始训练流程\n", "print(\"🚀 开始UNet训练流程（数据生成器版本）...\")\n", "print(\"=\" * 60)\n", "\n", "# 初始化变量\n", "train_generator = None\n", "val_generator = None\n", "temp_folder = None\n", "\n", "try:\n", "    # 1. 准备数据生成器\n", "    print(\"📦 步骤1: 准备数据生成器\")\n", "    monitor_memory()\n", "    \n", "    train_generator, val_generator, temp_folder = prepare_data_generators(\n", "        IMAGE_FOLDER, MASK_FOLDER, TRAINING_CONFIG\n", "    )\n", "    \n", "    print(\"\\n📊 数据生成器准备完成\")\n", "    monitor_memory()\n", "    \n", "    # 2. 启用混合精度训练（如果配置启用）\n", "    if TRAINING_CONFIG.get('enable_mixed_precision', False):\n", "        print(\"\\n🚀 启用混合精度训练...\")\n", "        policy = tf.keras.mixed_precision.Policy('mixed_float16')\n", "        tf.keras.mixed_precision.set_global_policy(policy)\n", "        print(f\"✅ 混合精度策略设置完成: {policy.name}\")\n", "    \n", "    # 3. 编译模型\n", "    print(\"\\n⚙️ 步骤3: 编译模型\")\n", "    \n", "    # 使用梯度裁剪的优化器增强数值稳定性\n", "    optimizer = keras.optimizers.<PERSON>(\n", "        learning_rate=TRAINING_CONFIG['learning_rate'],\n", "        clipnorm=1.0,  # 梯度范数裁剪\n", "        clipvalue=0.5,  # 梯度值裁剪\n", "        epsilon=1e-7\n", "    )\n", "    \n", "    model.compile(\n", "        optimizer=optimizer,\n", "        loss=combined_loss,\n", "        metrics=[dice_coef, iou_coef, 'accuracy']\n", "    )\n", "    \n", "    print(\"✅ 模型编译完成（包含梯度裁剪优化）\")\n", "    \n", "    # 4. 设置回调函数\n", "    print(\"\\n📋 步骤4: 设置训练回调\")\n", "    \n", "    callbacks = [\n", "        keras.callbacks.ModelCheckpoint(\n", "            filepath=os.path.join(OUTPUT_FOLDER, 'best_unet_model.h5'),\n", "            monitor='val_dice_coef',\n", "            mode='max',\n", "            save_best_only=True,\n", "            save_weights_only=False,\n", "            verbose=1\n", "        ),\n", "        keras.callbacks.ReduceLROnPlateau(\n", "            monitor='val_loss',\n", "            factor=0.5,\n", "            patience=5,\n", "            min_lr=1e-7,\n", "            verbose=1\n", "        ),\n", "        keras.callbacks.EarlyStopping(\n", "            monitor='val_dice_coef',\n", "            mode='max',\n", "            patience=10,\n", "            restore_best_weights=True,\n", "            verbose=1\n", "        )\n", "    ]\n", "    \n", "    print(\"✅ 回调函数设置完成\")\n", "    \n", "    # 5. 显示全部按需加载模式统计信息\n", "    print(\"\\n📊 全部按需加载模式统计信息\")\n", "    print(\"=\" * 60)\n", "    print(f\"🚀 训练模式: 全部按需加载 (use_all_data={TRAINING_CONFIG.get('use_all_data', False)})\")\n", "    print(f\"📁 训练文件数: {len(train_generator.file_pairs)}\")\n", "    print(f\"📁 验证文件数: {len(val_generator.file_pairs)}\")\n", "    print(f\"📊 训练图像块总数: {train_generator.total_patches}\")\n", "    print(f\"📊 验证图像块总数: {val_generator.total_patches}\")\n", "    print(f\"📊 训练批次数: {len(train_generator)}\")\n", "    print(f\"📊 验证批次数: {len(val_generator)}\")\n", "    print(f\"⚙️  批次大小: {TRAINING_CONFIG['batch_size']}\")\n", "    print(f\"⚙️  训练轮数: {TRAINING_CONFIG['epochs']}\")\n", "    print(f\"⚙️  学习率: {TRAINING_CONFIG['learning_rate']}\")\n", "    print(f\"⚙️  图像块大小: {TRAINING_CONFIG['patch_size']}x{TRAINING_CONFIG['patch_size']}\")\n", "    print(f\"⚙️  前景像素最小比例: {TRAINING_CONFIG['min_foreground_ratio']}\")\n", "    if TRAINING_CONFIG.get('enable_mixed_precision', False):\n", "        print(\"🚀 混合精度训练: 已启用\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 6. 开始训练\n", "    print(\"\\n🎯 步骤6: 开始模型训练\")\n", "    \n", "    history = model.fit(\n", "        train_generator,\n", "        epochs=TRAINING_CONFIG['epochs'],\n", "        validation_data=val_generator,\n", "        callbacks=callbacks,\n", "        verbose=1,\n", "        use_multiprocessing=False,  # 避免多进程问题\n", "        workers=1  # 单线程处理\n", "    )\n", "    \n", "    print(\"\\n🎉 训练完成！\")\n", "    \n", "    # 7. 保存最终模型\n", "    print(\"\\n💾 步骤7: 保存模型\")\n", "    final_model_path = os.path.join(OUTPUT_FOLDER, 'final_unet_model_full_data.h5')\n", "    model.save(final_model_path)\n", "    print(f\"✅ 最终模型已保存: {final_model_path}\")\n", "    \n", "    # 8. 保存训练历史\n", "    history_path = os.path.join(OUTPUT_FOLDER, 'training_history_full_data.json')\n", "    with open(history_path, 'w') as f:\n", "        # 转换numpy数组为列表以便JSON序列化\n", "        history_dict = {key: [float(val) for val in values] for key, values in history.history.items()}\n", "        json.dump(history_dict, f, indent=2)\n", "    print(f\"✅ 训练历史已保存: {history_path}\")\n", "    \n", "    # 9. 显示训练结果\n", "    print(\"\\n📊 全部按需加载模式训练结果摘要:\")\n", "    print(\"=\" * 60)\n", "    \n", "    final_metrics = {\n", "        'final_loss': history.history['loss'][-1],\n", "        'final_val_loss': history.history['val_loss'][-1],\n", "        'final_dice_coef': history.history['dice_coef'][-1],\n", "        'final_val_dice_coef': history.history['val_dice_coef'][-1],\n", "        'final_iou_coef': history.history['iou_coef'][-1],\n", "        'final_val_iou_coef': history.history['val_iou_coef'][-1],\n", "        'best_val_dice': max(history.history['val_dice_coef']),\n", "        'best_val_iou': max(history.history['val_iou_coef'])\n", "    }\n", "    \n", "    for metric, value in final_metrics.items():\n", "        print(f\"{metric}: {value:.4f}\")\n", "    \n", "    print(\"\\n🎯 全部按需加载模式训练成功完成！\")\n", "    print(f\"📁 模型文件保存在: {OUTPUT_FOLDER}\")\n", "    print(f\"🚀 训练模式: 全部按需加载 (use_all_data=True)\")\n", "    print(f\"📊 实际使用的训练图像块数: {train_generator.total_patches}\")\n", "    print(f\"📊 实际使用的验证图像块数: {val_generator.total_patches}\")\n", "    print(f\"⚙️  批次大小: {TRAINING_CONFIG['batch_size']}\")\n", "    print(f\"⚙️  训练轮数: {len(history.history['loss'])}\")\n", "    if TRAINING_CONFIG.get('enable_mixed_precision', False):\n", "        print(\"🚀 使用了混合精度训练加速\")\n", "    \n", "    # 10. 生成详细训练报告\n", "    print(\"\\n📋 步骤10: 生成详细训练报告\")\n", "    generate_detailed_training_report(history, TRAINING_CONFIG, train_generator, val_generator, OUTPUT_FOLDER)\n", "    \n", "except Exception as e:\n", "    print(f\"\\n❌ 训练过程中出现错误: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n", "    \n", "    print(\"\\n🔧 可能的解决方案:\")\n", "    print(\"1. 检查数据路径是否正确\")\n", "    print(\"2. 确认图像和掩膜文件存在且可读\")\n", "    print(\"3. 减少batch_size或max_patches_per_file\")\n", "    print(\"4. 检查GPU/CPU内存是否足够\")\n", "    print(\"5. 确认文件命名格式符合tile_x_y模式\")\n", "    \n", "finally:\n", "    # 清理临时文件夹\n", "    if temp_folder and os.path.exists(temp_folder):\n", "        print(f\"\\n🧹 清理临时文件: {temp_folder}\")\n", "        try:\n", "            shutil.rmtree(temp_folder)\n", "            print(\"✅ 临时文件清理完成\")\n", "        except Exception as e:\n", "            print(f\"⚠️  临时文件清理失败: {e}\")\n", "    \n", "    # 强制垃圾回收\n", "    gc.collect()\n", "    print(\"\\n📊 最终内存状态:\")\n", "    monitor_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 详细训练报告生成器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_detailed_training_report(history, config, train_gen, val_gen, output_folder):\\n\n", "    \\\"\\\"\\\"生成详细的训练报告\\\"\\\"\\\"\\n\n", "    import matplotlib.pyplot as plt\\n\n", "    import seaborn as sns\\n\n", "    import pandas as pd\\n\n", "    from datetime import datetime\\n\n", "    import json\\n\n", "    \\n\n", "    # 设置中文字体和样式\\n\n", "    plt.rcParams['font.sans-serif'] = ['Sim<PERSON>ei', 'DejaVu Sans']\\n\n", "    plt.rcParams['axes.unicode_minus'] = False\\n\n", "    sns.set_style('whitegrid')\\n\n", "    \\n\n", "    print(\\\"📋 正在生成详细训练报告...\\\")\\n\n", "    \\n\n", "    # 1. 创建报告文件夹\\n\n", "    report_folder = os.path.join(output_folder, 'training_report')\\n\n", "    os.makedirs(report_folder, exist_ok=True)\\n\n", "    \\n\n", "    # 2. 收集训练数据\\n\n", "    training_data = {\\n\n", "        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\\n\n", "        'config': config,\\n\n", "        'data_stats': {\\n\n", "            'train_patches': train_gen.total_patches,\\n\n", "            'val_patches': val_gen.total_patches,\\n\n", "            'train_batches': len(train_gen),\\n\n", "            'val_batches': len(val_gen),\\n\n", "            'train_files': len(train_gen.file_pairs),\\n\n", "            'val_files': len(val_gen.file_pairs)\\n\n", "        },\\n\n", "        'training_history': history.history,\\n\n", "        'final_metrics': {}\\n\n", "    }\\n\n", "    \\n\n", "    # 3. 计算最终指标\\n\n", "    for metric in history.history.keys():\\n\n", "        training_data['final_metrics'][metric] = float(history.history[metric][-1])\\n\n", "    \\n\n", "    # 4. 生成训练曲线图\\n\n", "    generate_training_curves(history, report_folder)\\n\n", "    \\n\n", "    # 5. 生成指标对比图\\n\n", "    generate_metrics_comparison(history, report_folder)\\n\n", "    \\n\n", "    # 6. 生成配置参数表\\n\n", "    generate_config_table(config, training_data['data_stats'], report_folder)\\n\n", "    \\n\n", "    # 7. 生成HTML报告\\n\n", "    generate_html_report(training_data, report_folder)\\n\n", "    \\n\n", "    # 8. 保存完整数据\\n\n", "    report_data_path = os.path.join(report_folder, 'complete_training_data.json')\\n\n", "    with open(report_data_path, 'w', encoding='utf-8') as f:\\n\n", "        # 转换numpy数组为列表\\n\n", "        json_data = {}\\n\n", "        for key, value in training_data.items():\\n\n", "            if key == 'training_history':\\n\n", "                json_data[key] = {k: [float(v) for v in vals] for k, vals in value.items()}\\n\n", "            else:\\n\n", "                json_data[key] = value\\n\n", "        json.dump(json_data, f, indent=2, ensure_ascii=False)\\n\n", "    \\n\n", "    print(f\\\"✅ 详细训练报告已生成: {report_folder}\\\")\\n\n", "    print(f\\\"📊 报告包含: 训练曲线图、指标对比、配置表格、HTML报告\\\")\\n\n", "    \\n\n", "    return report_folder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 训练结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_training_curves(history, output_folder):\\n\n", "    \\\"\\\"\\\"生成训练曲线图\\\"\\\"\\\"\\n\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\\n\n", "    fig.suptitle('UNet训练曲线 - 全部按需加载模式', fontsize=20, fontweight='bold')\\n\n", "    \\n\n", "    # 损失曲线\\n\n", "    axes[0, 0].plot(history.history['loss'], 'b-', linewidth=2, label='训练损失', alpha=0.8)\\n\n", "    axes[0, 0].plot(history.history['val_loss'], 'r-', linewidth=2, label='验证损失', alpha=0.8)\\n\n", "    axes[0, 0].set_title('损失函数曲线', fontsize=14, fontweight='bold')\\n\n", "    axes[0, 0].set_xlabel('训练轮数 (Epochs)')\\n\n", "    axes[0, 0].set_ylabel('损失值 (Loss)')\\n\n", "    axes[0, 0].legend()\\n\n", "    axes[0, 0].grid(True, alpha=0.3)\\n\n", "    \\n\n", "    # Dice系数曲线\\n\n", "    axes[0, 1].plot(history.history['dice_coef'], 'g-', linewidth=2, label='训练Dice', alpha=0.8)\\n\n", "    axes[0, 1].plot(history.history['val_dice_coef'], 'orange', linewidth=2, label='验证Dice', alpha=0.8)\\n\n", "    axes[0, 1].set_title('Dice系数曲线', fontsize=14, fontweight='bold')\\n\n", "    axes[0, 1].set_xlabel('训练轮数 (Epochs)')\\n\n", "    axes[0, 1].set_ylabel('Dice系数')\\n\n", "    axes[0, 1].legend()\\n\n", "    axes[0, 1].grid(True, alpha=0.3)\\n\n", "    \\n\n", "    # IoU系数曲线\\n\n", "    axes[1, 0].plot(history.history['iou_coef'], 'purple', linewidth=2, label='训练IoU', alpha=0.8)\\n\n", "    axes[1, 0].plot(history.history['val_iou_coef'], 'brown', linewidth=2, label='验证IoU', alpha=0.8)\\n\n", "    axes[1, 0].set_title('IoU系数曲线', fontsize=14, fontweight='bold')\\n\n", "    axes[1, 0].set_xlabel('训练轮数 (Epochs)')\\n\n", "    axes[1, 0].set_ylabel('IoU系数')\\n\n", "    axes[1, 0].legend()\\n\n", "    axes[1, 0].grid(True, alpha=0.3)\\n\n", "    \\n\n", "    # 准确率曲线\\n\n", "    axes[1, 1].plot(history.history['accuracy'], 'teal', linewidth=2, label='训练准确率', alpha=0.8)\\n\n", "    axes[1, 1].plot(history.history['val_accuracy'], 'navy', linewidth=2, label='验证准确率', alpha=0.8)\\n\n", "    axes[1, 1].set_title('准确率曲线', fontsize=14, fontweight='bold')\\n\n", "    axes[1, 1].set_xlabel('训练轮数 (Epochs)')\\n\n", "    axes[1, 1].set_ylabel('准确率')\\n\n", "    axes[1, 1].legend()\\n\n", "    axes[1, 1].grid(True, alpha=0.3)\\n\n", "    \\n\n", "    plt.tight_layout()\\n\n", "    plt.savefig(os.path.join(output_folder, 'training_curves.png'), dpi=300, bbox_inches='tight')\\n\n", "    plt.savefig(os.path.join(output_folder, 'training_curves.pdf'), bbox_inches='tight')\\n\n", "    plt.show()\\n\n", "    \\n\n", "    print(\\\"✅ 训练曲线图已生成\\\")\\n\n", "\\n\n", "def generate_metrics_comparison(history, output_folder):\\n\n", "    \\\"\\\"\\\"生成指标对比图\\\"\\\"\\\"\\n\n", "    # 最终指标对比\\n\n", "    final_metrics = {\\n\n", "        '训练损失': history.history['loss'][-1],\\n\n", "        '验证损失': history.history['val_loss'][-1],\\n\n", "        '训练Dice': history.history['dice_coef'][-1],\\n\n", "        '验证Dice': history.history['val_dice_coef'][-1],\\n\n", "        '训练IoU': history.history['iou_coef'][-1],\\n\n", "        '验证IoU': history.history['val_iou_coef'][-1],\\n\n", "        '训练准确率': history.history['accuracy'][-1],\\n\n", "        '验证准确率': history.history['val_accuracy'][-1]\\n\n", "    }\\n\n", "    \\n\n", "    # 创建对比图\\n\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\\n\n", "    \\n\n", "    # 损失对比\\n\n", "    loss_data = ['训练损失', '验证损失']\\n\n", "    loss_values = [final_metrics['训练损失'], final_metrics['验证损失']]\\n\n", "    colors1 = ['#3498db', '#e74c3c']\\n\n", "    bars1 = ax1.bar(loss_data, loss_values, color=colors1, alpha=0.8)\\n\n", "    ax1.set_title('最终损失对比', fontsize=14, fontweight='bold')\\n\n", "    ax1.set_ylabel('损失值')\\n\n", "    ax1.grid(True, alpha=0.3)\\n\n", "    \\n\n", "    # 添加数值标签\\n\n", "    for bar, value in zip(bars1, loss_values):\\n\n", "        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,\\n\n", "                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')\\n\n", "    \\n\n", "    # 精度指标对比\\n\n", "    metrics_data = ['训练Dice', '验证Dice', '训练IoU', '验证IoU', '训练准确率', '验证准确率']\\n\n", "    metrics_values = [final_metrics[m] for m in metrics_data]\\n\n", "    colors2 = ['#2ecc71', '#f39c12', '#9b59b6', '#8b4513', '#1abc9c', '#2c3e50']\\n\n", "    bars2 = ax2.bar(metrics_data, metrics_values, color=colors2, alpha=0.8)\\n\n", "    ax2.set_title('最终精度指标对比', fontsize=14, fontweight='bold')\\n\n", "    ax2.set_ylabel('指标值')\\n\n", "    ax2.set_ylim(0, 1)\\n\n", "    ax2.grid(True, alpha=0.3)\\n\n", "    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')\\n\n", "    \\n\n", "    # 添加数值标签\\n\n", "    for bar, value in zip(bars2, metrics_values):\\n\n", "        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\\n\n", "                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')\\n\n", "    \\n\n", "    plt.tight_layout()\\n\n", "    plt.savefig(os.path.join(output_folder, 'metrics_comparison.png'), dpi=300, bbox_inches='tight')\\n\n", "    plt.savefig(os.path.join(output_folder, 'metrics_comparison.pdf'), bbox_inches='tight')\\n\n", "    plt.show()\\n\n", "    \\n\n", "    print(\\\"✅ 指标对比图已生成\\\")\\n\n", "\\n\n", "def generate_config_table(config, data_stats, output_folder):\\n\n", "    \\\"\\\"\\\"生成配置参数表\\\"\\\"\\\"\\n\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))\\n\n", "    \\n\n", "    # 训练配置表\\n\n", "    config_data = [\\n\n", "        ['训练模式', '全部按需加载'],\\n\n", "        ['批次大小', config['batch_size']],\\n\n", "        ['训练轮数', config['epochs']],\\n\n", "        ['学习率', config['learning_rate']],\\n\n", "        ['图像块大小', f\\\"{config['patch_size']}x{config['patch_size']}\\\"],\\n\n", "        ['图像块步长', config['patch_step']],\\n\n", "        ['前景像素最小比例', config['min_foreground_ratio']],\\n\n", "        ['数据增强', '是' if config['use_data_augmentation'] else '否'],\\n\n", "        ['混合精度训练', '是' if config.get('enable_mixed_precision', False) else '否'],\\n\n", "        ['验证集比例', config['validation_split']],\\n\n", "        ['预取缓冲区', config['prefetch_buffer']]\\n\n", "    ]\\n\n", "    \\n\n", "    ax1.axis('tight')\\n\n", "    ax1.axis('off')\\n\n", "    table1 = ax1.table(cellText=config_data,\\n\n", "                      colLabels=['参数名称', '参数值'],\\n\n", "                      cellLoc='center',\\n\n", "                      loc='center',\\n\n", "                      colWidths=[0.6, 0.4])\\n\n", "    table1.auto_set_font_size(False)\\n\n", "    table1.set_fontsize(10)\\n\n", "    table1.scale(1, 2)\\n\n", "    ax1.set_title('训练配置参数', fontsize=14, fontweight='bold', pad=20)\\n\n", "    \\n\n", "    # 数据统计表\\n\n", "    data_table = [\\n\n", "        ['训练文件数', data_stats['train_files']],\\n\n", "        ['验证文件数', data_stats['val_files']],\\n\n", "        ['训练图像块数', data_stats['train_patches']],\\n\n", "        ['验证图像块数', data_stats['val_patches']],\\n\n", "        ['训练批次数', data_stats['train_batches']],\\n\n", "        ['验证批次数', data_stats['val_batches']],\\n\n", "        ['总图像块数', data_stats['train_patches'] + data_stats['val_patches']],\\n\n", "        ['总批次数', data_stats['train_batches'] + data_stats['val_batches']]\\n\n", "    ]\\n\n", "    \\n\n", "    ax2.axis('tight')\\n\n", "    ax2.axis('off')\\n\n", "    table2 = ax2.table(cellText=data_table,\\n\n", "                      colLabels=['数据项', '数量'],\\n\n", "                      cellLoc='center',\\n\n", "                      loc='center',\\n\n", "                      colWidths=[0.6, 0.4])\\n\n", "    table2.auto_set_font_size(False)\\n\n", "    table2.set_fontsize(10)\\n\n", "    table2.scale(1, 2)\\n\n", "    ax2.set_title('数据统计信息', fontsize=14, fontweight='bold', pad=20)\\n\n", "    \\n\n", "    plt.tight_layout()\\n\n", "    plt.savefig(os.path.join(output_folder, 'config_table.png'), dpi=300, bbox_inches='tight')\\n\n", "    plt.savefig(os.path.join(output_folder, 'config_table.pdf'), bbox_inches='tight')\\n\n", "    plt.show()\\n\n", "    \\n\n", "    print(\\\"✅ 配置参数表已生成\\\")\\n\n", "\\n\n", "def generate_html_report(training_data, output_folder):\\n\n", "    \\\"\\\"\\\"生成HTML格式的详细报告\\\"\\\"\\\"\\n\n", "    html_content = f\\\"\\\"\\\"\\n\n", "<!DOCTYPE html>\\n\n", "<html lang='zh-CN'>\\n\n", "<head>\\n\n", "    <meta charset='UTF-8'>\\n\n", "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\\n\n", "    <title>UNet训练报告 - 全部按需加载模式</title>\\n\n", "    <style>\\n\n", "        body {{ font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; margin: 20px; background-color: #f5f5f5; }}\\n\n", "        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}\\n\n", "        h1 {{ color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}\\n\n", "        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; }}\\n\n", "        .summary {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }}\\n\n", "        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }}\\n\n", "        .metric-card {{ background: #ecf0f1; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #3498db; }}\\n\n", "        .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}\\n\n", "        .metric-label {{ color: #7f8c8d; margin-top: 5px; }}\\n\n", "        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}\\n\n", "        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}\\n\n", "        th {{ background-color: #3498db; color: white; }}\\n\n", "        tr:nth-child(even) {{ background-color: #f2f2f2; }}\\n\n", "        .highlight {{ background-color: #e8f6f3; font-weight: bold; }}\\n\n", "        .timestamp {{ text-align: right; color: #7f8c8d; font-style: italic; }}\\n\n", "        .image-gallery {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin: 20px 0; }}\\n\n", "        .image-card {{ text-align: center; background: #f8f9fa; padding: 15px; border-radius: 8px; }}\\n\n", "        .image-card img {{ max-width: 100%; height: auto; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}\\n\n", "    </style>\\n\n", "</head>\\n\n", "<body>\\n\n", "    <div class='container'>\\n\n", "        <h1>🛰️ UNet卫星图像分割训练报告</h1>\\n\n", "        <div class='summary'>\\n\n", "            <h2 style='color: white; border: none; padding: 0;'>📋 训练概要</h2>\\n\n", "            <p><strong>训练模式:</strong> 全部按需加载模式 (use_all_data=True)</p>\\n\n", "            <p><strong>训练时间:</strong> {training_data['timestamp']}</p>\\n\n", "            <p><strong>模型架构:</strong> UNet (内存优化版本)</p>\\n\n", "            <p><strong>数据集:</strong> 卫星图像建筑物分割数据集</p>\\n\n", "        </div>\\n\n", "\\n\n", "        <h2>📊 最终训练指标</h2>\\n\n", "        <div class='metrics-grid'>\\n\n", "            <div class='metric-card'>\\n\n", "                <div class='metric-value'>{training_data['final_metrics']['val_dice_coef']:.4f}</div>\\n\n", "                <div class='metric-label'>验证Dice系数</div>\\n\n", "            </div>\\n\n", "            <div class='metric-card'>\\n\n", "                <div class='metric-value'>{training_data['final_metrics']['val_iou_coef']:.4f}</div>\\n\n", "                <div class='metric-label'>验证IoU系数</div>\\n\n", "            </div>\\n\n", "            <div class='metric-card'>\\n\n", "                <div class='metric-value'>{training_data['final_metrics']['val_accuracy']:.4f}</div>\\n\n", "                <div class='metric-label'>验证准确率</div>\\n\n", "            </div>\\n\n", "            <div class='metric-card'>\\n\n", "                <div class='metric-value'>{training_data['final_metrics']['val_loss']:.4f}</div>\\n\n", "                <div class='metric-label'>验证损失</div>\\n\n", "            </div>\\n\n", "        </div>\\n\n", "\\n\n", "        <h2>⚙️ 训练配置</h2>\\n\n", "        <table>\\n\n", "            <tr><th>参数</th><th>值</th></tr>\\n\n", "            <tr><td>训练模式</td><td class='highlight'>全部按需加载</td></tr>\\n\n", "            <tr><td>批次大小</td><td>{training_data['config']['batch_size']}</td></tr>\\n\n", "            <tr><td>训练轮数</td><td>{training_data['config']['epochs']}</td></tr>\\n\n", "            <tr><td>学习率</td><td>{training_data['config']['learning_rate']}</td></tr>\\n\n", "            <tr><td>图像块大小</td><td>{training_data['config']['patch_size']}x{training_data['config']['patch_size']}</td></tr>\\n\n", "            <tr><td>图像块步长</td><td>{training_data['config']['patch_step']}</td></tr>\\n\n", "            <tr><td>前景像素最小比例</td><td>{training_data['config']['min_foreground_ratio']}</td></tr>\\n\n", "            <tr><td>数据增强</td><td>{'是' if training_data['config']['use_data_augmentation'] else '否'}</td></tr>\\n\n", "            <tr><td>混合精度训练</td><td class='highlight'>{'是' if training_data['config'].get('enable_mixed_precision', False) else '否'}</td></tr>\\n\n", "            <tr><td>验证集比例</td><td>{training_data['config']['validation_split']}</td></tr>\\n\n", "        </table>\\n\n", "\\n\n", "        <h2>📈 数据统计</h2>\\n\n", "        <table>\\n\n", "            <tr><th>数据项</th><th>数量</th></tr>\\n\n", "            <tr><td>训练文件数</td><td>{training_data['data_stats']['train_files']}</td></tr>\\n\n", "            <tr><td>验证文件数</td><td>{training_data['data_stats']['val_files']}</td></tr>\\n\n", "            <tr><td>训练图像块数</td><td class='highlight'>{training_data['data_stats']['train_patches']:,}</td></tr>\\n\n", "            <tr><td>验证图像块数</td><td class='highlight'>{training_data['data_stats']['val_patches']:,}</td></tr>\\n\n", "            <tr><td>训练批次数</td><td>{training_data['data_stats']['train_batches']}</td></tr>\\n\n", "            <tr><td>验证批次数</td><td>{training_data['data_stats']['val_batches']}</td></tr>\\n\n", "            <tr><td>总图像块数</td><td class='highlight'>{training_data['data_stats']['train_patches'] + training_data['data_stats']['val_patches']:,}</td></tr>\\n\n", "        </table>\\n\n", "\\n\n", "        <h2>📊 训练结果图表</h2>\\n\n", "        <div class='image-gallery'>\\n\n", "            <div class='image-card'>\\n\n", "                <h3>训练曲线</h3>\\n\n", "                <img src='training_curves.png' alt='训练曲线'>\\n\n", "            </div>\\n\n", "            <div class='image-card'>\\n\n", "                <h3>指标对比</h3>\\n\n", "                <img src='metrics_comparison.png' alt='指标对比'>\\n\n", "            </div>\\n\n", "            <div class='image-card'>\\n\n", "                <h3>配置参数</h3>\\n\n", "                <img src='config_table.png' alt='配置参数表'>\\n\n", "            </div>\\n\n", "        </div>\\n\n", "\\n\n", "        <h2>📋 详细指标历史</h2>\\n\n", "        <table>\\n\n", "            <tr><th>指标</th><th>最终训练值</th><th>最终验证值</th><th>最佳验证值</th></tr>\\n\n", "            <tr><td>损失</td><td>{training_data['final_metrics']['loss']:.4f}</td><td>{training_data['final_metrics']['val_loss']:.4f}</td><td>{min(training_data['training_history']['val_loss']):.4f}</td></tr>\\n\n", "            <tr><td><PERSON><PERSON>系数</td><td>{training_data['final_metrics']['dice_coef']:.4f}</td><td>{training_data['final_metrics']['val_dice_coef']:.4f}</td><td>{max(training_data['training_history']['val_dice_coef']):.4f}</td></tr>\\n\n", "            <tr><td>IoU系数</td><td>{training_data['final_metrics']['iou_coef']:.4f}</td><td>{training_data['final_metrics']['val_iou_coef']:.4f}</td><td>{max(training_data['training_history']['val_iou_coef']):.4f}</td></tr>\\n\n", "            <tr><td>准确率</td><td>{training_data['final_metrics']['accuracy']:.4f}</td><td>{training_data['final_metrics']['val_accuracy']:.4f}</td><td>{max(training_data['training_history']['val_accuracy']):.4f}</td></tr>\\n\n", "        </table>\\n\n", "\\n\n", "        <div class='timestamp'>\\n\n", "            报告生成时间: {training_data['timestamp']}\\n\n", "        </div>\\n\n", "    </div>\\n\n", "</body>\\n\n", "</html>\\n\n", "    \\\"\\\"\\\"\\n\n", "    \\n\n", "    # 保存HTML报告\\n\n", "    html_path = os.path.join(output_folder, 'training_report.html')\\n\n", "    with open(html_path, 'w', encoding='utf-8') as f:\\n\n", "        f.write(html_content)\\n\n", "    \\n\n", "    print(f\\\"✅ HTML报告已生成: {html_path}\\\")\\n\n", "\\n\n", "# 📊 训练结果可视化\\n\n", "def plot_training_history(history):\\n\n", "    \\\"\\\"\\\"绘制训练历史\\\"\\\"\\\"\\n\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\\n\n", "    fig.suptitle('UNet训练历史 - 全部按需加载模式', fontsize=16)\\n\n", "    \n", "    # 损失\n", "    axes[0, 0].plot(history.history['loss'], label='训练损失')\n", "    axes[0, 0].plot(history.history['val_loss'], label='验证损失')\n", "    axes[0, 0].set_title('模型损失')\n", "    axes[0, 0].set_xlabel('轮次')\n", "    axes[0, 0].set_ylabel('损失')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True)\n", "    \n", "    # Dice系数\n", "    axes[0, 1].plot(history.history['dice_coef'], label='训练Dice')\n", "    axes[0, 1].plot(history.history['val_dice_coef'], label='验证Dice')\n", "    axes[0, 1].set_title('Dice系数')\n", "    axes[0, 1].set_xlabel('轮次')\n", "    axes[0, 1].set_ylabel('Dice系数')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True)\n", "    \n", "    # IoU系数\n", "    axes[1, 0].plot(history.history['iou_coef'], label='训练IoU')\n", "    axes[1, 0].plot(history.history['val_iou_coef'], label='验证IoU')\n", "    axes[1, 0].set_title('IoU系数')\n", "    axes[1, 0].set_xlabel('轮次')\n", "    axes[1, 0].set_ylabel('IoU系数')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True)\n", "    \n", "    # 准确率\n", "    axes[1, 1].plot(history.history['accuracy'], label='训练准确率')\n", "    axes[1, 1].plot(history.history['val_accuracy'], label='验证准确率')\n", "    axes[1, 1].set_title('准确率')\n", "    axes[1, 1].set_xlabel('轮次')\n", "    axes[1, 1].set_ylabel('准确率')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 保存图表\n", "    plot_path = os.path.join(OUTPUT_FOLDER, 'training_curves.png')\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"📊 训练曲线已保存: {plot_path}\")\n", "    \n", "    plt.show()\n", "\n", "# 如果训练成功，绘制训练历史\n", "try:\n", "    if 'history' in locals() and history is not None:\n", "        plot_training_history(history)\n", "    else:\n", "        print(\"⚠️  训练历史不可用，跳过可视化\")\n", "except Exception as e:\n", "    print(f\"⚠️  可视化失败: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 生成详细训练报告示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📋 如果需要单独生成报告（训练完成后）\n", "# 取消注释以下代码来生成详细报告\n", "\n", "# 假设你已经有了训练历史和配置\n", "# report_folder = generate_detailed_training_report(\n", "#     history=history,  # 训练历史对象\n", "#     config=TRAINING_CONFIG,  # 训练配置\n", "#     train_gen=train_generator,  # 训练数据生成器\n", "#     val_gen=val_generator,  # 验证数据生成器\n", "#     output_folder=OUTPUT_FOLDER  # 输出文件夹\n", "# )\n", "\n", "# print(f\"📊 详细报告已生成在: {report_folder}\")\n", "# print(\"📋 报告包含以下文件:\")\n", "# print(\"  - training_report.html (详细HTML报告)\")\n", "# print(\"  - training_curves.png/pdf (训练曲线图)\")\n", "# print(\"  - metrics_comparison.png/pdf (指标对比图)\")\n", "# print(\"  - config_table.png/pdf (配置参数表)\")\n", "# print(\"  - complete_training_data.json (完整训练数据)\")\n", "\n", "print(\"💡 训练完成后会自动生成详细报告\")\n", "print(\"📊 报告包含: 训练曲线、指标对比、配置表格、HTML报告等\")\n", "print(\"🎯 可用于向他人展示模型训练效果和精度\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 使用说明\n", "\n", "### 🔧 配置步骤\n", "\n", "1. **修改数据路径**: 在\"数据路径配置\"部分修改 `IMAGE_FOLDER` 和 `MASK_FOLDER` 路径\n", "2. **调整训练参数**: 根据您的硬件配置修改 `TRAINING_CONFIG` 中的参数\n", "3. **运行训练**: 按顺序执行所有代码块\n", "4. **查看报告**: 训练完成后自动生成详细报告\n", "\n", "### 📊 全部按需加载模式的优势\n", "\n", "- **🚀 最大化数据利用**: 使用所有可用的图像块，不限制数据量\n", "- **💾 内存友好**: 使用数据生成器，按需加载数据\n", "- **🔧 图像合并**: 自动检测并合并4个子瓦片为1个完整图像\n", "- **🎯 智能配对**: 改进的image-mask文件配对逻辑，支持tile_x_y格式\n", "- **⚡ 混合精度**: 支持混合精度训练加速\n", "- **🌐 云端优化**: 针对云端环境优化的参数配置\n", "- **📈 完整监控**: 包含Dice、IoU、准确率等多种指标\n", "- **🔄 自动保存**: 自动保存最佳模型和训练历史\n", "- **📋 详细报告**: 自动生成包含图表的详细训练报告\n", "\n", "### ⚙️ 关键参数说明\n", "\n", "- `batch_size`: 批次大小，根据GPU内存调整（推荐2-4）\n", "- `max_patches_per_file`: 每个文件最多图像块数量，控制数据量\n", "- `min_foreground_ratio`: 前景像素最小比例，过滤空白图像块\n", "- `use_data_augmentation`: 是否使用数据增强（翻转、旋转）\n", "- `memory_limit_gb`: 内存限制，用于监控\n", "\n", "### 🚀 适用场景\n", "\n", "**特别适合以下情况**:\n", "- 内存受限的云端环境（如32GB内存）\n", "- 大型数据集训练\n", "- 需要处理分块图像合并的场景\n", "- 长时间训练任务\n", "\n", "### 🔧 内存优化建议\n", "\n", "**如果仍然遇到内存问题**:\n", "1. 减少 `batch_size` 到 2\n", "2. 降低 `max_patches_per_file` 到 200\n", "3. 提高 `min_foreground_ratio` 到 0.05\n", "4. 关闭数据增强 `use_data_augmentation: False`\n", "\n", "### 📈 预期性能\n", "\n", "**32GB内存环境下**:\n", "- 可处理数千个图像块\n", "- 内存使用率保持在70-80%\n", "- 训练稳定，不会出现OOM错误\n", "- 支持完整的4个子瓦片合并\n", "\n", "### 🎯 训练完成后输出\n", "\n", "**模型文件**:\n", "- `best_unet_model.h5`: 验证集上表现最佳的模型\n", "- `final_unet_model_full_data.h5`: 全部按需加载模式训练的最终模型\n", "\n", "**训练数据**:\n", "- `training_history_full_data.json`: 完整的训练历史记录\n", "- `complete_training_data.json`: 包含配置和统计的完整数据\n", "\n", "**详细报告** (在 `training_report/` 文件夹中):\n", "- `training_report.html`: 详细的HTML格式报告，包含所有指标和图表\n", "- `training_curves.png/pdf`: 训练曲线图（损失、Dice、IoU、准确率）\n", "- `metrics_comparison.png/pdf`: 最终指标对比图\n", "- `config_table.png/pdf`: 训练配置和数据统计表格\n", "\n", "### 📊 报告展示功能\n", "\n", "**HTML报告包含**:\n", "- 🎯 训练概要和最终指标\n", "- ⚙️ 详细的训练配置参数\n", "- 📈 数据统计信息\n", "- 📊 高质量的训练曲线图表\n", "- 📋 指标历史对比表格\n", "- 🎨 专业的视觉设计，适合展示\n", "\n", "---\n", "\n", "**🎉 现在您可以在云端环境中进行全部按需加载模式的UNet训练，并获得详细的专业报告！**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}