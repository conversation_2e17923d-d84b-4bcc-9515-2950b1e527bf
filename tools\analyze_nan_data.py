#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaN数据分析工具
分析GEE下载图像中的NaN值分布和有效数据
"""

import os
import rasterio
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def analyze_image_nan(file_path, sample_size=1000):
    """
    分析单个图像文件中的NaN值分布
    """
    print(f"\n分析文件: {Path(file_path).name}")
    print("-" * 50)
    
    with rasterio.open(file_path) as src:
        height, width = src.height, src.width
        bands = src.count
        
        print(f"图像尺寸: {height} x {width} x {bands}")
        
        # 分析每个波段
        for band_idx in range(1, bands + 1):
            print(f"\n波段 {band_idx}:")
            
            # 读取整个波段（如果文件不太大）或采样
            if height * width < 50000000:  # 小于50M像素，读取全部
                band_data = src.read(band_idx)
                print(f"  读取完整数据: {band_data.shape}")
            else:
                # 大文件采样读取
                step_h = max(1, height // sample_size)
                step_w = max(1, width // sample_size)
                band_data = src.read(band_idx)[::step_h, ::step_w]
                print(f"  采样读取: {band_data.shape} (原始: {height}x{width})")
            
            # 统计NaN值
            total_pixels = band_data.size
            nan_pixels = np.isnan(band_data).sum()
            valid_pixels = total_pixels - nan_pixels
            
            print(f"  总像素数: {total_pixels:,}")
            print(f"  NaN像素数: {nan_pixels:,} ({nan_pixels/total_pixels*100:.1f}%)")
            print(f"  有效像素数: {valid_pixels:,} ({valid_pixels/total_pixels*100:.1f}%)")
            
            if valid_pixels > 0:
                valid_data = band_data[~np.isnan(band_data)]
                print(f"  有效数据范围: [{valid_data.min():.2f}, {valid_data.max():.2f}]")
                print(f"  有效数据均值: {valid_data.mean():.2f}")
                print(f"  有效数据标准差: {valid_data.std():.2f}")
                
                # 检查是否有其他异常值
                inf_pixels = np.isinf(valid_data).sum()
                if inf_pixels > 0:
                    print(f"  无穷值像素数: {inf_pixels:,}")
            else:
                print("  ⚠️  该波段没有有效数据！")
        
        return {
            'file': Path(file_path).name,
            'shape': (height, width, bands),
            'total_pixels': height * width * bands,
            'nan_ratio': nan_pixels / (height * width * bands) if bands > 0 else 1.0
        }

def create_valid_data_mask(input_folder, output_folder):
    """
    创建有效数据掩膜，用于训练
    """
    print("\n" + "="*60)
    print("创建有效数据掩膜")
    print("="*60)
    
    input_path = Path(input_folder)
    output_path = Path(output_folder)
    output_path.mkdir(parents=True, exist_ok=True)
    
    tiff_files = list(input_path.glob("*.tif")) + list(input_path.glob("*.tiff"))
    
    for tiff_file in tiff_files:
        print(f"\n处理: {tiff_file.name}")
        
        with rasterio.open(tiff_file) as src:
            # 读取所有波段
            image_data = src.read()
            profile = src.profile.copy()
            
            # 创建有效数据掩膜（所有波段都有效的像素）
            valid_mask = ~np.isnan(image_data).any(axis=0)
            
            valid_pixels = valid_mask.sum()
            total_pixels = valid_mask.size
            
            print(f"  有效像素比例: {valid_pixels/total_pixels*100:.1f}%")
            
            if valid_pixels > 0:
                # 对有效区域进行数据处理
                processed_data = np.zeros_like(image_data, dtype=np.float32)
                
                for band_idx in range(image_data.shape[0]):
                    band_data = image_data[band_idx]
                    valid_band_data = band_data[valid_mask]
                    
                    if len(valid_band_data) > 0:
                        # 使用有效数据的统计信息填充NaN
                        mean_val = valid_band_data.mean()
                        
                        # 创建处理后的波段
                        processed_band = band_data.copy()
                        processed_band[np.isnan(processed_band)] = mean_val
                        processed_data[band_idx] = processed_band
                        
                        print(f"  波段{band_idx+1}: 用均值{mean_val:.2f}填充NaN")
                
                # 保存处理后的数据
                output_file = output_path / f"processed_{tiff_file.name}"
                
                profile.update({
                    'dtype': 'float32',
                    'compress': 'lzw'
                })
                
                with rasterio.open(output_file, 'w', **profile) as dst:
                    dst.write(processed_data)
                
                print(f"  ✅ 保存到: {output_file.name}")
            else:
                print(f"  ❌ 该文件没有有效数据，跳过")

def main():
    """主函数"""
    image_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\image"
    
    print("="*60)
    print("GEE图像数据NaN分析")
    print("="*60)
    
    input_path = Path(image_folder)
    if not input_path.exists():
        print(f"❌ 文件夹不存在: {image_folder}")
        return
    
    tiff_files = list(input_path.glob("*.tif")) + list(input_path.glob("*.tiff"))
    
    if not tiff_files:
        print(f"❌ 未找到TIFF文件")
        return
    
    print(f"找到 {len(tiff_files)} 个图像文件")
    
    results = []
    for tiff_file in tiff_files:
        try:
            result = analyze_image_nan(tiff_file)
            results.append(result)
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    # 总结
    print("\n" + "="*60)
    print("分析总结")
    print("="*60)
    
    if results:
        total_files = len(results)
        high_nan_files = sum(1 for r in results if r['nan_ratio'] > 0.5)
        
        print(f"总文件数: {total_files}")
        print(f"高NaN比例文件(>50%): {high_nan_files}")
        
        if high_nan_files > 0:
            print(f"\n⚠️  发现 {high_nan_files} 个文件包含大量NaN值！")
            print("这可能是因为:")
            print("1. GEE导出区域超出了数据覆盖范围")
            print("2. 云层遮挡或数据质量问题")
            print("3. 导出参数设置不当")
            
            print(f"\n建议:")
            print("1. 检查GEE导出的区域设置")
            print("2. 使用云层过滤或质量掩膜")
            print("3. 重新导出数据或使用数据填充")
            
            # 询问是否创建处理后的数据
            output_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\processed"
            print(f"\n正在创建处理后的数据...")
            create_valid_data_mask(image_folder, output_folder)
        else:
            print("✅ 所有文件的数据质量良好")

if __name__ == "__main__":
    main()
