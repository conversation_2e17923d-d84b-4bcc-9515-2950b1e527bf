{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🛰️ UNet卫星图像建筑物分割训练 - 增强版（集成图像合并）\n", "\n", "本notebook集成了与本地代码一致的图像合并逻辑，适用于多种云平台\n", "\n", "## 📋 主要改进\n", "- **图像合并**: 自动检测并合并4个子瓦片为1个完整图像\n", "- **智能配对**: 改进的image-mask文件配对逻辑\n", "- **内存优化**: 优化内存使用，避免OOM错误\n", "- **平台兼容**: 支持Kaggle、<PERSON><PERSON>、Azure ML等平台\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 环境设置和依赖安装"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "opencv-python ********* requires numpy<2.3.0,>=2; python_version >= \"3.9\", but you have numpy 1.26.4 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0m\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m✅ 已安装TensorFlow 2.15.0\n", "安装 scikit-learn...\n", "Looking in indexes: http://mirrors.tencentyun.com/pypi/simple\n", "Requirement already satisfied: scikit-learn in /root/miniforge3/lib/python3.10/site-packages (1.7.1)\n", "Requirement already satisfied: numpy>=1.22.0 in /root/miniforge3/lib/python3.10/site-packages (from scikit-learn) (1.26.4)\n", "Requirement already satisfied: scipy>=1.8.0 in /root/miniforge3/lib/python3.10/site-packages (from scikit-learn) (1.15.3)\n", "Requirement already satisfied: joblib>=1.2.0 in /root/miniforge3/lib/python3.10/site-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /root/miniforge3/lib/python3.10/site-packages (from scikit-learn) (3.6.0)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-21 11:00:36.613505: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2025-07-21 11:00:36.666297: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:9261] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "2025-07-21 11:00:36.666334: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:607] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "2025-07-21 11:00:36.667506: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1515] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "2025-07-21 11:00:36.674985: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2025-07-21 11:00:37.616373: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n"]}, {"name": "stdout", "output_type": "stream", "text": ["安装 opencv-python...\n", "Looking in indexes: http://mirrors.tencentyun.com/pypi/simple\n", "Requirement already satisfied: opencv-python in /root/miniforge3/lib/python3.10/site-packages (*********)\n", "Collecting numpy<2.3.0,>=2 (from opencv-python)\n", "  Using cached http://mirrors.tencentyun.com/pypi/packages/b4/63/3de6a34ad7ad6646ac7d2f55ebc6ad439dbbf9c4370017c50cf403fb19b5/numpy-2.2.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.8 MB)\n", "Installing collected packages: numpy\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 1.26.4\n", "    Uninstalling numpy-1.26.4:\n", "      Successfully uninstalled numpy-1.26.4\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "patchify 0.2.3 requires numpy<2,>=1, but you have numpy 2.2.6 which is incompatible.\n", "tensorflow 2.15.0 requires numpy<2.0.0,>=1.23.5, but you have numpy 2.2.6 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed numpy-2.2.6\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mTensorFlow版本: 2.15.0\n", "GPU可用: []\n", "CUDA可用: True\n", "\n", "✅ 环境设置完成\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-21 11:00:43.727712: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:901] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2025-07-21 11:00:44.539604: W tensorflow/core/common_runtime/gpu/gpu_device.cc:2256] Cannot dlopen some GPU libraries. Please make sure the missing libraries mentioned above are installed properly if you would like to use GPU. Follow the guide at https://www.tensorflow.org/install/gpu for how to download and setup the required libraries for your platform.\n", "Skipping registering GPU devices...\n"]}], "source": ["# 🔧 安装兼容的TensorFlow版本\n", "! pip uninstall tensorflow -y -q\n", "! pip install tensorflow==2.15.0 -q\n", "print(\"✅ 已安装TensorFlow 2.15.0\")\n", "\n", "# 安装必要的依赖包\n", "import sys\n", "\n", "required_packages = [\n", "    'rasterio', 'geopandas', 'patchify', 'scikit-learn', \n", "    'matplotlib', 'seaborn', 'tensorflow', 'opencv-python'\n", "]\n", "\n", "for package in required_packages:\n", "    try:\n", "        __import__(package.replace('-', '_'))\n", "    except ImportError:\n", "        print(f\"安装 {package}...\")\n", "        !{sys.executable} -m pip install {package}\n", "\n", "# 导入基础库\n", "import os\n", "import sys\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 检查TensorFlow和GPU\n", "import tensorflow as tf\n", "print(f\"TensorFlow版本: {tf.__version__}\")\n", "print(f\"GPU可用: {tf.config.list_physical_devices('GPU')}\")\n", "print(f\"CUDA可用: {tf.test.is_built_with_cuda()}\")\n", "\n", "# 配置GPU内存增长\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "if gpus:\n", "    try:\n", "        for gpu in gpus:\n", "            tf.config.experimental.set_memory_growth(gpu, True)\n", "        print(f\"✅ GPU配置完成: {len(gpus)} 个GPU\")\n", "    except RuntimeError as e:\n", "        print(f\"❌ GPU配置失败: {e}\")\n", "\n", "print(\"\\n✅ 环境设置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📁 数据路径配置 - **请根据您的平台修改此部分**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 使用默认本地环境配置\n", "\n", "📁 数据路径配置:\n", "  平台: local\n", "  图像文件夹: /workspace/converted/image\n", "  掩膜文件夹: /workspace/converted/mask\n", "  输出文件夹: ./models\n", "\n", "✅ 路径配置完成\n"]}], "source": ["# ==========================================\n", "# 📍 数据路径配置 - 请根据您的平台修改\n", "# ==========================================\n", "\n", "# 🔧 平台检测和路径配置\n", "def detect_platform_and_configure_paths():\n", "    \"\"\"自动检测平台并配置路径\"\"\"\n", "    \n", "    # Kaggle平台检测\n", "    if '/kaggle/' in os.getcwd():\n", "        print(\"🔍 检测到Kaggle平台\")\n", "        return {\n", "            'platform': 'kaggle',\n", "            'image_folder': '/kaggle/input/your-dataset/converted/image',\n", "            'mask_folder': '/kaggle/input/your-dataset/converted/mask',\n", "            'output_folder': '/kaggle/working/models'\n", "        }\n", "    \n", "    # Google Colab检测\n", "    elif 'google.colab' in sys.modules:\n", "        print(\"🔍 检测到Google Colab平台\")\n", "        return {\n", "            'platform': 'colab',\n", "            'image_folder': '/content/drive/MyDrive/dataset/converted/image',\n", "            'mask_folder': '/content/drive/MyDrive/dataset/converted/mask',\n", "            'output_folder': '/content/drive/MyDrive/models'\n", "        }\n", "    \n", "    # Azure ML检测\n", "    elif 'AZUREML_' in os.environ:\n", "        print(\"🔍 检测到Azure ML平台\")\n", "        return {\n", "            'platform': 'azure',\n", "            'image_folder': './data/converted/image',\n", "            'mask_folder': './data/converted/mask',\n", "            'output_folder': './outputs/models'\n", "        }\n", "    \n", "    # 默认本地环境\n", "    else:\n", "        print(\"🔍 使用默认本地环境配置\")\n", "        return {\n", "            'platform': 'local',\n", "            'image_folder': '/workspace/converted/image',\n", "            'mask_folder': '/workspace/converted/mask',\n", "            'output_folder': './models'\n", "        }\n", "\n", "# 获取平台配置\n", "config = detect_platform_and_configure_paths()\n", "\n", "# 📁 数据路径设置\n", "IMAGE_FOLDER = config['image_folder']\n", "MASK_FOLDER = config['mask_folder']\n", "OUTPUT_FOLDER = config['output_folder']\n", "\n", "print(f\"\\n📁 数据路径配置:\")\n", "print(f\"  平台: {config['platform']}\")\n", "print(f\"  图像文件夹: {IMAGE_FOLDER}\")\n", "print(f\"  掩膜文件夹: {MASK_FOLDER}\")\n", "print(f\"  输出文件夹: {OUTPUT_FOLDER}\")\n", "\n", "# 创建输出文件夹\n", "os.makedirs(OUTPUT_FOLDER, exist_ok=True)\n", "\n", "# 验证路径存在\n", "if not os.path.exists(IMAGE_FOLDER):\n", "    print(f\"⚠️  警告: 图像文件夹不存在: {IMAGE_FOLDER}\")\n", "    print(\"请修改上面的路径配置\")\n", "if not os.path.exists(MASK_FOLDER):\n", "    print(f\"⚠️  警告: 掩膜文件夹不存在: {MASK_FOLDER}\")\n", "    print(\"请修改上面的路径配置\")\n", "\n", "print(\"\\n✅ 路径配置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 图像合并器 - 与本地代码一致"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 图像合并器类定义完成\n"]}], "source": ["# ==========================================\n", "# 🔧 图像合并器类 - 与本地代码一致\n", "# ==========================================\n", "import re\n", "import tempfile\n", "import shutil\n", "import rasterio\n", "from rasterio.merge import merge\n", "from pathlib import Path\n", "\n", "class ImageMerger:\n", "    \"\"\"图像合并器，用于合并GEE下载的分块图像\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.supported_formats = ['.tif', '.tiff']\n", "    \n", "    def find_related_images(self, image_folder, base_name=None):\n", "        \"\"\"查找相关的图像文件\"\"\"\n", "        image_folder = Path(image_folder)\n", "        if not image_folder.exists():\n", "            raise FileNotFoundError(f\"图像文件夹不存在: {image_folder}\")\n", "        \n", "        # 获取所有图像文件\n", "        image_files = []\n", "        for ext in self.supported_formats:\n", "            image_files.extend(image_folder.glob(f\"*{ext}\"))\n", "        \n", "        if not image_files:\n", "            raise FileNotFoundError(f\"在文件夹中未找到图像文件: {image_folder}\")\n", "        \n", "        # 按基础名称分组\n", "        grouped_files = {}\n", "        \n", "        for img_file in image_files:\n", "            # 提取基础名称（去除可能的分块后缀）\n", "            name = img_file.stem\n", "            \n", "            # 常见的GEE分块模式，优先匹配tile格式\n", "            patterns = [\n", "                r'(tile_\\d+_\\d+)_img-\\d+-\\d+$',  # tile_x_y_img-offset1-offset2\n", "                r'(.+)-\\d+-\\d+-\\d+$',            # name-0-0-0\n", "                r'(.+)_\\d+_\\d+$',                # name_1_2\n", "                r'(.+)-part\\d+$',                # name-part1\n", "                r'(.+)-\\d+$',                    # name-1\n", "                r'(.+)_\\d+$',                    # name_1\n", "            ]\n", "            \n", "            base = name\n", "            for pattern in patterns:\n", "                match = re.match(pattern, name)\n", "                if match:\n", "                    base = match.group(1)\n", "                    break\n", "            \n", "            if base not in grouped_files:\n", "                grouped_files[base] = []\n", "            grouped_files[base].append(img_file)\n", "        \n", "        return grouped_files\n", "    \n", "    def _extract_tile_base_name(self, filename):\n", "        \"\"\"提取瓦片的基础名称\"\"\"\n", "        # 匹配 tile_x_y 模式\n", "        tile_pattern = r'(tile_\\d+_\\d+)'\n", "        match = re.search(tile_pattern, filename.lower())\n", "        if match:\n", "            return match.group(1)\n", "        return filename\n", "    \n", "    def merge_images(self, image_files, output_path, method='first'):\n", "        \"\"\"合并多个图像文件\"\"\"\n", "        if not image_files:\n", "            raise ValueError(\"没有提供图像文件\")\n", "        \n", "        if len(image_files) == 1:\n", "            # 只有一个文件，直接复制\n", "            shutil.copy2(image_files[0], output_path)\n", "            print(f\"单个文件复制完成: {output_path}\")\n", "            return output_path\n", "        \n", "        print(f\"开始合并 {len(image_files)} 个图像文件...\")\n", "        \n", "        # 打开所有图像文件\n", "        src_files = []\n", "        try:\n", "            for img_file in image_files:\n", "                src = rasterio.open(img_file)\n", "                src_files.append(src)\n", "                print(f\"  - {img_file.name}\")\n", "            \n", "            # 合并图像\n", "            mosaic, out_trans = merge(src_files, method=method)\n", "            \n", "            # 获取元数据\n", "            out_meta = src_files[0].meta.copy()\n", "            out_meta.update({\n", "                \"driver\": \"<PERSON>iff\",\n", "                \"height\": mosaic.shape[1],\n", "                \"width\": mosaic.shape[2],\n", "                \"count\": mosaic.shape[0],\n", "                \"transform\": out_trans,\n", "                \"compress\": \"lzw\",\n", "                \"tiled\": True,\n", "                \"blockxsize\": 512,\n", "                \"blockysize\": 512,\n", "                \"interleave\": \"pixel\"\n", "            })\n", "            \n", "            # 保存合并后的图像\n", "            os.makedirs(os.path.dirname(output_path), exist_ok=True)\n", "            with rasterio.open(output_path, \"w\", **out_meta) as dest:\n", "                dest.write(mosaic)\n", "            \n", "            print(f\"图像合并完成: {os.path.basename(output_path)}\")\n", "            print(f\"输出图像尺寸: {mosaic.shape[1]} x {mosaic.shape[2]}\")\n", "            \n", "        finally:\n", "            # 关闭所有文件\n", "            for src in src_files:\n", "                src.close()\n", "        \n", "        return output_path\n", "    \n", "    def merge_folder_images(self, image_folder, output_folder, base_name=None):\n", "        \"\"\"合并文件夹中的所有相关图像\"\"\"\n", "        # 查找相关图像\n", "        grouped_files = self.find_related_images(image_folder, base_name)\n", "        \n", "        # 创建输出文件夹\n", "        os.makedirs(output_folder, exist_ok=True)\n", "        \n", "        results = {}\n", "        \n", "        for base, files in grouped_files.items():\n", "            if len(files) > 1:\n", "                # 需要合并\n", "                tile_base = self._extract_tile_base_name(base)\n", "                output_path = os.path.join(output_folder, f\"{tile_base}_merged.tif\")\n", "                merged_path = self.merge_images(files, output_path)\n", "                results[base] = merged_path\n", "                print(f\"✅ 合并完成: {base} -> {os.path.basename(merged_path)}\")\n", "            else:\n", "                # 单个文件，直接复制\n", "                tile_base = self._extract_tile_base_name(base)\n", "                output_path = os.path.join(output_folder, f\"{tile_base}.tif\")\n", "                shutil.copy2(files[0], output_path)\n", "                results[base] = output_path\n", "                print(f\"✅ 复制完成: {base} -> {os.path.basename(output_path)}\")\n", "        \n", "        return results\n", "\n", "print(\"✅ 图像合并器类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 数据加载和处理 - 集成图像合并功能"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 训练配置:\n", "  batch_size: 4\n", "  epochs: 50\n", "  learning_rate: 0.0001\n", "  validation_split: 0.2\n", "  patch_size: 256\n", "  patch_step: 128\n", "  min_foreground_ratio: 0.01\n", "  max_patches_per_file: 400\n", "  max_total_patches: 5000\n", "  use_all_files: True\n", "✅ 数据加载函数定义完成\n"]}], "source": ["from patchify import patchify\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 训练配置 - 内存优化版本\n", "TRAINING_CONFIG = {\n", "    'batch_size': 4,          # 减小批次大小避免内存溢出\n", "    'epochs': 50,             # 最大训练轮数\n", "    'learning_rate': 1e-4,    # 学习率\n", "    'validation_split': 0.2,  # 验证集比例\n", "    'patch_size': 256,        # 图像块大小\n", "    'patch_step': 128,        # 图像块步长\n", "    'min_foreground_ratio': 0.01,  # 提高阈值过滤更多空白块\n", "    'max_patches_per_file': 400,   # 每个文件最多500个图像块\n", "    'max_total_patches': 5000,     # 总共最多5000个图像块\n", "    'use_all_files': True          # 使用全部文件（云端版本）\n", "}\n", "\n", "print(\"📦 训练配置:\")\n", "for key, value in TRAINING_CONFIG.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "def create_patches(image, mask, patch_size=256, step=224):\n", "    \"\"\"创建图像块，确保图像和掩膜尺寸匹配\"\"\"\n", "    print(f\"创建图像块: patch_size={patch_size}, step={step}\")\n", "    print(f\"输入图像尺寸: {image.shape}\")\n", "    print(f\"输入掩膜尺寸: {mask.shape}\")\n", "    \n", "    # 检查并调整尺寸匹配\n", "    img_h, img_w = image.shape[:2]\n", "    mask_h, mask_w = mask.shape[:2]\n", "    \n", "    if img_h != mask_h or img_w != mask_w:\n", "        print(f\"⚠️  图像和掩膜尺寸不匹配，进行裁剪对齐...\")\n", "        # 取较小的尺寸进行裁剪\n", "        min_h = min(img_h, mask_h)\n", "        min_w = min(img_w, mask_w)\n", "        \n", "        image = image[:min_h, :min_w]\n", "        mask = mask[:min_h, :min_w]\n", "        \n", "        print(f\"对齐后图像尺寸: {image.shape}\")\n", "        print(f\"对齐后掩膜尺寸: {mask.shape}\")\n", "    \n", "    # 图像块\n", "    img_patches = patchify(image, (patch_size, patch_size, image.shape[2]), step=step)\n", "    img_patches = img_patches.reshape(-1, patch_size, patch_size, image.shape[2])\n", "    \n", "    # 掩膜块\n", "    mask_expanded = np.expand_dims(mask, axis=-1)\n", "    mask_patches = patchify(mask_expanded, (patch_size, patch_size, 1), step=step)\n", "    mask_patches = mask_patches.reshape(-1, patch_size, patch_size, 1)\n", "    \n", "    print(f\"生成图像块数量: {len(img_patches)}\")\n", "    print(f\"生成掩膜块数量: {len(mask_patches)}\")\n", "    \n", "    # 确保图像块和掩膜块数量一致\n", "    if len(img_patches) != len(mask_patches):\n", "        min_patches = min(len(img_patches), len(mask_patches))\n", "        print(f\"⚠️  图像块和掩膜块数量不一致，调整为: {min_patches}\")\n", "        img_patches = img_patches[:min_patches]\n", "        mask_patches = mask_patches[:min_patches]\n", "    \n", "    return img_patches, mask_patches\n", "\n", "def load_and_process_data_with_merge(image_folder, mask_folder, config):\n", "    \"\"\"加载和处理数据 - 集成图像合并功能\"\"\"\n", "    print(\"📦 开始加载和处理数据（集成图像合并）...\")\n", "    \n", "    # 获取原始文件列表\n", "    image_files = []\n", "    mask_files = []\n", "    \n", "    for ext in ['.tif', '.tiff']:\n", "        image_files.extend(Path(image_folder).glob(f\"*{ext}\"))\n", "        mask_files.extend(Path(mask_folder).glob(f\"*{ext}\"))\n", "    \n", "    image_files = [str(f) for f in image_files]\n", "    mask_files = [str(f) for f in mask_files]\n", "    \n", "    print(f\"找到 {len(image_files)} 个图像文件，{len(mask_files)} 个掩膜文件\")\n", "    \n", "    # 🔧 检查是否需要合并分块图像\n", "    print(\"\\n🔧 检查是否需要合并分块图像...\")\n", "    \n", "    merger = ImageMerger()\n", "    temp_folder = None\n", "    \n", "    try:\n", "        grouped_files = merger.find_related_images(image_folder)\n", "        \n", "        # 检查是否有需要合并的组\n", "        needs_merge = any(len(files) > 1 for files in grouped_files.values())\n", "        \n", "        if needs_merge:\n", "            print(\"✅ 发现分块图像，开始合并...\")\n", "            \n", "            # 创建临时文件夹\n", "            temp_folder = tempfile.mkdtemp(prefix='merged_images_')\n", "            print(f\"临时合并文件夹: {temp_folder}\")\n", "            \n", "            # 合并图像\n", "            merged_results = merger.merge_folder_images(image_folder, temp_folder)\n", "            \n", "            # 更新图像文件列表为合并后的文件\n", "            image_files = [path for path in merged_results.values()]\n", "            print(f\"\\n✅ 图像合并完成，共 {len(image_files)} 个文件\")\n", "            \n", "            # 打印合并后的文件名\n", "            for merged_file in image_files:\n", "                print(f\"  合并后文件: {os.path.basename(merged_file)}\")\n", "        else:\n", "            print(\"ℹ️  未发现需要合并的分块图像\")\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️  图像合并检查失败，使用原始文件: {e}\")\n", "    \n", "    # 🔧 改进的文件匹配逻辑\n", "    print(\"\\n🔧 开始智能文件配对...\")\n", "    matched_pairs = []\n", "    \n", "    for img_file in image_files:\n", "        img_name = os.path.basename(img_file)\n", "        \n", "        # 提取瓦片基础名称\n", "        tile_base = None\n", "        if 'tile_' in img_name.lower():\n", "            # 使用正则表达式提取tile_x_y\n", "            tile_pattern = r'(tile_\\d+_\\d+)'\n", "            match = re.search(tile_pattern, img_name.lower())\n", "            if match:\n", "                tile_base = match.group(1)\n", "        \n", "        if tile_base:\n", "            # 寻找匹配的掩膜文件\n", "            for mask_file in mask_files:\n", "                mask_name = os.path.basename(mask_file)\n", "                if tile_base in mask_name.lower():\n", "                    matched_pairs.append((img_file, mask_file))\n", "                    print(f\"✅ 配对成功: {os.path.basename(img_file)} <-> {os.path.basename(mask_file)}\")\n", "                    break\n", "        else:\n", "            print(f\"⚠️  无法提取瓦片信息: {img_name}\")\n", "    \n", "    print(f\"\\n📊 智能匹配结果: {len(matched_pairs)} 对文件\")\n", "    \n", "    if not matched_pairs:\n", "        raise ValueError(\"未找到任何匹配的图像-掩膜对！请检查文件命名和路径。\")\n", "    \n", "    # 🔧 处理数据并创建图像块\n", "    all_image_patches = []\n", "    all_mask_patches = []\n", "    total_patches = 0\n", "    \n", "    for i, (img_file, mask_file) in enumerate(matched_pairs):\n", "        # 检查是否已达到总图像块限制\n", "        if not config['use_all_files'] and total_patches >= config['max_total_patches']:\n", "            print(f\"🛑 已达到最大图像块数量限制 ({config['max_total_patches']})，停止处理\")\n", "            break\n", "            \n", "        print(f\"\\n处理文件对 {i+1}/{len(matched_pairs)}: {os.path.basename(img_file)}\")\n", "        \n", "        try:\n", "            # 读取图像和掩膜\n", "            with rasterio.open(img_file) as src:\n", "                image = src.read().transpose(1, 2, 0)  # 转换为HWC格式\n", "                if image.shape[2] > 3:\n", "                    image = image[:, :, :3]  # 只取前3个波段\n", "            \n", "            with rasterio.open(mask_file) as src:\n", "                mask = src.read(1)  # 读取第一个波段\n", "            \n", "            print(f\"  原始图像尺寸: {image.shape}\")\n", "            print(f\"  原始掩膜尺寸: {mask.shape}\")\n", "            \n", "            # 创建图像块\n", "            img_patches, mask_patches = create_patches(\n", "                image, mask, \n", "                patch_size=config['patch_size'], \n", "                step=config['patch_step']\n", "            )\n", "            \n", "            # 过滤有效的图像块\n", "            valid_patches = []\n", "            valid_masks = []\n", "            \n", "            for j, (img_patch, mask_patch) in enumerate(zip(img_patches, mask_patches)):\n", "                # 计算前景像素比例\n", "                foreground_ratio = np.sum(mask_patch > 0) / mask_patch.size\n", "                \n", "                if foreground_ratio >= config['min_foreground_ratio']:\n", "                    valid_patches.append(img_patch)\n", "                    valid_masks.append(mask_patch)\n", "            \n", "            # 限制每个文件的图像块数量\n", "            if not config['use_all_files'] and len(valid_patches) > config['max_patches_per_file']:\n", "                indices = np.random.choice(len(valid_patches), config['max_patches_per_file'], replace=False)\n", "                valid_patches = [valid_patches[idx] for idx in indices]\n", "                valid_masks = [valid_masks[idx] for idx in indices]\n", "                print(f\"  🎲 随机选择 {len(valid_patches)} 个图像块\")\n", "            \n", "            print(f\"  ✅ 保留 {len(valid_patches)}/{len(img_patches)} 个有效图像块\")\n", "            \n", "            all_image_patches.extend(valid_patches)\n", "            all_mask_patches.extend(valid_masks)\n", "            total_patches += len(valid_patches)\n", "            \n", "        except Exception as e:\n", "            print(f\"  ❌ 处理文件失败: {e}\")\n", "            continue\n", "    \n", "    # 清理临时文件夹\n", "    if temp_folder and os.path.exists(temp_folder):\n", "        print(f\"\\n🧹 清理临时文件: {temp_folder}\")\n", "        shutil.rmtree(temp_folder)\n", "    \n", "    # 转换为numpy数组\n", "    X = np.array(all_image_patches, dtype=np.float32)\n", "    y = np.array(all_mask_patches, dtype=np.float32)\n", "    \n", "    print(f\"\\n📊 数据集创建完成:\")\n", "    print(f\"  总图像块数: {len(X)}\")\n", "    print(f\"  图像块形状: {X.shape}\")\n", "    print(f\"  掩膜块形状: {y.shape}\")\n", "    \n", "    # 数据预处理\n", "    X = X / 255.0  # 归一化到[0,1]\n", "    y = (y > 0).astype(np.float32)  # 二值化掩膜\n", "    \n", "    # 转换为分类格式\n", "    y_categorical = np.zeros((*y.shape[:3], 2), dtype=np.float32)\n", "    y_categorical[..., 0] = (y[..., 0] == 0)  # 背景\n", "    y_categorical[..., 1] = (y[..., 0] == 1)  # 前景\n", "    \n", "    print(f\"  预处理后图像范围: [{X.min():.3f}, {X.max():.3f}]\")\n", "    print(f\"  预处理后掩膜形状: {y_categorical.shape}\")\n", "    print(f\"  类别分布: 背景={np.sum(y_categorical[..., 0]):.0f}, 前景={np.sum(y_categorical[..., 1]):.0f}\")\n", "    \n", "    return X, y_categorical\n", "\n", "print(\"✅ 数据加载函数定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 UNet模型定义"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 创建UNet模型...\n", "✅ 模型创建完成，参数数量: 31,055,362\n"]}], "source": ["from tensorflow import keras\n", "from tensorflow.keras import layers\n", "\n", "def conv_block(inputs, num_filters):\n", "    \"\"\"卷积块\"\"\"\n", "    x = layers.Conv2D(num_filters, 3, padding=\"same\")(inputs)\n", "    x = layers.BatchNormalization()(x)\n", "    x = layers.Activation(\"relu\")(x)\n", "    \n", "    x = layers.Conv2D(num_filters, 3, padding=\"same\")(x)\n", "    x = layers.BatchNormalization()(x)\n", "    x = layers.Activation(\"relu\")(x)\n", "    \n", "    return x\n", "\n", "def encoder_block(inputs, num_filters):\n", "    \"\"\"编码器块\"\"\"\n", "    x = conv_block(inputs, num_filters)\n", "    p = layers.MaxPool2D((2, 2))(x)\n", "    return x, p\n", "\n", "def decoder_block(inputs, skip_features, num_filters):\n", "    \"\"\"解码器块\"\"\"\n", "    x = layers.Conv2DTranspose(num_filters, (2, 2), strides=2, padding=\"same\")(inputs)\n", "    x = layers.Concatenate()([x, skip_features])\n", "    x = conv_block(x, num_filters)\n", "    return x\n", "\n", "def create_complete_unet(input_shape=(256, 256, 3), num_classes=2):\n", "    \"\"\"创建完整的UNet模型\"\"\"\n", "    inputs = layers.Input(input_shape)\n", "    \n", "    # 编码器\n", "    s1, p1 = encoder_block(inputs, 64)\n", "    s2, p2 = encoder_block(p1, 128)\n", "    s3, p3 = encoder_block(p2, 256)\n", "    s4, p4 = encoder_block(p3, 512)\n", "    \n", "    # 桥接\n", "    b1 = conv_block(p4, 1024)\n", "    \n", "    # 解码器\n", "    d1 = decoder_block(b1, s4, 512)\n", "    d2 = decoder_block(d1, s3, 256)\n", "    d3 = decoder_block(d2, s2, 128)\n", "    d4 = decoder_block(d3, s1, 64)\n", "    \n", "    # 输出层\n", "    outputs = layers.Conv2D(num_classes, 1, padding=\"same\", activation=\"softmax\")(d4)\n", "    \n", "    model = keras.Model(inputs, outputs, name=\"UNet\")\n", "    return model\n", "\n", "# 损失函数和指标\n", "def dice_coef(y_true, y_pred, smooth=1e-6):\n", "    \"\"\"Dice系数\"\"\"\n", "    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)\n", "    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)\n", "    \n", "    intersection = tf.reduce_sum(y_true_f * y_pred_f)\n", "    dice = (2. * intersection + smooth) / (tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) + smooth)\n", "    \n", "    return tf.cast(dice, tf.float32)\n", "\n", "def dice_loss(y_true, y_pred):\n", "    \"\"\"Dice损失\"\"\"\n", "    return 1 - dice_coef(y_true, y_pred)\n", "\n", "def focal_loss(y_true, y_pred, alpha=0.25, gamma=2.0):\n", "    \"\"\"Focal损失\"\"\"\n", "    epsilon = tf.keras.backend.epsilon()\n", "    y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)\n", "    \n", "    ce = -y_true * tf.math.log(y_pred)\n", "    weight = alpha * y_true * tf.pow((1 - y_pred), gamma)\n", "    focal = weight * ce\n", "    \n", "    return tf.reduce_mean(tf.reduce_sum(focal, axis=-1))\n", "\n", "def combined_loss(y_true, y_pred):\n", "    \"\"\"组合损失函数\"\"\"\n", "    dice = dice_loss(y_true, y_pred)\n", "    focal = focal_loss(y_true, y_pred)\n", "    \n", "    # 数值稳定性检查\n", "    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))\n", "    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))\n", "    \n", "    combined = dice + focal\n", "    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))\n", "    \n", "    return tf.cast(combined, tf.float32)\n", "\n", "def iou_coef(y_true, y_pred, smooth=1e-6):\n", "    \"\"\"IoU系数\"\"\"\n", "    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)\n", "    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)\n", "    \n", "    intersection = tf.reduce_sum(y_true_f * y_pred_f)\n", "    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection\n", "    \n", "    iou = (intersection + smooth) / (union + smooth)\n", "    return tf.cast(iou, tf.float32)\n", "\n", "# 创建模型\n", "print(\"🧠 创建UNet模型...\")\n", "model = create_complete_unet(input_shape=(256, 256, 3), num_classes=2)\n", "print(f\"✅ 模型创建完成，参数数量: {model.count_params():,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 执行训练"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始UNet训练流程...\n", "============================================================\n", "📦 步骤1: 加载和处理数据\n", "📦 开始加载和处理数据（集成图像合并）...\n", "找到 12 个图像文件，3 个掩膜文件\n", "\n", "🔧 检查是否需要合并分块图像...\n", "✅ 发现分块图像，开始合并...\n", "临时合并文件夹: /tmp/merged_images_lmvu4n45\n", "开始合并 4 个图像文件...\n", "  - tile_0_2_img-0000013568-0000000000.tif\n", "  - tile_0_2_img-0000013568-0000013568.tif\n", "  - tile_0_2_img-0000000000-0000000000.tif\n", "  - tile_0_2_img-0000000000-0000013568.tif\n", "图像合并完成: tile_0_2_merged.tif\n", "输出图像尺寸: 15628 x 22265\n", "✅ 合并完成: tile_0_2 -> tile_0_2_merged.tif\n", "开始合并 4 个图像文件...\n", "  - tile_0_1_img-0000013568-0000013568.tif\n", "  - tile_0_1_img-0000013568-0000000000.tif\n", "  - tile_0_1_img-0000000000-0000013568.tif\n", "  - tile_0_1_img-0000000000-0000000000.tif\n", "图像合并完成: tile_0_1_merged.tif\n", "输出图像尺寸: 15627 x 22265\n", "✅ 合并完成: tile_0_1 -> tile_0_1_merged.tif\n", "开始合并 4 个图像文件...\n", "  - tile_0_0_img-0000013568-0000013568.tif\n", "  - tile_0_0_img-0000000000-0000000000.tif\n", "  - tile_0_0_img-0000000000-0000013568.tif\n", "  - tile_0_0_img-0000013568-0000000000.tif\n", "图像合并完成: tile_0_0_merged.tif\n", "输出图像尺寸: 15626 x 22265\n", "✅ 合并完成: tile_0_0 -> tile_0_0_merged.tif\n", "\n", "✅ 图像合并完成，共 3 个文件\n", "  合并后文件: tile_0_2_merged.tif\n", "  合并后文件: tile_0_1_merged.tif\n", "  合并后文件: tile_0_0_merged.tif\n", "\n", "🔧 开始智能文件配对...\n", "✅ 配对成功: tile_0_2_merged.tif <-> tile_0_2_mask.tif\n", "✅ 配对成功: tile_0_1_merged.tif <-> tile_0_1_mask.tif\n", "✅ 配对成功: tile_0_0_merged.tif <-> tile_0_0_mask.tif\n", "\n", "📊 智能匹配结果: 3 对文件\n", "\n", "处理文件对 1/3: tile_0_2_merged.tif\n", "  原始图像尺寸: (15628, 22265, 3)\n", "  原始掩膜尺寸: (15628, 22265)\n", "创建图像块: patch_size=256, step=128\n", "输入图像尺寸: (15628, 22265, 3)\n", "输入掩膜尺寸: (15628, 22265)\n", "生成图像块数量: 20812\n", "生成掩膜块数量: 20812\n", "  ✅ 保留 19930/20812 个有效图像块\n", "\n", "处理文件对 2/3: tile_0_1_merged.tif\n", "  原始图像尺寸: (15627, 22265, 3)\n", "  原始掩膜尺寸: (15627, 22265)\n", "创建图像块: patch_size=256, step=128\n", "输入图像尺寸: (15627, 22265, 3)\n", "输入掩膜尺寸: (15627, 22265)\n", "生成图像块数量: 20812\n", "生成掩膜块数量: 20812\n", "  ✅ 保留 17323/20812 个有效图像块\n", "\n", "处理文件对 3/3: tile_0_0_merged.tif\n", "  原始图像尺寸: (15626, 22265, 3)\n", "  原始掩膜尺寸: (15626, 22265)\n", "创建图像块: patch_size=256, step=128\n", "输入图像尺寸: (15626, 22265, 3)\n", "输入掩膜尺寸: (15626, 22265)\n", "生成图像块数量: 20812\n", "生成掩膜块数量: 20812\n", "  ✅ 保留 3256/20812 个有效图像块\n", "\n", "🧹 清理临时文件: /tmp/merged_images_lmvu4n45\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mCanceled future for execute_request message before replies were done"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mT<PERSON> crashed while executing code in the the current cell or a previous cell. Please review the code in the cell(s) to identify a possible cause of the failure. Click <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. View Jupyter <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# 🚀 开始训练流程\n", "print(\"🚀 开始UNet训练流程...\")\n", "print(\"=\" * 60)\n", "\n", "try:\n", "    # 1. 加载和处理数据\n", "    print(\"📦 步骤1: 加载和处理数据\")\n", "    X, y = load_and_process_data_with_merge(IMAGE_FOLDER, MASK_FOLDER, TRAINING_CONFIG)\n", "    \n", "    if len(X) == 0:\n", "        raise ValueError(\"没有加载到任何数据！请检查数据路径和文件。\")\n", "    \n", "    # 2. 数据分割\n", "    print(\"\\n🔄 步骤2: 分割训练和验证数据\")\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X, y, \n", "        test_size=TRAINING_CONFIG['validation_split'], \n", "        random_state=42,\n", "        stratify=None  # 对于图像分割任务，通常不需要分层\n", "    )\n", "    \n", "    print(f\"训练集: {X_train.shape[0]} 个样本\")\n", "    print(f\"验证集: {X_val.shape[0]} 个样本\")\n", "    \n", "    # 3. 编译模型\n", "    print(\"\\n⚙️ 步骤3: 编译模型\")\n", "    model.compile(\n", "        optimizer=keras.optimizers.<PERSON>(learning_rate=TRAINING_CONFIG['learning_rate']),\n", "        loss=combined_loss,\n", "        metrics=[dice_coef, iou_coef, 'accuracy']\n", "    )\n", "    \n", "    print(\"✅ 模型编译完成\")\n", "    \n", "    # 4. 设置回调函数\n", "    print(\"\\n📋 步骤4: 设置训练回调\")\n", "    \n", "    callbacks = [\n", "        keras.callbacks.ModelCheckpoint(\n", "            filepath=os.path.join(OUTPUT_FOLDER, 'best_unet_model.h5'),\n", "            monitor='val_dice_coef',\n", "            mode='max',\n", "            save_best_only=True,\n", "            save_weights_only=False,\n", "            verbose=1\n", "        ),\n", "        keras.callbacks.ReduceLROnPlateau(\n", "            monitor='val_loss',\n", "            factor=0.5,\n", "            patience=5,\n", "            min_lr=1e-7,\n", "            verbose=1\n", "        ),\n", "        keras.callbacks.EarlyStopping(\n", "            monitor='val_dice_coef',\n", "            mode='max',\n", "            patience=10,\n", "            restore_best_weights=True,\n", "            verbose=1\n", "        )\n", "    ]\n", "    \n", "    print(\"✅ 回调函数设置完成\")\n", "    \n", "    # 5. 开始训练\n", "    print(\"\\n🎯 步骤5: 开始模型训练\")\n", "    print(\"=\" * 60)\n", "    \n", "    history = model.fit(\n", "        X_train, y_train,\n", "        batch_size=TRAINING_CONFIG['batch_size'],\n", "        epochs=TRAINING_CONFIG['epochs'],\n", "        validation_data=(X_val, y_val),\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    print(\"\\n🎉 训练完成！\")\n", "    \n", "    # 6. 保存最终模型\n", "    print(\"\\n💾 步骤6: 保存模型\")\n", "    final_model_path = os.path.join(OUTPUT_FOLDER, 'final_unet_model.h5')\n", "    model.save(final_model_path)\n", "    print(f\"✅ 最终模型已保存: {final_model_path}\")\n", "    \n", "    # 7. 保存训练历史\n", "    history_path = os.path.join(OUTPUT_FOLDER, 'training_history.json')\n", "    with open(history_path, 'w') as f:\n", "        # 转换numpy数组为列表以便JSON序列化\n", "        history_dict = {key: [float(val) for val in values] for key, values in history.history.items()}\n", "        json.dump(history_dict, f, indent=2)\n", "    print(f\"✅ 训练历史已保存: {history_path}\")\n", "    \n", "    # 8. 显示训练结果\n", "    print(\"\\n📊 训练结果摘要:\")\n", "    print(\"=\" * 60)\n", "    \n", "    final_metrics = {\n", "        'final_loss': history.history['loss'][-1],\n", "        'final_val_loss': history.history['val_loss'][-1],\n", "        'final_dice_coef': history.history['dice_coef'][-1],\n", "        'final_val_dice_coef': history.history['val_dice_coef'][-1],\n", "        'final_iou_coef': history.history['iou_coef'][-1],\n", "        'final_val_iou_coef': history.history['val_iou_coef'][-1],\n", "        'best_val_dice': max(history.history['val_dice_coef']),\n", "        'best_val_iou': max(history.history['val_iou_coef'])\n", "    }\n", "    \n", "    for metric, value in final_metrics.items():\n", "        print(f\"{metric}: {value:.4f}\")\n", "    \n", "    print(\"\\n🎯 训练成功完成！\")\n", "    print(f\"📁 模型文件保存在: {OUTPUT_FOLDER}\")\n", "    \n", "except Exception as e:\n", "    print(f\"\\n❌ 训练过程中出现错误: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n", "    \n", "    print(\"\\n🔧 可能的解决方案:\")\n", "    print(\"1. 检查数据路径是否正确\")\n", "    print(\"2. 确认图像和掩膜文件存在且可读\")\n", "    print(\"3. 减少batch_size或max_total_patches以降低内存使用\")\n", "    print(\"4. 检查GPU内存是否足够\")\n", "    print(\"5. 确认文件命名格式符合tile_x_y模式\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 训练结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 训练结果可视化\n", "def plot_training_history(history):\n", "    \"\"\"绘制训练历史\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle('UNet训练历史', fontsize=16)\n", "    \n", "    # 损失\n", "    axes[0, 0].plot(history.history['loss'], label='训练损失')\n", "    axes[0, 0].plot(history.history['val_loss'], label='验证损失')\n", "    axes[0, 0].set_title('模型损失')\n", "    axes[0, 0].set_xlabel('轮次')\n", "    axes[0, 0].set_ylabel('损失')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True)\n", "    \n", "    # Dice系数\n", "    axes[0, 1].plot(history.history['dice_coef'], label='训练Dice')\n", "    axes[0, 1].plot(history.history['val_dice_coef'], label='验证Dice')\n", "    axes[0, 1].set_title('Dice系数')\n", "    axes[0, 1].set_xlabel('轮次')\n", "    axes[0, 1].set_ylabel('Dice系数')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True)\n", "    \n", "    # IoU系数\n", "    axes[1, 0].plot(history.history['iou_coef'], label='训练IoU')\n", "    axes[1, 0].plot(history.history['val_iou_coef'], label='验证IoU')\n", "    axes[1, 0].set_title('IoU系数')\n", "    axes[1, 0].set_xlabel('轮次')\n", "    axes[1, 0].set_ylabel('IoU系数')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True)\n", "    \n", "    # 准确率\n", "    axes[1, 1].plot(history.history['accuracy'], label='训练准确率')\n", "    axes[1, 1].plot(history.history['val_accuracy'], label='验证准确率')\n", "    axes[1, 1].set_title('准确率')\n", "    axes[1, 1].set_xlabel('轮次')\n", "    axes[1, 1].set_ylabel('准确率')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 保存图表\n", "    plot_path = os.path.join(OUTPUT_FOLDER, 'training_curves.png')\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"📊 训练曲线已保存: {plot_path}\")\n", "    \n", "    plt.show()\n", "\n", "# 如果训练成功，绘制训练历史\n", "try:\n", "    if 'history' in locals():\n", "        plot_training_history(history)\n", "    else:\n", "        print(\"⚠️  训练历史不可用，跳过可视化\")\n", "except Exception as e:\n", "    print(f\"⚠️  可视化失败: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 使用说明\n", "\n", "### 🔧 配置步骤\n", "\n", "1. **修改数据路径**: 在\"数据路径配置\"部分修改 `IMAGE_FOLDER` 和 `MASK_FOLDER` 路径\n", "2. **调整训练参数**: 根据您的硬件配置修改 `TRAINING_CONFIG` 中的参数\n", "3. **运行训练**: 按顺序执行所有代码块\n", "\n", "### 📊 主要改进\n", "\n", "- **🔧 图像合并**: 自动检测并合并4个子瓦片为1个完整图像\n", "- **🎯 智能配对**: 改进的image-mask文件配对逻辑，支持tile_x_y格式\n", "- **💾 内存优化**: 优化内存使用，避免OOM错误\n", "- **📈 完整监控**: 包含Dice、IoU、准确率等多种指标\n", "- **🔄 自动保存**: 自动保存最佳模型和训练历史\n", "\n", "### ⚙️ 参数说明\n", "\n", "- `batch_size`: 批次大小，根据GPU内存调整\n", "- `max_total_patches`: 最大图像块数量，控制内存使用\n", "- `min_foreground_ratio`: 前景像素最小比例，过滤空白图像块\n", "- `use_all_files`: 是否使用全部文件（云端建议设为True）\n", "\n", "### 🚀 平台适配\n", "\n", "- **Kaggle**: 数据路径使用 `/kaggle/input/`\n", "- **Google Colab**: 数据路径使用 `/content/drive/MyDrive/`\n", "- **Azure ML**: 数据路径使用相对路径\n", "- **本地环境**: 使用绝对路径或相对路径\n", "\n", "---\n", "\n", "**🎯 训练完成后，您将获得:**\n", "- 最佳模型文件 (`best_unet_model.h5`)\n", "- 最终模型文件 (`final_unet_model.h5`)\n", "- 训练历史记录 (`training_history.json`)\n", "- 训练曲线图表 (`training_curves.png`)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}