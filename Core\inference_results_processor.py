import os
import json
import datetime
import numpy as np
from PIL import Image, ImageDraw
import geopandas as gpd
from shapely.geometry import Polygon
from rasterio.features import shapes
import rasterio
from rasterio.transform import from_bounds
from PyQt5.QtWidgets import QMessageBox
from skimage.transform import resize

class InferenceResultsProcessor:
    """处理推理结果的类，包括转换为shape文件和显示结果"""
    
    def __init__(self, project_config):
        """
        初始化处理器
        
        Args:
            project_config: 项目配置字典，包含必要的路径信息
        """
        self.project_config = project_config
        self.colors = [
            (255, 0, 0, 128),   # 红色
            (0, 255, 0, 128),   # 绿色
            (0, 0, 255, 128),   # 蓝色
            (255, 255, 0, 128), # 黄色
            (255, 0, 255, 128), # 紫色
            (0, 255, 255, 128)  # 青色
        ]
    
    def process_results(self, results, parent_window=None):
        """
        处理推理结果，生成可视化图像和shape文件
        
        Args:
            results: 推理结果列表
            parent_window: 父窗口，用于显示消息框
            
        Returns:
            tuple: (原始图像, 结果图像, shape文件路径)
        """
        try:
            # 创建结果目录
            result_dir = os.path.join(self.project_config['model_path'], 'Model')
            if not os.path.exists(result_dir):
                os.makedirs(result_dir)
                
            # 加载原始图像
            inference_temp_dir = os.path.join(
                self.project_config['model_path'], 
                'Model', 
                'inference_temp'
            )
            
            # 尝试不同格式的图像文件
            possible_formats = ['tif', 'png', 'jpg']
            original_image = None
            image_path = None
            
            for fmt in possible_formats:
                temp_path = os.path.join(inference_temp_dir, f'inference_image.{fmt}')
                if os.path.exists(temp_path):
                    original_image = Image.open(temp_path)
                    image_path = temp_path
                    print(f"找到并加载图像文件: {temp_path}")
                    break
                    
            if original_image is None:
                raise FileNotFoundError(
                    f"在目录 {inference_temp_dir} 中找不到推理图像文件 "
                    f"(尝试过的格式: {', '.join(possible_formats)})"
                )
            
            # 创建结果可视化图像
            result_image = original_image.copy()
            draw = ImageDraw.Draw(result_image, 'RGBA')
            
            print("处理分割结果...")
            print(f"检测到 {len(results)} 个目标")
            
            # 获取地理参考信息
            try:
                with rasterio.open(image_path) as src:
                    transform = src.transform
                    crs = src.crs
                    bounds = src.bounds
                    width = src.width
                    height = src.height
            except Exception as e:
                print(f"警告：无法从图像获取地理参考信息: {str(e)}")
                # 创建一个基于图像边界的简单变换
                bounds_path = os.path.join(inference_temp_dir, 'bounds.json')
                if os.path.exists(bounds_path):
                    with open(bounds_path, 'r') as f:
                        bounds_data = json.load(f)
                        min_lat = bounds_data['min_lat']
                        max_lat = bounds_data['max_lat']
                        min_lng = bounds_data['min_lng']
                        max_lng = bounds_data['max_lng']
                        transform = from_bounds(min_lng, min_lat, max_lng, max_lat, 
                                             original_image.width, original_image.height)
                        crs = 'EPSG:4326'
                else:
                    print("警告：找不到边界信息文件，将使用像素坐标")
                    transform = None
                    crs = None
            
            # 处理分割结果
            polygons = []
            labels = []
            scores = []
            
            for i, segment in enumerate(results):
                try:
                    # 获取分割掩码
                    mask = segment.get('segmentation', segment.get('mask'))  # 尝试两种可能的键名
                    label = segment.get('label', 'Unknown')
                    score = segment.get('score', None)
                    
                    # 打印调试信息
                    print(f"\n处理第 {i+1} 个目标:")
                    print(f"- 类别: {label}")
                    print(f"- 置信度: {score:.2f}" if score is not None else "- 置信度: 未知")
                    
                    # 确保掩码是numpy数组并且是二维的
                    if mask is None:
                        print(f"警告：第 {i+1} 个目标没有掩码数据")
                        continue
                        
                    if not isinstance(mask, np.ndarray):
                        mask = np.array(mask)
                    
                    if mask.ndim > 2:
                        mask = mask.squeeze()  # 移除单维度
                    
                    if mask.ndim != 2:
                        print(f"警告：掩码维度不正确: {mask.ndim}")
                        continue
                    
                    print(f"- 掩码形状: {mask.shape}")
                    
                    # 调整掩码大小到原始图像尺寸
                    if mask.shape != (height, width):
                        mask = resize(
                            mask,
                            (height, width),
                            order=0,  # 使用最近邻插值
                            preserve_range=True,
                            anti_aliasing=False
                        ).astype(np.uint8)
                        print(f"- 调整后掩码形状: {mask.shape}")
                    
                    # 将掩码转换为PIL图像
                    mask_image = Image.fromarray((mask > 0).astype(np.uint8) * 255)
                    
                    # 选择颜色
                    color = self.colors[i % len(self.colors)]
                    
                    # 创建彩色遮罩
                    colored_mask = Image.new('RGBA', mask_image.size, color)
                    
                    # 将遮罩应用到结果图像
                    result_image.paste(colored_mask, (0, 0), mask=mask_image)
                    
                    # 从掩码中提取多边形
                    if transform is not None:
                        # 使用地理参考信息提取多边形
                        for geom, _ in shapes(mask.astype(np.uint8), mask=mask > 0, transform=transform):
                            if geom['type'] == 'Polygon':
                                coords = geom['coordinates'][0]
                                if len(coords) > 3:  # 确保至少有3个点形成多边形
                                    polygons.append(Polygon(coords))
                                    labels.append(label)
                                    scores.append(score)
                    else:
                        # 使用像素坐标提取多边形
                        for geom, _ in shapes(mask.astype(np.uint8), mask=mask > 0):
                            if geom['type'] == 'Polygon':
                                coords = geom['coordinates'][0]
                                if len(coords) > 3:
                                    polygons.append(Polygon(coords))
                                    labels.append(label)
                                    scores.append(score)
                    
                except Exception as segment_error:
                    print(f"处理第 {i+1} 个目标时出错: {str(segment_error)}")
                    continue
            
            # 保存结果图像
            result_path = os.path.join(result_dir, 'segmentation_result.png')
            result_image.save(result_path)
            print(f"\n结果图像已保存到: {result_path}")
            
            # 创建并保存shapefile
            shape_file = None
            if polygons:
                try:
                    # 创建shape文件目录
                    shape_dir = os.path.join('dataset', 'shape')
                    if not os.path.exists(shape_dir):
                        os.makedirs(shape_dir)
                    
                    # 生成带时间戳的文件名
                    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                    shape_file = os.path.join(shape_dir, f'inference_result_{timestamp}.shp')
                    
                    # 创建GeoDataFrame
                    gdf = gpd.GeoDataFrame({
                        'geometry': polygons,
                        'label': labels,
                        'score': scores
                    })
                    
                    # 设置坐标系统
                    if crs:
                        gdf.set_crs(crs, inplace=True)
                    else:
                        gdf.set_crs(epsg=4326, inplace=True)
                    
                    # 保存为shapefile
                    gdf.to_file(shape_file)
                    print(f"Shape文件已保存到: {shape_file}")
                    
                except Exception as shape_error:
                    print(f"保存Shape文件时出错: {str(shape_error)}")
                    shape_file = None
            
            # 确保返回三个值
            return original_image, result_image, shape_file
                
        except Exception as e:
            print(f"处理结果时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            # 发生错误时也要返回三个值
            return None, None, None
