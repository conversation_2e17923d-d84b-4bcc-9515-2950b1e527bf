#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集诊断工具
检查GEE下载的图像文件是否完整和有效
"""

import os
import sys
import rasterio
import numpy as np
from pathlib import Path
import traceback

def check_tiff_file(file_path):
    """
    检查单个TIFF文件的完整性
    """
    results = {
        'file_path': str(file_path),
        'file_size': 0,
        'exists': False,
        'readable': False,
        'valid_geotiff': False,
        'has_data': False,
        'shape': None,
        'dtype': None,
        'crs': None,
        'transform': None,
        'bands': 0,
        'data_range': None,
        'has_nodata': False,
        'error': None
    }
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            results['error'] = "文件不存在"
            return results
        
        results['exists'] = True
        results['file_size'] = os.path.getsize(file_path)
        
        # 尝试用rasterio打开文件
        with rasterio.open(file_path) as src:
            results['readable'] = True
            results['valid_geotiff'] = True
            
            # 获取基本信息
            results['shape'] = (src.height, src.width)
            results['dtype'] = str(src.dtypes[0])
            results['crs'] = str(src.crs) if src.crs else None
            results['transform'] = src.transform
            results['bands'] = src.count
            
            # 尝试读取数据
            try:
                # 读取一个小样本来检查数据
                sample_data = src.read(1, window=rasterio.windows.Window(0, 0, 
                                     min(1000, src.width), min(1000, src.height)))
                results['has_data'] = True
                
                # 检查数据范围
                valid_data = sample_data[~np.isnan(sample_data)]
                if len(valid_data) > 0:
                    results['data_range'] = (float(valid_data.min()), float(valid_data.max()))
                
                # 检查是否有nodata值
                results['has_nodata'] = src.nodata is not None
                
            except Exception as e:
                results['error'] = f"数据读取失败: {str(e)}"
                
    except rasterio.errors.RasterioIOError as e:
        results['error'] = f"Rasterio IO错误: {str(e)}"
    except Exception as e:
        results['error'] = f"未知错误: {str(e)}"
    
    return results

def diagnose_dataset(image_folder, mask_folder=None):
    """
    诊断整个数据集
    """
    print("=" * 60)
    print("数据集诊断报告")
    print("=" * 60)
    
    image_path = Path(image_folder)
    if not image_path.exists():
        print(f"❌ 图像文件夹不存在: {image_folder}")
        return
    
    # 查找所有TIFF文件
    tiff_extensions = ['.tif', '.tiff']
    image_files = []
    for ext in tiff_extensions:
        image_files.extend(image_path.glob(f"*{ext}"))
    
    if not image_files:
        print(f"❌ 在 {image_folder} 中未找到TIFF文件")
        return
    
    print(f"📁 图像文件夹: {image_folder}")
    print(f"📊 找到 {len(image_files)} 个图像文件")
    print()
    
    # 检查每个图像文件
    valid_files = 0
    corrupted_files = 0
    total_size = 0
    
    for i, img_file in enumerate(image_files, 1):
        print(f"[{i}/{len(image_files)}] 检查: {img_file.name}")
        
        result = check_tiff_file(img_file)
        total_size += result['file_size']
        
        # 显示文件大小
        size_mb = result['file_size'] / (1024 * 1024)
        print(f"  📏 文件大小: {size_mb:.1f} MB")
        
        if result['error']:
            print(f"  ❌ 错误: {result['error']}")
            corrupted_files += 1
        else:
            if result['valid_geotiff'] and result['has_data']:
                print(f"  ✅ 文件正常")
                print(f"     - 尺寸: {result['shape']}")
                print(f"     - 数据类型: {result['dtype']}")
                print(f"     - 波段数: {result['bands']}")
                if result['data_range']:
                    print(f"     - 数据范围: [{result['data_range'][0]:.2f}, {result['data_range'][1]:.2f}]")
                if result['crs']:
                    print(f"     - 坐标系: {result['crs']}")
                valid_files += 1
            else:
                print(f"  ⚠️  文件可读但可能有问题")
                corrupted_files += 1
        print()
    
    # 检查掩膜文件（如果提供）
    if mask_folder:
        mask_path = Path(mask_folder)
        if mask_path.exists():
            mask_files = []
            for ext in tiff_extensions:
                mask_files.extend(mask_path.glob(f"*{ext}"))
            
            print(f"🎭 掩膜文件夹: {mask_folder}")
            print(f"📊 找到 {len(mask_files)} 个掩膜文件")
            
            for mask_file in mask_files:
                print(f"检查掩膜: {mask_file.name}")
                result = check_tiff_file(mask_file)
                
                size_mb = result['file_size'] / (1024 * 1024)
                print(f"  📏 文件大小: {size_mb:.1f} MB")
                
                if result['error']:
                    print(f"  ❌ 错误: {result['error']}")
                else:
                    print(f"  ✅ 掩膜文件正常")
                    print(f"     - 尺寸: {result['shape']}")
                    print(f"     - 数据类型: {result['dtype']}")
                print()
    
    # 总结报告
    print("=" * 60)
    print("诊断总结")
    print("=" * 60)
    print(f"📊 总文件数: {len(image_files)}")
    print(f"✅ 正常文件: {valid_files}")
    print(f"❌ 损坏文件: {corrupted_files}")
    print(f"📏 总大小: {total_size / (1024**3):.2f} GB")
    
    if corrupted_files > 0:
        print(f"\n⚠️  发现 {corrupted_files} 个损坏的文件！")
        print("建议:")
        print("1. 重新从GEE下载损坏的文件")
        print("2. 检查下载过程中是否有网络中断")
        print("3. 验证GEE导出设置是否正确")
    else:
        print(f"\n🎉 所有文件都正常！")

def main():
    """主函数"""
    # 默认路径
    default_image_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\image"
    default_mask_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\mask"
    
    # 检查图像文件夹
    if len(sys.argv) > 1:
        image_folder = sys.argv[1]
    else:
        image_folder = default_image_folder
    
    # 检查掩膜文件夹
    if len(sys.argv) > 2:
        mask_folder = sys.argv[2]
    else:
        mask_folder = default_mask_folder if os.path.exists(default_mask_folder) else None
    
    try:
        diagnose_dataset(image_folder, mask_folder)
    except KeyboardInterrupt:
        print("\n用户中断诊断")
    except Exception as e:
        print(f"诊断过程中出现错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
