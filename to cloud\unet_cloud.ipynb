{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UNet 模型训练 - 卫星图像分割\n", "\n", "本 notebook 用于训练 UNet 模型进行卫星图像分割。请确保在运行时使用 GPU 运行时。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 安装依赖"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["!pip install rasterio\n", "!pip install geopandas\n", "!pip install patchify\n", "!pip install segmentation-models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import cv2\n", "import numpy as np\n", "import rasterio\n", "import geopandas as gpd\n", "from rasterio.features import rasterize\n", "from patchify import patchify\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "import keras\n", "from keras.utils import to_categorical, Sequence\n", "import segmentation_models as sm\n", "from keras.optimizers import Adam\n", "from keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard\n", "from datetime import datetime\n", "import json\n", "\n", "# 设置混合精度\n", "tf.keras.mixed_precision.set_global_policy('mixed_float16')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 配置参数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["CONFIG = {\n", "    'dataset_path': './dataset',  # 数据集路径\n", "    'model': {\n", "        'name': 'unet',\n", "        'args': {\n", "            'init_features': 32\n", "        }\n", "    },\n", "    'trainer': {\n", "        'batch_size': 8,\n", "        'epochs': 100,\n", "        'learning_rate': 1e-4,\n", "        'slice_size': 256,\n", "        'overlap': 32,\n", "        'save_dir': './models/unet'\n", "    }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据生成器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class DataGenerator(keras.utils.Sequence):\n", "    def __init__(self, x_set, y_set, batch_size):\n", "        self.x, self.y = x_set, y_set\n", "        self.batch_size = batch_size\n", "\n", "    def __len__(self):\n", "        return int(np.ceil(len(self.x) / float(self.batch_size)))\n", "\n", "    def __getitem__(self, idx):\n", "        batch_x = self.x[idx * self.batch_size:(idx + 1) * self.batch_size]\n", "        batch_y = self.y[idx * self.batch_size:(idx + 1) * self.batch_size]\n", "        return batch_x, batch_y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. UNet 模型定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def create_unet_model(input_shape=(256, 256, 3), n_classes=2, init_features=32):\n", "    \"\"\"创建UNet模型\"\"\"\n", "    inputs = keras.layers.Input(input_shape)\n", "    \n", "    # Encoder\n", "    conv1 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(inputs)\n", "    conv1 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(conv1)\n", "    pool1 = keras.layers.MaxPooling2D(pool_size=(2, 2))(conv1)\n", "    \n", "    conv2 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(pool1)\n", "    conv2 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(conv2)\n", "    pool2 = keras.layers.MaxPooling2D(pool_size=(2, 2))(conv2)\n", "    \n", "    conv3 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(pool2)\n", "    conv3 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(conv3)\n", "    pool3 = keras.layers.MaxPooling2D(pool_size=(2, 2))(conv3)\n", "    \n", "    # Bridge\n", "    conv4 = keras.layers.Conv2D(init_features*8, 3, activation='relu', padding='same')(pool3)\n", "    conv4 = keras.layers.Conv2D(init_features*8, 3, activation='relu', padding='same')(conv4)\n", "    \n", "    # Decoder\n", "    up5 = keras.layers.Conv2DTranspose(init_features*4, 2, strides=(2, 2), padding='same')(conv4)\n", "    up5 = keras.layers.concatenate([up5, conv3])\n", "    conv5 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(up5)\n", "    conv5 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(conv5)\n", "    \n", "    up6 = keras.layers.Conv2DTranspose(init_features*2, 2, strides=(2, 2), padding='same')(conv5)\n", "    up6 = keras.layers.concatenate([up6, conv2])\n", "    conv6 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(up6)\n", "    conv6 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(conv6)\n", "    \n", "    up7 = keras.layers.Conv2DTranspose(init_features, 2, strides=(2, 2), padding='same')(conv6)\n", "    up7 = keras.layers.concatenate([up7, conv1])\n", "    conv7 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(up7)\n", "    conv7 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(conv7)\n", "    \n", "    outputs = keras.layers.Conv2D(n_classes, 1, activation='softmax')(conv7)\n", "    \n", "    model = keras.models.Model(inputs=[inputs], outputs=[outputs])\n", "    return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 训练函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def train_unet(config):\n", "    \"\"\"训练UNet模型\"\"\"\n", "    # 获取训练参数\n", "    dataset_path = config['dataset_path']\n", "    model_args = config['model']['args']\n", "    trainer_args = config['trainer']\n", "    \n", "    # 设置训练参数\n", "    batch_size = trainer_args['batch_size']\n", "    epochs = trainer_args['epochs']\n", "    learning_rate = trainer_args['learning_rate']\n", "    slice_size = trainer_args['slice_size']\n", "    overlap = trainer_args['overlap']\n", "    save_dir = trainer_args['save_dir']\n", "    \n", "    # 创建保存目录\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    \n", "    # 读取数据\n", "    with rasterio.open(os.path.join(dataset_path, 'image', 'test.tif')) as src:\n", "        image = src.read().transpose((1, 2, 0))\n", "        \n", "    gdf = gpd.read_file(os.path.join(dataset_path, 'shape', 'Buildings.shp'))\n", "    \n", "    # 确保图像是3波段\n", "    if image.shape[2] > 3:\n", "        image = image[:, :, :3]\n", "    \n", "    # 创建掩码\n", "    mask = rasterize(\n", "        [(shape, 1) for shape in gdf.geometry],\n", "        out_shape=image.shape[:2],\n", "        transform=src.transform,\n", "        fill=0,\n", "        dtype='uint8'\n", "    )\n", "    \n", "    # 数据预处理\n", "    minmaxscaler = MinMaxScaler()\n", "    image = minmaxscaler.fit_transform(image.reshape(-1, image.shape[-1])).reshape(image.shape)\n", "    mask = np.expand_dims(mask, axis=-1)\n", "    \n", "    # 创建图像块\n", "    def create_patches(img, mask, size=slice_size, stride=slice_size-overlap):\n", "        patches_img = patchify(img, (size, size, img.shape[-1]), step=stride)\n", "        patches_mask = patchify(mask, (size, size, 1), step=stride)\n", "        \n", "        patches_img = patches_img.reshape(-1, size, size, img.shape[-1])\n", "        patches_mask = patches_mask.reshape(-1, size, size, 1)\n", "        \n", "        return patches_img, patches_mask\n", "    \n", "    image_patches, mask_patches = create_patches(image, mask)\n", "    print(f\"创建了 {len(image_patches)} 个图像块\")\n", "    \n", "    # 转换标签为分类格式\n", "    mask_categorical = to_categorical(mask_patches, num_classes=2)\n", "    \n", "    # 划分训练集和验证集\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        image_patches, mask_categorical, \n", "        test_size=0.2, random_state=42\n", "    )\n", "    \n", "    # 创建模型\n", "    model = create_unet_model(\n", "        input_shape=(slice_size, slice_size, 3),\n", "        n_classes=2,\n", "        init_features=model_args.get('init_features', 32)\n", "    )\n", "    \n", "    # 配置优化器和损失函数\n", "    optimizer = Adam(learning_rate=learning_rate)\n", "    loss = sm.losses.CategoricalCELoss()\n", "    metrics = [\n", "        sm.metrics.IOUScore(threshold=0.5),\n", "        sm.metrics.FScore(threshold=0.5),\n", "        'accuracy'\n", "    ]\n", "    \n", "    model.compile(optimizer, loss, metrics)\n", "    \n", "    # 创建回调函数\n", "    callbacks = [\n", "        ReduceLROnPlateau(\n", "            monitor='val_loss',\n", "            factor=0.5,\n", "            patience=5,\n", "            min_lr=1e-6\n", "        ),\n", "        ModelCheckpoint(\n", "            os.path.join(save_dir, 'best_model.h5'),\n", "            monitor='val_iou_score',\n", "            mode='max',\n", "            save_best_only=True\n", "        ),\n", "        EarlyStopping(\n", "            monitor='val_loss',\n", "            patience=10,\n", "            restore_best_weights=True\n", "        ),\n", "        TensorBoard(\n", "            log_dir=os.path.join(save_dir, 'logs'),\n", "            histogram_freq=1\n", "        )\n", "    ]\n", "    \n", "    # 创建数据生成器\n", "    train_gen = DataGenerator(X_train, y_train, batch_size)\n", "    val_gen = DataGenerator(X_val, y_val, batch_size)\n", "    \n", "    # 训练模型\n", "    history = model.fit(\n", "        train_gen,\n", "        validation_data=val_gen,\n", "        epochs=epochs,\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    # 保存训练历史\n", "    history_path = os.path.join(save_dir, 'training_history.json')\n", "    with open(history_path, 'w') as f:\n", "        json.dump(history.history, f)\n", "    \n", "    return model, history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 上传数据集\n", "\n", "请上传您的卫星图像数据集。数据集应包含以下结构：\n", "```\n", "dataset/\n", "    ├── image/\n", "    │   └── test.tif\n", "    └── shape/\n", "        └── Buildings.shp\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from google.colab import files\n", "\n", "# 创建数据集目录\n", "os.makedirs('dataset/image', exist_ok=True)\n", "os.makedirs('dataset/shape', exist_ok=True)\n", "\n", "# 上传图像文件\n", "print(\"请上传卫星图像文件 (test.tif)...\")\n", "uploaded = files.upload()\n", "for filename in uploaded.keys():\n", "    os.rename(filename, 'dataset/image/test.tif')\n", "\n", "# 上传shapefile文件\n", "print(\"\\n请上传建筑物shapefile文件 (Buildings.shp)...\")\n", "uploaded = files.upload()\n", "for filename in uploaded.keys():\n", "    os.rename(filename, 'dataset/shape/Buildings.shp')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 训练模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 开始训练\n", "model, history = train_unet(CONFIG)\n", "\n", "# 绘制训练历史\n", "plt.figure(figsize=(12, 4))\n", "\n", "# 损失曲线\n", "plt.subplot(1, 2, 1)\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "\n", "# IoU 分数曲线\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history.history['iou_score'], label='Training IoU')\n", "plt.plot(history.history['val_iou_score'], label='Validation IoU')\n", "plt.title('Model IoU')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('IoU Score')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}