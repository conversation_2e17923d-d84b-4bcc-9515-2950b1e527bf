#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Shape文件范围的数据集下载工具

功能：
1. 读取Shape文件中的多边形
2. 根据多边形范围下载卫星图像
3. 将Shape文件转换为mask文件
4. 生成image+mask训练数据集

使用方法：
从GUI界面调用，或直接运行脚本
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
import geopandas as gpd
from shapely.geometry import box, Point, Polygon
import rasterio
from rasterio.features import rasterize
from rasterio.transform import from_bounds
from rasterio.crs import CRS
import requests
import mercantile
from PIL import Image
import math
import time
from datetime import datetime
import json

class ShapeBasedDatasetDownloader:
    """基于Shape文件的数据集下载器"""

    def __init__(self, output_dir="dataset"):
        """
        初始化下载器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.image_dir = self.output_dir / "image"
        self.mask_dir = self.output_dir / "mask"

        # 创建输出目录
        self.image_dir.mkdir(parents=True, exist_ok=True)
        self.mask_dir.mkdir(parents=True, exist_ok=True)

        # 下载配置
        self.download_delay = 0.15  # 默认150ms延迟

        # 下载统计
        self.download_stats = {
            'total_tiles': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'start_time': None,
            'end_time': None
        }

    def set_download_delay(self, delay_seconds):
        """
        设置下载延迟

        Args:
            delay_seconds: 延迟时间（秒）
        """
        self.download_delay = delay_seconds

    def load_shapefile(self, shapefile_path):
        """
        加载Shape文件

        Args:
            shapefile_path: Shape文件路径

        Returns:
            GeoDataFrame: 加载的地理数据
        """
        try:
            print(f"🔄 加载Shape文件: {shapefile_path}")
            gdf = gpd.read_file(shapefile_path)

            # 确保坐标系为WGS84
            if gdf.crs != 'EPSG:4326':
                print(f"🔄 转换坐标系从 {gdf.crs} 到 EPSG:4326")
                gdf = gdf.to_crs('EPSG:4326')

            print(f"✅ 成功加载 {len(gdf)} 个要素")
            print(f"📊 边界范围: {gdf.total_bounds}")

            return gdf

        except Exception as e:
            print(f"❌ 加载Shape文件失败: {e}")
            return None

    def calculate_tile_bounds(self, geometry, tile_size_meters=256):
        """
        计算几何体的瓦片边界

        Args:
            geometry: 几何体
            tile_size_meters: 瓦片大小（米）

        Returns:
            list: 瓦片边界列表
        """
        # 获取几何体边界
        bounds = geometry.bounds  # (minx, miny, maxx, maxy)

        # 计算合适的缩放级别
        # 根据几何体大小自动选择缩放级别
        width = bounds[2] - bounds[0]  # 经度差
        height = bounds[3] - bounds[1]  # 纬度差

        # 估算合适的缩放级别
        # 这里使用一个简单的启发式方法
        if max(width, height) > 0.1:  # 大于0.1度
            zoom = 12
        elif max(width, height) > 0.01:  # 大于0.01度
            zoom = 15
        else:
            zoom = 18

        print(f"📏 几何体尺寸: {width:.6f}° x {height:.6f}°")
        print(f"🔍 选择缩放级别: {zoom}")

        # 获取覆盖该区域的所有瓦片
        tiles = list(mercantile.tiles(bounds[0], bounds[1], bounds[2], bounds[3], zoom))

        print(f"📊 需要下载 {len(tiles)} 个瓦片")

        return tiles, zoom

    def download_satellite_tile(self, tile, zoom, tile_source="google"):
        """
        下载单个卫星瓦片

        Args:
            tile: 瓦片对象
            zoom: 缩放级别
            tile_source: 瓦片源

        Returns:
            PIL.Image or None: 下载的图像
        """
        try:
            # 构建瓦片URL
            if tile_source == "google":
                url = f"https://mt1.google.com/vt/lyrs=s&x={tile.x}&y={tile.y}&z={zoom}"
            elif tile_source == "arcgis":
                url = f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom}/{tile.y}/{tile.x}"
            elif tile_source == "bing":
                # Bing瓦片需要特殊的quadkey
                quadkey = self.tile_to_quadkey(tile.x, tile.y, zoom)
                url = f"https://ecn.t3.tiles.virtualearth.net/tiles/a{quadkey}.jpeg?g=587&mkt=en-us&token=YOUR_BING_KEY"
            else:
                # OpenStreetMap
                url = f"https://tile.openstreetmap.org/{zoom}/{tile.x}/{tile.y}.png"

            # 下载图像
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # 转换为PIL图像
            image = Image.open(requests.get(url, headers=headers, stream=True).raw)
            return image

        except Exception as e:
            print(f"⚠️ 下载瓦片失败 {tile.x}/{tile.y}/{zoom}: {e}")
            return None

    def tile_to_quadkey(self, x, y, z):
        """将瓦片坐标转换为Bing的quadkey"""
        quadkey = ""
        for i in range(z, 0, -1):
            digit = 0
            mask = 1 << (i - 1)
            if (x & mask) != 0:
                digit += 1
            if (y & mask) != 0:
                digit += 2
            quadkey += str(digit)
        return quadkey

    def create_mask_from_geometry(self, geometry, image_bounds, image_size):
        """
        从几何体创建mask

        Args:
            geometry: 几何体
            image_bounds: 图像边界 (minx, miny, maxx, maxy)
            image_size: 图像尺寸 (width, height)

        Returns:
            numpy.ndarray: mask数组
        """
        try:
            # 创建仿射变换
            transform = from_bounds(*image_bounds, image_size[0], image_size[1])

            # 创建mask
            if hasattr(geometry, '__iter__') and not isinstance(geometry, (str, bytes)):
                # 多个几何体
                geometries = [(geom, 1) for geom in geometry if geom.is_valid]
            else:
                # 单个几何体
                geometries = [(geometry, 1)] if geometry.is_valid else []

            if not geometries:
                print("⚠️ 没有有效的几何体")
                return np.zeros(image_size[::-1], dtype=np.uint8)

            # 栅格化
            mask = rasterize(
                geometries,
                out_shape=image_size[::-1],  # (height, width)
                transform=transform,
                fill=0,
                dtype=np.uint8,
                all_touched=True  # 🔑 添加all_touched参数，保持一致性
            )

            return mask

        except Exception as e:
            print(f"❌ 创建mask失败: {e}")
            return np.zeros(image_size[::-1], dtype=np.uint8)

    def process_shapefile_region(self, shapefile_path, tile_size=256, tile_source="google",
                                max_tiles_per_geometry=100):
        """
        处理Shape文件中的所有区域

        Args:
            shapefile_path: Shape文件路径
            tile_size: 瓦片大小
            tile_source: 瓦片源
            max_tiles_per_geometry: 每个几何体的最大瓦片数

        Returns:
            bool: 是否成功
        """
        print("🚀 开始处理Shape文件区域")
        print("="*60)

        self.download_stats['start_time'] = datetime.now()

        # 加载Shape文件
        gdf = self.load_shapefile(shapefile_path)
        if gdf is None:
            return False

        # 处理每个几何体
        for idx, row in gdf.iterrows():
            geometry = row.geometry

            print(f"\n[{idx+1}/{len(gdf)}] 处理几何体 {idx}")
            print("-" * 40)

            # 计算瓦片
            tiles, zoom = self.calculate_tile_bounds(geometry, tile_size)

            # 限制瓦片数量
            if len(tiles) > max_tiles_per_geometry:
                print(f"⚠️ 瓦片数量过多 ({len(tiles)})，限制为 {max_tiles_per_geometry}")
                tiles = tiles[:max_tiles_per_geometry]

            self.download_stats['total_tiles'] += len(tiles)

            # 下载并处理每个瓦片
            for tile_idx, tile in enumerate(tiles):
                print(f"  📥 下载瓦片 {tile_idx+1}/{len(tiles)}: {tile.x}/{tile.y}/{zoom}")

                # 下载卫星图像
                image = self.download_satellite_tile(tile, zoom, tile_source)
                if image is None:
                    self.download_stats['failed_downloads'] += 1
                    continue

                # 获取瓦片的地理边界
                tile_bounds = mercantile.bounds(tile)

                # 创建mask
                mask = self.create_mask_from_geometry(
                    geometry,
                    (tile_bounds.west, tile_bounds.south, tile_bounds.east, tile_bounds.north),
                    (tile_size, tile_size)
                )

                # 保存文件
                base_name = f"geometry_{idx}_tile_{tile.x}_{tile.y}_{zoom}"

                # 保存图像 (保持原始256×256尺寸)
                image_path = self.image_dir / f"{base_name}.tif"
                self.save_georeferenced_image(image, tile_bounds, str(image_path))

                # 保存mask
                mask_path = self.mask_dir / f"{base_name}.tif"
                self.save_georeferenced_mask(mask, tile_bounds, str(mask_path))

                self.download_stats['successful_downloads'] += 1

                # 添加延迟避免被限制
                time.sleep(self.download_delay)

        self.download_stats['end_time'] = datetime.now()
        self.print_download_summary()

        # 记录下载历史到数据集管理器
        try:
            from .dataset_manager import DatasetManager
            manager = DatasetManager(self.output_dir)
            manager.add_download_record(
                source_type="shapefile",
                source_path=shapefile_path,
                tile_count=self.download_stats['total_tiles'],
                success_count=self.download_stats['successful_downloads']
            )
            print("✅ 下载历史已记录")
        except Exception as e:
            print(f"⚠️ 记录下载历史失败: {e}")

        return True

    def save_georeferenced_image(self, image, bounds, output_path):
        """
        保存带地理参考的图像

        Args:
            image: PIL图像
            bounds: 地理边界
            output_path: 输出路径
        """
        try:
            # 转换为numpy数组
            img_array = np.array(image)

            # 如果是RGBA，转换为RGB
            if img_array.shape[2] == 4:
                img_array = img_array[:, :, :3]

            # 创建仿射变换
            transform = from_bounds(bounds.west, bounds.south, bounds.east, bounds.north,
                                  img_array.shape[1], img_array.shape[0])

            # 保存为GeoTIFF
            with rasterio.open(
                output_path, 'w',
                driver='GTiff',
                height=img_array.shape[0],
                width=img_array.shape[1],
                count=3,
                dtype=img_array.dtype,
                crs='EPSG:4326',
                transform=transform
            ) as dst:
                for i in range(3):
                    dst.write(img_array[:, :, i], i + 1)

        except Exception as e:
            print(f"❌ 保存图像失败: {e}")

    def save_georeferenced_mask(self, mask, bounds, output_path):
        """
        保存带地理参考的mask

        Args:
            mask: mask数组
            bounds: 地理边界
            output_path: 输出路径
        """
        try:
            # 创建仿射变换
            transform = from_bounds(bounds.west, bounds.south, bounds.east, bounds.north,
                                  mask.shape[1], mask.shape[0])

            # 保存为GeoTIFF
            with rasterio.open(
                output_path, 'w',
                driver='GTiff',
                height=mask.shape[0],
                width=mask.shape[1],
                count=1,
                dtype=mask.dtype,
                crs='EPSG:4326',
                transform=transform
            ) as dst:
                dst.write(mask, 1)

        except Exception as e:
            print(f"❌ 保存mask失败: {e}")

    def print_download_summary(self):
        """打印下载摘要"""
        duration = self.download_stats['end_time'] - self.download_stats['start_time']

        print("\n" + "="*60)
        print("📊 下载完成摘要")
        print("="*60)
        print(f"⏱️ 总耗时: {duration}")
        print(f"📊 总瓦片数: {self.download_stats['total_tiles']}")
        print(f"✅ 成功下载: {self.download_stats['successful_downloads']}")
        print(f"❌ 下载失败: {self.download_stats['failed_downloads']}")

        if self.download_stats['total_tiles'] > 0:
            success_rate = (self.download_stats['successful_downloads'] /
                          self.download_stats['total_tiles'] * 100)
            print(f"📈 成功率: {success_rate:.1f}%")

        print(f"\n📁 输出目录:")
        print(f"  🖼️ 图像: {self.image_dir}")
        print(f"  🎭 Mask: {self.mask_dir}")

        # 统计生成的文件
        image_files = list(self.image_dir.glob("*.tif"))
        mask_files = list(self.mask_dir.glob("*.tif"))

        print(f"\n📈 生成文件统计:")
        print(f"  🖼️ 图像文件: {len(image_files)} 个")
        print(f"  🎭 Mask文件: {len(mask_files)} 个")

        if len(image_files) > 0 and len(mask_files) > 0:
            print(f"\n🎉 数据集生成成功！")
            print(f"💡 现在可以使用这些数据进行模型训练")
        else:
            print(f"\n⚠️ 没有生成有效的数据集文件")

def main():
    """主函数 - 用于测试"""
    import argparse

    parser = argparse.ArgumentParser(description='基于Shape文件的数据集下载工具')
    parser.add_argument('shapefile', help='Shape文件路径')
    parser.add_argument('--output', '-o', default='dataset', help='输出目录')
    parser.add_argument('--tile-size', '-s', type=int, default=256, help='瓦片大小')
    parser.add_argument('--source', '-src', default='google',
                       choices=['google', 'bing', 'osm'], help='瓦片源')
    parser.add_argument('--max-tiles', '-m', type=int, default=100,
                       help='每个几何体的最大瓦片数')

    args = parser.parse_args()

    # 检查Shape文件是否存在
    if not os.path.exists(args.shapefile):
        print(f"❌ Shape文件不存在: {args.shapefile}")
        return

    # 创建下载器
    downloader = ShapeBasedDatasetDownloader(args.output)

    # 开始处理
    success = downloader.process_shapefile_region(
        args.shapefile,
        tile_size=args.tile_size,
        tile_source=args.source,
        max_tiles_per_geometry=args.max_tiles
    )

    if success:
        print("\n🎉 处理完成！")
    else:
        print("\n❌ 处理失败！")

if __name__ == "__main__":
    main()
