#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用训练好的模型对GEE数据进行预测并生成Shapefile

功能：
1. 加载训练好的模型
2. 对converted文件夹下的image进行预测
3. 将预测结果转换为Shapefile格式
4. 提供可视化对比功能

使用方法：
python predict_and_create_shapefile.py [model_path] [data_folder] [output_folder]

参数：
- model_path: 训练好的模型文件路径（可选，默认使用代码中指定的路径）
- data_folder: GEE数据文件夹路径（可选，默认使用代码中指定的路径）
- output_folder: 输出文件夹路径（可选，默认在data_folder下创建predictions子文件夹）
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
import rasterio
from rasterio.features import shapes
from rasterio.transform import from_bounds
import geopandas as gpd
from shapely.geometry import shape
import matplotlib.pyplot as plt
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

# 尝试导入深度学习框架
try:
    import torch
    import torch.nn.functional as F
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False

try:
    import tensorflow as tf
    from tensorflow import keras
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

def load_model(model_path):
    """
    加载训练好的模型（支持PyTorch和TensorFlow/Keras）
    """
    print(f"🔄 加载模型: {model_path}")

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None

    # 根据文件扩展名判断模型类型
    model_ext = Path(model_path).suffix.lower()

    if model_ext in ['.h5', '.hdf5']:
        # TensorFlow/Keras模型
        return load_keras_model(model_path)
    elif model_ext in ['.pth', '.pt']:
        # PyTorch模型
        return load_pytorch_model(model_path)
    else:
        # 尝试自动检测
        print("🔍 自动检测模型格式...")

        # 先尝试Keras
        keras_result = load_keras_model(model_path)
        if keras_result is not None:
            return keras_result

        # 再尝试PyTorch
        pytorch_result = load_pytorch_model(model_path)
        if pytorch_result is not None:
            return pytorch_result

        print(f"❌ 无法识别的模型格式: {model_ext}")
        return None

def load_keras_model(model_path):
    """
    加载Keras/TensorFlow模型
    """
    if not TENSORFLOW_AVAILABLE:
        print("❌ TensorFlow未安装，无法加载Keras模型")
        return None

    try:
        print("🔄 尝试加载Keras模型...")

        # 检查GPU可用性
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            print(f"🖥️ 使用设备: GPU ({len(gpus)} 个GPU可用)")
            # 设置GPU内存增长
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
        else:
            print("🖥️ 使用设备: CPU")

        # 定义常见的自定义对象
        custom_objects = {
            'combined_loss': create_combined_loss(),
            'dice_loss': dice_loss,
            'iou_score': iou_score,
            'dice_score': dice_score,
            'f1_score': f1_score,
        }

        # 尝试加载模型
        try:
            # 先尝试直接加载
            model = tf.keras.models.load_model(model_path)
        except Exception as e1:
            print(f"⚠️ 直接加载失败，尝试使用自定义对象: {str(e1)[:100]}...")
            try:
                # 使用自定义对象加载
                model = tf.keras.models.load_model(model_path, custom_objects=custom_objects)
            except Exception as e2:
                print(f"⚠️ 自定义对象加载失败，尝试编译=False: {str(e2)[:100]}...")
                # 最后尝试不编译模型
                model = tf.keras.models.load_model(model_path, compile=False)

        print(f"✅ Keras模型加载成功")
        print(f"📊 模型信息: {model.input_shape} -> {model.output_shape}")

        return model, 'keras'

    except Exception as e:
        print(f"❌ Keras模型加载失败: {e}")
        return None

def create_combined_loss():
    """
    创建组合损失函数
    """
    def combined_loss(y_true, y_pred):
        # 二元交叉熵损失
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)

        # Dice损失
        smooth = 1e-6
        y_true_f = tf.keras.backend.flatten(y_true)
        y_pred_f = tf.keras.backend.flatten(y_pred)
        intersection = tf.keras.backend.sum(y_true_f * y_pred_f)
        dice = (2. * intersection + smooth) / (tf.keras.backend.sum(y_true_f) + tf.keras.backend.sum(y_pred_f) + smooth)
        dice_loss = 1 - dice

        # 组合损失
        return bce + dice_loss

    return combined_loss

def dice_loss(y_true, y_pred):
    """Dice损失函数"""
    smooth = 1e-6
    y_true_f = tf.keras.backend.flatten(y_true)
    y_pred_f = tf.keras.backend.flatten(y_pred)
    intersection = tf.keras.backend.sum(y_true_f * y_pred_f)
    return 1 - (2. * intersection + smooth) / (tf.keras.backend.sum(y_true_f) + tf.keras.backend.sum(y_pred_f) + smooth)

def iou_score(y_true, y_pred):
    """IoU评分"""
    smooth = 1e-6
    y_true_f = tf.keras.backend.flatten(y_true)
    y_pred_f = tf.keras.backend.flatten(y_pred)
    intersection = tf.keras.backend.sum(y_true_f * y_pred_f)
    union = tf.keras.backend.sum(y_true_f) + tf.keras.backend.sum(y_pred_f) - intersection
    return (intersection + smooth) / (union + smooth)

def dice_score(y_true, y_pred):
    """Dice评分"""
    smooth = 1e-6
    y_true_f = tf.keras.backend.flatten(y_true)
    y_pred_f = tf.keras.backend.flatten(y_pred)
    intersection = tf.keras.backend.sum(y_true_f * y_pred_f)
    return (2. * intersection + smooth) / (tf.keras.backend.sum(y_true_f) + tf.keras.backend.sum(y_pred_f) + smooth)

def f1_score(y_true, y_pred):
    """F1评分"""
    def recall(y_true, y_pred):
        true_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_true * y_pred, 0, 1)))
        possible_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_true, 0, 1)))
        recall = true_positives / (possible_positives + tf.keras.backend.epsilon())
        return recall

    def precision(y_true, y_pred):
        true_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_true * y_pred, 0, 1)))
        predicted_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_pred, 0, 1)))
        precision = true_positives / (predicted_positives + tf.keras.backend.epsilon())
        return precision

    precision = precision(y_true, y_pred)
    recall = recall(y_true, y_pred)
    return 2*((precision*recall)/(precision+recall+tf.keras.backend.epsilon()))

def load_pytorch_model(model_path):
    """
    加载PyTorch模型
    """
    if not PYTORCH_AVAILABLE:
        print("❌ PyTorch未安装，无法加载PyTorch模型")
        return None

    try:
        print("🔄 尝试加载PyTorch模型...")

        # 检查是否有GPU可用
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️ 使用设备: {device}")

        # 加载模型
        model = torch.load(model_path, map_location=device)
        model.eval()

        print(f"✅ PyTorch模型加载成功")
        return model, device

    except Exception as e:
        print(f"❌ PyTorch模型加载失败: {e}")
        return None

def preprocess_image(image_path, target_size=(256, 256), framework='keras'):
    """
    预处理输入图像（支持PyTorch和Keras格式）
    """
    try:
        # 使用rasterio读取GeoTIFF
        with rasterio.open(image_path) as src:
            # 读取所有波段
            image_data = src.read()  # shape: (bands, height, width)

            # 转换为 (height, width, bands)
            if len(image_data.shape) == 3:
                image_data = np.transpose(image_data, (1, 2, 0))

            # 如果是单波段，扩展为3波段
            if len(image_data.shape) == 2:
                image_data = np.stack([image_data, image_data, image_data], axis=-1)
            elif image_data.shape[2] == 1:
                image_data = np.repeat(image_data, 3, axis=2)
            elif image_data.shape[2] > 3:
                # 只使用前3个波段
                image_data = image_data[:, :, :3]

            # 归一化到0-255范围
            if image_data.max() <= 1.0:
                image_data = (image_data * 255).astype(np.uint8)
            else:
                image_data = image_data.astype(np.uint8)

            # 调整大小
            original_shape = image_data.shape[:2]
            if target_size:
                image_data = cv2.resize(image_data, target_size, interpolation=cv2.INTER_LINEAR)

            if framework == 'keras':
                # Keras格式: (H, W, C)，归一化到0-1
                processed_data = image_data.astype(np.float32) / 255.0
                return processed_data, src.transform, src.crs, original_shape
            else:
                # PyTorch格式: (C, H, W)
                image_tensor = torch.from_numpy(image_data).permute(2, 0, 1).float()
                # 归一化到0-1
                image_tensor = image_tensor / 255.0
                # 添加batch维度
                image_tensor = image_tensor.unsqueeze(0)
                return image_tensor, src.transform, src.crs, original_shape

    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        return None, None, None, None

def predict_image(model, device_or_framework, image_data):
    """
    使用模型进行预测（支持PyTorch和Keras）
    """
    try:
        if device_or_framework == 'keras':
            return predict_with_keras(model, image_data)
        else:
            return predict_with_pytorch(model, device_or_framework, image_data)

    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return None

def predict_with_keras(model, image_data):
    """
    使用Keras模型进行预测
    """
    try:
        # 确保输入数据格式正确
        if len(image_data.shape) == 3:
            # 添加batch维度: (H, W, C) -> (1, H, W, C)
            image_batch = np.expand_dims(image_data, axis=0)
        else:
            image_batch = image_data

        # 归一化到0-1范围
        if image_batch.max() > 1.0:
            image_batch = image_batch.astype(np.float32) / 255.0

        print(f"📊 输入形状: {image_batch.shape}")

        # 模型预测
        predictions = model.predict(image_batch, verbose=0)

        print(f"📊 输出形状: {predictions.shape}")

        # 处理输出
        if len(predictions.shape) == 4:
            # (1, H, W, 1) -> (H, W)
            mask = predictions[0, :, :, 0]
        elif len(predictions.shape) == 3:
            # (1, H, W) -> (H, W)
            mask = predictions[0]
        else:
            mask = predictions

        # 转换为二值mask
        binary_mask = (mask > 0.5).astype(np.uint8)

        return binary_mask

    except Exception as e:
        print(f"❌ Keras预测失败: {e}")
        return None

def predict_with_pytorch(model, device, image_tensor):
    """
    使用PyTorch模型进行预测
    """
    try:
        with torch.no_grad():
            image_tensor = image_tensor.to(device)

            # 模型预测
            outputs = model(image_tensor)

            # 如果输出是logits，应用sigmoid
            if outputs.max() > 1.0 or outputs.min() < 0.0:
                predictions = torch.sigmoid(outputs)
            else:
                predictions = outputs

            # 转换为二值mask
            binary_mask = (predictions > 0.5).float()

            # 转换为numpy数组
            mask_np = binary_mask.squeeze().cpu().numpy()

            return mask_np.astype(np.uint8)

    except Exception as e:
        print(f"❌ PyTorch预测失败: {e}")
        return None

def create_shapefile_from_mask(mask, transform, crs, output_path):
    """
    将预测的mask转换为Shapefile
    """
    try:
        print(f"🔄 创建Shapefile: {output_path}")

        # 使用rasterio.features.shapes提取多边形
        mask_shapes = []

        # 提取值为1的区域（障碍物）
        for geom, value in shapes(mask.astype(np.uint8), mask=(mask == 1), transform=transform):
            if value == 1:
                mask_shapes.append({
                    'geometry': shape(geom),
                    'properties': {
                        'class': 'obstacle',
                        'value': 1,
                        'description': '障碍物区域'
                    }
                })

        if not mask_shapes:
            print("⚠️ 未检测到障碍物区域")
            return False

        # 创建GeoDataFrame
        gdf = gpd.GeoDataFrame(mask_shapes, crs=crs)

        # 保存为Shapefile
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        gdf.to_file(output_path)

        print(f"✅ Shapefile已保存: {output_path}")
        print(f"📊 检测到 {len(mask_shapes)} 个障碍物多边形")

        return True

    except Exception as e:
        print(f"❌ Shapefile创建失败: {e}")
        return False

def create_prediction_visualization(original_image, prediction_mask, ground_truth_mask, output_path):
    """
    创建预测结果可视化对比图
    """
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('模型预测结果对比', fontsize=16, fontweight='bold')

        # 1. 原始图像
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('原始卫星图像', fontsize=12)
        axes[0, 0].axis('off')

        # 2. 真实标签
        if ground_truth_mask is not None:
            # 创建彩色真实标签
            gt_colored = np.zeros((*ground_truth_mask.shape, 3), dtype=np.uint8)
            gt_colored[ground_truth_mask == 0] = [34, 139, 34]   # 绿色-背景
            gt_colored[ground_truth_mask == 1] = [220, 20, 60]   # 红色-障碍物

            axes[0, 1].imshow(gt_colored)
            axes[0, 1].set_title('真实标签 (Ground Truth)', fontsize=12)
        else:
            axes[0, 1].text(0.5, 0.5, '无真实标签', ha='center', va='center',
                           transform=axes[0, 1].transAxes, fontsize=14)
            axes[0, 1].set_title('真实标签 (不可用)', fontsize=12)
        axes[0, 1].axis('off')

        # 3. 预测结果
        pred_colored = np.zeros((*prediction_mask.shape, 3), dtype=np.uint8)
        pred_colored[prediction_mask == 0] = [34, 139, 34]   # 绿色-背景
        pred_colored[prediction_mask == 1] = [220, 20, 60]   # 红色-障碍物

        axes[1, 0].imshow(pred_colored)
        axes[1, 0].set_title('模型预测结果', fontsize=12)
        axes[1, 0].axis('off')

        # 4. 统计信息和图例
        axes[1, 1].axis('off')

        # 计算统计信息
        total_pixels = prediction_mask.size
        obstacle_pixels = np.sum(prediction_mask == 1)
        background_pixels = np.sum(prediction_mask == 0)

        stats_text = f"""
📊 预测统计信息:

🔢 总像素数: {total_pixels:,}
🟢 背景像素: {background_pixels:,} ({background_pixels/total_pixels*100:.1f}%)
🔴 障碍物像素: {obstacle_pixels:,} ({obstacle_pixels/total_pixels*100:.1f}%)

🎨 颜色说明:
🟢 绿色: 背景区域 (可布设)
🔴 红色: 障碍物区域 (不可布设)

💡 使用说明:
• 绿色区域适合布设地震检波器
• 红色区域应避免布设设备
• 预测结果已保存为Shapefile格式
"""

        if ground_truth_mask is not None:
            # 计算准确率指标
            accuracy = np.mean(prediction_mask == ground_truth_mask)
            stats_text += f"\n📈 预测准确率: {accuracy*100:.2f}%"

        axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes,
                       fontsize=10, verticalalignment='top', fontfamily='monospace',
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))

        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()

        print(f"✅ 可视化图已保存: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 可视化创建失败: {e}")
        return False

def process_single_image(model, device_or_framework, image_path, mask_path, output_folder):
    """
    处理单个图像文件
    """
    image_name = Path(image_path).stem
    print(f"\n📷 处理图像: {image_name}")
    print("-" * 50)

    # 确定框架类型
    framework = 'keras' if device_or_framework == 'keras' else 'pytorch'

    # 1. 预处理图像
    image_data, transform, crs, original_shape = preprocess_image(image_path, framework=framework)
    if image_data is None:
        return False

    # 2. 模型预测
    prediction_mask = predict_image(model, device_or_framework, image_data)
    if prediction_mask is None:
        return False

    # 调整预测结果尺寸到原始尺寸
    if prediction_mask.shape != original_shape:
        prediction_mask = cv2.resize(prediction_mask, (original_shape[1], original_shape[0]),
                                   interpolation=cv2.INTER_NEAREST)

    # 3. 加载真实标签（如果存在）
    ground_truth_mask = None
    if mask_path and os.path.exists(mask_path):
        try:
            with rasterio.open(mask_path) as src:
                ground_truth_mask = src.read(1)
        except:
            print("⚠️ 无法读取真实标签文件")

    # 4. 创建输出文件夹
    image_output_folder = Path(output_folder) / image_name
    os.makedirs(image_output_folder, exist_ok=True)

    # 5. 保存预测结果为Shapefile
    shapefile_path = image_output_folder / f"{image_name}_obstacles.shp"
    create_shapefile_from_mask(prediction_mask, transform, crs, str(shapefile_path))

    # 6. 保存预测mask为GeoTIFF
    mask_tiff_path = image_output_folder / f"{image_name}_prediction.tif"
    try:
        with rasterio.open(
            mask_tiff_path, 'w',
            driver='GTiff',
            height=prediction_mask.shape[0],
            width=prediction_mask.shape[1],
            count=1,
            dtype=prediction_mask.dtype,
            crs=crs,
            transform=transform
        ) as dst:
            dst.write(prediction_mask, 1)
        print(f"✅ 预测mask已保存: {mask_tiff_path}")
    except Exception as e:
        print(f"⚠️ 保存预测mask失败: {e}")

    # 7. 创建可视化对比图
    try:
        # 读取原始图像用于可视化
        with rasterio.open(image_path) as src:
            original_image = src.read([1, 2, 3])  # 读取RGB波段
            original_image = np.transpose(original_image, (1, 2, 0))

            # 归一化显示
            if original_image.max() <= 1.0:
                original_image = (original_image * 255).astype(np.uint8)
            else:
                original_image = original_image.astype(np.uint8)

        visualization_path = image_output_folder / f"{image_name}_comparison.png"
        create_prediction_visualization(original_image, prediction_mask, ground_truth_mask,
                                      str(visualization_path))
    except Exception as e:
        print(f"⚠️ 可视化创建失败: {e}")

    return True

def find_corresponding_mask(image_path, mask_folder):
    """
    查找对应的mask文件
    """
    image_name = Path(image_path).stem

    # 尝试不同的命名模式
    possible_names = [
        # 标准命名模式
        f"{image_name}_mask.tif",
        f"{image_name.replace('_image', '_mask')}.tif",
        f"{image_name.replace('image_', '')}_mask.tif",
        f"{image_name}_mask.tiff",

        # GEE数据特殊命名模式
        # 从 tile_0_0_img-0000000000-0000000000 提取 tile_0_0
        f"{extract_tile_prefix(image_name)}_mask.tif",
        f"{extract_tile_prefix(image_name)}_mask.tiff",

        # 其他可能的模式
        f"{image_name.replace('_img', '_mask')}.tif",
        f"{image_name.split('-')[0].replace('_img', '_mask')}.tif",
    ]

    for name in possible_names:
        mask_path = Path(mask_folder) / name
        if mask_path.exists():
            return str(mask_path)

    return None

def extract_tile_prefix(image_name):
    """
    从图像名称中提取tile前缀
    例如：tile_0_0_img-0000000000-0000000000 -> tile_0_0
    """
    # 查找 '_img' 的位置
    img_pos = image_name.find('_img')
    if img_pos != -1:
        return image_name[:img_pos]

    # 如果没有找到 '_img'，尝试其他模式
    parts = image_name.split('-')
    if len(parts) > 0:
        base_part = parts[0]
        if '_img' in base_part:
            return base_part.replace('_img', '')

    return image_name

def process_data_folder(model, device, data_folder, output_folder):
    """
    批量处理数据文件夹
    """
    data_path = Path(data_folder)

    # 查找图像文件
    image_folder = data_path / "image"
    mask_folder = data_path / "mask"

    if not image_folder.exists():
        print(f"❌ 图像文件夹不存在: {image_folder}")
        return False

    # 查找所有图像文件
    image_files = []
    for ext in ['.tif', '.tiff', '.TIF', '.TIFF']:
        image_files.extend(image_folder.glob(f"*{ext}"))

    if not image_files:
        print(f"❌ 在图像文件夹中未找到TIFF文件: {image_folder}")
        return False

    print(f"📁 数据文件夹: {data_folder}")
    print(f"📁 输出文件夹: {output_folder}")
    print(f"📊 找到 {len(image_files)} 个图像文件")
    print()

    # 批量处理
    success_count = 0
    failed_count = 0

    for i, image_file in enumerate(image_files, 1):
        print(f"[{i}/{len(image_files)}] 处理: {image_file.name}")

        # 查找对应的mask文件
        mask_path = None
        if mask_folder.exists():
            mask_path = find_corresponding_mask(str(image_file), str(mask_folder))
            if mask_path:
                print(f"📋 找到对应的mask文件: {Path(mask_path).name}")
            else:
                print("⚠️ 未找到对应的mask文件，将只进行预测")

        try:
            success = process_single_image(model, device, str(image_file), mask_path, output_folder)

            if success:
                success_count += 1
                print(f"✅ 处理成功")
            else:
                failed_count += 1
                print(f"❌ 处理失败")

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理失败: {e}")

        print()

    # 处理结果总结
    print("=" * 60)
    print("批量预测完成")
    print("=" * 60)
    print(f"📊 总文件数: {len(image_files)}")
    print(f"✅ 成功处理: {success_count}")
    print(f"❌ 处理失败: {failed_count}")

    if success_count > 0:
        print(f"\n🎉 成功处理 {success_count} 个图像文件！")
        print(f"📁 预测结果保存在: {output_folder}")
        print(f"\n💡 输出文件说明:")
        print(f"• 📄 *_obstacles.shp: Shapefile格式的障碍物多边形")
        print(f"• 🗺️ *_prediction.tif: GeoTIFF格式的预测mask")
        print(f"• 🎨 *_comparison.png: 可视化对比图")

        # 显示文件夹结构示例
        print(f"\n📂 输出文件夹结构:")
        print(f"{Path(output_folder).name}/")
        for image_file in image_files[:3]:  # 显示前3个作为示例
            stem = image_file.stem
            print(f"├── {stem}/")
            print(f"│   ├── 📄 {stem}_obstacles.shp     (Shapefile)")
            print(f"│   ├── 🗺️ {stem}_prediction.tif    (预测mask)")
            print(f"│   └── 🎨 {stem}_comparison.png    (对比图)")
        if len(image_files) > 3:
            print(f"└── ... (还有 {len(image_files)-3} 个文件夹)")

    return success_count > 0

def main():
    """主函数"""
    # 默认路径配置
    default_model_path = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\trained_models\unet_model.h5"  # 修改为你的模型路径
    default_data_folder = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted"

    print("=" * 60)
    print("🤖 模型预测和Shapefile生成工具")
    print("=" * 60)
    print("本工具将使用训练好的模型对GEE数据进行预测并生成Shapefile")
    print()

    # 解析命令行参数
    if len(sys.argv) >= 2:
        model_path = sys.argv[1]
        print(f"🤖 使用命令行指定的模型: {model_path}")
    else:
        model_path = default_model_path
        print(f"🤖 使用默认模型: {model_path}")

    if len(sys.argv) >= 3:
        data_folder = sys.argv[2]
        print(f"📁 使用命令行指定的数据文件夹: {data_folder}")
    else:
        data_folder = default_data_folder
        print(f"📁 使用默认数据文件夹: {data_folder}")

    if len(sys.argv) >= 4:
        output_folder = sys.argv[3]
        print(f"📁 使用命令行指定的输出文件夹: {output_folder}")
    else:
        output_folder = str(Path(data_folder) / "predictions")
        print(f"📁 输出文件夹: {output_folder}")

    print()

    # 检查文件和文件夹是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("\n💡 使用方法:")
        print("1. 修改代码中的 default_model_path")
        print("2. 或使用命令行参数: python predict_and_create_shapefile.py <model_path> [data_folder] [output_folder]")
        return

    if not os.path.exists(data_folder):
        print(f"❌ 数据文件夹不存在: {data_folder}")
        print("\n💡 使用方法:")
        print("1. 修改代码中的 default_data_folder")
        print("2. 或使用命令行参数: python predict_and_create_shapefile.py [model_path] <data_folder> [output_folder]")
        return

    try:
        # 1. 加载模型
        model_result = load_model(model_path)
        if model_result is None:
            return

        model, device = model_result

        # 2. 批量处理数据
        success = process_data_folder(model, device, data_folder, output_folder)

        if not success:
            print("❌ 批量处理失败")
        else:
            print("\n🎉 所有处理完成！")
            print("现在你可以:")
            print("1. 在GIS软件中打开生成的Shapefile文件")
            print("2. 查看可视化对比图验证预测效果")
            print("3. 使用统一的数据集验证模型性能")

    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()