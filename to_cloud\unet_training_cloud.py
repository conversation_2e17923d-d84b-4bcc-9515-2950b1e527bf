#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛰️ UNet卫星图像建筑物分割云端训练脚本 - 支持初训和增量训练

本脚本专门用于云端环境的卫星图像建筑物分割模型训练，
支持从零开始训练和基于预训练模型的增量训练。

主要特点:
- 🚀 全部按需加载模式，充分利用云端资源
- 🔄 支持初训和增量训练两种模式
- 🔧 图像合并功能，自动处理GEE分块图像
- 💾 数据生成器，内存友好的数据加载
- 🎯 智能配对，改进的image-mask文件配对逻辑
- ⚡ 混合精度训练支持（默认启用）
- 🌐 多平台支持（Colab、Kaggle、Azure ML等）
- 📈 完整的训练监控和报告生成
- 🔒 自动备份和版本管理

作者: Leon
日期: 2025-07-29
版本: 2.0 - 增量训练支持版
"""

import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
import psutil
import gc
import re
import tempfile
import shutil
import rasterio
from rasterio.merge import merge
from datetime import datetime
import traceback

warnings.filterwarnings('ignore')

# 检查和安装依赖包
def check_and_install_dependencies():
    """检查并安装必要的依赖包"""
    print("🔧 检查和安装依赖包...")

    required_packages = [
        'tensorflow==2.15.0',
        'rasterio',
        'geopandas',
        'patchify',
        'scikit-learn',
        'matplotlib',
        'seaborn',
        'opencv-python',
        'psutil'
    ]

    import subprocess
    import importlib

    for package in required_packages:
        package_name = package.split('==')[0].replace('-', '_')
        try:
            importlib.import_module(package_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"📦 安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

    print("✅ 依赖包检查完成")

# 导入TensorFlow和相关库
def import_tensorflow():
    """导入并配置TensorFlow"""
    try:
        import tensorflow as tf
        print(f"TensorFlow版本: {tf.__version__}")
        print(f"GPU可用: {tf.config.list_physical_devices('GPU')}")
        print(f"CUDA可用: {tf.test.is_built_with_cuda()}")

        # 配置GPU内存增长
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"✅ GPU配置完成: {len(gpus)} 个GPU")
            except RuntimeError as e:
                print(f"❌ GPU配置失败: {e}")

        return tf
    except ImportError as e:
        print(f"❌ TensorFlow导入失败: {e}")
        sys.exit(1)

# 内存监控函数
def monitor_memory():
    """监控内存使用情况"""
    memory = psutil.virtual_memory()
    print(f"📊 内存状态:")
    print(f"  总内存: {memory.total/1024**3:.1f} GB")
    print(f"  已用内存: {memory.used/1024**3:.1f} GB")
    print(f"  可用内存: {memory.available/1024**3:.1f} GB")
    print(f"  使用率: {memory.percent:.1f}%")

    if memory.percent > 85:
        print("⚠️  内存使用率过高")
    elif memory.percent > 75:
        print("🔶 内存使用率较高")
    else:
        print("✅ 内存使用正常")

# 平台检测和路径配置
def detect_platform_and_configure_paths():
    """自动检测平台并配置路径"""

    # Kaggle平台检测
    if '/kaggle/' in os.getcwd():
        print("🔍 检测到Kaggle平台")
        return {
            'platform': 'kaggle',
            'image_folder': '/kaggle/input/your-dataset/converted/image',
            'mask_folder': '/kaggle/input/your-dataset/converted/mask',
            'output_folder': '/kaggle/working/models'
        }

    # Google Colab检测
    elif 'google.colab' in sys.modules:
        print("🔍 检测到Google Colab平台")
        return {
            'platform': 'colab',
            'image_folder': '/content/drive/MyDrive/dataset/converted/image',
            'mask_folder': '/content/drive/MyDrive/dataset/converted/mask',
            'output_folder': '/content/drive/MyDrive/models'
        }

    # Azure ML检测
    elif 'AZUREML_' in os.environ:
        print("🔍 检测到Azure ML平台")
        return {
            'platform': 'azure',
            'image_folder': './data/converted/image',
            'mask_folder': './data/converted/mask',
            'output_folder': './outputs/models'
        }

    # 默认本地环境
    else:
        print("🔍 使用默认本地环境配置")
        return {
            'platform': 'local',
            'image_folder': './data/converted/image',
            'mask_folder': './data/converted/mask',
            'output_folder': './models'
        }

# 图像合并器类
class ImageMerger:
    """图像合并器，用于合并GEE下载的分块图像"""

    def __init__(self):
        self.supported_formats = ['.tif', '.tiff']

    def find_related_images(self, image_folder, base_name=None):
        """查找相关的图像文件"""
        image_folder = Path(image_folder)
        if not image_folder.exists():
            raise FileNotFoundError(f"图像文件夹不存在: {image_folder}")

        # 获取所有图像文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(image_folder.glob(f"*{ext}"))

        if not image_files:
            raise FileNotFoundError(f"在文件夹中未找到图像文件: {image_folder}")

        # 按基础名称分组
        grouped_files = {}

        for img_file in image_files:
            # 提取基础名称（去除可能的分块后缀）
            name = img_file.stem

            # 常见的GEE分块模式，优先匹配tile格式
            patterns = [
                r'(tile_\d+_\d+)_img-\d+-\d+$',  # tile_x_y_img-offset1-offset2
                r'(.+)-\d+-\d+-\d+$',            # name-0-0-0
                r'(.+)_\d+_\d+$',                # name_1_2
                r'(.+)-part\d+$',                # name-part1
                r'(.+)-\d+$',                    # name-1
                r'(.+)_\d+$',                    # name_1
            ]

            base = name
            for pattern in patterns:
                match = re.match(pattern, name)
                if match:
                    base = match.group(1)
                    break

            if base not in grouped_files:
                grouped_files[base] = []
            grouped_files[base].append(img_file)

        return grouped_files

    def _extract_tile_base_name(self, filename):
        """提取瓦片的基础名称"""
        # 匹配 tile_x_y 模式
        tile_pattern = r'(tile_\d+_\d+)'
        match = re.search(tile_pattern, filename.lower())
        if match:
            return match.group(1)
        return filename

    def merge_images(self, image_files, output_path, method='first'):
        """合并多个图像文件"""
        if not image_files:
            raise ValueError("没有提供图像文件")

        if len(image_files) == 1:
            # 只有一个文件，直接复制
            shutil.copy2(image_files[0], output_path)
            print(f"单个文件复制完成: {output_path}")
            return output_path

        print(f"开始合并 {len(image_files)} 个图像文件...")

        # 打开所有图像文件
        src_files = []
        try:
            for img_file in image_files:
                src = rasterio.open(img_file)
                src_files.append(src)
                print(f"  - {img_file.name}")

            # 合并图像
            mosaic, out_trans = merge(src_files, method=method)

            # 获取元数据
            out_meta = src_files[0].meta.copy()
            out_meta.update({
                "driver": "GTiff",
                "height": mosaic.shape[1],
                "width": mosaic.shape[2],
                "count": mosaic.shape[0],
                "transform": out_trans,
                "compress": "lzw",
                "tiled": True,
                "blockxsize": 512,
                "blockysize": 512,
                "interleave": "pixel"
            })

            # 保存合并后的图像
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with rasterio.open(output_path, "w", **out_meta) as dest:
                dest.write(mosaic)

            print(f"图像合并完成: {os.path.basename(output_path)}")
            print(f"输出图像尺寸: {mosaic.shape[1]} x {mosaic.shape[2]}")

        finally:
            # 关闭所有文件
            for src in src_files:
                src.close()

        return output_path

    def merge_folder_images(self, image_folder, output_folder, base_name=None):
        """合并文件夹中的所有相关图像"""
        # 查找相关图像
        grouped_files = self.find_related_images(image_folder, base_name)

        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)

        results = {}

        for base, files in grouped_files.items():
            if len(files) > 1:
                # 需要合并
                tile_base = self._extract_tile_base_name(base)
                output_path = os.path.join(output_folder, f"{tile_base}_merged.tif")
                merged_path = self.merge_images(files, output_path)
                results[base] = merged_path
                print(f"✅ 合并完成: {base} -> {os.path.basename(merged_path)}")
            else:
                # 单个文件，直接复制
                tile_base = self._extract_tile_base_name(base)
                output_path = os.path.join(output_folder, f"{tile_base}.tif")
                shutil.copy2(files[0], output_path)
                results[base] = output_path
                print(f"✅ 复制完成: {base} -> {os.path.basename(output_path)}")

        return results

# 训练配置
TRAINING_CONFIG = {
    # 训练模式配置
    'training_mode': 'initial',  # 'initial' 或 'incremental'
    'pretrained_model_path': None,  # 预训练模型路径（增量训练时使用）
    'model_save_name': 'unet_model_cloud.h5',  # 模型保存名称

    # 基础训练参数
    'batch_size': 8,          # 批次大小
    'epochs': 50,             # 最大训练轮数
    'learning_rate': 1e-4,    # 学习率（初训）
    'incremental_learning_rate': 2e-5,  # 增量训练学习率
    'validation_split': 0.2,  # 验证集比例

    # 图像处理参数
    'patch_size': 256,        # 图像块大小
    'patch_step': 224,        # 图像块步长（重叠32像素）
    'min_foreground_ratio': 0.01,  # 前景像素最小比例

    # 全部按需加载模式参数
    'use_all_data': True,     # 启用全部按需加载模式
    'max_patches_per_file': None,  # 不限制每个文件的图像块数量
    'max_total_patches': None,     # 不限制总图像块数量
    'use_data_augmentation': True, # 是否使用数据增强
    'shuffle': True,          # 是否打乱数据
    'prefetch_buffer': 4,     # 预取缓冲区大小

    # 云端优化参数
    'use_all_files': True,    # 使用全部文件
    'memory_limit_gb': 50,    # 内存限制
    'enable_mixed_precision': True,  # 启用混合精度训练（默认启用）

    # 增量训练专用参数
    'create_backup': True,    # 是否创建预训练模型备份
    'gradient_clip_norm': 1.0,  # 梯度裁剪范数
    'gradient_clip_value': 0.5,  # 梯度裁剪值
    'early_stopping_patience': 10,  # 早停耐心
    'lr_reduce_patience': 5,  # 学习率衰减耐心
}

# 数据生成器类
class DataGenerator:
    """数据生成器 - 避免一次性加载所有数据到内存"""

    def __init__(self, file_pairs, config, is_training=True, shuffle=True):
        self.file_pairs = file_pairs
        self.config = config
        self.is_training = is_training
        self.shuffle = shuffle
        self.batch_size = config['batch_size']

        # 预处理所有文件，生成图像块索引
        self.patch_indices = self._generate_patch_indices()
        self.total_patches = len(self.patch_indices)

        print(f"📊 数据生成器初始化完成:")
        print(f"  文件对数量: {len(file_pairs)}")
        print(f"  总图像块数: {self.total_patches}")
        print(f"  批次大小: {self.batch_size}")
        print(f"  批次数量: {len(self)}")

        self.on_epoch_end()

    def _generate_patch_indices(self):
        """生成所有图像块的索引信息"""
        print("🔍 生成图像块索引...")
        patch_indices = []

        for file_idx, (img_file, mask_file) in enumerate(self.file_pairs):
            try:
                # 读取图像尺寸信息（不加载实际数据）
                with rasterio.open(img_file) as src:
                    height, width = src.height, src.width
                    bands = src.count

                # 计算可能的图像块位置
                patch_size = self.config['patch_size']
                step = self.config['patch_step']

                patches_per_file = 0
                for y in range(0, height - patch_size + 1, step):
                    for x in range(0, width - patch_size + 1, step):
                        patch_indices.append({
                            'file_idx': file_idx,
                            'img_file': img_file,
                            'mask_file': mask_file,
                            'x': x,
                            'y': y,
                            'patch_size': patch_size
                        })
                        patches_per_file += 1

                        # 全部按需加载模式：不限制图像块数量
                        max_patches = self.config.get('max_patches_per_file', None)
                        use_all_data = self.config.get('use_all_data', False)

                        # 只有在非全部数据模式下才限制数量
                        if not use_all_data and max_patches and patches_per_file >= max_patches:
                            break
                    # 只有在非全部数据模式下才限制数量
                    max_patches = self.config.get('max_patches_per_file', None)
                    use_all_data = self.config.get('use_all_data', False)
                    if not use_all_data and max_patches and patches_per_file >= max_patches:
                        break

                print(f"  文件 {file_idx+1}: {os.path.basename(img_file)} -> {patches_per_file} 个图像块")

            except Exception as e:
                print(f"  ❌ 处理文件失败: {img_file} - {e}")
                continue

        return patch_indices

    def __len__(self):
        """返回批次数量"""
        return int(np.ceil(len(self.patch_indices) / float(self.batch_size)))

    def __getitem__(self, idx):
        """获取一个批次的数据"""
        # 获取当前批次的索引
        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]

        # 加载批次数据
        batch_x = []
        batch_y = []

        for i in batch_indices:
            if i >= len(self.patch_indices):
                continue

            patch_info = self.patch_indices[i]

            try:
                # 加载图像块
                img_patch, mask_patch = self._load_patch(patch_info)

                # 检查前景比例
                foreground_ratio = np.sum(mask_patch > 0) / mask_patch.size
                if foreground_ratio >= self.config['min_foreground_ratio']:
                    batch_x.append(img_patch)
                    batch_y.append(mask_patch)
                else:
                    # 如果当前块不符合要求，尝试下一个
                    continue

            except Exception as e:
                # 如果加载失败，跳过这个块
                continue

        # 如果批次数据不足，用零填充
        while len(batch_x) < self.batch_size:
            if len(batch_x) > 0:
                batch_x.append(np.zeros_like(batch_x[0]))
                batch_y.append(np.zeros_like(batch_y[0]))
            else:
                # 如果完全没有有效数据，创建默认形状
                patch_size = self.config['patch_size']
                batch_x.append(np.zeros((patch_size, patch_size, 3), dtype=np.float32))
                batch_y.append(np.zeros((patch_size, patch_size, 1), dtype=np.float32))

        # 转换为numpy数组
        X = np.array(batch_x[:self.batch_size], dtype=np.float32)
        y = np.array(batch_y[:self.batch_size], dtype=np.float32)

        # 数据预处理
        X = X / 255.0  # 归一化
        y = (y > 0).astype(np.float32)  # 二值化

        # 转换为分类格式
        y_categorical = np.zeros((*y.shape[:3], 2), dtype=np.float32)
        y_categorical[..., 0] = (y[..., 0] == 0)  # 背景
        y_categorical[..., 1] = (y[..., 0] == 1)  # 前景

        # 数据增强（仅训练时）
        if self.is_training and self.config.get('use_data_augmentation', True):
            X, y_categorical = self._augment_batch(X, y_categorical)

        return X, y_categorical

    def _load_patch(self, patch_info):
        """加载单个图像块"""
        x, y = patch_info['x'], patch_info['y']
        patch_size = patch_info['patch_size']

        # 读取图像块
        with rasterio.open(patch_info['img_file']) as src:
            # 读取指定区域
            window = rasterio.windows.Window(x, y, patch_size, patch_size)
            image = src.read(window=window).transpose(1, 2, 0)
            if image.shape[2] > 3:
                image = image[:, :, :3]

        # 读取掩膜块
        with rasterio.open(patch_info['mask_file']) as src:
            window = rasterio.windows.Window(x, y, patch_size, patch_size)
            mask = src.read(1, window=window)
            mask = np.expand_dims(mask, axis=-1)

        return image.astype(np.float32), mask.astype(np.float32)

    def _augment_batch(self, images, masks):
        """批次数据增强"""
        augmented_images = []
        augmented_masks = []

        for img, mask in zip(images, masks):
            # 随机水平翻转
            if np.random.random() > 0.5:
                img = np.fliplr(img)
                mask = np.fliplr(mask)

            # 随机垂直翻转
            if np.random.random() > 0.5:
                img = np.flipud(img)
                mask = np.flipud(mask)

            # 随机旋转90度
            if np.random.random() > 0.5:
                k = np.random.randint(1, 4)
                img = np.rot90(img, k)
                mask = np.rot90(mask, k)

            augmented_images.append(img)
            augmented_masks.append(mask)

        return np.array(augmented_images), np.array(augmented_masks)

    def on_epoch_end(self):
        """每个epoch结束时调用"""
        self.indices = np.arange(len(self.patch_indices))
        if self.shuffle:
            np.random.shuffle(self.indices)

# 数据准备函数
def prepare_data_generators(image_folder, mask_folder, config):
    """准备数据生成器 - 集成图像合并功能"""
    print("📦 开始准备数据生成器（集成图像合并）...")

    # 获取原始文件列表
    image_files = []
    mask_files = []

    for ext in ['.tif', '.tiff']:
        image_files.extend(Path(image_folder).glob(f"*{ext}"))
        mask_files.extend(Path(mask_folder).glob(f"*{ext}"))

    image_files = [str(f) for f in image_files]
    mask_files = [str(f) for f in mask_files]

    print(f"找到 {len(image_files)} 个图像文件，{len(mask_files)} 个掩膜文件")

    # 检查是否需要合并分块图像
    print("\n🔧 检查是否需要合并分块图像...")

    merger = ImageMerger()
    temp_folder = None

    try:
        grouped_files = merger.find_related_images(image_folder)

        # 检查是否有需要合并的组
        needs_merge = any(len(files) > 1 for files in grouped_files.values())

        if needs_merge:
            print("✅ 发现分块图像，开始合并...")

            # 创建临时文件夹
            temp_folder = tempfile.mkdtemp(prefix='merged_images_')
            print(f"临时合并文件夹: {temp_folder}")

            # 合并图像
            merged_results = merger.merge_folder_images(image_folder, temp_folder)

            # 更新图像文件列表为合并后的文件
            image_files = [path for path in merged_results.values()]
            print(f"\n✅ 图像合并完成，共 {len(image_files)} 个文件")

            # 打印合并后的文件名
            for merged_file in image_files:
                print(f"  合并后文件: {os.path.basename(merged_file)}")
        else:
            print("ℹ️  未发现需要合并的分块图像")

    except Exception as e:
        print(f"⚠️  图像合并检查失败，使用原始文件: {e}")

    # 改进的文件匹配逻辑
    print("\n🔧 开始智能文件配对...")
    matched_pairs = []

    for img_file in image_files:
        img_name = os.path.basename(img_file)

        # 提取瓦片基础名称
        tile_base = None
        if 'tile_' in img_name.lower():
            # 使用正则表达式提取tile_x_y
            tile_pattern = r'(tile_\d+_\d+)'
            match = re.search(tile_pattern, img_name.lower())
            if match:
                tile_base = match.group(1)

        if tile_base:
            # 寻找匹配的掩膜文件
            for mask_file in mask_files:
                mask_name = os.path.basename(mask_file)
                if tile_base in mask_name.lower():
                    matched_pairs.append((img_file, mask_file))
                    print(f"✅ 配对成功: {os.path.basename(img_file)} <-> {os.path.basename(mask_file)}")
                    break
        else:
            print(f"⚠️  无法提取瓦片信息: {img_name}")

    print(f"\n📊 智能匹配结果: {len(matched_pairs)} 对文件")

    if not matched_pairs:
        raise ValueError("未找到任何匹配的图像-掩膜对！请检查文件命名和路径。")

    # 分割训练和验证数据
    from sklearn.model_selection import train_test_split

    train_pairs, val_pairs = train_test_split(
        matched_pairs,
        test_size=config['validation_split'],
        random_state=42
    )

    print(f"\n📊 数据分割结果:")
    print(f"  训练文件对: {len(train_pairs)}")
    print(f"  验证文件对: {len(val_pairs)}")

    # 创建数据生成器
    print("\n🔧 创建数据生成器...")

    train_generator = DataGenerator(
        train_pairs,
        config,
        is_training=True,
        shuffle=config.get('shuffle', True)
    )

    val_generator = DataGenerator(
        val_pairs,
        config,
        is_training=False,
        shuffle=False
    )

    print("\n✅ 数据生成器创建完成")

    # 返回生成器和临时文件夹路径（用于清理）
    return train_generator, val_generator, temp_folder

# UNet模型定义
def conv_block(inputs, num_filters):
    """卷积块"""
    from tensorflow.keras import layers

    x = layers.Conv2D(num_filters, 3, padding="same")(inputs)
    x = layers.BatchNormalization()(x)
    x = layers.Activation("relu")(x)

    x = layers.Conv2D(num_filters, 3, padding="same")(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation("relu")(x)

    return x

def encoder_block(inputs, num_filters):
    """编码器块"""
    from tensorflow.keras import layers

    x = conv_block(inputs, num_filters)
    p = layers.MaxPool2D((2, 2))(x)
    return x, p

def decoder_block(inputs, skip_features, num_filters):
    """解码器块"""
    from tensorflow.keras import layers

    x = layers.Conv2DTranspose(num_filters, (2, 2), strides=2, padding="same")(inputs)
    x = layers.Concatenate()([x, skip_features])
    x = conv_block(x, num_filters)
    return x

def create_complete_unet(input_shape=(256, 256, 3), num_classes=2):
    """创建完整的UNet模型"""
    from tensorflow import keras
    from tensorflow.keras import layers

    inputs = layers.Input(input_shape)

    # 编码器
    s1, p1 = encoder_block(inputs, 64)
    s2, p2 = encoder_block(p1, 128)
    s3, p3 = encoder_block(p2, 256)
    s4, p4 = encoder_block(p3, 512)

    # 桥接
    b1 = conv_block(p4, 1024)

    # 解码器
    d1 = decoder_block(b1, s4, 512)
    d2 = decoder_block(d1, s3, 256)
    d3 = decoder_block(d2, s2, 128)
    d4 = decoder_block(d3, s1, 64)

    # 输出层
    outputs = layers.Conv2D(num_classes, 1, padding="same", activation="softmax")(d4)

    model = keras.Model(inputs, outputs, name="Complete_UNet")
    return model

def load_pretrained_model(model_path, config):
    """加载预训练模型用于增量训练"""
    import tensorflow as tf
    from tensorflow import keras

    print(f"🔄 加载预训练模型: {model_path}")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"预训练模型文件不存在: {model_path}")

    try:
        # 加载模型
        model = keras.models.load_model(
            model_path,
            custom_objects={
                'dice_coef': dice_coef,
                'dice_loss': dice_loss,
                'focal_loss': focal_loss,
                'combined_loss': combined_loss,
                'iou_coef': iou_coef
            },
            compile=False  # 不编译，稍后重新编译
        )

        print(f"✅ 成功加载预训练模型，参数量: {model.count_params():,}")
        print(f"✅ 模型输入形状: {model.input_shape}")
        print(f"✅ 模型输出形状: {model.output_shape}")

        # 验证模型形状是否匹配
        expected_input_shape = (None, config['patch_size'], config['patch_size'], 3)
        if model.input_shape != expected_input_shape:
            print(f"⚠️  警告: 模型输入形状不匹配")
            print(f"   期望: {expected_input_shape}")
            print(f"   实际: {model.input_shape}")

        return model

    except Exception as e:
        print(f"❌ 加载预训练模型失败: {e}")
        print("🔧 尝试创建新模型...")
        return None

def create_model_backup(model_path, backup_folder):
    """创建模型备份"""
    if not os.path.exists(model_path):
        print(f"⚠️  预训练模型不存在，跳过备份: {model_path}")
        return None

    try:
        os.makedirs(backup_folder, exist_ok=True)

        # 生成备份文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_name = os.path.basename(model_path)
        backup_name = f"backup_{timestamp}_{model_name}"
        backup_path = os.path.join(backup_folder, backup_name)

        # 复制模型文件
        shutil.copy2(model_path, backup_path)
        print(f"✅ 模型备份完成: {backup_path}")

        return backup_path

    except Exception as e:
        print(f"⚠️  模型备份失败: {e}")
        return None

# 损失函数和指标
def dice_coef(y_true, y_pred, smooth=1e-6):
    """Dice系数"""
    import tensorflow as tf

    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)

    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    dice = (2. * intersection + smooth) / (tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) + smooth)

    return tf.cast(dice, tf.float32)

def dice_loss(y_true, y_pred):
    """Dice损失"""
    return 1 - dice_coef(y_true, y_pred)

def focal_loss(y_true, y_pred, alpha=0.25, gamma=2.0):
    """Focal损失"""
    import tensorflow as tf

    epsilon = tf.keras.backend.epsilon()
    y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)

    ce = -y_true * tf.math.log(y_pred)
    weight = alpha * y_true * tf.pow((1 - y_pred), gamma)
    focal = weight * ce

    return tf.reduce_mean(tf.reduce_sum(focal, axis=-1))

def combined_loss(y_true, y_pred):
    """组合损失函数"""
    import tensorflow as tf

    dice = dice_loss(y_true, y_pred)
    focal = focal_loss(y_true, y_pred)

    # 数值稳定性检查
    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))
    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))

    combined = dice + focal
    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))

    return tf.cast(combined, tf.float32)

def iou_coef(y_true, y_pred, smooth=1e-6):
    """IoU系数"""
    import tensorflow as tf

    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)

    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection

    iou = (intersection + smooth) / (union + smooth)
    return tf.cast(iou, tf.float32)

# 训练函数
def train_unet_model(image_folder, mask_folder, output_folder, config):
    """训练UNet模型 - 支持初训和增量训练"""
    import tensorflow as tf
    from tensorflow import keras

    # 确定训练模式
    training_mode = config.get('training_mode', 'initial')
    pretrained_model_path = config.get('pretrained_model_path', None)

    print(f"🚀 开始UNet训练流程 - {training_mode.upper()}训练模式")
    print("=" * 60)

    if training_mode == 'incremental':
        print(f"📂 预训练模型路径: {pretrained_model_path}")

    # 初始化变量
    train_generator = None
    val_generator = None
    temp_folder = None
    backup_path = None

    try:
        # 1. 准备数据生成器
        print("📦 步骤1: 准备数据生成器")
        monitor_memory()

        train_generator, val_generator, temp_folder = prepare_data_generators(
            image_folder, mask_folder, config
        )

        print("\n📊 数据生成器准备完成")
        monitor_memory()

        # 2. 启用混合精度训练（默认启用）
        if config.get('enable_mixed_precision', True):
            print("\n🚀 启用混合精度训练...")
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            print(f"✅ 混合精度策略设置完成: {policy.name}")

        # 3. 创建或加载模型
        print(f"\n⚙️ 步骤3: {'加载预训练模型' if training_mode == 'incremental' else '创建新模型'}")

        model = None

        if training_mode == 'incremental' and pretrained_model_path:
            # 创建备份
            if config.get('create_backup', True):
                backup_folder = os.path.join(output_folder, 'backups')
                backup_path = create_model_backup(pretrained_model_path, backup_folder)

            # 加载预训练模型
            model = load_pretrained_model(pretrained_model_path, config)

            if model is None:
                print("⚠️  预训练模型加载失败，切换到初训模式")
                training_mode = 'initial'

        # 如果是初训模式或预训练模型加载失败，创建新模型
        if model is None:
            model = create_complete_unet(
                input_shape=(config['patch_size'], config['patch_size'], 3),
                num_classes=2
            )
            print(f"✅ 新模型创建完成，参数数量: {model.count_params():,}")

        # 4. 编译模型
        print("\n⚙️ 步骤4: 编译模型")

        # 根据训练模式选择学习率
        if training_mode == 'incremental':
            learning_rate = config.get('incremental_learning_rate', 2e-5)
            print(f"📉 使用增量训练学习率: {learning_rate}")
        else:
            learning_rate = config.get('learning_rate', 1e-4)
            print(f"📈 使用初训学习率: {learning_rate}")

        # 使用梯度裁剪的优化器增强数值稳定性
        optimizer = keras.optimizers.Adam(
            learning_rate=learning_rate,
            clipnorm=config.get('gradient_clip_norm', 1.0),
            clipvalue=config.get('gradient_clip_value', 0.5),
            epsilon=1e-7
        )

        model.compile(
            optimizer=optimizer,
            loss=combined_loss,
            metrics=[dice_coef, iou_coef, 'accuracy']
        )

        print(f"✅ 模型编译完成 - {training_mode.upper()}训练模式")

        # 5. 设置回调函数
        print("\n📋 步骤5: 设置训练回调")

        # 根据训练模式设置不同的保存名称
        model_save_name = config.get('model_save_name', 'unet_model_cloud.h5')
        if training_mode == 'incremental':
            # 为增量训练添加后缀
            name_parts = model_save_name.split('.')
            if len(name_parts) > 1:
                model_save_name = f"{name_parts[0]}_incremental.{name_parts[1]}"
            else:
                model_save_name = f"{model_save_name}_incremental"

        callbacks = [
            keras.callbacks.ModelCheckpoint(
                filepath=os.path.join(output_folder, f'best_{model_save_name}'),
                monitor='val_dice_coef',
                mode='max',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=config.get('lr_reduce_patience', 5),
                min_lr=1e-7,
                verbose=1
            ),
            keras.callbacks.EarlyStopping(
                monitor='val_dice_coef',
                mode='max',
                patience=config.get('early_stopping_patience', 10),
                restore_best_weights=True,
                verbose=1
            )
        ]

        print(f"✅ 回调函数设置完成 - 模型将保存为: best_{model_save_name}")

        # 6. 显示统计信息
        print("\n📊 训练统计信息")
        print("=" * 60)
        print(f"🚀 训练模式: {training_mode.upper()}")
        print(f"🚀 数据加载模式: 全部按需加载 (use_all_data={config.get('use_all_data', False)})")
        if training_mode == 'incremental':
            print(f"📂 预训练模型: {pretrained_model_path}")
            if backup_path:
                print(f"💾 模型备份: {backup_path}")
        print(f"📁 训练文件数: {len(train_generator.file_pairs)}")
        print(f"📁 验证文件数: {len(val_generator.file_pairs)}")
        print(f"📊 训练图像块总数: {train_generator.total_patches}")
        print(f"📊 验证图像块总数: {val_generator.total_patches}")
        print(f"📊 训练批次数: {len(train_generator)}")
        print(f"📊 验证批次数: {len(val_generator)}")
        print(f"⚙️  批次大小: {config['batch_size']}")
        print(f"⚙️  训练轮数: {config['epochs']}")
        print(f"⚙️  学习率: {learning_rate}")
        print(f"⚙️  图像块大小: {config['patch_size']}x{config['patch_size']}")
        print(f"⚙️  前景像素最小比例: {config['min_foreground_ratio']}")
        print(f"⚙️  梯度裁剪范数: {config.get('gradient_clip_norm', 1.0)}")
        print(f"⚙️  早停耐心: {config.get('early_stopping_patience', 10)}")
        if config.get('enable_mixed_precision', True):
            print("🚀 混合精度训练: 已启用")
        print("=" * 60)

        # 7. 开始训练
        print(f"\n🎯 步骤7: 开始模型训练 - {training_mode.upper()}模式")

        history = model.fit(
            train_generator,
            epochs=config['epochs'],
            validation_data=val_generator,
            callbacks=callbacks,
            verbose=1,
            use_multiprocessing=False,  # 避免多进程问题
            workers=1  # 单线程处理
        )

        print(f"\n🎉 {training_mode.upper()}训练完成！")

        # 8. 保存最终模型
        print("\n💾 步骤8: 保存模型")

        # 根据训练模式设置最终模型名称
        final_model_name = f'final_{model_save_name}'
        final_model_path = os.path.join(output_folder, final_model_name)
        model.save(final_model_path)
        print(f"✅ 最终模型已保存: {final_model_path}")

        # 9. 保存训练历史
        print("\n📊 步骤9: 保存训练历史")

        # 根据训练模式设置历史文件名称
        history_name = f'training_history_{training_mode}.json'
        history_path = os.path.join(output_folder, history_name)

        with open(history_path, 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            history_dict = {key: [float(val) for val in values] for key, values in history.history.items()}

            # 添加训练元信息
            metadata = {
                'training_mode': training_mode,
                'pretrained_model_path': pretrained_model_path if training_mode == 'incremental' else None,
                'learning_rate': learning_rate,
                'batch_size': config['batch_size'],
                'epochs_completed': len(history.history['loss']),
                'total_training_patches': train_generator.total_patches,
                'total_validation_patches': val_generator.total_patches,
                'mixed_precision_enabled': config.get('enable_mixed_precision', True),
                'training_timestamp': datetime.now().isoformat()
            }

            # 合并历史数据和元信息
            full_history = {
                'metadata': metadata,
                'history': history_dict
            }

            json.dump(full_history, f, indent=2, ensure_ascii=False)
        print(f"✅ 训练历史已保存: {history_path}")

        # 10. 显示训练结果
        print(f"\n📊 {training_mode.upper()}训练结果摘要:")
        print("=" * 60)

        final_metrics = {
            'final_loss': history.history['loss'][-1],
            'final_val_loss': history.history['val_loss'][-1],
            'final_dice_coef': history.history['dice_coef'][-1],
            'final_val_dice_coef': history.history['val_dice_coef'][-1],
            'final_iou_coef': history.history['iou_coef'][-1],
            'final_val_iou_coef': history.history['val_iou_coef'][-1],
            'best_val_dice': max(history.history['val_dice_coef']),
            'best_val_iou': max(history.history['val_iou_coef'])
        }

        for metric, value in final_metrics.items():
            print(f"{metric}: {value:.4f}")

        print(f"\n🎯 {training_mode.upper()}训练成功完成！")
        print(f"📁 模型文件保存在: {output_folder}")
        print(f"🚀 训练模式: {training_mode.upper()}")
        if training_mode == 'incremental':
            print(f"📂 基于预训练模型: {os.path.basename(pretrained_model_path) if pretrained_model_path else 'N/A'}")
        print(f"🚀 数据加载模式: 全部按需加载")
        print(f"📊 实际使用的训练图像块数: {train_generator.total_patches}")
        print(f"📊 实际使用的验证图像块数: {val_generator.total_patches}")
        print(f"⚙️  批次大小: {config['batch_size']}")
        print(f"⚙️  实际训练轮数: {len(history.history['loss'])}")
        print(f"⚙️  最终学习率: {learning_rate}")
        if config.get('enable_mixed_precision', True):
            print("🚀 使用了混合精度训练加速")

        # 11. 生成训练曲线
        print("\n📋 步骤11: 生成训练曲线")
        plot_training_history(history, output_folder, training_mode)

        return model, history

    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        traceback.print_exc()

        print("\n🔧 可能的解决方案:")
        print("1. 检查数据路径是否正确")
        print("2. 确认图像和掩膜文件存在且可读")
        print("3. 减少batch_size或max_patches_per_file")
        print("4. 检查GPU/CPU内存是否足够")
        print("5. 确认文件命名格式符合tile_x_y模式")

        return None, None

    finally:
        # 清理临时文件夹
        if temp_folder and os.path.exists(temp_folder):
            print(f"\n🧹 清理临时文件: {temp_folder}")
            try:
                shutil.rmtree(temp_folder)
                print("✅ 临时文件清理完成")
            except Exception as e:
                print(f"⚠️  临时文件清理失败: {e}")

        # 强制垃圾回收
        gc.collect()
        print("\n📊 最终内存状态:")
        monitor_memory()

# 训练结果可视化
def plot_training_history(history, output_folder, training_mode='initial'):
    """绘制训练历史"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    mode_title = '增量训练' if training_mode == 'incremental' else '初始训练'
    fig.suptitle(f'UNet {mode_title}历史 - 云端混合精度模式', fontsize=16)

    # 损失
    axes[0, 0].plot(history.history['loss'], label='训练损失', linewidth=2)
    axes[0, 0].plot(history.history['val_loss'], label='验证损失', linewidth=2)
    axes[0, 0].set_title('模型损失')
    axes[0, 0].set_xlabel('轮次')
    axes[0, 0].set_ylabel('损失')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # Dice系数
    axes[0, 1].plot(history.history['dice_coef'], label='训练Dice', linewidth=2)
    axes[0, 1].plot(history.history['val_dice_coef'], label='验证Dice', linewidth=2)
    axes[0, 1].set_title('Dice系数')
    axes[0, 1].set_xlabel('轮次')
    axes[0, 1].set_ylabel('Dice系数')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # IoU系数
    axes[1, 0].plot(history.history['iou_coef'], label='训练IoU', linewidth=2)
    axes[1, 0].plot(history.history['val_iou_coef'], label='验证IoU', linewidth=2)
    axes[1, 0].set_title('IoU系数')
    axes[1, 0].set_xlabel('轮次')
    axes[1, 0].set_ylabel('IoU系数')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 准确率
    axes[1, 1].plot(history.history['accuracy'], label='训练准确率', linewidth=2)
    axes[1, 1].plot(history.history['val_accuracy'], label='验证准确率', linewidth=2)
    axes[1, 1].set_title('准确率')
    axes[1, 1].set_xlabel('轮次')
    axes[1, 1].set_ylabel('准确率')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图表
    plot_name = f'training_curves_{training_mode}.png'
    plot_path = os.path.join(output_folder, plot_name)
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"📊 训练曲线已保存: {plot_path}")

    plt.show()

def main():
    """主函数"""
    print("🛰️ UNet卫星图像建筑物分割训练脚本")
    print("=" * 60)

    # 1. 检查依赖
    check_and_install_dependencies()

    # 2. 导入TensorFlow
    import_tensorflow()

    # 3. 检查内存
    print("\n📊 初始内存状态:")
    monitor_memory()

    # 4. 配置路径
    print("\n📁 配置数据路径...")
    config = detect_platform_and_configure_paths()

    IMAGE_FOLDER = config['image_folder']
    MASK_FOLDER = config['mask_folder']
    OUTPUT_FOLDER = config['output_folder']

    print(f"平台: {config['platform']}")
    print(f"图像文件夹: {IMAGE_FOLDER}")
    print(f"掩膜文件夹: {MASK_FOLDER}")
    print(f"输出文件夹: {OUTPUT_FOLDER}")

    # 创建输出文件夹
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)

    # 5. 显示训练配置
    print("\n⚙️ 训练配置:")
    for key, value in TRAINING_CONFIG.items():
        print(f"  {key}: {value}")

    # 6. 验证路径存在
    if not os.path.exists(IMAGE_FOLDER):
        print(f"⚠️  警告: 图像文件夹不存在: {IMAGE_FOLDER}")
        print("请修改脚本中的路径配置")
        return
    if not os.path.exists(MASK_FOLDER):
        print(f"⚠️  警告: 掩膜文件夹不存在: {MASK_FOLDER}")
        print("请修改脚本中的路径配置")
        return

    print("\n✅ 环境初始化完成")

    # 7. 开始训练
    print("\n🚀 开始训练流程...")
    model, history = train_unet_model(
        IMAGE_FOLDER,
        MASK_FOLDER,
        OUTPUT_FOLDER,
        TRAINING_CONFIG
    )

    if model is not None and history is not None:
        print("\n🎉 训练流程完成！")
        print(f"📁 输出文件保存在: {OUTPUT_FOLDER}")
        print("\n📋 输出文件包括:")
        print("  - best_unet_model.h5: 最佳模型")
        print("  - final_unet_model_full_data.h5: 最终模型")
        print("  - training_history_full_data.json: 训练历史")
        print("  - training_curves.png: 训练曲线图")
    else:
        print("\n❌ 训练失败，请检查错误信息并重试")

# 配置修改函数（供用户调用）
def update_config(**kwargs):
    """更新训练配置

    使用示例:
    update_config(batch_size=4, patch_size=384, epochs=20)
    """
    global TRAINING_CONFIG
    for key, value in kwargs.items():
        if key in TRAINING_CONFIG:
            TRAINING_CONFIG[key] = value
            print(f"✅ 更新配置: {key} = {value}")
        else:
            print(f"⚠️  未知配置项: {key}")

    print("\n📋 当前配置:")
    for key, value in TRAINING_CONFIG.items():
        print(f"  {key}: {value}")

def update_paths(image_folder=None, mask_folder=None, output_folder=None):
    """更新数据路径

    使用示例:
    update_paths(
        image_folder='/path/to/images',
        mask_folder='/path/to/masks',
        output_folder='/path/to/output'
    )
    """
    if image_folder:
        print(f"✅ 更新图像文件夹: {image_folder}")
    if mask_folder:
        print(f"✅ 更新掩膜文件夹: {mask_folder}")
    if output_folder:
        print(f"✅ 更新输出文件夹: {output_folder}")

    print("请重新运行 main() 函数以使用新路径")

def setup_initial_training(**kwargs):
    """设置初始训练配置

    使用示例:
    setup_initial_training(
        batch_size=8,
        epochs=50,
        learning_rate=1e-4,
        model_save_name='unet_model_v1.h5'
    )
    """
    global TRAINING_CONFIG

    # 设置为初始训练模式
    TRAINING_CONFIG['training_mode'] = 'initial'
    TRAINING_CONFIG['pretrained_model_path'] = None

    # 更新其他配置
    for key, value in kwargs.items():
        if key in TRAINING_CONFIG:
            TRAINING_CONFIG[key] = value
            print(f"✅ 更新配置: {key} = {value}")
        else:
            print(f"⚠️  未知配置项: {key}")

    print("\n📋 初始训练配置:")
    print(f"  训练模式: {TRAINING_CONFIG['training_mode']}")
    print(f"  批次大小: {TRAINING_CONFIG['batch_size']}")
    print(f"  训练轮数: {TRAINING_CONFIG['epochs']}")
    print(f"  学习率: {TRAINING_CONFIG['learning_rate']}")
    print(f"  模型保存名: {TRAINING_CONFIG['model_save_name']}")
    print(f"  混合精度: {TRAINING_CONFIG['enable_mixed_precision']}")

def setup_incremental_training(pretrained_model_path, **kwargs):
    """设置增量训练配置

    使用示例:
    setup_incremental_training(
        pretrained_model_path='/path/to/pretrained_model.h5',
        batch_size=8,
        epochs=25,
        incremental_learning_rate=2e-5,
        model_save_name='unet_model_v2.h5'
    )
    """
    global TRAINING_CONFIG

    # 设置为增量训练模式
    TRAINING_CONFIG['training_mode'] = 'incremental'
    TRAINING_CONFIG['pretrained_model_path'] = pretrained_model_path

    # 更新其他配置
    for key, value in kwargs.items():
        if key in TRAINING_CONFIG:
            TRAINING_CONFIG[key] = value
            print(f"✅ 更新配置: {key} = {value}")
        else:
            print(f"⚠️  未知配置项: {key}")

    print("\n📋 增量训练配置:")
    print(f"  训练模式: {TRAINING_CONFIG['training_mode']}")
    print(f"  预训练模型: {TRAINING_CONFIG['pretrained_model_path']}")
    print(f"  批次大小: {TRAINING_CONFIG['batch_size']}")
    print(f"  训练轮数: {TRAINING_CONFIG['epochs']}")
    print(f"  增量学习率: {TRAINING_CONFIG['incremental_learning_rate']}")
    print(f"  模型保存名: {TRAINING_CONFIG['model_save_name']}")
    print(f"  创建备份: {TRAINING_CONFIG['create_backup']}")
    print(f"  混合精度: {TRAINING_CONFIG['enable_mixed_precision']}")

def quick_setup_for_datasets(dataset_mode='mixed'):
    """快速设置常用的数据集训练配置

    参数:
    dataset_mode: 'single', 'mixed', 'large'
    """
    global TRAINING_CONFIG

    if dataset_mode == 'single':
        # 单个数据集配置
        TRAINING_CONFIG.update({
            'batch_size': 8,
            'epochs': 30,
            'learning_rate': 1e-4,
            'early_stopping_patience': 8
        })
        print("📋 已设置单数据集训练配置")

    elif dataset_mode == 'mixed':
        # 混合数据集配置（推荐用于您的情况）
        TRAINING_CONFIG.update({
            'batch_size': 6,
            'epochs': 25,
            'learning_rate': 5e-5,
            'incremental_learning_rate': 1e-5,
            'early_stopping_patience': 10,
            'lr_reduce_patience': 5
        })
        print("📋 已设置混合数据集训练配置（推荐）")

    elif dataset_mode == 'large':
        # 大数据集配置
        TRAINING_CONFIG.update({
            'batch_size': 4,
            'epochs': 50,
            'learning_rate': 2e-5,
            'incremental_learning_rate': 5e-6,
            'early_stopping_patience': 15
        })
        print("📋 已设置大数据集训练配置")

    print(f"  批次大小: {TRAINING_CONFIG['batch_size']}")
    print(f"  训练轮数: {TRAINING_CONFIG['epochs']}")
    print(f"  学习率: {TRAINING_CONFIG['learning_rate']}")
    print(f"  增量学习率: {TRAINING_CONFIG['incremental_learning_rate']}")
    print(f"  早停耐心: {TRAINING_CONFIG['early_stopping_patience']}")

if __name__ == "__main__":
    main()
