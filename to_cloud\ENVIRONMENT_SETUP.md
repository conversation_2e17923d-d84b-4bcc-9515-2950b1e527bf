# 🛰️ UNet卫星图像分割训练环境配置指南

本文档详细说明如何从零开始建立环境并运行UNet训练脚本。

## 📋 目录

- [系统要求](#系统要求)
- [环境创建](#环境创建)
- [依赖安装](#依赖安装)
- [数据准备](#数据准备)
- [脚本配置](#脚本配置)
- [运行训练](#运行训练)
- [故障排除](#故障排除)

## 🖥️ 系统要求

### 硬件要求
- **内存**: 最少16GB，推荐32GB+
- **GPU**: 支持CUDA的NVIDIA GPU（推荐8GB+ VRAM）
- **存储**: 至少20GB可用空间
- **CPU**: 多核处理器（推荐8核+）

### 软件要求
- **操作系统**: Windows 10/11, Linux (Ubuntu 18.04+), macOS 10.15+
- **Python**: 3.8-3.11
- **CUDA**: 11.2+ (如果使用GPU)
- **Git**: 用于代码管理

## 🐍 环境创建

### 方法1: 使用Conda（推荐）

```bash
# 1. 创建新的conda环境
conda create -n unet_training python=3.9 -y

# 2. 激活环境
conda activate unet_training

# 3. 安装基础包
conda install -c conda-forge gdal rasterio geopandas -y
```

### 方法2: 使用venv

```bash
# 1. 创建虚拟环境
python -m venv unet_training_env

# 2. 激活环境
# Windows:
unet_training_env\Scripts\activate
# Linux/macOS:
source unet_training_env/bin/activate

# 3. 升级pip
python -m pip install --upgrade pip
```

## 📦 依赖安装

### 自动安装（推荐）
脚本会自动检查和安装依赖，但您也可以手动预安装：

```bash
# 核心深度学习框架
pip install tensorflow==2.15.0

# 地理数据处理
pip install rasterio geopandas

# 图像处理和科学计算
pip install opencv-python patchify scikit-learn

# 可视化和工具
pip install matplotlib seaborn psutil

# 其他工具
pip install tqdm pyyaml
```

### GPU支持配置

```bash
# 检查CUDA版本
nvidia-smi

# 安装对应的TensorFlow GPU版本
pip install tensorflow[and-cuda]==2.15.0

# 验证GPU可用性
python -c "import tensorflow as tf; print('GPU可用:', tf.config.list_physical_devices('GPU'))"
```

### 完整依赖列表

创建 `requirements.txt` 文件：

```txt
# 深度学习框架
tensorflow==2.15.0

# 地理数据处理
rasterio>=1.3.0
geopandas>=0.12.0
GDAL>=3.4.0
shapely>=1.8.0

# 图像处理
opencv-python>=4.5.0
Pillow>=8.0.0
patchify>=0.2.3

# 科学计算
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 系统工具
psutil>=5.8.0
tqdm>=4.62.0

# 其他
pyyaml>=6.0
```

安装：
```bash
pip install -r requirements.txt
```

## 📁 数据准备

### 数据结构要求

```
project_folder/
├── data/
│   ├── converted/
│   │   ├── image/
│   │   │   ├── tile_0_0_merged.tif
│   │   │   ├── tile_0_1_merged.tif
│   │   │   └── tile_0_2_merged.tif
│   │   └── mask/
│   │       ├── tile_0_0_mask.tif
│   │       ├── tile_0_1_mask.tif
│   │       └── tile_0_2_mask.tif
│   └── raw/  # 原始GEE导出数据
│       ├── tile_0_0_img-0000000000-0000000000.tif
│       ├── tile_0_0_img-0000000000-0000013568.tif
│       └── ...
├── models/  # 输出文件夹
└── unet_training_script.py
```

### 数据格式要求

1. **图像文件**:
   - 格式: GeoTIFF (.tif)
   - 通道: RGB (3通道)
   - 数据类型: uint8 或 uint16
   - 命名: `tile_x_y_*.tif` 格式

2. **掩膜文件**:
   - 格式: GeoTIFF (.tif)
   - 通道: 单通道
   - 数据类型: uint8
   - 值: 0(背景) 和 1(前景)
   - 命名: 与对应图像文件匹配

### 数据预处理（如需要）

```python
# 数据格式转换示例
import rasterio
import numpy as np

def convert_mask_format(input_path, output_path):
    """转换掩膜格式为二值"""
    with rasterio.open(input_path) as src:
        mask = src.read(1)
        profile = src.profile
    
    # 转换为二值
    binary_mask = (mask > 0).astype(np.uint8)
    
    # 保存
    with rasterio.open(output_path, 'w', **profile) as dst:
        dst.write(binary_mask, 1)
```

## ⚙️ 脚本配置

### 基本配置

编辑 `unet_training_script.py` 中的路径：

```python
# 方法1: 直接修改脚本中的路径检测函数
def detect_platform_and_configure_paths():
    return {
        'platform': 'local',
        'image_folder': '/your/path/to/converted/image',  # 修改这里
        'mask_folder': '/your/path/to/converted/mask',    # 修改这里
        'output_folder': '/your/path/to/output'           # 修改这里
    }

# 方法2: 使用脚本提供的配置函数
from unet_training_script import update_paths, update_config

# 更新路径
update_paths(
    image_folder='/your/path/to/converted/image',
    mask_folder='/your/path/to/converted/mask',
    output_folder='/your/path/to/output'
)

# 更新训练配置
update_config(
    batch_size=8,
    epochs=30,
    patch_size=256,
    learning_rate=1e-4
)
```

### 训练参数调优

根据您的硬件配置调整参数：

```python
# 内存充足的配置
TRAINING_CONFIG = {
    'batch_size': 16,
    'epochs': 50,
    'patch_size': 512,
    'enable_mixed_precision': True,
}

# 内存受限的配置
TRAINING_CONFIG = {
    'batch_size': 4,
    'epochs': 30,
    'patch_size': 256,
    'enable_mixed_precision': False,
}

# GPU内存不足的配置
TRAINING_CONFIG = {
    'batch_size': 2,
    'epochs': 25,
    'patch_size': 224,
    'enable_mixed_precision': True,
}
```

## 🚀 运行训练

### 基本运行

```bash
# 激活环境
conda activate unet_training  # 或 source unet_training_env/bin/activate

# 运行训练脚本
python unet_training_script.py
```

### 高级运行选项

```python
# 在Python中运行
from unet_training_script import main, update_config, update_paths

# 1. 更新配置
update_config(batch_size=8, epochs=20, patch_size=384)

# 2. 更新路径（如果需要）
update_paths(
    image_folder='./data/converted/image',
    mask_folder='./data/converted/mask',
    output_folder='./models'
)

# 3. 运行训练
main()
```

### 后台运行（Linux/macOS）

```bash
# 使用nohup后台运行
nohup python unet_training_script.py > training.log 2>&1 &

# 查看日志
tail -f training.log

# 查看进程
ps aux | grep python
```

## 📊 输出文件说明

训练完成后，输出文件夹将包含：

```
models/
├── best_unet_model.h5              # 验证集上最佳的模型
├── final_unet_model_full_data.h5   # 最终训练完成的模型
├── training_history_full_data.json # 完整的训练历史数据
└── training_curves.png             # 训练曲线图
```

### 模型使用示例

```python
import tensorflow as tf
import numpy as np
from PIL import Image

# 加载模型
model = tf.keras.models.load_model('models/best_unet_model.h5', 
                                   custom_objects={
                                       'dice_coef': dice_coef,
                                       'iou_coef': iou_coef,
                                       'combined_loss': combined_loss
                                   })

# 预测示例
def predict_image(image_path, model):
    # 加载和预处理图像
    image = Image.open(image_path)
    image = np.array(image) / 255.0
    image = np.expand_dims(image, axis=0)
    
    # 预测
    prediction = model.predict(image)
    
    # 后处理
    mask = (prediction[0, :, :, 1] > 0.5).astype(np.uint8)
    
    return mask
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 内存不足错误
```
ResourceExhaustedError: OOM when allocating tensor
```

**解决方案**:
```python
# 减少批次大小
update_config(batch_size=2)

# 减少图像块大小
update_config(patch_size=224)

# 启用混合精度
update_config(enable_mixed_precision=True)
```

#### 2. CUDA/GPU相关错误
```
Could not load dynamic library 'libcudart.so.11.0'
```

**解决方案**:
```bash
# 重新安装CUDA兼容的TensorFlow
pip uninstall tensorflow
pip install tensorflow[and-cuda]==2.15.0

# 或使用CPU版本
pip install tensorflow-cpu==2.15.0
```

#### 3. 数据加载错误
```
FileNotFoundError: 图像文件夹不存在
```

**解决方案**:
```python
# 检查路径是否正确
import os
print("当前工作目录:", os.getcwd())
print("图像文件夹存在:", os.path.exists('path/to/images'))

# 使用绝对路径
update_paths(
    image_folder=os.path.abspath('path/to/images'),
    mask_folder=os.path.abspath('path/to/masks')
)
```

#### 4. 依赖包冲突
```
ImportError: cannot import name 'xxx'
```

**解决方案**:
```bash
# 重新创建环境
conda deactivate
conda remove -n unet_training --all
conda create -n unet_training python=3.9 -y
conda activate unet_training

# 重新安装依赖
pip install -r requirements.txt
```

### 性能优化建议

1. **数据预处理优化**:
   - 将数据转换为TFRecord格式
   - 使用SSD存储训练数据
   - 预先合并分块图像

2. **训练优化**:
   - 使用混合精度训练
   - 调整批次大小以充分利用GPU
   - 使用学习率调度

3. **内存优化**:
   - 监控内存使用情况
   - 及时清理临时文件
   - 使用数据生成器而非一次性加载

## 📞 技术支持

如果遇到问题，请：

1. 检查错误日志
2. 确认环境配置正确
3. 验证数据格式和路径
4. 尝试减少资源使用（批次大小、图像大小等）

---

**最后更新**: 2025-01-26  
**版本**: 1.0  
**兼容性**: Python 3.8-3.11, TensorFlow 2.15.0
