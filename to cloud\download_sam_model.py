import requests
from pathlib import Path
import sys

def download_file(url, filename):
    """下载文件并显示进度"""
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    block_size = 1024  # 1 KB
    downloaded = 0
    
    print(f"开始下载模型文件: {filename}")
    print(f"文件大小: {total_size / (1024*1024):.1f} MB")
    
    with open(filename, 'wb') as f:
        for data in response.iter_content(block_size):
            downloaded += len(data)
            f.write(data)
            
            # 显示下载进度
            done = int(50 * downloaded / total_size)
            sys.stdout.write('\r[{}{}] {:.1f}%'.format(
                '=' * done,
                ' ' * (50-done),
                downloaded * 100 / total_size
            ))
            sys.stdout.flush()
    
    print("\n下载完成!")

def main():
    # SAM ViT-H 模型
    url = "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    filename = "sam_vit_h.pth"
    
    if Path(filename).exists():
        print(f"模型文件 {filename} 已存在，跳过下载")
        return
    
    try:
        download_file(url, filename)
    except Exception as e:
        print(f"下载失败: {e}")

if __name__ == "__main__":
    main()
