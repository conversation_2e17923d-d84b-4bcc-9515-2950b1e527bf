from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QProgressBar, 
                             QLabel, QPushButton, QTextEdit, QHBoxLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette
from datetime import datetime

class InferenceProgressDialog(QDialog):
    """推理进度对话框"""
    
    # 定义信号
    progress_updated = pyqtSignal(str, int)  # 进度更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("模型推理进度")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 添加状态标签
        self.status_label = QLabel("准备开始...")
        self.status_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 添加详细日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 设置终端风格
        self.log_text.setFont(QFont("Consolas", 11))
        palette = self.log_text.palette()
        palette.setColor(QPalette.Base, QColor("#2B2B2B"))
        palette.setColor(QPalette.Text, QColor("#A9B7C6"))
        self.log_text.setPalette(palette)
        layout.addWidget(self.log_text)
        
        # 底部按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 添加清除日志按钮
        self.clear_button = QPushButton("清除日志")
        self.clear_button.clicked.connect(self.clear_log)
        self.clear_button.setFixedWidth(100)
        button_layout.addWidget(self.clear_button)
        
        # 添加取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setFixedWidth(100)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.progress_updated.connect(self.update_progress)
        
    def update_progress(self, message, progress=None):
        """更新进度信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        self.status_label.setText(message)
        if progress is not None:
            self.progress_bar.setValue(progress)
            if progress >= 100:
                self.status_label.setText("推理完成")
                self.progress_bar.setValue(100)
                # 禁用取消按钮，因为已经完成
                self.cancel_button.setEnabled(False)
            
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        
    def clear_log(self):
        """清除日志内容"""
        self.log_text.clear()
