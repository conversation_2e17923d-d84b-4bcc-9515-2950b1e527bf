import os
import cv2
import numpy as np
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import tensorflow as tf
import keras
from keras.utils import to_categorical, Sequence
import segmentation_models as sm
from keras.optimizers import Adam
from keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard
from datetime import datetime
import json

# 设置混合精度
tf.keras.mixed_precision.set_global_policy('mixed_float16')

class DataGenerator(keras.utils.Sequence):
    def __init__(self, x_set, y_set, batch_size):
        self.x, self.y = x_set, y_set
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.x) / float(self.batch_size)))

    def __getitem__(self, idx):
        batch_x = self.x[idx * self.batch_size:(idx + 1) * self.batch_size]
        batch_y = self.y[idx * self.batch_size:(idx + 1) * self.batch_size]
        return batch_x, batch_y

def create_unet_model(input_shape=(256, 256, 3), n_classes=2, init_features=32):
    """创建UNet模型"""
    inputs = keras.layers.Input(input_shape)

    # Encoder
    conv1 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(inputs)
    conv1 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(conv1)
    pool1 = keras.layers.MaxPooling2D(pool_size=(2, 2))(conv1)

    conv2 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(pool1)
    conv2 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(conv2)
    pool2 = keras.layers.MaxPooling2D(pool_size=(2, 2))(conv2)

    conv3 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(pool2)
    conv3 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(conv3)
    pool3 = keras.layers.MaxPooling2D(pool_size=(2, 2))(conv3)

    # Bridge
    conv4 = keras.layers.Conv2D(init_features*8, 3, activation='relu', padding='same')(pool3)
    conv4 = keras.layers.Conv2D(init_features*8, 3, activation='relu', padding='same')(conv4)

    # Decoder
    up5 = keras.layers.Conv2DTranspose(init_features*4, 2, strides=(2, 2), padding='same')(conv4)
    up5 = keras.layers.concatenate([up5, conv3])
    conv5 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(up5)
    conv5 = keras.layers.Conv2D(init_features*4, 3, activation='relu', padding='same')(conv5)

    up6 = keras.layers.Conv2DTranspose(init_features*2, 2, strides=(2, 2), padding='same')(conv5)
    up6 = keras.layers.concatenate([up6, conv2])
    conv6 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(up6)
    conv6 = keras.layers.Conv2D(init_features*2, 3, activation='relu', padding='same')(conv6)

    up7 = keras.layers.Conv2DTranspose(init_features, 2, strides=(2, 2), padding='same')(conv6)
    up7 = keras.layers.concatenate([up7, conv1])
    conv7 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(up7)
    conv7 = keras.layers.Conv2D(init_features, 3, activation='relu', padding='same')(conv7)

    outputs = keras.layers.Conv2D(n_classes, 1, activation='softmax')(conv7)

    model = keras.models.Model(inputs=[inputs], outputs=[outputs])
    return model

def train_unet(config):
    """训练UNet模型"""
    # 获取训练参数
    dataset_path = config['dataset_path']
    model_args = config['model']['args']
    trainer_args = config['trainer']

    # 设置训练参数
    batch_size = trainer_args['batch_size']
    epochs = trainer_args['epochs']
    learning_rate = trainer_args['learning_rate']
    slice_size = trainer_args['slice_size']
    overlap = trainer_args['overlap']
    save_dir = trainer_args['save_dir']

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 读取数据
    with rasterio.open(os.path.join(dataset_path, 'image', 'test.tif')) as src:
        image = src.read().transpose((1, 2, 0))

    gdf = gpd.read_file(os.path.join(dataset_path, 'shape', 'Buildings.shp'))

    # 确保图像是3波段
    if image.shape[2] > 3:
        image = image[:, :, :3]

    # 创建掩码
    mask = rasterize(
        [(shape, 1) for shape in gdf.geometry],
        out_shape=image.shape[:2],
        transform=src.transform,
        fill=0,
        dtype='uint8',
        all_touched=True  # 🔑 添加all_touched参数
    )

    # 数据预处理
    minmaxscaler = MinMaxScaler()
    image = minmaxscaler.fit_transform(image.reshape(-1, image.shape[-1])).reshape(image.shape)
    mask = np.expand_dims(mask, axis=-1)

    # 创建图像块
    def create_patches(img, mask, size=slice_size, stride=slice_size-overlap):
        patches_img = patchify(img, (size, size, img.shape[-1]), step=stride)
        patches_mask = patchify(mask, (size, size, 1), step=stride)

        patches_img = patches_img.reshape(-1, size, size, img.shape[-1])
        patches_mask = patches_mask.reshape(-1, size, size, 1)

        return patches_img, patches_mask

    image_patches, mask_patches = create_patches(image, mask)
    print(f"创建了 {len(image_patches)} 个图像块")

    # 转换标签为分类格式
    mask_categorical = to_categorical(mask_patches, num_classes=2)

    # 划分训练集和验证集
    X_train, X_val, y_train, y_val = train_test_split(
        image_patches, mask_categorical,
        test_size=0.2, random_state=42
    )

    # 创建模型
    model = create_unet_model(
        input_shape=(slice_size, slice_size, 3),
        n_classes=2,
        init_features=model_args.get('init_features', 32)
    )

    # 配置优化器和损失函数
    optimizer = Adam(learning_rate=learning_rate)
    loss = sm.losses.CategoricalCELoss()
    metrics = [
        sm.metrics.IOUScore(threshold=0.5),
        sm.metrics.FScore(threshold=0.5),
        'accuracy'
    ]

    model.compile(optimizer, loss, metrics)

    # 创建回调函数
    callbacks = [
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-6
        ),
        ModelCheckpoint(
            os.path.join(save_dir, 'best_model.h5'),
            monitor='val_iou_score',
            mode='max',
            save_best_only=True
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True
        ),
        TensorBoard(
            log_dir=os.path.join(save_dir, 'logs'),
            histogram_freq=1
        )
    ]

    # 创建数据生成器
    train_gen = DataGenerator(X_train, y_train, batch_size)
    val_gen = DataGenerator(X_val, y_val, batch_size)

    # 训练模型
    history = model.fit(
        train_gen,
        validation_data=val_gen,
        epochs=epochs,
        callbacks=callbacks,
        verbose=1
    )

    # 保存训练历史
    history_path = os.path.join(save_dir, 'training_history.json')
    with open(history_path, 'w') as f:
        history_dict = {key: [float(x) for x in value] for key, value in history.history.items()}
        json.dump(history_dict, f, indent=4)

    # 绘制训练曲线
    plt.figure(figsize=(12, 4))

    # 损失曲线
    plt.subplot(121)
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    # IoU曲线
    plt.subplot(122)
    plt.plot(history.history['iou_score'], label='Training IoU')
    plt.plot(history.history['val_iou_score'], label='Validation IoU')
    plt.title('Model IoU')
    plt.xlabel('Epoch')
    plt.ylabel('IoU')
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_curves.png'))
    plt.close()

    # 进行预测并可视化
    test_idx = np.random.randint(0, len(X_val))
    test_image = X_val[test_idx:test_idx+1]
    true_mask = np.argmax(y_val[test_idx], axis=-1)
    pred_mask = np.argmax(model.predict(test_image), axis=-1)[0]

    plt.figure(figsize=(12, 4))
    plt.subplot(131)
    plt.title('Input Image')
    plt.imshow(test_image[0])
    plt.subplot(132)
    plt.title('True Mask')
    plt.imshow(true_mask, cmap='gray')
    plt.subplot(133)
    plt.title('Predicted Mask')
    plt.imshow(pred_mask, cmap='gray')
    plt.savefig(os.path.join(save_dir, 'prediction_example.png'))
    plt.close()

    return True

# 模型训练配置
CONFIG = {
    'dataset_path': './dataset',  # 数据集路径
    'model': {
        'args': {
            'input_shape': (256, 256, 3),
            'n_classes': 2,
            'init_features': 32
        }
    },
    'trainer': {
        'batch_size': 8,
        'epochs': 100,
        'learning_rate': 0.001,
        'slice_size': 256,
        'overlap': 0.2,
        'save_dir': './models'  # 模型保存路径
    }
}

if __name__ == "__main__":
    # 使用内置配置进行训练
    train_unet(CONFIG)
