from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QSpinBox, QComboBox, QPushButton, QGroupBox,
                            QFormLayout, QDoubleSpinBox, QLineEdit, QFileDialog,
                            QMessageBox, QProgressDialog, QCheckBox, QApplication)
from PyQt5.QtCore import Qt, QTimer
import os
import sys
import json
import subprocess
import traceback
from pathlib import Path
import time
import uuid
import functools

class ModelTrainingDialog(QDialog):
    def __init__(self, parent=None, model_save_path=None):
        super().__init__(parent)
        self.model_save_path = model_save_path
        self.supported_models = ["UNet", "DeepLabV3+", "ResNet", "FCN", "LoGCAN"]

        # 检测可用设备
        self.available_devices = self.detect_available_devices()

        # 读取项目配置文件
        self.project_config_path = "D:/Satellite_Image_Auto_Offset/Autooffset_Test/config/project_config.json"
        self.project_config = self.load_project_config()

        # 设置默认配置 - 针对不同模型优化调整
        self.default_config = {
            "batch_size": 2,  # 减少默认批次大小以避免内存问题
            "learning_rate": 1e-3,  # 提高默认学习率，特别适合LoGCAN
            "epochs": 30,  # 减少默认训练轮数，避免过拟合
            "slice_size": 256,  # 增加默认切片大小，提高效果
            "overlap": 32
        }

        # 尝试加载上次的配置
        self.last_config = self.load_last_config()
        if self.last_config:
            # 更新默认配置
            if 'trainer' in self.last_config:
                trainer_args = self.last_config['trainer']
                self.default_config.update({
                    'batch_size': trainer_args.get('batch_size', self.default_config['batch_size']),
                    'learning_rate': trainer_args.get('learning_rate', self.default_config['learning_rate']),
                    'epochs': trainer_args.get('epochs', self.default_config['epochs']),
                    'slice_size': trainer_args.get('slice_size', self.default_config['slice_size']),
                    'overlap': trainer_args.get('overlap', self.default_config['overlap'])
                })

        # 模型特定参数
        self.model_specific_params = {
            "UNet": {
                "init_features": 16,  # 优化后的特征数
                "dropout": 0.1,
                "model_type": "memory_optimized",  # 默认使用内存优化模型
                "dropout_rate": 0.1,
                "data_mode": "image+shape",  # 数据输入模式
                "image_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",  # 使用正确的图像
                "shape_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings_rafha.shp",  # 使用正确的标注
                "image_folder": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",  # 图像文件夹
                "mask_folder": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask/",  # 掩膜文件夹
                "use_all_data": True,  # 使用全部数据
                "use_data_augmentation": True,  # 启用数据增强
                "min_foreground_ratio": 0.005,  # 前景像素最小比例
                "max_patches_per_file": None,  # 每个文件最大图像块数（None表示不限制）
                "max_total_patches": None  # 总最大图像块数（None表示不限制）
            },
            "DeepLabV3+": {
                "output_stride": 16,
                "backbone": "resnet50",
                "data_mode": "image+mask",  # 数据输入模式
                "image_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",
                "shape_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings.shp",
                "image_folder": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",  # 图像文件夹
                "mask_folder": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask/",  # 掩膜文件夹
                "multiclass_mode": False,
                "num_classes": 2,
                "class_names": "背景,建筑物",
                "shape_attribute_field": "class_type",
                "use_simple_preprocessing": True,  # 使用简单预处理
                "use_data_augmentation": True,  # 启用数据增强
                "min_foreground_ratio": 0.005  # 前景像素最小比例
            },
            "ResNet": {
                "backbone": "resnet50",
                "image_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",
                "shape_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings.shp"
            },
            "FCN": {
                "backbone": "vgg16",
                "image_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",
                "shape_path": "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings.shp"
            },
            "LoGCAN": {
                "pretrained": True,
                "transform_channel": 128,
                "data_mode": "image+shape",  # 默认使用image+shape模式
                "image_path": "satellite_20250924_144816.tif",
                "shape_path": "arar_multiclass/arar_multiclass.shp",
                "image_folder": "d:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image",
                "mask_folder": "d:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask",
                "multiclass_mode": True,  # 默认启用多分类
                "num_classes": 4,  # 默认4个类别
                "class_names": "背景,建筑物,道路,植被",  # 默认类别名称
                "shape_attribute_field": "ClassValue",  # 默认属性字段
                "use_data_augmentation": True,
                "min_foreground_ratio": 0.005,
                "class_mapping": "建筑物:1,道路:2,植被:3",  # 默认类别映射
                "use_class_weights": True
            }
        }

        # 如果存在上次的配置，更新模型特定参数
        if self.last_config and 'model' in self.last_config:
            # 兼容新旧配置格式
            if 'name' in self.last_config['model']:
                # 旧格式：model.name 和 model.args
                model_name = self.last_config['model']['name']
                model_args = self.last_config['model'].get('args', {})
            else:
                # 新格式：直接在model下的参数，需要推断模型名称
                # 从配置文件名或其他方式推断模型名称
                model_name = None
                model_args = {}

            if model_name and model_name in self.model_specific_params:
                self.model_specific_params[model_name].update(model_args)

        self.setup_ui()

        # 如果有上次的配置，设置上次使用的模型
        if self.last_config and 'model' in self.last_config:
            # 兼容新旧配置格式
            if 'name' in self.last_config['model']:
                last_model = self.last_config['model']['name']
                index = self.model_combo.findText(last_model)
                if index >= 0:
                    self.model_combo.setCurrentIndex(index)

    def detect_available_devices(self):
        """检测可用的训练设备"""
        devices = ["CPU"]  # CPU 总是可用的

        try:
            import torch
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    gpu_name = torch.cuda.get_device_name(i)
                    devices.append(f"GPU {i}: {gpu_name}")
                # print(f"检测到 {len(devices)-1} 个 GPU 设备")
                pass
            else:
                # print("未检测到可用的 GPU，将使用 CPU 进行训练")
                pass
        except ImportError:
            # print("未安装 PyTorch，将使用 CPU 进行训练")
            pass
        except Exception as e:
            # print(f"检测设备时出错: {str(e)}")
            # print("将使用 CPU 进行训练")
            pass

        return devices

    def load_project_config(self):
        try:
            with open(self.project_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取项目配置文件失败：{str(e)}")
            return {}

    def load_last_config(self):
        """加载上次的配置文件"""
        try:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_dir = os.path.join(project_root, "config")

            # 查找最新的配置文件，优先选择改进版本
            config_files = []
            for model in self.supported_models:
                # 处理特殊命名和优先选择改进版本
                if model == "DeepLabV3+":
                    config_name = "deeplabv3+_config.json"
                elif model == "UNet":
                    config_name = "unet_config.json"
                else:
                    config_name = f"{model.lower()}_config.json"
                config_path = os.path.join(config_dir, config_name)
                if os.path.exists(config_path):
                    config_files.append((config_path, os.path.getmtime(config_path)))

            if config_files:
                # 按修改时间排序，获取最新的配置文件
                latest_config = max(config_files, key=lambda x: x[1])[0]
                with open(latest_config, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载上次配置失败：{str(e)}")
        return None

    def setup_ui(self):
        self.setWindowTitle("模型训练设置")
        self.setMinimumWidth(500)

        layout = QVBoxLayout(self)

        # 设备选择
        device_group = QGroupBox("设备设置")
        device_layout = QVBoxLayout()

        device_label = QLabel("选择训练设备:")
        self.device_combo = QComboBox()
        self.device_combo.addItems(self.available_devices)

        # 如果有 GPU 可用，默认选择第一个 GPU
        if len(self.available_devices) > 1:
            self.device_combo.setCurrentIndex(1)
        else:
            # 如果没有 GPU，禁用设备选择
            self.device_combo.setEnabled(False)
            device_label.setToolTip("当前系统仅支持 CPU 训练")

        device_layout.addWidget(device_label)
        device_layout.addWidget(self.device_combo)
        device_group.setLayout(device_layout)
        layout.addWidget(device_group)

        # 模型选择
        model_group = QGroupBox("模型设置")
        model_layout = QVBoxLayout()

        model_label = QLabel("选择模型:")
        self.model_combo = QComboBox()
        self.model_combo.addItems(self.supported_models)
        self.model_combo.currentIndexChanged.connect(self.on_model_changed)

        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_combo)
        model_group.setLayout(model_layout)
        layout.addWidget(model_group)

        # 创建模型特定参数组
        self.model_params_group = QGroupBox("模型特定参数")
        self.model_params_layout = None  # 初始化为None，由on_model_changed创建
        layout.addWidget(self.model_params_group)

        # 通用训练参数设置(只有非LoGCAN模型显示)
        self.param_group = QGroupBox("训练参数")
        param_layout = QFormLayout()

        # 图像切片大小
        self.slice_size = QSpinBox()
        self.slice_size.setRange(128, 1024)
        self.slice_size.setValue(self.default_config["slice_size"])
        self.slice_size.setSingleStep(128)
        param_layout.addRow("切片大小:", self.slice_size)

        # 重叠大小
        self.overlap = QSpinBox()
        self.overlap.setRange(0, 128)
        self.overlap.setValue(self.default_config["overlap"])
        self.overlap.setSingleStep(16)
        param_layout.addRow("重叠大小:", self.overlap)

        # Batch大小 - 添加内存优化提示
        batch_layout = QHBoxLayout()
        self.batch_size = QSpinBox()
        self.batch_size.setRange(1, 64)
        self.batch_size.setValue(self.default_config["batch_size"])
        self.batch_size.valueChanged.connect(self.on_batch_size_changed)

        # 添加内存优化建议按钮
        memory_tip_btn = QPushButton("💡")
        memory_tip_btn.setMaximumWidth(30)
        memory_tip_btn.setToolTip("点击查看内存优化建议")
        memory_tip_btn.clicked.connect(self.show_memory_optimization_tips)

        batch_layout.addWidget(self.batch_size)
        batch_layout.addWidget(memory_tip_btn)
        param_layout.addRow("Batch大小:", batch_layout)

        # 训练轮数
        self.epochs = QSpinBox()
        self.epochs.setRange(1, 1000)
        self.epochs.setValue(self.default_config["epochs"])
        param_layout.addRow("训练轮数:", self.epochs)

        # 学习率 - 添加模型特定建议
        lr_layout = QHBoxLayout()
        self.learning_rate = QDoubleSpinBox()
        self.learning_rate.setRange(0.00001, 1.0)
        self.learning_rate.setValue(self.default_config["learning_rate"])
        self.learning_rate.setDecimals(5)

        # 添加学习率建议按钮
        lr_tip_btn = QPushButton("💡")
        lr_tip_btn.setMaximumWidth(30)
        lr_tip_btn.setToolTip("点击查看不同模型的学习率建议")
        lr_tip_btn.clicked.connect(self.show_learning_rate_tips)

        lr_layout.addWidget(self.learning_rate)
        lr_layout.addWidget(lr_tip_btn)
        param_layout.addRow("学习率:", lr_layout)

        self.param_group.setLayout(param_layout)
        layout.addWidget(self.param_group)

        # 模型保存路径
        save_group = QGroupBox("保存设置")
        save_layout = QFormLayout()

        # 保存路径
        save_path_layout = QHBoxLayout()
        self.save_path_edit = QLineEdit()
        # 初始路径将在on_model_changed中设置
        # 确保model_save_path是字符串类型
        if isinstance(self.model_save_path, str):
            self.save_path_edit.setText(self.model_save_path)
        elif isinstance(self.model_save_path, dict) and 'model_path' in self.model_save_path:
            # 如果传入的是项目配置字典，使用model_path
            self.save_path_edit.setText(self.model_save_path.get('model_path', ''))
        else:
            self.save_path_edit.setText("")
        self.save_path_edit.setReadOnly(False)
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_save_file)
        save_path_layout.addWidget(self.save_path_edit)
        save_path_layout.addWidget(browse_btn)
        save_layout.addRow("保存路径:", save_path_layout)

        # 预训练模型路径（继续训练功能）
        pretrained_layout = QHBoxLayout()
        self.pretrained_path_edit = QLineEdit()
        self.pretrained_path_edit.setPlaceholderText("可选：选择已有模型继续训练（仅UNet支持）")

        browse_pretrained_btn = QPushButton("浏览...")
        browse_pretrained_btn.clicked.connect(self.browse_pretrained_path)
        clear_pretrained_btn = QPushButton("清除")
        clear_pretrained_btn.clicked.connect(lambda: self.pretrained_path_edit.clear())

        pretrained_layout.addWidget(self.pretrained_path_edit)
        pretrained_layout.addWidget(browse_pretrained_btn)
        pretrained_layout.addWidget(clear_pretrained_btn)
        save_layout.addRow("预训练模型:", pretrained_layout)

        save_group.setLayout(save_layout)
        layout.addWidget(save_group)

        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("开始训练")
        cancel_button = QPushButton("取消")

        ok_button.clicked.connect(self.validate_and_accept)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # 初始化模型特定参数
        self.on_model_changed()

    def get_settings(self):
        """获取当前训练设置"""
        current_model = self.model_combo.currentText()

        # 获取选择的设备
        device = self.device_combo.currentText()
        use_gpu = device.startswith("GPU")
        gpu_id = int(device.split(":")[0].split()[-1]) if use_gpu else -1

        # 获取模型特定参数
        model_params = {}
        for param_name, widget in self.current_params.items():
            if isinstance(widget, (QCheckBox, QSpinBox, QDoubleSpinBox)):
                model_params[param_name] = widget.value() if isinstance(widget, (QSpinBox, QDoubleSpinBox)) else widget.isChecked()
            elif isinstance(widget, QComboBox):
                if param_name == "model_type" and current_model == "UNet":
                    # UNet固定使用完整模型
                    model_params[param_name] = "complete"
                else:
                    model_params[param_name] = widget.currentText()
            elif isinstance(widget, QLineEdit):
                model_params[param_name] = widget.text()

        settings = {
            'model': current_model,
            'config': {
                'model': {
                    'name': current_model,
                    'args': model_params
                }
            },
            'epochs': self.epochs.value(),
            'batch_size': self.batch_size.value(),
            'learning_rate': self.learning_rate.value(),
            'slice_size': self.slice_size.value(),
            'overlap': self.overlap.value(),
            'save_path': self.save_path_edit.text(),
            'pretrained_path': self.pretrained_path_edit.text() if hasattr(self, 'pretrained_path_edit') else None,
            'output_mode': '标准模式',
            'device': {
                'use_gpu': use_gpu,
                'gpu_id': gpu_id
            }
        }

        return settings

    def save_logcan_config(self):
        """保存LogCAN配置到文件"""
        try:
            current_model = self.model_combo.currentText()
            if current_model != "LoGCAN":
                return

            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(project_root, "config", "logcan_config.json")

            # 获取当前所有LogCAN参数
            model_params = {}
            for param_name, widget in self.current_params.items():
                if isinstance(widget, (QCheckBox, QSpinBox, QDoubleSpinBox)):
                    model_params[param_name] = widget.value() if isinstance(widget, (QSpinBox, QDoubleSpinBox)) else widget.isChecked()
                elif isinstance(widget, QComboBox):
                    model_params[param_name] = widget.currentText()
                elif isinstance(widget, QLineEdit):
                    model_params[param_name] = widget.text()

            # 解析类别映射
            parsed_mapping = self._parse_class_mapping(model_params.get("class_mapping", ""))

            # 构建配置
            config = {
                "model": {
                    "num_classes": model_params.get("num_classes", 4),
                    "transform_channel": model_params.get("transform_channel", 128),
                    "backbone": "resnet50",
                    "pretrained": model_params.get("pretrained", "True") == "True" if isinstance(model_params.get("pretrained"), str) else model_params.get("pretrained", True),
                    "class_field": model_params.get("shape_attribute_field", "ClassValue"),
                    "class_names": model_params.get("class_names", "背景,建筑物,道路,植被").split(",") if isinstance(model_params.get("class_names"), str) else model_params.get("class_names", ["背景", "建筑物", "道路", "植被"]),
                    "multiclass_mode": model_params.get("multiclass_mode", True),
                    "class_mapping": parsed_mapping,
                    "use_class_weights": model_params.get("use_class_weights", True)
                },
                "training": {
                    "epochs": self.epochs.value(),
                    "batch_size": self.batch_size.value(),
                    "learning_rate": self.learning_rate.value(),
                    "device": "cuda" if self.device_combo.currentText().startswith("GPU") else "cpu"
                },
                "data": {
                    "data_mode": model_params.get("data_mode", "image+shape"),
                    "slice_size": self.slice_size.value(),
                    "overlap": self.overlap.value(),
                    "use_augmentation": model_params.get("use_data_augmentation", True),
                    "min_foreground_ratio": model_params.get("min_foreground_ratio", 0.005)
                },
                "paths": {
                    "image_path": model_params.get("image_path", ""),
                    "shape_path": model_params.get("shape_path", ""),
                    "image_folder": model_params.get("image_folder", ""),
                    "mask_folder": model_params.get("mask_folder", ""),
                    "save_dir": self.save_path_edit.text()
                }
            }

            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)

            print(f"LogCAN配置已保存到: {config_path}")

        except Exception as e:
            print(f"保存LogCAN配置失败: {e}")

    def accept(self):
        """重写accept方法，在关闭前保存配置"""
        self.save_logcan_config()
        super().accept()

    def reject(self):
        """重写reject方法，在取消前也保存配置"""
        self.save_logcan_config()
        super().reject()

    def validate_and_accept(self):
        """验证参数并开始训练"""
        if not self.validate_parameters():
            return

        current_model = self.model_combo.currentText()

        # 获取模型特定参数
        model_params = {}
        for param_name, widget in self.current_params.items():
            if isinstance(widget, (QCheckBox, QSpinBox, QDoubleSpinBox)):
                model_params[param_name] = widget.value() if isinstance(widget, (QSpinBox, QDoubleSpinBox)) else widget.isChecked()
            elif isinstance(widget, QComboBox):
                if param_name == "model_type" and current_model == "UNet":
                    # UNet固定使用完整模型
                    model_params[param_name] = "complete"
                else:
                    model_params[param_name] = widget.currentText()
            elif isinstance(widget, QLineEdit):
                model_params[param_name] = widget.text()

        # 构建基本配置
        if current_model == "LoGCAN":
            # 检查数据模式
            data_mode = model_params.get('data_mode', 'image+shape')

            if data_mode == 'image+shape':
                # image+shape模式：使用单个文件
                image_path = self.current_params["image_path"].text()
                shape_path = self.current_params["shape_path"].text()

                if not image_path:
                    QMessageBox.critical(self, "错误", "请选择图像数据文件")
                    return
                if not shape_path:
                    QMessageBox.critical(self, "错误", "请选择形状数据文件")
                    return
            else:
                # image+mask模式：跳过这个特殊处理，使用通用逻辑
                pass

            if data_mode == 'image+shape':
                # 确保保存路径不为空
                save_path = self.save_path_edit.text()
                if not save_path:
                    save_path = "D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models"
                    print(f"[警告] 保存路径为空，使用默认值: {save_path}")

                # 确保保存目录存在
                save_dir = os.path.dirname(save_path) if os.path.isfile(save_path) else save_path
                os.makedirs(save_dir, exist_ok=True)

                # 获取GUI中的所有LogCAN参数
                gui_params = {}
                for param_name, widget in self.current_params.items():
                    if hasattr(widget, 'value'):
                        gui_params[param_name] = widget.value()
                    elif hasattr(widget, 'text'):
                        gui_params[param_name] = widget.text()
                    elif hasattr(widget, 'currentText'):
                        value = widget.currentText()
                        # 处理pretrained参数的字符串到布尔值转换
                        if param_name == "pretrained":
                            gui_params[param_name] = value == "True"
                        else:
                            gui_params[param_name] = value
                    elif hasattr(widget, 'isChecked'):
                        gui_params[param_name] = widget.isChecked()

                # 合并模型参数和GUI参数
                merged_params = {**model_params, **gui_params}

                # 生成新LogCAN训练代码所需的配置格式
                device_text = self.device_combo.currentText()
                device = "cuda" if device_text.startswith("GPU") else "cpu"

                config = {
                    "model": {
                        "num_classes": merged_params.get("num_classes", 2),
                        "transform_channel": merged_params.get("transform_channel", 128),
                        "backbone": "resnet50",
                        "pretrained": merged_params.get("pretrained", True)
                    },
                    "training": {
                        "epochs": self.epochs.value(),
                        "batch_size": self.batch_size.value(),
                        "learning_rate": self.learning_rate.value(),
                        "weight_decay": 1e-4,
                        "device": device,
                        "save_interval": 5,
                        "val_interval": 1,
                        "gradient_clip_norm": 1.0,
                        "loss": {
                            "use_focal": False,
                            "use_dice": True,
                            "ce_weight": 1.0,
                            "focal_weight": 1.0,
                            "dice_weight": 0.5,
                            "aux_weight": 0.4
                        },
                        "optimizer": {
                            "type": "Adam"
                        },
                        "scheduler": {
                            "type": "CosineAnnealingLR",
                            "eta_min": 1e-6
                        }
                    },
                    "data": {
                        "data_mode": "image+shape",
                        "slice_size": self.slice_size.value(),
                        "overlap": self.overlap.value(),
                        "train_ratio": 0.8,
                        "use_augmentation": merged_params.get("use_data_augmentation", True),
                        "min_foreground_ratio": merged_params.get("min_foreground_ratio", 0.01)
                    },
                    "paths": {
                        "image_path": image_path,
                        "shape_path": shape_path,
                        "image_folder": "",
                        "mask_folder": "",
                        "save_dir": save_dir
                    }
                }

                # 处理多分类配置
                if merged_params.get("multiclass_mode", False):
                    config["model"]["num_classes"] = merged_params.get("num_classes", 2)
                    if "class_names" in merged_params:
                        config["model"]["class_names"] = merged_params["class_names"]
                    if "shape_attribute_field" in merged_params:
                        config["model"]["class_field"] = merged_params["shape_attribute_field"]
                    if "class_mapping" in merged_params:
                        config["model"]["class_mapping"] = self._parse_class_mapping(merged_params["class_mapping"])
                    config["model"]["use_class_weights"] = merged_params.get("use_class_weights", True)

                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

                # 已移除rsseg验证，LogCAN现在使用独立的训练系统

                # 规范化路径，确保不为None
                if not image_path:
                    QMessageBox.critical(self, "错误", "图像文件路径不能为空")
                    return

                if not shape_path:
                    QMessageBox.critical(self, "错误", "形状文件路径不能为空")
                    return

                if not save_path:
                    QMessageBox.critical(self, "错误", "保存路径不能为空")
                    return

                try:
                    normalized_image_path = os.path.normpath(image_path)
                    normalized_shape_path = os.path.normpath(shape_path)
                    normalized_save_path = os.path.normpath(save_path)

                    # 验证文件存在
                    if not os.path.exists(normalized_image_path):
                        QMessageBox.critical(self, "错误", f"图像文件不存在: {normalized_image_path}")
                        return

                    if not os.path.exists(normalized_shape_path):
                        QMessageBox.critical(self, "错误", f"形状文件不存在: {normalized_shape_path}")
                        return

                except Exception as e:
                    QMessageBox.critical(self, "错误", f"路径验证失败: {str(e)}")
                    return

                # 确保保存目录存在
                os.makedirs(normalized_save_path, exist_ok=True)

                try:
                    # 保存配置文件
                    config_filename = f"logcan_config.json"
                    config_path = os.path.join(project_root, "config", config_filename)
                    os.makedirs(os.path.dirname(config_path), exist_ok=True)

                    with open(config_path, 'w') as f:
                        json.dump(config, f, indent=4)

                    # 构建可以直接在CMD中运行的启动命令
                    cmd = (
                        f'start "卫星影像偏移校正模型训练 - LoGCAN" cmd /k "'
                        f'cd /d "{project_root}" && '
                        f'set PYTHONPATH={project_root} && '
                        f'echo ============================================= && '
                        f'echo 卫星影像偏移校正模型训练 - LoGCAN && '
                        f'echo ============================================= && '
                        f'echo [信息] 训练配置 && '
                        f'echo - 图像文件: {normalized_image_path} && '
                        f'echo - 形状文件: {normalized_shape_path} && '
                        f'echo - 保存路径: {normalized_save_path} && '
                        f'echo - 配置文件: {config_path} && '
                        f'echo. && '
                        f'echo [信息] 开始训练模型... && '
                        f'echo ============================================= && '
                        f'"{sys.executable}" -c "from Core.LogCAN_Training_New import train_logcan_new; train_logcan_new(\\"{config_path}\\", verbose=True)" && '
                        f'echo. && '
                        f'echo ============================================= && '
                        f'echo [成功] 训练完成！ && '
                        f'echo 模型已保存到: {normalized_save_path} && '
                        f'echo ============================================= && '
                        f'pause || '
                        f'echo. && '
                        f'echo ============================================= && '
                        f'echo [错误] 训练失败！请查看错误信息 && '
                        f'echo ============================================= && '
                        f'pause"'
                    )

                    # 使用os.system直接执行，不需要Popen和后续操作
                    os.system(cmd)

                    # 显示成功信息并立即关闭对话框
                    QMessageBox.information(self, "成功", f"LoGCAN训练进程已启动，模型将在新窗口中训练。\n图像文件: {normalized_image_path}\n形状文件: {normalized_shape_path}\n保存目录: {normalized_save_path}")

                    # 直接使用QDialog基类方法关闭对话框，并立即返回
                    QDialog.accept(self)
                    return

                except Exception as e:
                    QMessageBox.critical(self, "错误", f"创建训练命令失败：{str(e)}\n{traceback.format_exc()}")
            else:
                # image+mask模式：跳过LoGCAN的特殊处理，使用通用逻辑
                pass

        # LoGCAN的image+mask模式也使用新的训练代码
        if current_model == "LoGCAN" and model_params.get('data_mode', 'image+shape') == 'image+mask':
            try:
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

                # 验证用户选择的路径
                image_folder = self.current_params["image_folder"].text()
                mask_folder = self.current_params["mask_folder"].text()

                if not image_folder:
                    QMessageBox.critical(self, "错误", "请选择图像文件夹")
                    return
                if not mask_folder:
                    QMessageBox.critical(self, "错误", "请选择掩膜文件夹")
                    return

                if not os.path.exists(image_folder):
                    QMessageBox.critical(self, "错误", f"图像文件夹不存在: {image_folder}")
                    return
                if not os.path.exists(mask_folder):
                    QMessageBox.critical(self, "错误", f"掩膜文件夹不存在: {mask_folder}")
                    return

                # 确保保存目录存在
                save_path = self.save_path_edit.text()
                save_dir = os.path.dirname(save_path) if os.path.isfile(save_path) else save_path
                os.makedirs(save_dir, exist_ok=True)

                # 获取GUI中的所有LogCAN参数
                gui_params = {}
                for param_name, widget in self.current_params.items():
                    if hasattr(widget, 'value'):
                        gui_params[param_name] = widget.value()
                    elif hasattr(widget, 'text'):
                        gui_params[param_name] = widget.text()
                    elif hasattr(widget, 'currentText'):
                        value = widget.currentText()
                        # 处理pretrained参数的字符串到布尔值转换
                        if param_name == "pretrained":
                            gui_params[param_name] = value == "True"
                        else:
                            gui_params[param_name] = value
                    elif hasattr(widget, 'isChecked'):
                        gui_params[param_name] = widget.isChecked()

                # 合并模型参数和GUI参数
                merged_params = {**model_params, **gui_params}

                # 生成新LogCAN训练代码所需的配置格式
                device_text = self.device_combo.currentText()
                device = "cuda" if device_text.startswith("GPU") else "cpu"

                config = {
                    "model": {
                        "num_classes": merged_params.get("num_classes", 2),
                        "transform_channel": merged_params.get("transform_channel", 128),
                        "backbone": "resnet50",
                        "pretrained": merged_params.get("pretrained", True)
                    },
                    "training": {
                        "epochs": self.epochs.value(),
                        "batch_size": self.batch_size.value(),
                        "learning_rate": self.learning_rate.value(),
                        "weight_decay": 1e-4,
                        "device": device,
                        "save_interval": 5,
                        "val_interval": 1,
                        "gradient_clip_norm": 1.0,
                        "loss": {
                            "use_focal": False,
                            "use_dice": True,
                            "ce_weight": 1.0,
                            "focal_weight": 1.0,
                            "dice_weight": 0.5,
                            "aux_weight": 0.4
                        },
                        "optimizer": {
                            "type": "Adam"
                        },
                        "scheduler": {
                            "type": "CosineAnnealingLR",
                            "eta_min": 1e-6
                        }
                    },
                    "data": {
                        "data_mode": "image+mask",
                        "slice_size": self.slice_size.value(),
                        "overlap": self.overlap.value(),
                        "train_ratio": 0.8,
                        "use_augmentation": merged_params.get("use_data_augmentation", True),
                        "min_foreground_ratio": merged_params.get("min_foreground_ratio", 0.01)
                    },
                    "paths": {
                        "image_path": "",
                        "shape_path": "",
                        "image_folder": image_folder,
                        "mask_folder": mask_folder,
                        "save_dir": save_dir
                    }
                }

                # 处理多分类配置
                if merged_params.get("multiclass_mode", False):
                    config["model"]["num_classes"] = merged_params.get("num_classes", 2)
                    if "class_names" in merged_params:
                        config["model"]["class_names"] = merged_params["class_names"]
                    if "shape_attribute_field" in merged_params:
                        config["model"]["class_field"] = merged_params["shape_attribute_field"]
                    if "class_mapping" in merged_params:
                        config["model"]["class_mapping"] = self._parse_class_mapping(merged_params["class_mapping"])
                    config["model"]["use_class_weights"] = merged_params.get("use_class_weights", True)

                # 保存配置文件
                config_filename = "logcan_config.json"
                config_path = os.path.join(project_root, "config", config_filename)
                os.makedirs(os.path.dirname(config_path), exist_ok=True)

                with open(config_path, 'w') as f:
                    json.dump(config, f, indent=4)

                # 构建训练命令
                cmd = (
                    f'start "卫星影像偏移校正模型训练 - LoGCAN (image+mask)" cmd /k "'
                    f'cd /d "{project_root}" && '
                    f'set PYTHONPATH={project_root} && '
                    f'echo ============================================= && '
                    f'echo 卫星影像偏移校正模型训练 - LoGCAN && '
                    f'echo ============================================= && '
                    f'echo [信息] 训练配置 && '
                    f'echo - 图像文件夹: {image_folder} && '
                    f'echo - 掩膜文件夹: {mask_folder} && '
                    f'echo - 保存路径: {save_dir} && '
                    f'echo - 配置文件: {config_path} && '
                    f'echo. && '
                    f'echo [信息] 开始训练模型... && '
                    f'echo ============================================= && '
                    f'"{sys.executable}" -c "from Core.LogCAN_Training_New import train_logcan_new; train_logcan_new(\\"{config_path}\\", verbose=True)" && '
                    f'echo. && '
                    f'echo ============================================= && '
                    f'echo [成功] 训练完成！ && '
                    f'echo 模型已保存到: {save_dir} && '
                    f'echo ============================================= && '
                    f'pause || '
                    f'echo. && '
                    f'echo ============================================= && '
                    f'echo [错误] 训练失败！请查看错误信息 && '
                    f'echo ============================================= && '
                    f'pause"'
                )

                # 执行命令
                os.system(cmd)

                # 显示成功信息并关闭对话框
                QMessageBox.information(self, "成功", f"LoGCAN训练进程已启动，模型将在新窗口中训练。\n图像文件夹: {image_folder}\n掩膜文件夹: {mask_folder}\n保存目录: {save_dir}")
                QDialog.accept(self)
                return

            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建训练命令失败：{str(e)}\n{traceback.format_exc()}")
                return

        # 其他模型的训练命令
        if current_model != "LoGCAN":
            try:
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

                # 生成配置文件 - 根据用户选择的模式

                # 检查用户选择的数据模式
                selected_data_mode = model_params.get('data_mode', 'image+mask')

                if selected_data_mode == 'image+mask':
                    # image+mask模式：使用用户在GUI中实际选择的文件夹路径
                    image_folder = self.current_params["image_folder"].text()
                    mask_folder = self.current_params["mask_folder"].text()

                    # 验证用户选择的路径
                    if not image_folder:
                        QMessageBox.critical(self, "错误", "请选择图像文件夹")
                        return
                    if not mask_folder:
                        QMessageBox.critical(self, "错误", "请选择掩膜文件夹")
                        return

                    # 验证路径存在
                    if not os.path.exists(image_folder):
                        QMessageBox.critical(self, "错误", f"图像文件夹不存在: {image_folder}")
                        return
                    if not os.path.exists(mask_folder):
                        QMessageBox.critical(self, "错误", f"掩膜文件夹不存在: {mask_folder}")
                        return

                    model_params['data_mode'] = 'image+mask'
                    model_params['image_folder'] = image_folder
                    model_params['mask_folder'] = mask_folder
                else:
                    # image+shape模式：使用单文件路径
                    # 保持用户在GUI中选择的文件路径
                    model_params['data_mode'] = 'image+shape'
                    # image_path和shape_path已经在model_params中了

                # 处理DeepLabV3+的多分类配置
                if current_model == "DeepLabV3+" and model_params.get("multiclass_mode", False):
                    # 构建多分类配置
                    class_names_str = model_params.get("class_names", "背景,建筑物")
                    class_names = [name.strip() for name in class_names_str.split(",")]
                    num_classes = model_params.get("num_classes", len(class_names))

                    # 确保类别数量和名称一致
                    if len(class_names) != num_classes:
                        # 调整类别名称列表
                        if len(class_names) < num_classes:
                            class_names.extend([f"类别{i}" for i in range(len(class_names), num_classes)])
                        else:
                            class_names = class_names[:num_classes]

                    # 生成类别映射（跳过背景类别）
                    class_mapping = {}
                    for i, name in enumerate(class_names[1:], 1):  # 从索引1开始，跳过背景
                        class_mapping[name] = i

                    # 生成类别颜色
                    class_colors = ["#000000"]  # 背景色
                    for i in range(1, num_classes):
                        hue = ((i - 1) * 137.5) % 360  # 黄金角度分布
                        from PyQt5.QtGui import QColor
                        color = QColor.fromHsv(int(hue), 200, 200)
                        class_colors.append(color.name())

                    # 添加多分类配置到模型参数
                    model_params["multiclass_config"] = {
                        "num_classes": num_classes,
                        "class_names": class_names,
                        "class_colors": class_colors,
                        "class_mapping": class_mapping,
                        "shape_attribute_field": model_params.get("shape_attribute_field", "class_type"),
                        "background_class_id": 0,
                        "ignore_index": 255
                    }

                # 处理LoGCAN的多分类配置
                if current_model == "LoGCAN" and model_params.get("multiclass_mode", False):
                    # 构建多分类配置
                    class_names_str = model_params.get("class_names", "背景,建筑物")
                    class_names = [name.strip() for name in class_names_str.split(",")]
                    num_classes = model_params.get("num_classes", len(class_names))

                    # 确保类别数量和名称一致
                    if len(class_names) != num_classes:
                        # 调整类别名称列表
                        if len(class_names) < num_classes:
                            class_names.extend([f"类别{i}" for i in range(len(class_names), num_classes)])
                        else:
                            class_names = class_names[:num_classes]

                    # 生成类别映射（跳过背景类别）
                    class_mapping = {}
                    for i, name in enumerate(class_names[1:], 1):  # 从索引1开始，跳过背景
                        class_mapping[name] = i

                    # 生成类别颜色
                    class_colors = ["#000000"]  # 背景色
                    for i in range(1, num_classes):
                        hue = ((i - 1) * 137.5) % 360  # 黄金角度分布
                        from PyQt5.QtGui import QColor
                        color = QColor.fromHsv(int(hue), 200, 200)
                        class_colors.append(color.name())

                    # 添加多分类配置到模型参数
                    model_params["multiclass_config"] = {
                        "num_classes": num_classes,
                        "class_names": class_names,
                        "class_colors": class_colors,
                        "class_mapping": class_mapping,
                        "shape_attribute_field": model_params.get("shape_attribute_field", "class_type"),
                        "background_class_id": 0,
                        "ignore_index": num_classes  # LoGCAN使用类别数作为ignore_index
                    }

                # 根据模型类型生成不同的配置格式
                if current_model == "LoGCAN":
                    # 获取GUI中的所有LogCAN参数
                    gui_params = {}
                    for param_name, widget in self.current_params.items():
                        if hasattr(widget, 'value'):
                            gui_params[param_name] = widget.value()
                        elif hasattr(widget, 'text'):
                            gui_params[param_name] = widget.text()
                        elif hasattr(widget, 'currentText'):
                            value = widget.currentText()
                            # 处理pretrained参数的字符串到布尔值转换
                            if param_name == "pretrained":
                                gui_params[param_name] = value == "True"
                            else:
                                gui_params[param_name] = value
                        elif hasattr(widget, 'isChecked'):
                            gui_params[param_name] = widget.isChecked()

                    # 合并模型参数和GUI参数
                    merged_params = {**model_params, **gui_params}



                    # 生成新LogCAN训练代码所需的配置格式
                    device_text = self.device_combo.currentText()
                    device = "cuda" if device_text.startswith("GPU") else "cpu"

                    # 确定数据模式
                    data_mode = merged_params.get("data_mode", "image+mask")

                    config = {
                        "model": {
                            "num_classes": merged_params.get("num_classes", 2),
                            "transform_channel": merged_params.get("transform_channel", 128),
                            "backbone": "resnet50",
                            "pretrained": merged_params.get("pretrained", True),
                            # 多分类配置（借鉴DeepLab的设计）
                            "class_field": merged_params.get("shape_attribute_field", "ClassValue"),
                            "class_mapping": self._parse_class_mapping(merged_params.get("class_mapping", "")),
                            "use_class_weights": merged_params.get("use_class_weights", True)
                        },
                        "training": {
                            "epochs": self.epochs.value(),
                            "batch_size": self.batch_size.value(),
                            "learning_rate": self.learning_rate.value(),
                            "weight_decay": 1e-4,
                            "device": device,
                            "save_interval": 5,
                            "val_interval": 1,
                            "gradient_clip_norm": 1.0,
                            "loss": {
                                "use_focal": False,
                                "use_dice": True,
                                "ce_weight": 1.0,
                                "focal_weight": 1.0,
                                "dice_weight": 0.5,
                                "aux_weight": 0.4
                            },
                            "optimizer": {
                                "type": "Adam"
                            },
                            "scheduler": {
                                "type": "CosineAnnealingLR",
                                "eta_min": 1e-6
                            }
                        },
                        "data": {
                            "data_mode": data_mode,
                            "slice_size": self.slice_size.value(),
                            "overlap": self.overlap.value(),
                            "train_ratio": 0.8,
                            "use_augmentation": merged_params.get("use_data_augmentation", True),
                            "min_foreground_ratio": merged_params.get("min_foreground_ratio", 0.01)
                        },
                        "paths": {
                            "save_dir": os.path.dirname(self.save_path_edit.text())
                        }
                    }

                    # 根据数据模式设置路径
                    if data_mode == "image+shape":
                        config["paths"]["image_path"] = merged_params.get("image_path", "")
                        config["paths"]["shape_path"] = merged_params.get("shape_path", "")
                        config["paths"]["image_folder"] = ""
                        config["paths"]["mask_folder"] = ""
                    else:  # image+mask
                        config["paths"]["image_path"] = ""
                        config["paths"]["shape_path"] = ""
                        config["paths"]["image_folder"] = merged_params.get("image_folder", "")
                        config["paths"]["mask_folder"] = merged_params.get("mask_folder", "")

                    # 处理多分类配置
                    if merged_params.get("multiclass_mode", False):
                        config["model"]["num_classes"] = merged_params.get("num_classes", 2)
                        if "class_names" in merged_params:
                            config["model"]["class_names"] = merged_params["class_names"]
                        if "shape_attribute_field" in merged_params:
                            config["model"]["class_field"] = merged_params["shape_attribute_field"]
                else:
                    # 其他模型使用原有配置格式
                    config = {
                        "model": {
                            "name": current_model,
                            "args": model_params
                        },
                        "trainer": {
                            "epochs": self.epochs.value(),
                            "batch_size": self.batch_size.value(),
                            "learning_rate": self.learning_rate.value(),
                            "slice_size": self.slice_size.value(),
                            "overlap": self.overlap.value(),
                            "save_dir": os.path.dirname(self.save_path_edit.text()),
                            "model_save_path": self.save_path_edit.text(),  # 添加完整的模型保存路径
                        "device": self.get_settings()['device'],
                        "memory_optimization": {
                            "use_mixed_precision": True,
                            "reduce_samples": self.batch_size.value() <= 2,  # 小批次时启用样本减少
                            "max_samples": 15000 if self.batch_size.value() <= 2 else None
                        }
                    }
                }

                # 根据数据模式添加相应的配置
                if selected_data_mode == 'image+mask':
                    # image+mask模式：添加文件夹路径
                    config["image_folder"] = model_params.get('image_folder', '')
                    config["mask_folder"] = model_params.get('mask_folder', '')
                else:
                    # image+shape模式：添加单文件路径
                    config["image_path"] = model_params.get('image_path', '')
                    config["shape_path"] = model_params.get('shape_path', '')

                # 添加数据集路径（用于兼容性）
                config["dataset_path"] = self.project_config.get('dataset_path', '')

                # 保存配置文件
                # 处理特殊命名，优先生成改进版本
                if current_model == "DeepLabV3+":
                    config_filename = "deeplabv3+_config.json"
                elif current_model == "UNet":
                    config_filename = "unet_config.json"
                else:
                    config_filename = f"{current_model.lower()}_config.json"
                config_path = os.path.join(project_root, "config", config_filename)
                os.makedirs(os.path.dirname(config_path), exist_ok=True)

                with open(config_path, 'w') as f:
                    json.dump(config, f, indent=4)

                # 获取训练脚本路径
                # 处理模型名称中的特殊字符
                script_model_name = current_model.replace("+", "Plus").replace(" ", "").replace("(", "").replace(")", "")
                train_script = os.path.join(project_root, "Core", f"Shape_Segmentation_Model_{script_model_name}.py")

                if not os.path.exists(train_script):
                    QMessageBox.critical(self, "错误", f"找不到训练脚本：{train_script}")
                    return

                # 构建训练命令（UNet模型时传递保存文件名）
                if current_model == "UNet":
                    cmd = (
                        f'start "卫星影像偏移校正模型训练 - {current_model}" cmd /k "'
                        f'cd /d "{project_root}" && '
                        f'set PYTHONPATH={project_root} && '
                        f'echo ============================================= && '
                        f'echo 卫星影像偏移校正模型训练 - {current_model} && '
                        f'echo ============================================= && '
                        f'echo [信息] 训练配置 && '
                        f'echo - 数据模式: {model_params.get("data_mode", "unknown")} && '
                        f'echo - 保存文件: {self.save_path_edit.text()} && '
                        f'echo - 配置文件: {config_path} && '
                        f'echo. && '
                        f'echo [信息] 开始训练模型... && '
                        f'echo ============================================= && '
                        f'"{sys.executable}" "{train_script}" --config "{config_path}" --model_save_path "{self.save_path_edit.text()}"' +
                        (f' --pretrained_path "{self.pretrained_path_edit.text()}"' if self.pretrained_path_edit.text().strip() else '') + ' && '
                        f'echo. && '
                        f'echo ============================================= && '
                        f'echo [成功] 训练完成！ && '
                        f'echo 模型已保存到: {self.save_path_edit.text()} && '
                        f'echo ============================================= && '
                        f'pause || '
                        f'echo. && '
                        f'echo ============================================= && '
                        f'echo [错误] 训练失败！请查看错误信息 && '
                        f'echo ============================================= && '
                        f'pause"'
                    )
                else:
                    cmd = (
                        f'start "卫星影像偏移校正模型训练 - {current_model}" cmd /k "'
                        f'cd /d "{project_root}" && '
                        f'set PYTHONPATH={project_root} && '
                        f'echo ============================================= && '
                        f'echo 卫星影像偏移校正模型训练 - {current_model} && '
                        f'echo ============================================= && '
                        f'echo [信息] 训练配置 && '
                        f'echo - 数据集路径: {config["dataset_path"]} && '
                        f'echo - 保存路径: {config["trainer"]["save_dir"]} && '
                        f'echo - 配置文件: {config_path} && '
                        f'echo. && '
                        f'echo [信息] 开始训练模型... && '
                        f'echo ============================================= && '
                        f'"{sys.executable}" "{train_script}" --config "{config_path}" && '
                        f'echo. && '
                        f'echo ============================================= && '
                        f'echo [成功] 训练完成！ && '
                        f'echo 模型已保存到: {config["trainer"]["save_dir"]} && '
                        f'echo ============================================= && '
                        f'pause || '
                        f'echo. && '
                        f'echo ============================================= && '
                        f'echo [错误] 训练失败！请查看错误信息 && '
                        f'echo ============================================= && '
                        f'pause"'
                    )

                # 执行命令
                os.system(cmd)

                # 显示成功信息并关闭对话框
                QMessageBox.information(self, "成功", f"训练进程已启动，模型将在新窗口中训练。\n保存目录: {config['trainer']['save_dir']}")
                QDialog.accept(self)

            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建训练命令失败：{str(e)}\n{traceback.format_exc()}")

    def on_model_changed(self):
        """当选择的模型改变时更新UI"""
        # 获取当前选择的模型
        current_model = self.model_combo.currentText()
        # 处理特殊命名，优先选择改进版本
        if current_model == "DeepLabV3+":
            config_filename = "deeplabv3+_config.json"
        elif current_model == "UNet":
            config_filename = "unet_config.json"
        else:
            config_filename = f"{current_model.lower()}_config.json"

        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "config",
            config_filename
        )
        config_data = None
        if os.path.exists(config_path):
            with open(config_path, "r", encoding="utf-8") as f:
                config_data = json.load(f)
        # 用config文件内容覆盖self.model_specific_params和self.default_config
        if config_data:
            if current_model == "LoGCAN":
                # LoGCAN使用新的配置格式
                if "model" in config_data:
                    # 更新模型特定参数
                    model_config = config_data["model"]

                    # 映射配置文件中的键到模型参数键
                    config_to_param_mapping = {
                        "num_classes": "num_classes",
                        "transform_channel": "transform_channel",
                        "pretrained": "pretrained",
                        "class_field": "shape_attribute_field",
                        "class_names": "class_names",
                        "multiclass_mode": "multiclass_mode",
                        "class_mapping": "class_mapping",
                        "use_class_weights": "use_class_weights"
                    }

                    for config_key, param_key in config_to_param_mapping.items():
                        if config_key in model_config and param_key in self.model_specific_params[current_model]:
                            value = model_config[config_key]
                            # 特殊处理class_names - 如果是列表，转换为字符串
                            if param_key == "class_names" and isinstance(value, list):
                                value = ",".join(value)
                            self.model_specific_params[current_model][param_key] = value

                if "training" in config_data:
                    # 更新训练参数
                    training_config = config_data["training"]
                    self.default_config.update({
                        'batch_size': training_config.get('batch_size', self.default_config['batch_size']),
                        'learning_rate': training_config.get('learning_rate', self.default_config['learning_rate']),
                        'epochs': training_config.get('epochs', self.default_config['epochs']),
                    })

                if "data" in config_data:
                    # 更新数据参数
                    data_config = config_data["data"]
                    self.default_config.update({
                        'slice_size': data_config.get('slice_size', self.default_config['slice_size']),
                        'overlap': data_config.get('overlap', self.default_config['overlap'])
                    })

                if "paths" in config_data:
                    # 更新路径参数
                    paths_config = config_data["paths"]
                    path_params = ['image_path', 'shape_path', 'image_folder', 'mask_folder']
                    for param in path_params:
                        if param in paths_config and param in self.model_specific_params[current_model]:
                            self.model_specific_params[current_model][param] = paths_config[param]
            else:
                # 其他模型使用旧的配置格式
                if "model" in config_data and "args" in config_data["model"]:
                    # 只更新预定义的参数，避免显示多余的参数
                    config_args = config_data["model"]["args"]
                    predefined_params = self.model_specific_params[current_model].keys()
                    filtered_args = {k: v for k, v in config_args.items() if k in predefined_params}
                    self.model_specific_params[current_model].update(filtered_args)
                # 刷新通用训练参数
                if "trainer" in config_data:
                    self.default_config.update({
                        'batch_size': config_data["trainer"].get('batch_size', self.default_config['batch_size']),
                        'learning_rate': config_data["trainer"].get('learning_rate', self.default_config['learning_rate']),
                        'epochs': config_data["trainer"].get('epochs', self.default_config['epochs']),
                        'slice_size': config_data["trainer"].get('slice_size', self.default_config['slice_size']),
                        'overlap': config_data["trainer"].get('overlap', self.default_config['overlap'])
                    })
        # 清除现有的模型特定参数 - 使用更可靠的清理方法
        self.clear_model_params_layout()
        self.current_params = {}
        # 所有模型都显示通用训练参数
        self.param_group.setVisible(True)
        # 根据模型类型更新保存路径的默认扩展名
        self.update_save_path_for_model(current_model)

        # 根据模型类型自动调整推荐学习率
        self.auto_adjust_learning_rate_for_model(current_model)

        # 添加模型特定参数
        if current_model in self.model_specific_params:
            params = self.model_specific_params[current_model]
            for param_name, default_value in params.items():
                if isinstance(default_value, bool):
                    # 处理pretrained参数 - 使用下拉框而不是复选框
                    if param_name == "pretrained" and current_model == "LoGCAN":
                        widget = QComboBox()
                        widget.addItems(["True", "False"])
                        widget.setCurrentText("True" if default_value else "False")
                        widget.setToolTip("是否使用ImageNet预训练权重\nTrue: 使用预训练权重（推荐，收敛更快）\nFalse: 从随机初始化开始训练")
                        param_label = "预训练权重"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    # 处理use_class_weights参数 - 使用复选框
                    elif param_name == "use_class_weights" and current_model == "LoGCAN":
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        widget.setToolTip("是否启用自动类别权重平衡\n勾选: 自动计算并应用类别权重，解决类别不平衡问题\n不勾选: 使用默认权重")
                        param_label = "使用类别权重"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    else:
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        param_label = param_name.replace("_", " ").title()
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                elif isinstance(default_value, float):
                    # 检查是否是min_foreground_ratio参数，需要特殊处理
                    if param_name == "min_foreground_ratio":
                        widget = QDoubleSpinBox()
                        widget.setDecimals(3)  # 先设置精度
                        widget.setRange(0.001, 1.0)
                        widget.setSingleStep(0.001)
                        widget.setValue(default_value)
                    else:
                        widget = QDoubleSpinBox()
                        widget.setDecimals(2)  # 其他float参数使用2位小数
                        widget.setRange(0, 1)
                        widget.setValue(default_value)
                        widget.setSingleStep(0.1)
                    param_label = param_name.replace("_", " ").title()
                    self.model_params_layout.addRow(f"{param_label}:", widget)
                    self.current_params[param_name] = widget
                    continue
                elif isinstance(default_value, int):
                    widget = QSpinBox()
                    widget.setRange(1, 2048)
                    widget.setValue(default_value)
                    if param_name == "transform_channel":
                        widget.setRange(32, 512)
                        widget.setSingleStep(32)
                    param_label = param_name.replace("_", " ").title()
                    self.model_params_layout.addRow(f"{param_label}:", widget)
                    self.current_params[param_name] = widget
                    continue
                elif isinstance(default_value, str):
                    # 处理class_mapping参数 - 类别映射输入框
                    if param_name == "class_mapping" and current_model == "LoGCAN":
                        widget = QLineEdit()
                        widget.setText(default_value)
                        widget.setPlaceholderText('如：{"建筑物": 1, "道路": 2} 或 建筑物:1, 道路:2')
                        widget.setToolTip("类别映射：支持JSON格式或简单格式\n例如：{\"建筑物\": 1, \"道路\": 2}\n或：建筑物:1, 道路:2")
                        param_label = "类别映射"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "image_path" and current_model == "LoGCAN":
                        layout = QHBoxLayout()
                        widget = QLineEdit()
                        widget.setReadOnly(True)
                        widget.setText(default_value if default_value else "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif")
                        browse_btn = QPushButton("浏览...")
                        def browse_logcan_image_path(edt=widget):
                            path, _ = QFileDialog.getOpenFileName(
                                self,
                                "选择LoGCAN训练图像数据文件",
                                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",
                                "TIFF Files (*.tif *.tiff)"
                            )
                            if path:
                                edt.setText(path)
                        browse_btn.clicked.connect(functools.partial(browse_logcan_image_path, widget))
                        layout.addWidget(widget)
                        layout.addWidget(browse_btn)
                        param_label = "图像数据文件"
                        self.model_params_layout.addRow(f"{param_label}:", layout)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "shape_path" and current_model == "LoGCAN":
                        layout = QHBoxLayout()
                        widget = QLineEdit()
                        widget.setReadOnly(True)
                        widget.setText(default_value if default_value else "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings.shp")
                        browse_btn = QPushButton("浏览...")
                        def browse_logcan_shape_path(edt=widget):
                            path, _ = QFileDialog.getOpenFileName(
                                self,
                                "选择LoGCAN训练形状数据文件",
                                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/",
                                "Shapefile (*.shp)"
                            )
                            if path:
                                edt.setText(path)
                        browse_btn.clicked.connect(functools.partial(browse_logcan_shape_path, widget))
                        layout.addWidget(widget)
                        layout.addWidget(browse_btn)
                        param_label = "形状数据文件"
                        self.model_params_layout.addRow(f"{param_label}:", layout)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "data_mode" and current_model == "UNet":
                        # UNet数据输入模式选择
                        widget = QComboBox()
                        widget.addItems(["image+shape", "image+mask"])
                        widget.setCurrentText(default_value)  # 使用配置文件中的默认值
                        widget.currentTextChanged.connect(self.on_unet_data_mode_changed)
                        param_label = "数据输入模式"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget

                        # 立即创建所有文件选择控件，确保位置固定
                        self.create_unet_file_selection_widgets(current_model)
                        continue
                    elif param_name == "data_mode" and current_model == "DeepLabV3+":
                        # DeepLabV3+数据输入模式选择
                        widget = QComboBox()
                        widget.addItems(["image+shape", "image+mask"])
                        widget.setCurrentText(default_value)  # 使用配置文件中的默认值
                        widget.currentTextChanged.connect(self.on_deeplab_data_mode_changed)
                        param_label = "数据输入模式"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget

                        # 立即创建所有文件选择控件，确保位置固定
                        self.create_deeplab_file_selection_widgets(current_model)
                        continue
                    elif param_name == "data_mode" and current_model == "LoGCAN":
                        # LoGCAN数据输入模式选择
                        widget = QComboBox()
                        widget.addItems(["image+shape", "image+mask"])
                        widget.setCurrentText(default_value)  # 使用配置文件中的默认值
                        widget.currentTextChanged.connect(self.on_logcan_data_mode_changed)
                        param_label = "数据输入模式"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget

                        # 立即创建所有文件选择控件，确保位置固定
                        self.create_logcan_file_selection_widgets(current_model)
                        continue
                    elif param_name == "image_path" and current_model == "UNet":
                        # UNet的文件选择控件已在create_unet_file_selection_widgets中创建
                        continue
                    elif param_name == "image_path" and current_model == "DeepLabV3+":
                        # DeepLabV3+的文件选择控件已在create_deeplab_file_selection_widgets中创建
                        continue
                    elif param_name == "image_path" and current_model == "LoGCAN":
                        # LoGCAN的文件选择控件已在create_logcan_file_selection_widgets中创建
                        continue
                    elif param_name == "shape_path" and current_model == "LoGCAN":
                        # LoGCAN的文件选择控件已在create_logcan_file_selection_widgets中创建
                        continue
                    elif param_name == "image_folder" and current_model == "LoGCAN":
                        # LoGCAN的文件选择控件已在create_logcan_file_selection_widgets中创建
                        continue
                    elif param_name == "mask_folder" and current_model == "LoGCAN":
                        # LoGCAN的文件选择控件已在create_logcan_file_selection_widgets中创建
                        continue
                    elif param_name == "image_path" and current_model in ["ResNet", "FCN"]:
                        layout = QHBoxLayout()
                        widget = QLineEdit()
                        widget.setReadOnly(True)
                        widget.setText(default_value if default_value else "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif")
                        browse_btn = QPushButton("浏览...")
                        def browse_image_path(edt=widget):
                            path, _ = QFileDialog.getOpenFileName(
                                self,
                                f"选择{current_model}训练图像数据文件",
                                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",
                                "TIFF Files (*.tif *.tiff)"
                            )
                            if path:
                                edt.setText(path)
                        browse_btn.clicked.connect(functools.partial(browse_image_path, widget))
                        layout.addWidget(widget)
                        layout.addWidget(browse_btn)
                        param_label = "图像数据文件"
                        self.model_params_layout.addRow(f"{param_label}:", layout)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "image_folder" and current_model == "UNet":
                        # UNet的文件选择控件已在create_unet_file_selection_widgets中创建
                        continue
                    elif param_name == "image_folder" and current_model == "DeepLabV3+":
                        # DeepLabV3+的文件选择控件已在create_deeplab_file_selection_widgets中创建
                        continue
                    elif param_name == "mask_folder" and current_model == "UNet":
                        # UNet的文件选择控件已在create_unet_file_selection_widgets中创建
                        continue
                    elif param_name == "mask_folder" and current_model == "DeepLabV3+":
                        # DeepLabV3+的文件选择控件已在create_deeplab_file_selection_widgets中创建
                        continue
                    elif param_name == "shape_path" and current_model == "UNet":
                        # UNet的文件选择控件已在create_unet_file_selection_widgets中创建
                        continue
                    elif param_name == "shape_path" and current_model == "DeepLabV3+":
                        # DeepLabV3+的文件选择控件已在create_deeplab_file_selection_widgets中创建
                        continue
                    elif param_name == "shape_path" and current_model in ["ResNet", "FCN"]:
                        layout = QHBoxLayout()
                        widget = QLineEdit()
                        widget.setReadOnly(True)
                        widget.setText(default_value if default_value else "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings.shp")
                        browse_btn = QPushButton("浏览...")
                        def browse_shape_path(edt=widget):
                            path, _ = QFileDialog.getOpenFileName(
                                self,
                                f"选择{current_model}训练形状数据文件",
                                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/",
                                "Shapefile (*.shp)"
                            )
                            if path:
                                edt.setText(path)
                        browse_btn.clicked.connect(functools.partial(browse_shape_path, widget))
                        layout.addWidget(widget)
                        layout.addWidget(browse_btn)
                        param_label = "形状数据文件"
                        self.model_params_layout.addRow(f"{param_label}:", layout)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "model_type" and current_model == "UNet":
                        # UNet固定使用完整模型，不显示选择框
                        continue
                    elif param_name == "dropout_rate" and current_model == "UNet":
                        # UNet Dropout率
                        widget = QDoubleSpinBox()
                        widget.setRange(0.0, 0.5)
                        widget.setSingleStep(0.05)
                        widget.setDecimals(2)
                        widget.setValue(default_value)
                        param_label = "Dropout率"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "use_all_data" and current_model == "UNet":
                        # UNet 使用全部数据开关
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        param_label = "使用全部数据"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "use_data_augmentation" and current_model == "UNet":
                        # UNet 数据增强开关
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        param_label = "启用数据增强"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    # min_foreground_ratio现在由通用float处理逻辑处理
                    elif param_name in ["max_patches_per_file", "max_total_patches"] and current_model == "UNet":
                        # UNet 图像块数量限制（跳过显示，因为None值不适合GUI）
                        continue
                    elif param_name == "use_simple_preprocessing" and current_model == "DeepLabV3+":
                        # DeepLabV3+ 简单预处理开关
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        param_label = "使用简单预处理"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "use_data_augmentation" and current_model == "DeepLabV3+":
                        # DeepLabV3+ 数据增强开关
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        param_label = "启用数据增强"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    # min_foreground_ratio现在由通用float处理逻辑处理
                    elif param_name == "multiclass_mode" and current_model == "DeepLabV3+":
                        # DeepLabV3+ 多分类模式开关
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        widget.stateChanged.connect(self.on_multiclass_mode_changed)
                        param_label = "启用多分类模式"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "num_classes" and current_model == "DeepLabV3+":
                        # DeepLabV3+ 类别数量
                        widget = QSpinBox()
                        widget.setRange(2, 20)
                        widget.setValue(default_value)
                        param_label = "类别数量"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "class_names" and current_model == "DeepLabV3+":
                        # DeepLabV3+ 类别名称
                        widget = QLineEdit()
                        widget.setText(default_value)
                        widget.setPlaceholderText("用逗号分隔，如：背景,建筑物,道路")
                        param_label = "类别名称"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "shape_attribute_field" and current_model == "DeepLabV3+":
                        # DeepLabV3+ Shapefile属性字段
                        widget = QLineEdit()
                        widget.setText(default_value)
                        widget.setPlaceholderText("Shapefile中的类别属性字段名")
                        param_label = "属性字段名"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "multiclass_mode" and current_model == "LoGCAN":
                        # LoGCAN 多分类模式开关
                        widget = QCheckBox()
                        widget.setChecked(default_value)
                        widget.stateChanged.connect(self.on_logcan_multiclass_mode_changed)
                        param_label = "启用多分类模式"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget

                        # 记录多分类模式状态，稍后统一处理
                        self._logcan_multiclass_initial_state = default_value
                        continue
                    elif param_name == "num_classes" and current_model == "LoGCAN":
                        # LoGCAN 类别数量
                        widget = QSpinBox()
                        widget.setRange(2, 20)
                        widget.setValue(default_value)
                        param_label = "类别数量"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "class_names" and current_model == "LoGCAN":
                        # LoGCAN 类别名称
                        widget = QLineEdit()
                        widget.setText(default_value)
                        widget.setPlaceholderText("用逗号分隔，如：背景,建筑物,道路")
                        param_label = "类别名称"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    elif param_name == "shape_attribute_field" and current_model == "LoGCAN":
                        # LoGCAN Shapefile属性字段
                        widget = QLineEdit()
                        widget.setText(default_value)
                        widget.setPlaceholderText("Shapefile中的类别属性字段名")
                        param_label = "属性字段名"
                        self.model_params_layout.addRow(f"{param_label}:", widget)
                        self.current_params[param_name] = widget
                        continue
                    else:
                        widget = QComboBox()
                        if "backbone" in param_name:
                            if current_model == "DeepLabV3+":
                                widget.addItems(["resnet50", "resnet101", "xception"])
                            elif current_model == "ResNet":
                                widget.addItems(["resnet18", "resnet34", "resnet50", "resnet101"])
                            elif current_model == "FCN":
                                widget.addItems(["vgg16", "vgg19", "resnet50"])
                            widget.setCurrentText(default_value)
                else:
                    continue
                param_label = param_name.replace("_", " ").title()
                self.model_params_layout.addRow(f"{param_label}:", widget)
                self.current_params[param_name] = widget
        # 刷新通用训练参数
        self.slice_size.setValue(self.default_config["slice_size"])
        self.overlap.setValue(self.default_config["overlap"])
        self.batch_size.setValue(self.default_config["batch_size"])
        self.epochs.setValue(self.default_config["epochs"])
        self.learning_rate.setValue(self.default_config["learning_rate"])

        # 强制刷新界面布局
        self.model_params_group.updateGeometry()
        self.model_params_group.adjustSize()
        self.updateGeometry()
        self.adjustSize()
        QApplication.processEvents()

        # 如果是UNet模型，延迟触发一次数据模式切换来确保控件可见性正确
        if current_model == "UNet" and "data_mode" in self.current_params:
            def delayed_update():
                current_mode = self.current_params["data_mode"].currentText()
                self.on_unet_data_mode_changed(current_mode)
            QTimer.singleShot(0, delayed_update)

        # 如果是DeepLabV3+模型，延迟触发一次数据模式切换来确保控件可见性正确
        if current_model == "DeepLabV3+" and "data_mode" in self.current_params:
            def delayed_deeplab_update():
                current_mode = self.current_params["data_mode"].currentText()
                self.on_deeplab_data_mode_changed(current_mode)
            QTimer.singleShot(0, delayed_deeplab_update)

        # 如果是LoGCAN模型，延迟触发一次数据模式切换来确保控件可见性正确
        if current_model == "LoGCAN" and "data_mode" in self.current_params:
            def delayed_logcan_update():
                current_mode = self.current_params["data_mode"].currentText()
                self.on_logcan_data_mode_changed(current_mode)
            QTimer.singleShot(0, delayed_logcan_update)

        # 如果是LoGCAN模型，在所有参数创建完成后处理多分类模式
        if current_model == "LoGCAN" and hasattr(self, '_logcan_multiclass_initial_state'):
            self.on_logcan_multiclass_mode_changed(2 if self._logcan_multiclass_initial_state else 0)
            delattr(self, '_logcan_multiclass_initial_state')

    def create_unet_file_selection_widgets(self, current_model):
        """创建UNet的文件选择控件，确保位置固定"""
        import functools

        # 尝试读取UNet配置文件获取默认路径
        unet_config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "config",
            "unet_config.json"
        )

        default_paths = {
            'image_path': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",
            'shape_path': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings_rafha_wgs84.shp",
            'image_folder': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image",
            'mask_folder': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask"
        }

        # 如果UNet配置文件存在，读取其中的路径
        if os.path.exists(unet_config_path):
            try:
                with open(unet_config_path, 'r', encoding='utf-8') as f:
                    unet_config = json.load(f)
                    model_args = unet_config.get('model', {}).get('args', {})

                    # 更新默认路径
                    if 'image_path' in model_args:
                        default_paths['image_path'] = model_args['image_path']
                    if 'shape_path' in model_args:
                        default_paths['shape_path'] = model_args['shape_path']
                    if 'image_folder' in model_args:
                        default_paths['image_folder'] = model_args['image_folder']
                    if 'mask_folder' in model_args:
                        default_paths['mask_folder'] = model_args['mask_folder']


            except Exception as e:
                print(f"⚠️ 读取UNet配置文件失败，使用默认路径: {e}")

        # 创建图像文件选择控件 (image+shape模式)
        layout = QHBoxLayout()
        widget = QLineEdit()
        widget.setReadOnly(True)
        widget.setText(default_paths['image_path'])
        browse_btn = QPushButton("浏览...")
        def browse_image_path(edt=widget):
            path, _ = QFileDialog.getOpenFileName(
                self,
                f"选择{current_model}训练图像数据文件",
                edt.text() or os.path.dirname(default_paths['image_path']),
                "TIFF Files (*.tif *.tiff)"
            )
            if path:
                edt.setText(path)
        browse_btn.clicked.connect(functools.partial(browse_image_path, widget))
        layout.addWidget(widget)
        layout.addWidget(browse_btn)
        param_label = "图像数据文件"
        self.model_params_layout.addRow(f"{param_label}:", layout)
        self.current_params["image_path"] = widget

        # 创建形状文件选择控件 (image+shape模式)
        layout2 = QHBoxLayout()
        widget2 = QLineEdit()
        widget2.setReadOnly(True)
        widget2.setText(default_paths['shape_path'])
        browse_btn2 = QPushButton("浏览...")
        def browse_shape_path(edt=widget2):
            path, _ = QFileDialog.getOpenFileName(
                self,
                f"选择{current_model}训练形状数据文件",
                edt.text() or os.path.dirname(default_paths['shape_path']),
                "Shapefile (*.shp)"
            )
            if path:
                edt.setText(path)
        browse_btn2.clicked.connect(functools.partial(browse_shape_path, widget2))
        layout2.addWidget(widget2)
        layout2.addWidget(browse_btn2)
        param_label2 = "形状数据文件"
        self.model_params_layout.addRow(f"{param_label2}:", layout2)
        self.current_params["shape_path"] = widget2

        # 创建图像文件夹选择控件 (image+mask模式)
        layout3 = QHBoxLayout()
        widget3 = QLineEdit()
        widget3.setReadOnly(True)
        widget3.setText(default_paths['image_folder'])

        browse_btn3 = QPushButton("浏览...")
        def browse_image_folder(edt=widget3):
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择图像文件夹",
                edt.text() or default_paths['image_folder']
            )
            if folder:
                edt.setText(folder)
        browse_btn3.clicked.connect(functools.partial(browse_image_folder, widget3))
        layout3.addWidget(widget3)
        layout3.addWidget(browse_btn3)
        param_label3 = "图像文件夹"
        row_index3 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label3}:", layout3)
        self.current_params["image_folder"] = widget3
        # 初始时隐藏（image+shape模式）
        widget3.setVisible(False)
        browse_btn3.setVisible(False)
        # 获取并隐藏标签
        label_item3 = self.model_params_layout.itemAt(row_index3, QFormLayout.LabelRole)
        if label_item3 and label_item3.widget():
            label_item3.widget().setVisible(False)

        # 创建掩膜文件夹选择控件 (image+mask模式)
        layout4 = QHBoxLayout()
        widget4 = QLineEdit()
        widget4.setReadOnly(True)
        widget4.setText(default_paths['mask_folder'])

        browse_btn4 = QPushButton("浏览...")
        def browse_mask_folder(edt=widget4):
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择掩膜文件夹",
                edt.text() or default_paths['mask_folder']
            )
            if folder:
                edt.setText(folder)
        browse_btn4.clicked.connect(functools.partial(browse_mask_folder, widget4))
        layout4.addWidget(widget4)
        layout4.addWidget(browse_btn4)
        param_label4 = "掩膜文件夹"
        row_index4 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label4}:", layout4)
        self.current_params["mask_folder"] = widget4
        # 初始时隐藏（image+shape模式）
        widget4.setVisible(False)
        browse_btn4.setVisible(False)
        # 获取并隐藏标签
        label_item4 = self.model_params_layout.itemAt(row_index4, QFormLayout.LabelRole)
        if label_item4 and label_item4.widget():
            label_item4.widget().setVisible(False)

    def on_multiclass_mode_changed(self, state):
        """多分类模式切换回调"""
        is_multiclass = state == 2  # Qt.Checked

        # 根据多分类模式显示/隐藏相关控件
        multiclass_params = ["num_classes", "class_names", "shape_attribute_field"]

        for param_name in multiclass_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 查找对应的标签
                for i in range(self.model_params_layout.rowCount()):
                    field_widget = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                    if field_widget and field_widget.widget() == widget:
                        # 找到对应的标签
                        label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setVisible(is_multiclass)
                        widget.setVisible(is_multiclass)
                        break

        # 如果启用多分类，设置默认值
        if is_multiclass and "num_classes" in self.current_params:
            num_classes_widget = self.current_params["num_classes"]
            if num_classes_widget.value() < 3:
                num_classes_widget.setValue(3)

        # 更新界面
        self.model_params_group.updateGeometry()
        self.adjustSize()

    def on_unet_data_mode_changed(self, mode):
        """UNet数据模式切换处理"""
        if not hasattr(self, 'current_params'):
            return

        is_folder_mode = (mode == "image+mask")

        # 切换单文件模式控件的可见性
        single_file_params = ['image_path', 'shape_path']
        for param_name in single_file_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 获取包含widget的layout
                for i in range(self.model_params_layout.rowCount()):
                    if self.model_params_layout.itemAt(i, QFormLayout.FieldRole):
                        field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                        if hasattr(field_item, 'layout') and field_item.layout():
                            # 检查layout中是否包含我们的widget
                            layout = field_item.layout()
                            for j in range(layout.count()):
                                layout_item = layout.itemAt(j)
                                if layout_item and layout_item.widget() == widget:
                                    # 找到了，设置整个layout的可见性
                                    for k in range(layout.count()):
                                        layout_item = layout.itemAt(k)
                                        if layout_item:
                                            item_widget = layout_item.widget()
                                            if item_widget:  # 检查widget是否存在
                                                item_widget.setVisible(not is_folder_mode)
                                    # 设置标签的可见性
                                    label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                                    if label_item and label_item.widget():
                                        label_item.widget().setVisible(not is_folder_mode)
                                    break

        # 切换文件夹模式控件的可见性
        folder_params = ['image_folder', 'mask_folder']
        for param_name in folder_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 获取包含widget的layout
                for i in range(self.model_params_layout.rowCount()):
                    if self.model_params_layout.itemAt(i, QFormLayout.FieldRole):
                        field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                        if hasattr(field_item, 'layout') and field_item.layout():
                            # 检查layout中是否包含我们的widget
                            layout = field_item.layout()
                            for j in range(layout.count()):
                                layout_item = layout.itemAt(j)
                                if layout_item and layout_item.widget() == widget:
                                    # 找到了，设置整个layout的可见性
                                    for k in range(layout.count()):
                                        layout_item = layout.itemAt(k)
                                        if layout_item:
                                            item_widget = layout_item.widget()
                                            if item_widget:  # 检查widget是否存在
                                                item_widget.setVisible(is_folder_mode)
                                    # 设置标签的可见性
                                    label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                                    if label_item and label_item.widget():
                                        label_item.widget().setVisible(is_folder_mode)
                                    break

        # 刷新布局
        self.model_params_group.updateGeometry()
        self.model_params_group.adjustSize()
        self.updateGeometry()
        self.adjustSize()
        QApplication.processEvents()

    def clear_model_params_layout(self):
        """清理模型特定参数布局中的所有控件"""
        try:
            # 获取当前布局
            current_layout = self.model_params_group.layout()

            if current_layout:
                # 递归删除所有子控件和布局项
                self._recursive_delete_layout_items(current_layout)

                # 重用现有布局，不删除它
                self.model_params_layout = current_layout
            else:
                # 如果没有布局，创建新的
                self.model_params_layout = QFormLayout()
                self.model_params_group.setLayout(self.model_params_layout)

            # 强制处理事件
            QApplication.processEvents()

        except Exception as e:
            print(f"清理模型参数布局时出错: {str(e)}")
            # 如果出错，尝试简单清理
            try:
                current_layout = self.model_params_group.layout()
                if current_layout:
                    # 简单清理：只移除所有项目
                    while current_layout.count():
                        item = current_layout.takeAt(0)
                        if item and item.widget():
                            item.widget().deleteLater()
                    self.model_params_layout = current_layout
                else:
                    # 如果没有布局，创建新的
                    self.model_params_layout = QFormLayout()
                    self.model_params_group.setLayout(self.model_params_layout)
                QApplication.processEvents()
            except Exception as fallback_error:
                print(f"备用清理方法也失败: {str(fallback_error)}")
                # 最后的备用方案：确保有一个可用的布局
                try:
                    if not self.model_params_layout:
                        self.model_params_layout = QFormLayout()
                        if not self.model_params_group.layout():
                            self.model_params_group.setLayout(self.model_params_layout)
                except:
                    pass

    def _recursive_delete_layout_items(self, layout):
        """递归删除布局中的所有项目"""
        try:
            while layout.count():
                item = layout.takeAt(0)
                if item:
                    widget = item.widget()
                    child_layout = item.layout()

                    if widget:
                        # 安全地删除控件
                        try:
                            widget.hide()
                            widget.setParent(None)
                            widget.deleteLater()
                        except:
                            pass
                    elif child_layout:
                        # 递归删除子布局
                        self._recursive_delete_layout_items(child_layout)
                        child_layout.setParent(None)
                        child_layout.deleteLater()
        except Exception as e:
            print(f"递归删除布局项目时出错: {str(e)}")

    def create_deeplab_file_selection_widgets(self, current_model):
        """创建DeepLabV3+的文件选择控件，确保位置固定"""
        import functools

        # 尝试读取DeepLab配置文件获取默认路径
        deeplab_config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "config",
            "deeplabv3+_config.json"
        )

        default_paths = {
            'image_path': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",
            'shape_path': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/Buildings.shp",
            'image_folder': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",
            'mask_folder': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask/"
        }

        if os.path.exists(deeplab_config_path):
            try:
                with open(deeplab_config_path, 'r', encoding='utf-8') as f:
                    deeplab_config = json.load(f)
                    model_args = deeplab_config.get('model', {}).get('args', {})

                    # 更新默认路径
                    if 'image_path' in model_args:
                        default_paths['image_path'] = model_args['image_path']
                    if 'shape_path' in model_args:
                        default_paths['shape_path'] = model_args['shape_path']
                    if 'image_folder' in model_args:
                        default_paths['image_folder'] = model_args['image_folder']
                    if 'mask_folder' in model_args:
                        default_paths['mask_folder'] = model_args['mask_folder']

            except Exception as e:
                print(f"⚠️ 读取DeepLab配置文件失败，使用默认路径: {e}")

        # 创建图像文件选择控件 (image+shape模式)
        layout = QHBoxLayout()
        widget = QLineEdit()
        widget.setReadOnly(True)
        widget.setText(default_paths['image_path'])
        browse_btn = QPushButton("浏览...")
        def browse_image_path(edt=widget):
            path, _ = QFileDialog.getOpenFileName(
                self,
                "选择DeepLab训练图像数据文件",
                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",
                "TIFF Files (*.tif *.tiff)"
            )
            if path:
                edt.setText(path)
        browse_btn.clicked.connect(functools.partial(browse_image_path, widget))
        layout.addWidget(widget)
        layout.addWidget(browse_btn)
        param_label = "图像数据文件"
        self.model_params_layout.addRow(f"{param_label}:", layout)
        self.current_params["image_path"] = widget

        # 创建形状文件选择控件 (image+shape模式)
        layout2 = QHBoxLayout()
        widget2 = QLineEdit()
        widget2.setReadOnly(True)
        widget2.setText(default_paths['shape_path'])
        browse_btn2 = QPushButton("浏览...")
        def browse_shape_path(edt=widget2):
            path, _ = QFileDialog.getOpenFileName(
                self,
                "选择DeepLab训练形状数据文件",
                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/",
                "Shapefile (*.shp)"
            )
            if path:
                edt.setText(path)
        browse_btn2.clicked.connect(functools.partial(browse_shape_path, widget2))
        layout2.addWidget(widget2)
        layout2.addWidget(browse_btn2)
        param_label2 = "形状数据文件"
        self.model_params_layout.addRow(f"{param_label2}:", layout2)
        self.current_params["shape_path"] = widget2

        # 创建图像文件夹选择控件 (image+mask模式)
        layout3 = QHBoxLayout()
        widget3 = QLineEdit()
        widget3.setReadOnly(True)
        widget3.setText(default_paths['image_folder'])

        browse_btn3 = QPushButton("浏览...")
        def browse_image_folder(edt=widget3):
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择DeepLab训练图像文件夹",
                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/"
            )
            if folder:
                edt.setText(folder)
        browse_btn3.clicked.connect(functools.partial(browse_image_folder, widget3))
        layout3.addWidget(widget3)
        layout3.addWidget(browse_btn3)
        param_label3 = "图像文件夹"
        row_index3 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label3}:", layout3)
        self.current_params["image_folder"] = widget3
        # 初始时隐藏（image+shape模式）
        widget3.setVisible(False)
        browse_btn3.setVisible(False)
        # 获取并隐藏标签
        label_item3 = self.model_params_layout.itemAt(row_index3, QFormLayout.LabelRole)
        if label_item3 and label_item3.widget():
            label_item3.widget().setVisible(False)

        # 创建掩膜文件夹选择控件 (image+mask模式)
        layout4 = QHBoxLayout()
        widget4 = QLineEdit()
        widget4.setReadOnly(True)
        widget4.setText(default_paths['mask_folder'])

        browse_btn4 = QPushButton("浏览...")
        def browse_mask_folder(edt=widget4):
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择DeepLab训练掩膜文件夹",
                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask/"
            )
            if folder:
                edt.setText(folder)
        browse_btn4.clicked.connect(functools.partial(browse_mask_folder, widget4))
        layout4.addWidget(widget4)
        layout4.addWidget(browse_btn4)
        param_label4 = "掩膜文件夹"
        row_index4 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label4}:", layout4)
        self.current_params["mask_folder"] = widget4
        # 初始时隐藏（image+shape模式）
        widget4.setVisible(False)
        browse_btn4.setVisible(False)
        # 获取并隐藏标签
        label_item4 = self.model_params_layout.itemAt(row_index4, QFormLayout.LabelRole)
        if label_item4 and label_item4.widget():
            label_item4.widget().setVisible(False)

    def on_deeplab_data_mode_changed(self, mode):
        """DeepLab数据模式切换处理"""
        if not hasattr(self, 'current_params'):
            return

        is_folder_mode = (mode == "image+mask")

        # 切换单文件模式控件的可见性
        single_file_params = ['image_path', 'shape_path']
        for param_name in single_file_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 获取包含widget的layout
                for i in range(self.model_params_layout.rowCount()):
                    if self.model_params_layout.itemAt(i, QFormLayout.FieldRole):
                        field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                        if hasattr(field_item, 'layout') and field_item.layout():
                            # 检查layout中是否包含我们的widget
                            layout = field_item.layout()
                            for j in range(layout.count()):
                                if layout.itemAt(j) and layout.itemAt(j).widget() == widget:
                                    # 找到了包含widget的layout，设置整个layout的可见性
                                    for k in range(layout.count()):
                                        item_widget = layout.itemAt(k).widget() if layout.itemAt(k) else None
                                        if item_widget:  # 检查widget是否存在
                                            item_widget.setVisible(not is_folder_mode)
                                    # 设置标签的可见性
                                    label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                                    if label_item and label_item.widget():
                                        label_item.widget().setVisible(not is_folder_mode)
                                    break

        # 切换文件夹模式控件的可见性
        folder_params = ['image_folder', 'mask_folder']
        for param_name in folder_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 获取包含widget的layout
                for i in range(self.model_params_layout.rowCount()):
                    if self.model_params_layout.itemAt(i, QFormLayout.FieldRole):
                        field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                        if hasattr(field_item, 'layout') and field_item.layout():
                            # 检查layout中是否包含我们的widget
                            layout = field_item.layout()
                            for j in range(layout.count()):
                                if layout.itemAt(j) and layout.itemAt(j).widget() == widget:
                                    # 找到了包含widget的layout，设置整个layout的可见性
                                    for k in range(layout.count()):
                                        item_widget = layout.itemAt(k).widget() if layout.itemAt(k) else None
                                        if item_widget:  # 检查widget是否存在
                                            item_widget.setVisible(is_folder_mode)
                                    # 设置标签的可见性
                                    label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                                    if label_item and label_item.widget():
                                        label_item.widget().setVisible(is_folder_mode)
                                    break

    def update_save_path_for_model(self, model_name):
        """根据模型类型更新保存路径的默认扩展名"""
        base_dir = "D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models/"

        # 定义不同模型的文件扩展名
        model_extensions = {
            "UNet": ".h5",
            "ResNet": ".h5",
            "DeepLabV3+": ".h5",
            "FCN": ".h5",
            "LoGCAN": ".ckpt"
        }

        extension = model_extensions.get(model_name, ".h5")
        default_filename = f"{model_name.lower()}_model{extension}"

        # 总是更新为对应模型的正确扩展名
        new_path = os.path.join(base_dir, default_filename)
        self.save_path_edit.setText(new_path)

    def browse_save_file(self):
        """选择模型保存文件名"""
        current_model = self.model_combo.currentText()

        # 根据模型类型设置文件过滤器
        if current_model == "LoGCAN":
            file_filter = "PyTorch Checkpoint (*.ckpt);;All Files (*)"
            default_name = "logcan_model.ckpt"
        else:
            file_filter = "HDF5 Model (*.h5);;All Files (*)"
            default_name = f"{current_model.lower()}_model.h5"

        current_path = self.save_path_edit.text()
        if not current_path:
            current_path = f"D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models/{default_name}"

        path, _ = QFileDialog.getSaveFileName(
            self,
            f"选择{current_model}模型保存文件",
            current_path,
            file_filter
        )
        if path:
            self.save_path_edit.setText(path)

    def browse_pretrained_path(self):
        """选择预训练模型文件"""
        current_model = self.model_combo.currentText()

        # 只有UNet支持继续训练
        if current_model != "UNet":
            QMessageBox.information(self, "提示", "目前只有UNet模型支持从预训练模型继续训练")
            return

        # 设置文件过滤器
        file_filter = "HDF5 Model (*.h5);;All Files (*)"

        # 默认目录
        default_dir = "D:/Satellite_Image_Auto_Offset/Autooffset_Test/trained_models/"

        path, _ = QFileDialog.getOpenFileName(
            self,
            "选择预训练UNet模型文件",
            default_dir,
            file_filter
        )
        if path:
            self.pretrained_path_edit.setText(path)

    def on_batch_size_changed(self, value):
        """当批次大小改变时提供建议"""
        if value > 4:
            self.batch_size.setStyleSheet("QSpinBox { background-color: #fff3cd; }")
            self.batch_size.setToolTip("批次大小较大，可能导致内存不足。建议使用1-4")
        else:
            self.batch_size.setStyleSheet("")
            self.batch_size.setToolTip("推荐的批次大小，有助于避免内存问题")

    def show_memory_optimization_tips(self):
        """显示内存优化建议"""
        tips = """
🚀 内存优化建议：

📊 推荐参数设置：
• Batch大小: 1-2 (避免内存溢出)
• 切片大小: 128 (减少内存占用)
• 重叠大小: 16-32 (适中即可)

⚡ 如果仍然遇到内存问题：
• 将Batch大小设为1
• 将切片大小减少到64
• 关闭其他占用内存的程序
• 考虑使用更少的训练样本

💡 性能提示：
• 小批次大小会增加训练时间但减少内存使用
• 较小的切片大小会生成更多样本但单个样本更小
• 建议在训练前关闭浏览器等大内存程序

🔧 系统要求：
• 推荐至少8GB RAM
• 如果使用GPU，确保有足够的显存
        """
        QMessageBox.information(self, "内存优化建议", tips)

    def show_learning_rate_tips(self):
        """显示不同模型的学习率建议"""
        current_model = self.model_combo.currentText()

        if current_model == "LoGCAN":
            tips = """
🎯 LoGCAN学习率建议：

📈 推荐设置：
• 主学习率: 0.001 (1e-3) ⭐ 推荐
• 适用于二分类建筑物分割
• 使用Adam优化器，收敛更稳定

🔧 调优建议：
• 如果收敛太慢: 尝试 0.002 (2e-3)
• 如果训练不稳定: 尝试 0.0005 (5e-4)
• 多分类任务: 建议 0.0005 (5e-4)

💡 注意事项：
• 骨干网络学习率自动设为主学习率的1/10
• 使用余弦退火调度器，学习率会自动衰减
• 建议训练30轮，避免过拟合

🎯 快速设置：点击确定后，学习率将自动设为0.001
            """
            # 自动设置推荐学习率
            reply = QMessageBox.question(self, "LoGCAN学习率建议", tips,
                                       QMessageBox.Ok | QMessageBox.Cancel)
            if reply == QMessageBox.Ok:
                self.learning_rate.setValue(0.001)

        elif current_model == "UNet":
            tips = """
🎯 UNet学习率建议：

📈 推荐设置：
• 学习率: 0.0001 (1e-4) ⭐ 推荐
• 适用于各种分割任务
• 使用Adam优化器

🔧 调优建议：
• 大数据集: 0.0001 (1e-4)
• 小数据集: 0.0005 (5e-4)
• 精细调优: 0.00005 (5e-5)

🎯 快速设置：点击确定后，学习率将自动设为0.0001
            """
            reply = QMessageBox.question(self, "UNet学习率建议", tips,
                                       QMessageBox.Ok | QMessageBox.Cancel)
            if reply == QMessageBox.Ok:
                self.learning_rate.setValue(0.0001)

        else:
            tips = f"""
🎯 {current_model}学习率建议：

📈 通用建议：
• 深度学习模型: 0.0001 - 0.001
• 从较小值开始: 0.0001
• 根据训练效果调整

🔧 调优策略：
• 损失不下降: 增大学习率
• 训练不稳定: 减小学习率
• 观察前几轮的训练曲线

🎯 快速设置：点击确定后，学习率将自动设为0.0001
            """
            reply = QMessageBox.question(self, f"{current_model}学习率建议", tips,
                                       QMessageBox.Ok | QMessageBox.Cancel)
            if reply == QMessageBox.Ok:
                self.learning_rate.setValue(0.0001)

    def auto_adjust_learning_rate_for_model(self, model_name):
        """根据模型类型自动调整推荐学习率"""
        if model_name == "LoGCAN":
            # LoGCAN推荐使用较高的学习率
            self.learning_rate.setValue(0.001)
            self.epochs.setValue(30)  # 同时调整推荐训练轮数
        elif model_name == "UNet":
            # UNet推荐使用较低的学习率
            self.learning_rate.setValue(0.0001)
            self.epochs.setValue(100)
        elif model_name in ["DeepLabV3+", "ResNet", "FCN"]:
            # 其他模型使用中等学习率
            self.learning_rate.setValue(0.0001)
            self.epochs.setValue(100)

    def _parse_class_mapping(self, class_mapping_str):
        """解析类别映射字符串，借鉴DeepLab的类别映射机制"""
        if not class_mapping_str or class_mapping_str.strip() == "":
            return {}

        try:
            # 尝试解析JSON格式
            import json
            return json.loads(class_mapping_str)
        except:
            # 如果JSON解析失败，尝试简单的键值对格式
            try:
                mapping = {}
                pairs = class_mapping_str.split(',')
                for pair in pairs:
                    if ':' in pair:
                        key, value = pair.split(':', 1)
                        mapping[key.strip()] = int(value.strip())
                return mapping
            except:
                print(f"警告: 无法解析类别映射 '{class_mapping_str}'，使用空映射")
                return {}

    def validate_parameters(self):
        """验证参数有效性"""
        errors = []

        # 检查学习率范围
        if not 1e-6 <= self.learning_rate.value() <= 1e-2:
            errors.append("学习率应在0.000001到0.01之间")

        # 检查重叠率不超过切片大小
        if self.overlap.value() >= self.slice_size.value():
            errors.append("重叠大小不能超过切片尺寸")

        # 检查保存文件名
        save_path = self.save_path_edit.text()
        if not save_path:
            errors.append("请选择模型保存文件名")
        else:
            # 检查父目录是否存在且可写
            save_dir = os.path.dirname(save_path)
            if not os.path.exists(save_dir):
                try:
                    os.makedirs(save_dir, exist_ok=True)
                except Exception as e:
                    errors.append(f"创建保存目录失败：{str(e)}")
            if not os.access(save_dir, os.W_OK):
                errors.append("保存目录无写入权限")

        # 根据不同模型添加特定的验证
        current_model = self.model_combo.currentText()
        if current_model == "UNet":
            init_features = self.current_params["init_features"].value()
            if not (init_features & (init_features-1) == 0):
                errors.append("初始特征数应为2的幂次（如32、64等）")

            # 检查数据模式
            data_mode = self.current_params.get("data_mode")
            if data_mode and data_mode.currentText() == "image+mask":
                # image+mask模式验证
                image_folder = self.current_params.get("image_folder")
                mask_folder = self.current_params.get("mask_folder")

                if image_folder:
                    folder_path = image_folder.text()
                    if not folder_path:
                        errors.append("请选择图像文件夹")
                    elif not os.path.exists(folder_path):
                        errors.append("选择的图像文件夹不存在")
                    elif not os.path.isdir(folder_path):
                        errors.append("图像路径必须是文件夹")
                    else:
                        # 检查文件夹中是否有图像文件
                        image_files = [f for f in os.listdir(folder_path)
                                     if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]
                        if not image_files:
                            errors.append("图像文件夹中没有找到支持的图像文件")

                if mask_folder:
                    folder_path = mask_folder.text()
                    if not folder_path:
                        errors.append("请选择掩膜文件夹")
                    elif not os.path.exists(folder_path):
                        errors.append("选择的掩膜文件夹不存在")
                    elif not os.path.isdir(folder_path):
                        errors.append("掩膜路径必须是文件夹")
                    else:
                        # 检查文件夹中是否有掩膜文件
                        mask_files = [f for f in os.listdir(folder_path)
                                    if f.lower().endswith(('.tif', '.tiff', '.png'))]
                        if not mask_files:
                            errors.append("掩膜文件夹中没有找到支持的掩膜文件")
            else:
                # image+shape模式验证
                image_path = self.current_params["image_path"].text()
                if not image_path:
                    errors.append("请选择UNet训练图像数据文件")
                elif not os.path.exists(image_path):
                    errors.append("选择的图像数据文件不存在")
                elif not os.path.isfile(image_path):
                    errors.append("图像数据路径必须是文件")

                # 验证形状数据路径
                shape_path = self.current_params["shape_path"].text()
                if not shape_path:
                    errors.append("请选择UNet训练形状数据文件")
                elif not os.path.exists(shape_path):
                    errors.append("选择的形状数据文件不存在")
                elif not os.path.isfile(shape_path):
                    errors.append("形状数据路径必须是文件")

        elif current_model == "DeepLabV3+":
            # DeepLabV3+根据数据模式进行不同的验证
            data_mode = self.current_params.get("data_mode", None)
            if data_mode:
                current_data_mode = data_mode.currentText()

                if current_data_mode == "image+shape":
                    # image+shape模式验证
                    image_path = self.current_params["image_path"].text()
                    if not image_path:
                        errors.append("请选择DeepLab训练图像数据文件")
                    elif not os.path.exists(image_path):
                        errors.append("选择的图像数据文件不存在")
                    elif not os.path.isfile(image_path):
                        errors.append("图像数据路径必须是文件")

                    # 验证形状数据路径
                    shape_path = self.current_params["shape_path"].text()
                    if not shape_path:
                        errors.append("请选择DeepLab训练形状数据文件")
                    elif not os.path.exists(shape_path):
                        errors.append("选择的形状数据文件不存在")
                    elif not os.path.isfile(shape_path):
                        errors.append("形状数据路径必须是文件")

                elif current_data_mode == "image+mask":
                    # image+mask模式验证
                    image_folder = self.current_params["image_folder"].text()
                    if not image_folder:
                        errors.append("请选择DeepLab训练图像文件夹")
                    elif not os.path.exists(image_folder):
                        errors.append("选择的图像文件夹不存在")
                    elif not os.path.isdir(image_folder):
                        errors.append("图像路径必须是文件夹")

                    # 验证掩膜文件夹路径
                    mask_folder = self.current_params["mask_folder"].text()
                    if not mask_folder:
                        errors.append("请选择DeepLab训练掩膜文件夹")
                    elif not os.path.exists(mask_folder):
                        errors.append("选择的掩膜文件夹不存在")
                    elif not os.path.isdir(mask_folder):
                        errors.append("掩膜路径必须是文件夹")
            else:
                errors.append("DeepLab数据模式未设置")

        elif current_model in ["ResNet", "FCN"]:
            # 验证图像数据路径
            image_path = self.current_params["image_path"].text()
            if not image_path:
                errors.append(f"请选择{current_model}训练图像数据文件")
            elif not os.path.exists(image_path):
                errors.append("选择的图像数据文件不存在")
            elif not os.path.isfile(image_path):
                errors.append("图像数据路径必须是文件")

            # 验证形状数据路径
            shape_path = self.current_params["shape_path"].text()
            if not shape_path:
                errors.append(f"请选择{current_model}训练形状数据文件")
            elif not os.path.exists(shape_path):
                errors.append("选择的形状数据文件不存在")
            elif not os.path.isfile(shape_path):
                errors.append("形状数据路径必须是文件")

        elif current_model == "LoGCAN":
            # LoGCAN根据数据模式进行不同的验证
            data_mode = self.current_params.get("data_mode", None)
            if data_mode:
                current_data_mode = data_mode.currentText()

                if current_data_mode == "image+shape":
                    # image+shape模式验证
                    image_path = self.current_params["image_path"].text()
                    if not image_path:
                        errors.append("请选择LoGCAN训练图像数据文件")
                    elif not os.path.exists(image_path):
                        errors.append("选择的图像数据文件不存在")
                    elif not os.path.isfile(image_path):
                        errors.append("图像数据路径必须是文件")

                    # 验证形状数据路径
                    shape_path = self.current_params["shape_path"].text()
                    if not shape_path:
                        errors.append("请选择LoGCAN训练形状数据文件")
                    elif not os.path.exists(shape_path):
                        errors.append("选择的形状数据文件不存在")
                    elif not os.path.isfile(shape_path):
                        errors.append("形状数据路径必须是文件")

                elif current_data_mode == "image+mask":
                    # image+mask模式验证
                    image_folder = self.current_params.get("image_folder")
                    mask_folder = self.current_params.get("mask_folder")

                    if image_folder:
                        folder_path = image_folder.text()
                        if not folder_path:
                            errors.append("请选择图像文件夹")
                        elif not os.path.exists(folder_path):
                            errors.append("选择的图像文件夹不存在")
                        elif not os.path.isdir(folder_path):
                            errors.append("图像路径必须是文件夹")
                        else:
                            # 检查文件夹中是否有图像文件
                            image_files = [f for f in os.listdir(folder_path)
                                         if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]
                            if not image_files:
                                errors.append("图像文件夹中没有找到支持的图像文件")

                    if mask_folder:
                        folder_path = mask_folder.text()
                        if not folder_path:
                            errors.append("请选择掩膜文件夹")
                        elif not os.path.exists(folder_path):
                            errors.append("选择的掩膜文件夹不存在")
                        elif not os.path.isdir(folder_path):
                            errors.append("掩膜路径必须是文件夹")
                        else:
                            # 检查文件夹中是否有掩膜文件
                            mask_files = [f for f in os.listdir(folder_path)
                                        if f.lower().endswith(('.tif', '.tiff', '.png'))]
                            if not mask_files:
                                errors.append("掩膜文件夹中没有找到支持的掩膜文件")
            else:
                errors.append("LoGCAN模型缺少数据模式设置")

        if errors:
            QMessageBox.critical(self, "参数错误", "\n".join(errors))
            return False
        return True

    # 已移除rsseg相关验证代码，LogCAN现在使用独立的训练系统

    def create_logcan_file_selection_widgets(self, current_model):
        """创建LoGCAN的文件选择控件，确保位置固定"""
        import functools

        # 尝试读取LoGCAN配置文件获取默认路径
        logcan_config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "config",
            "logcan_config.json"
        )

        default_paths = {
            'image_path': "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/satellite_20250805_150742.tif",
            'shape_path': "D:/Satellite_Image_Auto_Offset/GIS Data 20250106/GIS Data 20250106/Building_A.shp",
            'image_folder': "d:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image",
            'mask_folder': "d:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask"
        }

        if os.path.exists(logcan_config_path):
            try:
                with open(logcan_config_path, 'r', encoding='utf-8') as f:
                    logcan_config = json.load(f)
                    # 正确读取paths配置
                    paths_config = logcan_config.get('paths', {})

                    # 更新默认路径
                    if 'image_path' in paths_config and paths_config['image_path']:
                        default_paths['image_path'] = paths_config['image_path']
                    if 'shape_path' in paths_config and paths_config['shape_path']:
                        default_paths['shape_path'] = paths_config['shape_path']
                    if 'image_folder' in paths_config and paths_config['image_folder']:
                        default_paths['image_folder'] = paths_config['image_folder']
                    if 'mask_folder' in paths_config and paths_config['mask_folder']:
                        default_paths['mask_folder'] = paths_config['mask_folder']

                    print(f"✅ 从配置文件加载路径:")
                    print(f"   image_folder: {default_paths['image_folder']}")
                    print(f"   mask_folder: {default_paths['mask_folder']}")

            except Exception as e:
                print(f"⚠️ 读取LoGCAN配置文件失败，使用默认路径: {e}")

        # 创建图像文件选择控件 (image+shape模式)
        layout1 = QHBoxLayout()
        widget1 = QLineEdit()
        widget1.setReadOnly(True)
        widget1.setText(default_paths['image_path'])
        browse_btn1 = QPushButton("浏览...")
        def browse_image_file(edt=widget1):
            file, _ = QFileDialog.getOpenFileName(
                self,
                "选择LoGCAN训练图像数据文件",
                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image/",
                "TIFF Files (*.tif *.tiff)"
            )
            if file:
                edt.setText(file)
        browse_btn1.clicked.connect(functools.partial(browse_image_file, widget1))
        layout1.addWidget(widget1)
        layout1.addWidget(browse_btn1)
        param_label1 = "图像数据文件"
        row_index1 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label1}:", layout1)
        self.current_params["image_path"] = widget1
        # 初始时隐藏（默认image+mask模式）
        widget1.setVisible(False)
        browse_btn1.setVisible(False)
        # 获取并隐藏标签
        label_item1 = self.model_params_layout.itemAt(row_index1, QFormLayout.LabelRole)
        if label_item1 and label_item1.widget():
            label_item1.widget().setVisible(False)

        # 创建形状文件选择控件 (image+shape模式)
        layout2 = QHBoxLayout()
        widget2 = QLineEdit()
        widget2.setReadOnly(True)
        widget2.setText(default_paths['shape_path'])
        browse_btn2 = QPushButton("浏览...")
        def browse_shape_file(edt=widget2):
            file, _ = QFileDialog.getOpenFileName(
                self,
                "选择LoGCAN训练形状数据文件",
                edt.text() or "D:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/shape/",
                "Shapefile (*.shp)"
            )
            if file:
                edt.setText(file)
        browse_btn2.clicked.connect(functools.partial(browse_shape_file, widget2))
        layout2.addWidget(widget2)
        layout2.addWidget(browse_btn2)
        param_label2 = "形状数据文件"
        row_index2 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label2}:", layout2)
        self.current_params["shape_path"] = widget2
        # 初始时隐藏（默认image+mask模式）
        widget2.setVisible(False)
        browse_btn2.setVisible(False)
        # 获取并隐藏标签
        label_item2 = self.model_params_layout.itemAt(row_index2, QFormLayout.LabelRole)
        if label_item2 and label_item2.widget():
            label_item2.widget().setVisible(False)

        # 创建图像文件夹选择控件 (image+mask模式)
        layout3 = QHBoxLayout()
        widget3 = QLineEdit()
        widget3.setReadOnly(True)
        widget3.setText(default_paths['image_folder'])
        browse_btn3 = QPushButton("浏览...")
        def browse_image_folder(edt=widget3):
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择LoGCAN训练图像文件夹",
                edt.text() or "d:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/image"
            )
            if folder:
                edt.setText(folder)
        browse_btn3.clicked.connect(functools.partial(browse_image_folder, widget3))
        layout3.addWidget(widget3)
        layout3.addWidget(browse_btn3)
        param_label3 = "图像文件夹"
        row_index3 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label3}:", layout3)
        self.current_params["image_folder"] = widget3
        # 初始时可见（默认image+mask模式）
        widget3.setVisible(True)
        browse_btn3.setVisible(True)
        # 获取并显示标签
        label_item3 = self.model_params_layout.itemAt(row_index3, QFormLayout.LabelRole)
        if label_item3 and label_item3.widget():
            label_item3.widget().setVisible(True)

        # 创建掩膜文件夹选择控件 (image+mask模式)
        layout4 = QHBoxLayout()
        widget4 = QLineEdit()
        widget4.setReadOnly(True)
        widget4.setText(default_paths['mask_folder'])
        browse_btn4 = QPushButton("浏览...")
        def browse_mask_folder(edt=widget4):
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择LoGCAN训练掩膜文件夹",
                edt.text() or "d:/Satellite_Image_Auto_Offset/Autooffset_Test/dataset/mask"
            )
            if folder:
                edt.setText(folder)
        browse_btn4.clicked.connect(functools.partial(browse_mask_folder, widget4))
        layout4.addWidget(widget4)
        layout4.addWidget(browse_btn4)
        param_label4 = "掩膜文件夹"
        row_index4 = self.model_params_layout.rowCount()
        self.model_params_layout.addRow(f"{param_label4}:", layout4)
        self.current_params["mask_folder"] = widget4
        # 初始时可见（默认image+mask模式）
        widget4.setVisible(True)
        browse_btn4.setVisible(True)
        # 获取并显示标签
        label_item4 = self.model_params_layout.itemAt(row_index4, QFormLayout.LabelRole)
        if label_item4 and label_item4.widget():
            label_item4.widget().setVisible(True)

    def on_logcan_data_mode_changed(self, mode):
        """LoGCAN数据模式切换处理"""
        if not hasattr(self, 'current_params'):
            return

        is_folder_mode = (mode == "image+mask")

        # 切换单文件模式控件的可见性
        single_file_params = ['image_path', 'shape_path']
        for param_name in single_file_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 获取包含widget的layout
                for i in range(self.model_params_layout.rowCount()):
                    if self.model_params_layout.itemAt(i, QFormLayout.FieldRole):
                        field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                        if hasattr(field_item, 'layout') and field_item.layout():
                            # 检查layout中是否包含我们的widget
                            layout = field_item.layout()
                            for j in range(layout.count()):
                                if layout.itemAt(j) and layout.itemAt(j).widget() == widget:
                                    # 找到了包含widget的layout，设置整个layout的可见性
                                    for k in range(layout.count()):
                                        item_widget = layout.itemAt(k).widget() if layout.itemAt(k) else None
                                        if item_widget:  # 检查widget是否存在
                                            item_widget.setVisible(not is_folder_mode)
                                    # 设置标签的可见性
                                    label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                                    if label_item and label_item.widget():
                                        label_item.widget().setVisible(not is_folder_mode)
                                    break

        # 切换文件夹模式控件的可见性
        folder_params = ['image_folder', 'mask_folder']
        for param_name in folder_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 获取包含widget的layout
                for i in range(self.model_params_layout.rowCount()):
                    if self.model_params_layout.itemAt(i, QFormLayout.FieldRole):
                        field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                        if hasattr(field_item, 'layout') and field_item.layout():
                            # 检查layout中是否包含我们的widget
                            layout = field_item.layout()
                            for j in range(layout.count()):
                                if layout.itemAt(j) and layout.itemAt(j).widget() == widget:
                                    # 找到了包含widget的layout，设置整个layout的可见性
                                    for k in range(layout.count()):
                                        item_widget = layout.itemAt(k).widget() if layout.itemAt(k) else None
                                        if item_widget:  # 检查widget是否存在
                                            item_widget.setVisible(is_folder_mode)
                                    # 设置标签的可见性
                                    label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                                    if label_item and label_item.widget():
                                        label_item.widget().setVisible(is_folder_mode)
                                    break

        # 刷新布局
        self.model_params_group.updateGeometry()
        self.model_params_group.adjustSize()
        self.updateGeometry()
        self.adjustSize()
        QApplication.processEvents()

    def on_logcan_multiclass_mode_changed(self, state):
        """LoGCAN多分类模式切换处理"""
        if not hasattr(self, 'current_params'):
            return

        is_multiclass = (state == 2)  # Qt.Checked = 2

        # 控制多分类相关参数的可见性
        multiclass_params = ['num_classes', 'class_names', 'shape_attribute_field', 'class_mapping']
        for param_name in multiclass_params:
            if param_name in self.current_params:
                widget = self.current_params[param_name]
                # 查找对应的标签和控件
                for i in range(self.model_params_layout.rowCount()):
                    field_item = self.model_params_layout.itemAt(i, QFormLayout.FieldRole)
                    if field_item and field_item.widget() == widget:
                        # 设置控件可见性
                        widget.setVisible(is_multiclass)
                        # 设置标签可见性
                        label_item = self.model_params_layout.itemAt(i, QFormLayout.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setVisible(is_multiclass)
                        break
