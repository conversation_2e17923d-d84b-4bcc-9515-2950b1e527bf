import geopandas as gpd
from shapely.geometry import Point, Polygon
import numpy as np
from shapely import make_valid
import time
from rtree import index

def find_nearest_valid_grid(point, grid_gdf, obstacles_idx, obstacles_gdf, max_distance=100):
    buffer_distance = 25  # 使用网格大小作为初始缓冲区
    max_attempts = 10  # 最大尝试次数
    
    for _ in range(max_attempts):
        buffer = point.buffer(buffer_distance)
        nearby_grids = grid_gdf[grid_gdf.intersects(buffer)]
        
        if nearby_grids.empty:
            buffer_distance *= 2
            continue
        
        possible_obstacles = list(obstacles_idx.intersection(buffer.bounds))
        valid_grids = nearby_grids[~nearby_grids.geometry.apply(lambda g: any(obstacles_gdf.loc[possible_obstacles].intersects(g)))]

        if not valid_grids.empty:
            distances = valid_grids.geometry.centroid.distance(point)
            nearest_point = valid_grids.iloc[distances.argmin()].geometry.centroid
            if nearest_point.distance(point) <= max_distance:
                return nearest_point
        
        buffer_distance *= 2
    
    return None

def safe_read_file(file_path):
    try:
        print(f"正在读取文件: {file_path}")
        start_time = time.time()
        gdf = gpd.read_file(file_path)
        print(f"文件读取完成，耗时: {time.time() - start_time:.2f} 秒")
        return gdf
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return None

def repair_geometries(gdf):
    print("正在修复几何图形...")
    start_time = time.time()
    total = len(gdf)
    repaired_geometries = []
    for i, geom in enumerate(gdf.geometry):
        if i % 100 == 0:
            print(f"已处理 {i}/{total} 个几何图形，耗时: {time.time() - start_time:.2f} 秒")
        try:
            if not geom.is_valid:
                repaired_geometries.append(make_valid(geom))
            else:
                repaired_geometries.append(geom)
        except Exception as e:
            print(f"修复第 {i} 个几何图形时出错: {e}")
            repaired_geometries.append(None)
    
    gdf['geometry'] = repaired_geometries
    gdf = gdf.dropna(subset=['geometry'])
    print(f"几何图形修复完成，总耗时: {time.time() - start_time:.2f} 秒")
    return gdf

def create_spatial_index(gdf):
    print("正在创建空间索引...")
    start_time = time.time()
    idx = index.Index()
    for i, geom in enumerate(gdf.geometry):
        idx.insert(i, geom.bounds)
    print(f"空间索引创建完成，耗时: {time.time() - start_time:.2f} 秒")
    return idx

def main():
    obstacles_gdf = safe_read_file(r"D:\Satellite_Image_Auto_Offset\Shape\Obstacles\Big_Obstacles_UTM.shp")
    grid_gdf = safe_read_file(r"D:\Satellite_Image_Auto_Offset\Shape\Grid\grid.shp")
    grid_label_gdf = safe_read_file(r"D:\Satellite_Image_Auto_Offset\Shape\Grid\Grid_label.shp")

    if obstacles_gdf is None or grid_gdf is None or grid_label_gdf is None:
        print("无法读取所需的文件，程序终止。")
        return

    obstacles_gdf = repair_geometries(obstacles_gdf)
    obstacles_idx = create_spatial_index(obstacles_gdf)

    updated_points = []
    problematic_points = []  # 用于存储问题点
    normal_points = []
    points_in_obstacles = []  # 用于存储落在障碍物内的点
    total_points = len(grid_label_gdf)

    print(f"开始处理 {total_points} 个点...")
    start_time = time.time()

    for idx, row in grid_label_gdf.iterrows():
        if idx % 100 == 0:
            print(f"已处理 {idx}/{total_points} 个点，耗时: {time.time() - start_time:.2f} 秒")
        
        point = row.geometry
        
        try:
            possible_obstacles = list(obstacles_idx.intersection(point.bounds))
            if any(obstacles_gdf.loc[possible_obstacles].intersects(point)):
                points_in_obstacles.append(row)  # 将落在障碍物内的点添加到列表
                new_point = find_nearest_valid_grid(point, grid_gdf, obstacles_idx, obstacles_gdf)
                if new_point:
                    # 再次检查新点是否在障碍物外
                    possible_obstacles = list(obstacles_idx.intersection(new_point.bounds))
                    if any(obstacles_gdf.loc[possible_obstacles].intersects(new_point)):
                        print(f"警告：点 {idx} 的新位置仍在障碍物内")
                        problematic_points.append(row)  # 将问题点添加到列表
                    else:
                        updated_points.append((idx, new_point))
                else:
                    print(f"警告：无法为点 {idx} 找到有效的新位置")
                    problematic_points.append(row)  # 将问题点添加到列表
            else:
                # 点不在障碍物内，不需要更新
                normal_points.append(row)
        except Exception as e:
            print(f"处理点 {idx} 时出错: {e}")
            problematic_points.append(row)  # 将问题点添加到列表

    print(f"所有点处理完成，总耗时: {time.time() - start_time:.2f} 秒")
    print(f"更新点数量: {len(updated_points)}")
    print(f"问题点数量: {len(problematic_points)}")
    print(f"正常点数量: {len(normal_points)}")
    print(f"落在障碍物内的点数量: {len(points_in_obstacles)}")  # 打印落在障碍物内的点数量

    # 创建更新后的 GeoDataFrame
    if updated_points:
        updated_indices = [idx for idx, _ in updated_points]
        updated_geometries = [point for _, point in updated_points]
        updated_grid_label_gdf = grid_label_gdf.loc[updated_indices].copy()
        updated_grid_label_gdf['geometry'] = updated_geometries

        # 保存更新后的文件
        output_path = r"D:\Satellite_Image_Auto_Offset\Shape\Grid\Updated_Grid_label.shp"
        try:
            print(f"正在保存更新后的文件到: {output_path}")
            start_time = time.time()
            updated_grid_label_gdf.to_file(output_path)
            print(f"文件保存完成，耗时: {time.time() - start_time:.2f} 秒")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        print("没有需要更新的点。")

    # 创建落在障碍物内的点的 GeoDataFrame
    if points_in_obstacles:
        points_in_obstacles_gdf = gpd.GeoDataFrame(points_in_obstacles, crs=grid_label_gdf.crs)
        print(f"发现 {len(points_in_obstacles)} 个落在障碍物内的点。")

        # 保存落在障碍物内的点文件
        obstacles_points_path = r"D:\Satellite_Image_Auto_Offset\Shape\Grid\Obstacles_Points.shp"
        try:
            print(f"正在保存落在障碍物内的点文件到: {obstacles_points_path}")
            start_time = time.time()
            points_in_obstacles_gdf.to_file(obstacles_points_path)
            print(f"落在障碍物内的点文件保存完成，耗时: {time.time() - start_time:.2f} 秒")
        except Exception as e:
            print(f"保存落在障碍物内的点文件时出错: {e}")
    else:
        print("没有发现落在障碍物内的点。")

    # 创建问题点的 GeoDataFrame
    if problematic_points:
        problematic_points_gdf = gpd.GeoDataFrame(problematic_points, crs=grid_label_gdf.crs)
        print(f"发现 {len(problematic_points)} 个问题点。")

        # 保存问题点文件
        problematic_points_path = r"D:\Satellite_Image_Auto_Offset\Shape\Grid\Problematic_Grid_label.shp"
        try:
            print(f"正在保存问题点文件到: {problematic_points_path}")
            start_time = time.time()
            problematic_points_gdf.to_file(problematic_points_path)
            print(f"问题点文件保存完成，耗时: {time.time() - start_time:.2f} 秒")
        except Exception as e:
            print(f"保存问题点文件时出错: {e}")
    else:
        print("没有发现问题点。")

if __name__ == "__main__":
    main()
