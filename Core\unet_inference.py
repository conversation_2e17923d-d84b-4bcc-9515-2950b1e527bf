"""
UNet模型推理脚本
专门用于自训练UNet模型的推理，确保与训练时的数据预处理保持一致
"""

import os
import sys
import numpy as np
import rasterio
from rasterio.merge import merge
from rasterio.transform import Affine
from tensorflow.keras.models import load_model
import tensorflow as tf

# 定义自定义损失函数和指标（与训练时保持一致）
def dice_loss(y_true, y_pred, smooth=1e-5):
    """Dice损失函数"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)
    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f)
    dice_coef = (2. * intersection + smooth) / (union + smooth)
    dice_coef = tf.clip_by_value(dice_coef, 1e-7, 1.0 - 1e-7)
    return tf.cast(1 - dice_coef, tf.float32)

def focal_loss(gamma=2., alpha=.25):
    """Focal损失函数"""
    def focal_loss_fixed(y_true, y_pred):
        epsilon = tf.keras.backend.epsilon()
        y_pred = tf.clip_by_value(y_pred, epsilon, 1. - epsilon)
        p_t = tf.where(tf.equal(y_true, 1), y_pred, 1 - y_pred)
        alpha_factor = tf.ones_like(y_true) * alpha
        alpha_t = tf.where(tf.equal(y_true, 1), alpha_factor, 1 - alpha_factor)
        cross_entropy = -tf.math.log(p_t)
        weight = alpha_t * tf.pow((1 - p_t), gamma)
        focal_loss_val = weight * cross_entropy
        focal_loss_val = tf.reduce_mean(tf.reduce_sum(focal_loss_val, axis=-1))
        focal_loss_val = tf.where(tf.math.is_finite(focal_loss_val), focal_loss_val, tf.constant(0.0))
        return tf.cast(focal_loss_val, tf.float32)
    return focal_loss_fixed

def combined_loss(y_true, y_pred):
    """组合损失函数"""
    dice = dice_loss(y_true, y_pred)
    focal = focal_loss(gamma=2., alpha=.25)(y_true, y_pred)
    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))
    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))
    combined = dice + focal
    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))
    return tf.cast(combined, tf.float32)

def iou_coef(y_true, y_pred, smooth=1e-5):
    """IoU系数"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)
    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection
    iou = (intersection + smooth) / (union + smooth)
    iou = tf.clip_by_value(iou, 1e-7, 1.0 - 1e-7)
    return tf.cast(iou, tf.float32)
from tqdm import tqdm
import matplotlib.pyplot as plt
import shutil
from scipy import ndimage
from shapely.geometry import Polygon, MultiPolygon
import geopandas as gpd
from sklearn.preprocessing import MinMaxScaler
from skimage.measure import label, regionprops
from skimage.morphology import remove_small_objects, remove_small_holes
from skimage.filters import gaussian
import cv2

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

def preprocess_image_for_unet(image):
    """
    使用与UNet训练时完全相同的预处理方法
    """
    print("使用UNet训练时的预处理方法...")

    # 确保图像是3波段
    if image.shape[2] > 3:
        image = image[:, :, :3]
        print(f"转换为3波段，形状: {image.shape}")

    # 使用与训练时相同的MinMaxScaler预处理
    minmaxscaler = MinMaxScaler()
    original_shape = image.shape
    image_reshaped = image.reshape(-1, image.shape[-1])
    image_normalized = minmaxscaler.fit_transform(image_reshaped)
    image_processed = image_normalized.reshape(original_shape)

    print(f"预处理前值范围: {image.min():.4f} - {image.max():.4f}")
    print(f"预处理后值范围: {image_processed.min():.4f} - {image_processed.max():.4f}")

    return image_processed

def get_model_input_size(model):
    """获取模型的输入尺寸"""
    try:
        if hasattr(model, 'input_shape'):
            input_shape = model.input_shape
            if isinstance(input_shape, list):
                input_shape = input_shape[0]

            if len(input_shape) >= 3:
                height, width = input_shape[1], input_shape[2]
                if height is not None and width is not None:
                    return height, width

        print("警告：无法获取模型输入尺寸，使用默认值256x256")
        return 256, 256

    except Exception as e:
        print(f"获取模型输入尺寸时出错: {str(e)}，使用默认值256x256")
        return 256, 256

def create_patches(image, patch_size=256, stride=128):
    """创建图像块"""
    height, width, _ = image.shape
    for y in range(0, height - patch_size + 1, stride):
        for x in range(0, width - patch_size + 1, stride):
            yield (image[y:y+patch_size, x:x+patch_size, :3], (y, x))

def predict_and_save_patches(model, image, output_dir, src_transform, src_crs, batch_size=16):
    """预测并保存图像块"""
    # 动态获取模型输入尺寸
    patch_height, patch_width = get_model_input_size(model)
    patch_size = patch_height
    stride = patch_size // 2

    print(f"模型输入尺寸: {patch_height}x{patch_width}")
    print(f"使用patch大小: {patch_size}x{patch_size}, 步长: {stride}")

    patches_generator = create_patches(image, patch_size=patch_size, stride=stride)
    total_patches = sum(1 for _ in create_patches(image, patch_size=patch_size, stride=stride))

    print("开始分块预测...")
    print(f"📊 总共需要预测 {total_patches} 个图像块")

    # 创建进度条，确保立即显示
    progress_bar = tqdm(total=total_patches, desc="预测进度",
                       bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]',
                       mininterval=0.1,  # 最小更新间隔
                       maxinterval=1.0,  # 最大更新间隔
                       miniters=1)       # 最小更新步数

    # 立即更新一次进度条以确保显示
    progress_bar.update(0)

    batch_patches = []
    batch_positions = []

    for i, (patch, position) in enumerate(patches_generator):
        batch_patches.append(patch)
        batch_positions.append(position)

        if len(batch_patches) == batch_size or (i + 1) == total_patches:
            predictions = model.predict(np.array(batch_patches), batch_size=batch_size, verbose=0)

            for j, (pred, (y, x)) in enumerate(zip(predictions, batch_positions)):
                output_file = os.path.join(output_dir, f'pred_{y}_{x}.tif')
                patch_transform = Affine(src_transform.a, src_transform.b, src_transform.c + src_transform.a * x,
                                       src_transform.d, src_transform.e, src_transform.f + src_transform.e * y)

                with rasterio.open(output_file, 'w', driver='GTiff',
                                 height=patch_size, width=patch_size,
                                 count=1, dtype=rasterio.float32,
                                 crs=src_crs, transform=patch_transform) as dst:
                    # 使用正确的预测结果索引
                    if len(pred.shape) == 3 and pred.shape[-1] > 1:
                        dst.write(pred[:,:,1], 1)  # 使用第二个通道（建筑物类别）
                    else:
                        dst.write(pred.squeeze(), 1)

            progress_bar.update(len(batch_patches))
            batch_patches = []
            batch_positions = []

    progress_bar.close()

def merge_predictions(input_dir, output_file, original_shape, src_transform, src_crs):
    """合并预测结果"""
    prediction_files = [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f.endswith('.tif')]

    print(f"找到 {len(prediction_files)} 个预测文件")
    print("开始合并预测结果...")

    merged_array = np.zeros(original_shape, dtype=np.float32)

    # 创建合并进度条
    merge_progress = tqdm(prediction_files, desc="合并进度",
                         bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]',
                         mininterval=0.1,
                         maxinterval=1.0,
                         miniters=1)

    for f in merge_progress:
        filename = os.path.basename(f)
        parts = filename.split('_')
        y = int(parts[1])
        x = int(parts[2].split('.')[0])

        with rasterio.open(f) as src:
            prediction = src.read(1)
            height, width = prediction.shape

            row_end = min(y + height, original_shape[0])
            col_end = min(x + width, original_shape[1])
            merged_array[y:row_end, x:col_end] = prediction[:row_end-y, :col_end-x]

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with rasterio.open(output_file, "w", driver="GTiff",
                     height=original_shape[0], width=original_shape[1],
                     count=1, dtype=rasterio.float32,
                     crs=src_crs, transform=src_transform) as dest:
        dest.write(merged_array, 1)

    print(f"合并预测结果保存到 {output_file}")

def raster_to_polygon(raster_path, output_path, threshold=None, min_area=50, simplify_tolerance=1.0, min_hole_area=10):
    """将栅格转换为多边形，自动选择最佳阈值"""
    print("\n开始将栅格转换为多边形...")

    with rasterio.open(raster_path) as src:
        mask = src.read(1)
        transform = src.transform
        crs = src.crs

    print(f"栅格值范围: {mask.min():.4f} - {mask.max():.4f}")
    print(f"栅格值统计: 均值={mask.mean():.4f}, 标准差={mask.std():.4f}")

    # 自动选择阈值
    if threshold is None:
        # 使用Otsu方法或基于统计的方法选择阈值
        from skimage.filters import threshold_otsu
        try:
            # 将值缩放到0-255范围用于Otsu
            mask_scaled = ((mask - mask.min()) / (mask.max() - mask.min()) * 255).astype(np.uint8)
            otsu_threshold = threshold_otsu(mask_scaled) / 255.0
            threshold = mask.min() + otsu_threshold * (mask.max() - mask.min())
            print(f"使用Otsu自动阈值: {threshold:.4f}")
        except:
            # 如果Otsu失败，使用均值+标准差
            threshold = mask.mean() + 0.5 * mask.std()
            print(f"使用统计阈值 (均值+0.5*标准差): {threshold:.4f}")
    else:
        print(f"使用指定阈值: {threshold:.4f}")

    # 应用阈值
    binary_mask = (mask > threshold).astype(np.uint8)
    positive_pixels = np.sum(binary_mask == 1)
    total_pixels = binary_mask.size
    positive_ratio = positive_pixels / total_pixels

    print(f"阈值 {threshold:.4f} 后的像素统计:")
    print(f"  背景像素 (0): {np.sum(binary_mask==0):,}")
    print(f"  建筑像素 (1): {positive_pixels:,}")
    print(f"  建筑像素比例: {positive_ratio:.2%}")

    # 如果建筑像素比例过高或过低，调整阈值
    if positive_ratio > 0.8:
        print("警告：建筑像素比例过高，可能需要提高阈值")
        threshold = mask.mean() + mask.std()
        binary_mask = (mask > threshold).astype(np.uint8)
        positive_pixels = np.sum(binary_mask == 1)
        positive_ratio = positive_pixels / total_pixels
        print(f"调整后阈值: {threshold:.4f}, 建筑像素比例: {positive_ratio:.2%}")
    elif positive_ratio < 0.01:
        print("警告：建筑像素比例过低，可能需要降低阈值")
        threshold = mask.mean() - 0.5 * mask.std()
        binary_mask = (mask > threshold).astype(np.uint8)
        positive_pixels = np.sum(binary_mask == 1)
        positive_ratio = positive_pixels / total_pixels
        print(f"调整后阈值: {threshold:.4f}, 建筑像素比例: {positive_ratio:.2%}")

    # 使用形态学操作清理掩码
    print("应用形态学操作...")
    binary_mask = ndimage.binary_closing(binary_mask, structure=np.ones((3,3)), iterations=2)
    binary_mask = ndimage.binary_opening(binary_mask, structure=np.ones((3,3)), iterations=1)
    binary_mask = ndimage.binary_fill_holes(binary_mask)

    # 转换为多边形
    print("转换为多边形...")
    from skimage.measure import label, regionprops
    label_image = label(binary_mask)
    regions = regionprops(label_image)

    print(f"找到 {len(regions)} 个连通区域")

    polygons = []
    for i, region in enumerate(regions):
        if region.area >= min_area:
            try:
                # 获取区域边界
                from skimage.measure import find_contours
                # 创建单个区域的掩码
                single_region_mask = (label_image == region.label).astype(np.uint8)

                # 找到轮廓
                contours = find_contours(single_region_mask, 0.5)

                for contour in contours:
                    if len(contour) >= 3:
                        # 转换坐标 (注意：find_contours返回的是(row, col)格式)
                        geo_coords = [transform * (c[1], c[0]) for c in contour]

                        # 确保多边形闭合
                        if len(geo_coords) > 2 and geo_coords[0] != geo_coords[-1]:
                            geo_coords.append(geo_coords[0])

                        if len(geo_coords) >= 4:  # 至少4个点（包括闭合点）
                            poly = Polygon(geo_coords)

                            if poly.is_valid and poly.area > 0:
                                poly = poly.simplify(simplify_tolerance, preserve_topology=True)
                                if poly.area > 0:  # 确保简化后仍有面积
                                    polygons.append(poly)

            except Exception as e:
                print(f"处理区域 {i} 时出错: {e}")
                continue

    # 保存结果
    if polygons:
        gdf = gpd.GeoDataFrame(geometry=polygons, crs=crs)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        gdf.to_file(output_path)
        print(f"生成的多边形数量: {len(polygons)}")
        print(f"多边形结果已保存到 {output_path}")

        # 计算多边形面积统计
        areas = [poly.area for poly in polygons]
        print(f"多边形面积统计:")
        print(f"  最小面积: {min(areas):.2f}")
        print(f"  最大面积: {max(areas):.2f}")
        print(f"  平均面积: {np.mean(areas):.2f}")

    else:
        print("警告：未生成任何多边形")
        print("可能的原因:")
        print("1. 阈值设置不当")
        print("2. 最小面积参数过大")
        print("3. 模型预测质量较差")
        print(f"建议尝试不同的阈值，当前使用: {threshold:.4f}")

def run_unet_inference(model_path, image_path, output_dir="unet_prediction_output"):
    """
    运行UNet模型推理

    Args:
        model_path: 训练好的UNet模型路径
        image_path: 输入图像路径
        output_dir: 输出目录
    """
    print("=" * 60)
    print("UNet模型推理")
    print("=" * 60)
    print(f"模型路径: {model_path}")
    print(f"图像路径: {image_path}")
    print(f"输出目录: {output_dir}")

    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在: {model_path}")
        return False

    if not os.path.exists(image_path):
        print(f"错误：图像文件不存在: {image_path}")
        return False

    # 创建输出目录
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)

    patches_dir = os.path.join(output_dir, "patches")
    os.makedirs(patches_dir)

    try:
        # 读取图像
        print("\n读取输入图像...")
        with rasterio.open(image_path) as src:
            image = src.read().transpose((1, 2, 0))
            src_transform = src.transform
            src_crs = src.crs
            print(f"图像形状: {image.shape}")
            print(f"图像数据类型: {image.dtype}")
            print(f"图像值范围: {image.min()} - {image.max()}")

        # 预处理图像
        print("\n预处理图像...")
        image_processed = preprocess_image_for_unet(image)

        # 加载模型
        print(f"\n加载模型: {model_path}")

        # 定义自定义对象字典
        custom_objects = {
            'combined_loss': combined_loss,
            'dice_loss': dice_loss,
            'focal_loss': focal_loss,
            'iou_coef': iou_coef
        }

        try:
            model = load_model(model_path, custom_objects=custom_objects, compile=False)
            print("✅ 成功加载UNet模型（包含自定义损失函数）")
        except Exception as e:
            print(f"⚠️ 使用自定义对象加载失败，尝试不编译加载: {e}")
            try:
                model = load_model(model_path, compile=False)
                print("✅ 成功加载UNet模型（不编译）")
            except Exception as e2:
                print(f"❌ 模型加载失败: {e2}")
                return False

        print(f"模型输入形状: {model.input_shape}")
        print(f"模型输出形状: {model.output_shape}")

        # 预测
        print("\n开始预测...")
        predict_and_save_patches(model, image_processed, patches_dir, src_transform, src_crs)

        # 合并结果
        print("\n合并预测结果...")
        merged_path = os.path.join(output_dir, "merged_prediction.tif")
        merge_predictions(patches_dir, merged_path, image.shape[:2], src_transform, src_crs)

        # 转换为多边形 - 使用自适应阈值
        print("\n转换为多边形...")
        polygon_path = os.path.join(output_dir, "predicted_buildings.shp")
        optimized_raster_to_polygon(merged_path, polygon_path, threshold=None, min_area=15, max_area=50000)  # 最小15平方米，最大5万平方米

        print("\n" + "=" * 60)
        print("推理完成！")
        print(f"合并结果: {merged_path}")
        print(f"多边形结果: {polygon_path}")
        print("=" * 60)

        return True

    except Exception as e:
        print(f"\n推理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def optimized_raster_to_polygon(raster_path, output_path, threshold=None, min_area=15, max_area=50000):
    """
    优化后的栅格转多边形函数，支持自适应阈值

    Args:
        raster_path: 输入栅格文件路径
        output_path: 输出矢量文件路径
        threshold: 二值化阈值，None表示自适应阈值
        min_area: 最小面积阈值，降低到5平方米以保留更多小建筑物
        max_area: 最大面积阈值
    """
    with rasterio.open(raster_path) as src:
        prediction = src.read(1)
        transform = src.transform
        crs = src.crs

    print(f"栅格值范围: {prediction.min():.4f} - {prediction.max():.4f}")
    print(f"栅格值统计: 均值={prediction.mean():.4f}, 标准差={prediction.std():.4f}")

    # 改进的智能阈值选择 - 更激进地捕获高亮区域
    if threshold is None:
        # 计算不同百分位数的值
        p25 = np.percentile(prediction, 25)
        p50 = np.percentile(prediction, 50)
        p75 = np.percentile(prediction, 75)
        p90 = np.percentile(prediction, 90)
        p95 = np.percentile(prediction, 95)

        print(f"百分位数统计: P25={p25:.4f}, P50={p50:.4f}, P75={p75:.4f}, P90={p90:.4f}, P95={p95:.4f}")

        # 检查数据分布特征 - 修复阈值选择逻辑
        if prediction.max() <= 1.0:
            # 归一化数据 (0-1范围)
            # 使用更保守的阈值策略，避免丢失大面积高亮区域
            if p95 > 0.9:
                # 有很多高置信度预测，使用较低阈值捕获更多区域
                threshold = min(0.3, p25 + 0.1)
                print(f"检测到高置信度预测，使用低阈值: {threshold:.4f}")
            elif p90 > 0.7:
                # 中高置信度，使用中等阈值
                threshold = min(0.4, p50)
                print(f"检测到中高置信度预测，使用中等阈值: {threshold:.4f}")
            elif p75 > 0.5:
                # 中等置信度，使用适中阈值
                threshold = min(0.5, p75 * 0.8)
                print(f"检测到中等置信度预测，使用适中阈值: {threshold:.4f}")
            else:
                # 整体较暗，使用更低的阈值
                threshold = max(p25, 0.2)
                print(f"检测到较暗图像，使用低阈值: {threshold:.4f}")
        else:
            # 非归一化数据，使用相对阈值
            if prediction.max() > 200:
                # 可能是0-255范围
                threshold = max(prediction.mean() + 0.5 * prediction.std(), prediction.max() * 0.3)
                print(f"检测到0-255范围数据，使用相对阈值: {threshold:.4f}")
            else:
                # 其他范围，使用统计阈值
                threshold = max(prediction.mean(), prediction.max() * 0.4)
                print(f"检测到其他范围数据，使用统计阈值: {threshold:.4f}")
    else:
        print(f"使用指定阈值: {threshold:.4f}")

    print(f"\n开始优化版栅格转多边形处理 (阈值: {threshold:.4f})...")

    # 轻微高斯平滑减少噪声，但保持边缘
    prediction_smooth = gaussian(prediction, sigma=0.05, preserve_range=True)
    print(f"应用轻微高斯平滑 (sigma=0.05)")

    # 应用阈值
    binary_mask = (prediction_smooth > threshold).astype(np.uint8)

    # 统计阈值后的像素
    building_pixels = np.sum(binary_mask == 1)
    total_pixels = binary_mask.size
    building_ratio = building_pixels / total_pixels

    print(f"阈值 {threshold:.4f} 后的像素统计:")
    print(f"  背景像素 (0): {np.sum(binary_mask==0):,}")
    print(f"  建筑像素 (1): {building_pixels:,}")
    print(f"  建筑像素比例: {building_ratio:.2%}")

    # 如果建筑像素比例太低，可能阈值设置过高
    if building_ratio < 0.01:  # 少于1%
        print(f"⚠️ 警告：建筑像素比例过低 ({building_ratio:.2%})，可能需要降低阈值")
        # 自动降低阈值重试
        new_threshold = threshold * 0.7
        print(f"自动降低阈值到 {new_threshold:.4f} 重试...")
        binary_mask = (prediction_smooth > new_threshold).astype(np.uint8)
        building_pixels = np.sum(binary_mask == 1)
        building_ratio = building_pixels / total_pixels
        print(f"新阈值后的建筑像素比例: {building_ratio:.2%}")

    # 温和的噪声过滤和形态学操作
    print("应用温和的噪声过滤...")

    # 1. 移除很小的噪声点（面积小于5像素的区域）
    binary_mask = remove_small_objects(binary_mask.astype(bool), min_size=5).astype(np.uint8)

    # 2. 轻微的闭运算，填充小缝隙但保持形状
    kernel = np.ones((2,2), np.uint8)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel, iterations=1)

    # 3. 填充很小的内部孔洞
    binary_mask = remove_small_holes(binary_mask.astype(bool), area_threshold=10).astype(np.uint8)

    # 标记连通区域并过滤
    labeled = label(binary_mask)
    regions = regionprops(labeled)

    print(f"找到 {len(regions)} 个连通区域")

    # 过滤区域 - 将像素面积转换为实际面积（平方米）
    valid_regions = []
    pixel_size_x = abs(transform.a)  # X方向像素大小（度）
    pixel_size_y = abs(transform.e)  # Y方向像素大小（度）

    # 将度转换为米（近似）：1度 ≈ 111000米
    pixel_area_sqm = pixel_size_x * pixel_size_y * (111000 ** 2)

    print(f"像素面积: {pixel_area_sqm:.2f} 平方米/像素")

    for region in regions:
        pixel_area = region.area
        real_area_sqm = pixel_area * pixel_area_sqm

        # 主要使用面积过滤，保持简单有效
        if min_area <= real_area_sqm <= max_area:
            # 只对非常小的区域进行额外的几何检查
            if real_area_sqm < 50:  # 只对小于50平方米的区域进行严格检查
                # 检查长宽比，只过滤极端细长的噪声
                bbox = region.bbox
                height = bbox[2] - bbox[0]
                width = bbox[3] - bbox[1]

                if height > 0 and width > 0:
                    aspect_ratio = max(height, width) / min(height, width)
                    if aspect_ratio > 20:  # 只过滤极端细长的区域（20:1）
                        continue

                # 检查实心度，只过滤明显的噪声
                if region.solidity < 0.3:  # 只过滤极度稀疏的区域
                    continue

            valid_regions.append(region)

    print(f"通过过滤的区域: {len(valid_regions)} 个")

    # 生成多边形 - 保持原始形状
    polygons = []
    for i, region in enumerate(valid_regions):
        try:
            # 创建单个区域的掩码
            single_region_mask = (labeled == region.label).astype(np.uint8)

            # 使用OpenCV找轮廓
            contours, _ = cv2.findContours(single_region_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if len(contour) >= 4:  # 至少4个点
                    # 尝试多种轮廓处理方法
                    polygon_created = False

                    # 方法1: 不进行简化，直接使用原始轮廓
                    try:
                        geo_coords = []
                        # 降采样以减少点数，但保持形状
                        step = max(1, len(contour) // 100)  # 最多100个点
                        for j in range(0, len(contour), step):
                            point = contour[j]
                            col, row = point[0][0], point[0][1]
                            x, y = transform * (col, row)
                            geo_coords.append((x, y))

                        # 确保多边形闭合
                        if len(geo_coords) > 2 and geo_coords[0] != geo_coords[-1]:
                            geo_coords.append(geo_coords[0])

                        if len(geo_coords) >= 4:
                            poly = Polygon(geo_coords)
                            if poly.is_valid and poly.area > 0:
                                polygons.append(poly)
                                polygon_created = True
                    except Exception as e:
                        pass

                    # 方法2: 如果方法1失败，尝试轻微简化
                    if not polygon_created:
                        try:
                            epsilon = 0.001 * cv2.arcLength(contour, True)  # 更轻微的简化
                            approx = cv2.approxPolyDP(contour, epsilon, True)

                            if len(approx) >= 3:
                                geo_coords = []
                                for point in approx:
                                    col, row = point[0][0], point[0][1]
                                    x, y = transform * (col, row)
                                    geo_coords.append((x, y))

                                if len(geo_coords) > 2 and geo_coords[0] != geo_coords[-1]:
                                    geo_coords.append(geo_coords[0])

                                if len(geo_coords) >= 4:
                                    poly = Polygon(geo_coords)
                                    if poly.is_valid and poly.area > 0:
                                        polygons.append(poly)
                                        polygon_created = True
                        except Exception as e:
                            pass

                    # 方法3: 如果还是失败，尝试使用凸包
                    if not polygon_created:
                        try:
                            hull = cv2.convexHull(contour)
                            if len(hull) >= 3:
                                geo_coords = []
                                for point in hull:
                                    col, row = point[0][0], point[0][1]
                                    x, y = transform * (col, row)
                                    geo_coords.append((x, y))

                                if len(geo_coords) > 2 and geo_coords[0] != geo_coords[-1]:
                                    geo_coords.append(geo_coords[0])

                                if len(geo_coords) >= 4:
                                    poly = Polygon(geo_coords)
                                    if poly.is_valid and poly.area > 0:
                                        polygons.append(poly)
                        except Exception as e:
                            continue

        except Exception as e:
            print(f"处理区域 {i} 时出错: {e}")
            continue

    print(f"生成的多边形数量: {len(polygons)}")

    # 保存结果
    if polygons:
        gdf = gpd.GeoDataFrame(geometry=polygons, crs=crs)

        # 后处理：移除小噪声多边形
        print("应用后处理噪声过滤...")
        original_count = len(gdf)

        # 计算投影面积用于精确过滤
        try:
            gdf_projected = gdf.to_crs('EPSG:3857')
            areas = gdf_projected.geometry.area

            # 移除面积小于20平方米的多边形（这些很可能是噪声）
            noise_threshold = 20  # 平方米
            valid_mask = areas >= noise_threshold
            gdf = gdf[valid_mask]

            removed_count = original_count - len(gdf)
            print(f"移除了 {removed_count} 个小于{noise_threshold}㎡的噪声多边形")

        except Exception as e:
            print(f"后处理过滤失败，保留所有多边形: {e}")

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        gdf.to_file(output_path)

        # 统计多边形面积和形状
        try:
            gdf_projected = gdf.to_crs('EPSG:3857')
            areas = gdf_projected.geometry.area
            vertex_counts = [len(geom.exterior.coords) for geom in gdf.geometry]

            print(f"多边形统计:")
            print(f"  数量: {len(polygons)}")
            print(f"  面积 (m²): 最小={areas.min():.2f}, 最大={areas.max():.2f}, 平均={areas.mean():.2f}")
            print(f"  顶点数: 最小={min(vertex_counts)}, 最大={max(vertex_counts)}, 平均={np.mean(vertex_counts):.1f}")
            print(f"  总面积: {areas.sum():.2f} m²")

            # 形状分布统计
            triangles = sum(1 for count in vertex_counts if count == 4)  # 闭合三角形有4个点
            rectangles = sum(1 for count in vertex_counts if count == 5)  # 闭合矩形有5个点
            complex_shapes = sum(1 for count in vertex_counts if count > 5)

            print(f"形状分布:")
            print(f"  三角形: {triangles} 个 ({triangles/len(polygons)*100:.1f}%)")
            print(f"  矩形: {rectangles} 个 ({rectangles/len(polygons)*100:.1f}%)")
            print(f"  复杂形状: {complex_shapes} 个 ({complex_shapes/len(polygons)*100:.1f}%)")

        except Exception as e:
            print(f"统计计算失败: {e}")

        print(f"✅ 优化版建筑物矢量化完成: {output_path}")
        return True
    else:
        print("❌ 未生成任何有效多边形")
        return False

if __name__ == "__main__":
    # 默认路径
    default_model = "trained_models/unet_model_im.h5"
    default_image = "dataset/image/satellite_20250805_150742.tif"

    if len(sys.argv) >= 3:
        model_path = sys.argv[1]
        image_path = sys.argv[2]
        output_dir = sys.argv[3] if len(sys.argv) > 3 else "unet_prediction_output"
    else:
        model_path = default_model
        image_path = default_image
        output_dir = "unet_prediction_output"

    run_unet_inference(model_path, image_path, output_dir)
