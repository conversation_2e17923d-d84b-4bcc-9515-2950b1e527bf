"""
LogCAN模型定义
重新实现LogCAN模型结构，包括backbone、segmentation head、classifier等组件
确保支持二分类和多分类
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

class ConvBNReLU(nn.Module):
    """基础卷积块"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=bias)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.relu(self.bn(self.conv(x)))

class ResNetBackbone(nn.Module):
    """ResNet骨干网络"""
    
    def __init__(self, backbone='resnet50', pretrained=True, output_stride=32):
        super().__init__()
        
        if backbone == 'resnet50':
            resnet = models.resnet50(pretrained=pretrained)
        elif backbone == 'resnet101':
            resnet = models.resnet101(pretrained=pretrained)
        else:
            raise ValueError(f"不支持的backbone: {backbone}")
        
        # 提取特征层
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool
        
        self.layer1 = resnet.layer1  # 256 channels, 1/4
        self.layer2 = resnet.layer2  # 512 channels, 1/8
        self.layer3 = resnet.layer3  # 1024 channels, 1/16
        self.layer4 = resnet.layer4  # 2048 channels, 1/32
        
        # 如果需要更小的output_stride，可以修改后面层的stride
        if output_stride == 16:
            self.layer4[0].conv2.stride = (1, 1)
            self.layer4[0].downsample[0].stride = (1, 1)
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        feat1 = self.layer1(x)  # 1/4
        feat2 = self.layer2(feat1)  # 1/8
        feat3 = self.layer3(feat2)  # 1/16
        feat4 = self.layer4(feat3)  # 1/32
        
        return [feat1, feat2, feat3, feat4]

class SpatialGatherModule(nn.Module):
    """空间聚集模块"""
    
    def __init__(self, scale=1):
        super().__init__()
        self.scale = scale
    
    def forward(self, features, probs):
        """
        features: (B, C, H, W)
        probs: (B, K, H, W) - 类别概率图
        """
        batch_size, num_classes, h, w = probs.size()
        probs = probs.view(batch_size, num_classes, -1)  # (B, K, HW)
        probs = F.softmax(self.scale * probs, dim=2)
        
        features = features.view(batch_size, features.size(1), -1)  # (B, C, HW)
        features = features.permute(0, 2, 1)  # (B, HW, C)
        
        # 聚集特征
        context = torch.matmul(probs, features)  # (B, K, C)
        context = context.permute(0, 2, 1).contiguous().unsqueeze(-1)  # (B, C, K, 1)
        
        return context

class SelfAttentionBlock(nn.Module):
    """自注意力块"""
    
    def __init__(self, key_channels, query_channels, transform_channels, out_channels):
        super().__init__()
        
        self.key_project = nn.Sequential(
            nn.Conv2d(key_channels, transform_channels, 1, bias=False),
            nn.BatchNorm2d(transform_channels),
            nn.ReLU(inplace=True)
        )
        
        self.query_project = nn.Sequential(
            nn.Conv2d(query_channels, transform_channels, 1, bias=False),
            nn.BatchNorm2d(transform_channels),
            nn.ReLU(inplace=True)
        )
        
        self.value_project = nn.Sequential(
            nn.Conv2d(key_channels, transform_channels, 1, bias=False),
            nn.BatchNorm2d(transform_channels),
            nn.ReLU(inplace=True)
        )
        
        self.out_project = nn.Sequential(
            nn.Conv2d(transform_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        self.transform_channels = transform_channels
    
    def forward(self, query_feats, key_feats, value_feats):
        batch_size = query_feats.size(0)
        
        # 投影
        query = self.query_project(query_feats)
        query = query.reshape(*query.shape[:2], -1)
        query = query.permute(0, 2, 1).contiguous()  # (B, HW, C)
        
        key = self.key_project(key_feats)
        key = key.reshape(*key.shape[:2], -1)  # (B, C, K)
        
        value = self.value_project(value_feats)
        value = value.reshape(*value.shape[:2], -1)
        value = value.permute(0, 2, 1).contiguous()  # (B, K, C)
        
        # 计算注意力
        sim_map = torch.matmul(query, key)  # (B, HW, K)
        sim_map = (self.transform_channels ** -0.5) * sim_map
        sim_map = F.softmax(sim_map, dim=-1)
        
        # 应用注意力
        context = torch.matmul(sim_map, value)  # (B, HW, C)
        context = context.permute(0, 2, 1).contiguous()
        context = context.reshape(batch_size, -1, *query_feats.shape[2:])  # (B, C, H, W)
        
        context = self.out_project(context)
        
        return context

class MRAM(nn.Module):
    """多尺度区域注意力模块"""
    
    def __init__(self, in_channels, inner_channels, num_classes, patch_size=(2, 2)):
        super().__init__()
        self.patch_size = patch_size
        
        # 特征解码器
        self.feat_decoder = nn.Conv2d(in_channels, num_classes, 1)
        
        # 注意力网络
        self.attention_net = SelfAttentionBlock(
            key_channels=in_channels,
            query_channels=in_channels,
            transform_channels=inner_channels,
            out_channels=in_channels
        )
        
        # 空间聚集
        self.spatial_gather = SpatialGatherModule()
        
        # 特征融合
        self.fusion_conv = nn.Sequential(
            ConvBNReLU(in_channels * 2, in_channels),
            nn.Dropout2d(0.1),
            ConvBNReLU(in_channels, in_channels),
            nn.Dropout2d(0.1)
        )
    
    def patch_split(self, x, patch_size):
        """分割成patch"""
        B, C, H, W = x.size()
        num_h, num_w = patch_size
        patch_h, patch_w = H // num_h, W // num_w
        
        out = x.view(B, C, num_h, patch_h, num_w, patch_w)
        out = out.permute(0, 2, 4, 1, 3, 5).contiguous()
        out = out.view(-1, C, patch_h, patch_w)
        
        return out
    
    def patch_recover(self, x, patch_size):
        """恢复patch"""
        N, C, patch_h, patch_w = x.size()
        num_h, num_w = patch_size
        H, W = num_h * patch_h, num_w * patch_w
        B = N // (num_h * num_w)
        
        out = x.view(B, num_h, num_w, C, patch_h, patch_w)
        out = out.permute(0, 3, 1, 4, 2, 5).contiguous()
        out = out.view(B, C, H, W)
        
        return out
    
    def forward(self, feat, global_center):
        # 生成预测
        pred = self.feat_decoder(feat)
        
        # 分割成patch
        patch_feat = self.patch_split(feat, self.patch_size)
        patch_pred = self.patch_split(pred, self.patch_size)
        
        # 获取局部中心
        local_center = self.spatial_gather(patch_feat, patch_pred)
        
        # 扩展全局中心
        num_h, num_w = self.patch_size
        global_center = global_center.repeat(num_h * num_w, 1, 1, 1)
        
        # 应用注意力
        new_feat = self.attention_net(patch_feat, local_center, global_center)
        
        # 恢复patch
        new_feat = self.patch_recover(new_feat, self.patch_size)
        
        # 特征融合
        out = self.fusion_conv(torch.cat([feat, new_feat], dim=1))
        
        return out

class LogCANHead(nn.Module):
    """LogCAN分割头部"""
    
    def __init__(self, in_channels=[256, 512, 1024, 2048], transform_channel=128, 
                 num_classes=2, mram_inner_channel=64):
        super().__init__()
        
        # 特征变换
        self.bottlenecks = nn.ModuleList([
            ConvBNReLU(in_ch, transform_channel) for in_ch in in_channels
        ])
        
        # 初始分类器
        self.decoder_stage1 = nn.Conv2d(transform_channel, num_classes, 1)
        
        # 空间聚集
        self.global_gather = SpatialGatherModule()
        
        # MRAM模块
        self.mram_modules = nn.ModuleList([
            MRAM(transform_channel, mram_inner_channel, num_classes) 
            for _ in range(len(in_channels))
        ])
        
        # 特征融合卷积
        self.fusion_convs = nn.ModuleList([
            ConvBNReLU(transform_channel * 2, transform_channel) 
            for _ in range(len(in_channels) - 1)
        ])
        
        # 最终分类器
        self.final_classifier = ConvBNReLU(transform_channel, num_classes)
    
    def upsample_add(self, x_small, x_big):
        """上采样并相加"""
        x_small = F.interpolate(x_small, scale_factor=2, mode='bilinear', align_corners=False)
        return torch.cat([x_small, x_big], dim=1)
    
    def forward(self, features):
        # 特征变换
        feats = [bottleneck(feat) for bottleneck, feat in zip(self.bottlenecks, features)]
        
        # 获取最高层特征的初始预测
        pred1 = self.decoder_stage1(feats[-1])
        
        # 获取全局中心
        global_center = self.global_gather(feats[-1], pred1)
        
        # 从高层到低层处理
        current_feat = feats[-1]
        current_feat = self.mram_modules[-1](current_feat, global_center)
        
        for i in range(len(feats) - 2, -1, -1):
            # 上采样并融合
            current_feat = self.fusion_convs[i](self.upsample_add(current_feat, feats[i]))
            # 应用MRAM
            current_feat = self.mram_modules[i](current_feat, global_center)
        
        # 最终分类
        out = self.final_classifier(current_feat)
        
        # 上采样到原始尺寸
        out = F.interpolate(out, scale_factor=4, mode='bilinear', align_corners=False)

        # 确保辅助输出也有正确的尺寸
        if pred1 is not None:
            pred1 = F.interpolate(pred1, size=out.shape[2:], mode='bilinear', align_corners=False)
        
        return out, pred1

class LogCANModel(nn.Module):
    """完整的LogCAN模型"""
    
    def __init__(self, num_classes=2, backbone='resnet50', transform_channel=128, 
                 pretrained=True, output_stride=32):
        super().__init__()
        
        self.backbone = ResNetBackbone(backbone, pretrained, output_stride)
        self.head = LogCANHead(
            in_channels=[256, 512, 1024, 2048],
            transform_channel=transform_channel,
            num_classes=num_classes
        )
        
        self.num_classes = num_classes
    
    def forward(self, x):
        features = self.backbone(x)
        main_out, aux_out = self.head(features)
        
        if self.training:
            return main_out, aux_out
        else:
            return main_out

def create_logcan_model(config):
    """创建LogCAN模型"""
    model_config = config.model_config
    
    model = LogCANModel(
        num_classes=model_config['num_classes'],
        backbone=model_config.get('backbone', 'resnet50'),
        transform_channel=model_config.get('transform_channel', 128),
        pretrained=model_config.get('pretrained', True),
        output_stride=model_config.get('output_stride', 32)
    )
    
    return model
