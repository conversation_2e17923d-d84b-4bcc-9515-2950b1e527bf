#!/usr/bin/env python3
"""
图像合并工具
用于合并GEE下载的分块图像文件
"""

import os
import re
import numpy as np
import rasterio
from rasterio.merge import merge
from rasterio.warp import calculate_default_transform, reproject, Resampling
import glob
from pathlib import Path


class ImageMerger:
    """图像合并器，用于合并GEE下载的分块图像"""

    def __init__(self):
        self.supported_formats = ['.tif', '.tiff']

    def find_related_images(self, image_folder, base_name=None):
        """
        查找相关的图像文件

        Args:
            image_folder: 图像文件夹路径
            base_name: 基础文件名（可选）

        Returns:
            dict: 按基础名称分组的图像文件列表
        """
        image_folder = Path(image_folder)
        if not image_folder.exists():
            raise FileNotFoundError(f"图像文件夹不存在: {image_folder}")

        # 获取所有图像文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(image_folder.glob(f"*{ext}"))

        if not image_files:
            raise FileNotFoundError(f"在文件夹中未找到图像文件: {image_folder}")

        # 按基础名称分组
        grouped_files = {}

        for img_file in image_files:
            # 提取基础名称（去除可能的分块后缀）
            name = img_file.stem

            # 常见的GEE分块模式，优先匹配tile格式
            patterns = [
                r'(tile_\d+_\d+)_img-\d+-\d+$',  # tile_x_y_img-offset1-offset2 (你的格式)
                r'(.+)-\d+-\d+-\d+$',            # name-0-0-0
                r'(.+)_\d+_\d+$',                # name_1_2
                r'(.+)-part\d+$',                # name-part1
                r'(.+)-\d+$',                    # name-1
                r'(.+)_\d+$',                    # name_1
            ]

            base = name
            for pattern in patterns:
                match = re.match(pattern, name)
                if match:
                    base = match.group(1)
                    break

            if base not in grouped_files:
                grouped_files[base] = []
            grouped_files[base].append(img_file)

        # 如果指定了base_name，只返回匹配的组
        if base_name:
            if base_name in grouped_files:
                return {base_name: grouped_files[base_name]}
            else:
                # 尝试模糊匹配
                for key in grouped_files.keys():
                    if base_name in key or key in base_name:
                        return {key: grouped_files[key]}
                raise FileNotFoundError(f"未找到匹配的图像组: {base_name}")

        return grouped_files

    def _extract_tile_base_name(self, filename):
        """提取瓦片的基础名称，如从 tile_0_0_img-0000000000 提取 tile_0_0"""
        import re

        # 匹配 tile_x_y 模式
        tile_pattern = r'(tile_\d+_\d+)'
        match = re.search(tile_pattern, filename.lower())
        if match:
            return match.group(1)

        # 如果没有匹配到tile模式，返回原始文件名
        return filename

    def merge_images(self, image_files, output_path, method='first'):
        """
        合并多个图像文件

        Args:
            image_files: 图像文件路径列表
            output_path: 输出文件路径
            method: 合并方法 ('first', 'last', 'min', 'max', 'mean')

        Returns:
            str: 输出文件路径
        """
        if not image_files:
            raise ValueError("没有提供图像文件")

        if len(image_files) == 1:
            # 只有一个文件，直接复制
            import shutil
            shutil.copy2(image_files[0], output_path)
            print(f"单个文件复制完成: {output_path}")
            return output_path

        print(f"开始合并 {len(image_files)} 个图像文件...")

        # 打开所有图像文件
        src_files = []
        try:
            for img_file in image_files:
                src = rasterio.open(img_file)
                src_files.append(src)
                print(f"  - {img_file}")

            # 合并图像
            mosaic, out_trans = merge(src_files, method=method)

            # 获取元数据
            out_meta = src_files[0].meta.copy()
            out_meta.update({
                "driver": "GTiff",
                "height": mosaic.shape[1],
                "width": mosaic.shape[2],
                "count": mosaic.shape[0],
                "transform": out_trans,
                "compress": "lzw",
                "tiled": True,
                "blockxsize": 512,
                "blockysize": 512,
                "interleave": "pixel"
            })

            # 保存合并后的图像
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with rasterio.open(output_path, "w", **out_meta) as dest:
                dest.write(mosaic)

            print(f"图像合并完成: {output_path}")
            print(f"输出图像尺寸: {mosaic.shape[1]} x {mosaic.shape[2]}")

        finally:
            # 关闭所有文件
            for src in src_files:
                src.close()

        return output_path

    def merge_folder_images(self, image_folder, output_folder, base_name=None):
        """
        合并文件夹中的所有相关图像

        Args:
            image_folder: 输入图像文件夹
            output_folder: 输出文件夹
            base_name: 指定要合并的基础名称（可选）

        Returns:
            dict: 合并结果 {base_name: output_path}
        """
        # 查找相关图像
        grouped_files = self.find_related_images(image_folder, base_name)

        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)

        results = {}

        for base, files in grouped_files.items():
            if len(files) > 1:
                # 需要合并 - 使用瓦片基础名称作为输出文件名
                tile_base = self._extract_tile_base_name(base)
                output_path = os.path.join(output_folder, f"{tile_base}_merged.tif")
                merged_path = self.merge_images(files, output_path)
                results[base] = merged_path
                print(f"✅ 合并完成: {base} -> {merged_path}")
            else:
                # 单个文件，直接复制 - 使用瓦片基础名称
                tile_base = self._extract_tile_base_name(base)
                output_path = os.path.join(output_folder, f"{tile_base}.tif")
                import shutil
                shutil.copy2(files[0], output_path)
                results[base] = output_path
                print(f"✅ 复制完成: {base} -> {output_path}")

        return results


def merge_gee_images(image_folder, output_folder, base_name=None):
    """
    便捷函数：合并GEE下载的图像

    Args:
        image_folder: GEE下载的图像文件夹路径
        output_folder: 输出文件夹路径
        base_name: 指定要合并的基础名称（可选）

    Returns:
        dict: 合并结果
    """
    merger = ImageMerger()
    return merger.merge_folder_images(image_folder, output_folder, base_name)


if __name__ == "__main__":
    # 测试代码
    import sys

    if len(sys.argv) < 3:
        print("用法: python image_merger.py <input_folder> <output_folder> [base_name]")
        sys.exit(1)

    input_folder = sys.argv[1]
    output_folder = sys.argv[2]
    base_name = sys.argv[3] if len(sys.argv) > 3 else None

    try:
        results = merge_gee_images(input_folder, output_folder, base_name)
        print(f"\n🎉 合并完成！共处理 {len(results)} 个图像组:")
        for base, path in results.items():
            print(f"  {base}: {path}")
    except Exception as e:
        print(f"❌ 合并失败: {e}")
        sys.exit(1)
