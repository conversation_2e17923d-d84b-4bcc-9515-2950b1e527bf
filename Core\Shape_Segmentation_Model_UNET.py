import os
import numpy as np
import rasterio
import geopandas as gpd
from rasterio.features import rasterize
from patchify import patchify
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import backend as K
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import ReduceLROnPlateau, ModelCheckpoint, EarlyStopping, TensorBoard, Callback
import sys
# shapely导入已移除，不再需要
import json
from datetime import datetime
import argparse
from sklearn.utils.class_weight import compute_class_weight
# from sklearn.metrics import classification_report, confusion_matrix
# import seaborn as sns  # 可选依赖，如果需要可以安装
import time

# 设置混合精度
tf.keras.mixed_precision.set_global_policy('mixed_float16')


class TrainingReporter:
    """训练报告生成器"""

    def __init__(self, save_dir, model_name="UNet"):
        self.base_save_dir = save_dir
        self.model_name = model_name
        self.start_time = None
        self.end_time = None
        self.training_info = {}
        self.dataset_info = {}
        self.model_info = {}

        # 生成带时间戳的文件夹名
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.save_dir = os.path.join(save_dir, f"{model_name}_{self.timestamp}")

        # 确保保存目录存在
        os.makedirs(self.save_dir, exist_ok=True)
        print(f"📁 训练报告将保存到: {self.save_dir}")

    def start_training(self, config, matched_pairs, train_generator, val_generator):
        """开始训练时记录信息"""
        self.start_time = time.time()

        # 记录训练配置
        self.training_info = {
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'model_name': self.model_name,
            'config': config,
            'epochs': config.get('trainer', {}).get('epochs', 'Unknown'),
            'batch_size': config.get('trainer', {}).get('batch_size', 'Unknown'),
            'learning_rate': config.get('trainer', {}).get('learning_rate', 'Unknown'),
        }

        # 记录数据集信息
        slice_size = config.get('model', {}).get('args', {}).get('slice_size', 256)
        overlap = config.get('model', {}).get('args', {}).get('overlap', 32)

        self.dataset_info = {
            'total_file_pairs': len(matched_pairs) if matched_pairs else 0,
            'file_pairs_count': len(matched_pairs) if matched_pairs else 0,  # HTML报告需要的字段
            'train_batches': len(train_generator) if train_generator else 0,
            'val_batches': len(val_generator) if val_generator else 0,
            'train_patches': getattr(train_generator, 'total_patches', 'Unknown') if train_generator else 'Unknown',
            'val_patches': getattr(val_generator, 'total_patches', 'Unknown') if val_generator else 'Unknown',
            'patch_size': f"{slice_size}×{slice_size}" if slice_size != 'Unknown' else 'Unknown',
            'overlap': f"{overlap}像素" if overlap != 'Unknown' else 'Unknown',
        }

        print(f"📊 训练报告器已启动，保存目录: {self.save_dir}")

    def record_model_info(self, model):
        """记录模型信息"""
        try:
            total_params = model.count_params()
            trainable_params = sum([K.count_params(w) for w in model.trainable_weights])
            non_trainable_params = sum([K.count_params(w) for w in model.non_trainable_weights])

            # 估算模型大小 (参数数量 * 4字节 / 1024 / 1024)
            model_size_mb = (total_params * 4) / (1024 * 1024)

            self.model_info = {
                'total_params': total_params,
                'trainable_params': trainable_params,
                'non_trainable_params': non_trainable_params,
                'model_size_mb': round(model_size_mb, 1),
                'model_layers': len(model.layers),
                'input_shape': str(model.input_shape) if hasattr(model, 'input_shape') else 'Unknown',
                'output_shape': str(model.output_shape) if hasattr(model, 'output_shape') else 'Unknown',
            }
        except Exception as e:
            print(f"⚠️  记录模型信息失败: {e}")
            self.model_info = {'error': str(e)}

    def end_training(self, history, model_save_path):
        """训练结束时生成完整报告"""
        self.end_time = time.time()
        training_duration = self.end_time - self.start_time

        # 生成训练曲线图
        self._plot_training_curves(history)

        # 生成详细报告
        self._generate_detailed_report(history, model_save_path, training_duration)

        # 生成HTML报告
        self._generate_html_report(history, model_save_path, training_duration)

        # 保存训练历史数据
        self._save_training_history(history)

        print(f"📈 训练报告已生成完成，保存在: {self.save_dir}")

    def _plot_training_curves(self, history):
        """绘制训练曲线"""
        try:
            # 设置字体和解决负号显示问题
            try:
                # 设置matplotlib后端为Agg，避免GUI相关的字体问题
                import matplotlib
                matplotlib.use('Agg')

                # 尝试设置中文字体
                import matplotlib.font_manager as fm
                import matplotlib.pyplot as plt

                # 查找可用的中文字体
                font_list = [f.name for f in fm.fontManager.ttflist]
                chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
                available_font = None

                for font in chinese_fonts:
                    if font in font_list:
                        available_font = font
                        break

                if available_font:
                    plt.rcParams['font.sans-serif'] = [available_font]
                    print(f"✅ 使用字体: {available_font}")
                else:
                    # 如果没有中文字体，使用英文字体
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    print("⚠️ 未找到中文字体，使用英文字体")

                # 解决负号显示问题
                plt.rcParams['axes.unicode_minus'] = False
                # 设置字体大小
                plt.rcParams['font.size'] = 10

            except Exception as e:
                print(f"⚠️ 字体设置失败，使用默认设置: {e}")
                # 使用默认字体
                import matplotlib.pyplot as plt
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False

            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'{self.model_name} 训练报告', fontsize=16, fontweight='bold')

            # 1. 损失曲线
            axes[0, 0].plot(history.history['loss'], label='训练损失', linewidth=2)
            axes[0, 0].plot(history.history['val_loss'], label='验证损失', linewidth=2)
            axes[0, 0].set_title('模型损失', fontsize=14)
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. IoU曲线
            if 'iou_coef' in history.history:
                axes[0, 1].plot(history.history['iou_coef'], label='训练IoU', linewidth=2)
                axes[0, 1].plot(history.history['val_iou_coef'], label='验证IoU', linewidth=2)
                axes[0, 1].set_title('IoU系数', fontsize=14)
                axes[0, 1].set_xlabel('Epoch')
                axes[0, 1].set_ylabel('IoU')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)

            # 3. 准确率曲线
            if 'accuracy' in history.history:
                axes[1, 0].plot(history.history['accuracy'], label='训练准确率', linewidth=2)
                axes[1, 0].plot(history.history['val_accuracy'], label='验证准确率', linewidth=2)
                axes[1, 0].set_title('准确率', fontsize=14)
                axes[1, 0].set_xlabel('Epoch')
                axes[1, 0].set_ylabel('Accuracy')
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)

            # 4. 学习率曲线（如果有）
            if 'lr' in history.history:
                axes[1, 1].plot(history.history['lr'], label='学习率', linewidth=2, color='orange')
                axes[1, 1].set_title('学习率变化', fontsize=14)
                axes[1, 1].set_xlabel('Epoch')
                axes[1, 1].set_ylabel('Learning Rate')
                axes[1, 1].legend()
                axes[1, 1].grid(True, alpha=0.3)
                axes[1, 1].set_yscale('log')
            else:
                # 如果没有学习率数据，显示训练总结
                axes[1, 1].axis('off')
                summary_text = self._get_training_summary(history)
                axes[1, 1].text(0.1, 0.5, summary_text, fontsize=12,
                               verticalalignment='center', transform=axes[1, 1].transAxes)

            plt.tight_layout()
            curves_path = os.path.join(self.save_dir, f'training_curves_{self.timestamp}.png')
            plt.savefig(curves_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 训练曲线图已保存: {curves_path}")

        except Exception as e:
            print(f"⚠️  绘制训练曲线失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_training_summary(self, history):
        """获取训练总结文本"""
        try:
            final_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]

            summary = f"训练总结\n"
            summary += f"━━━━━━━━━━━━━━━━\n"
            summary += f"最终训练损失: {final_loss:.4f}\n"
            summary += f"最终验证损失: {final_val_loss:.4f}\n"

            if 'iou_coef' in history.history:
                final_iou = history.history['iou_coef'][-1]
                final_val_iou = history.history['val_iou_coef'][-1]
                summary += f"最终训练IoU: {final_iou:.4f}\n"
                summary += f"最终验证IoU: {final_val_iou:.4f}\n"

            if 'accuracy' in history.history:
                final_acc = history.history['accuracy'][-1]
                final_val_acc = history.history['val_accuracy'][-1]
                summary += f"最终训练准确率: {final_acc:.4f}\n"
                summary += f"最终验证准确率: {final_val_acc:.4f}\n"

            return summary
        except Exception as e:
            return f"总结生成失败: {e}"

    def _generate_detailed_report(self, history, model_save_path, training_duration):
        """生成详细的训练报告"""
        try:
            report_path = os.path.join(self.save_dir, f'training_report_{self.timestamp}.md')

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"# {self.model_name} 训练报告\n\n")

                # 基本信息
                f.write("## 📊 基本信息\n\n")
                f.write(f"- **模型名称**: {self.model_name}\n")
                f.write(f"- **训练开始时间**: {self.training_info.get('start_time', 'Unknown')}\n")
                f.write(f"- **训练结束时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"- **训练总时长**: {self._format_duration(training_duration)}\n")
                f.write(f"- **模型保存路径**: {model_save_path}\n\n")

                # 训练配置
                f.write("## ⚙️ 训练配置\n\n")
                f.write(f"- **训练轮数**: {self.training_info.get('epochs', 'Unknown')}\n")
                f.write(f"- **批次大小**: {self.training_info.get('batch_size', 'Unknown')}\n")
                f.write(f"- **学习率**: {self.training_info.get('learning_rate', 'Unknown')}\n")
                f.write(f"- **图像块大小**: {self.dataset_info.get('patch_size', 'Unknown')}\n")
                f.write(f"- **重叠像素**: {self.dataset_info.get('overlap', 'Unknown')}\n\n")

                # 数据集信息
                f.write("## 📁 数据集信息\n\n")
                f.write(f"- **文件对数量**: {self.dataset_info.get('total_file_pairs', 'Unknown')}\n")
                f.write(f"- **训练批次数**: {self.dataset_info.get('train_batches', 'Unknown')}\n")
                f.write(f"- **验证批次数**: {self.dataset_info.get('val_batches', 'Unknown')}\n")
                f.write(f"- **训练图像块数**: {self.dataset_info.get('train_patches', 'Unknown')}\n")
                f.write(f"- **验证图像块数**: {self.dataset_info.get('val_patches', 'Unknown')}\n")
                f.write(f"- **图像块大小**: {self.dataset_info.get('patch_size', 'Unknown')}\n")
                f.write(f"- **重叠像素**: {self.dataset_info.get('overlap', 'Unknown')}\n\n")

                # 模型信息
                f.write("## 🏗️ 模型信息\n\n")
                if 'error' not in self.model_info:
                    total_params = self.model_info.get('total_params', 'Unknown')
                    trainable_params = self.model_info.get('trainable_params', 'Unknown')
                    non_trainable_params = self.model_info.get('non_trainable_params', 'Unknown')
                    model_size_mb = self.model_info.get('model_size_mb', 'Unknown')

                    f.write(f"- **总参数数**: {total_params:,}\n" if isinstance(total_params, int) else f"- **总参数数**: {total_params}\n")
                    f.write(f"- **可训练参数**: {trainable_params:,}\n" if isinstance(trainable_params, int) else f"- **可训练参数**: {trainable_params}\n")
                    f.write(f"- **不可训练参数**: {non_trainable_params:,}\n" if isinstance(non_trainable_params, int) else f"- **不可训练参数**: {non_trainable_params}\n")
                    f.write(f"- **模型大小**: {model_size_mb} MB\n" if model_size_mb != 'Unknown' else f"- **模型大小**: {model_size_mb}\n")
                    f.write(f"- **模型层数**: {self.model_info.get('model_layers', 'Unknown')}\n")
                    f.write(f"- **输入形状**: {self.model_info.get('input_shape', 'Unknown')}\n")
                    f.write(f"- **输出形状**: {self.model_info.get('output_shape', 'Unknown')}\n\n")
                else:
                    f.write(f"- **模型信息获取失败**: {self.model_info.get('error', 'Unknown')}\n\n")

                # 训练结果
                f.write("## 📈 训练结果\n\n")
                if history and history.history:
                    final_epoch = len(history.history['loss'])
                    f.write(f"- **实际训练轮数**: {final_epoch}\n")

                    # 最终指标
                    final_loss = history.history['loss'][-1]
                    final_val_loss = history.history['val_loss'][-1]
                    f.write(f"- **最终训练损失**: {final_loss:.6f}\n")
                    f.write(f"- **最终验证损失**: {final_val_loss:.6f}\n")

                    if 'iou_coef' in history.history:
                        final_iou = history.history['iou_coef'][-1]
                        final_val_iou = history.history['val_iou_coef'][-1]
                        f.write(f"- **最终训练IoU**: {final_iou:.6f}\n")
                        f.write(f"- **最终验证IoU**: {final_val_iou:.6f}\n")

                    if 'accuracy' in history.history:
                        final_acc = history.history['accuracy'][-1]
                        final_val_acc = history.history['val_accuracy'][-1]
                        f.write(f"- **最终训练准确率**: {final_acc:.6f}\n")
                        f.write(f"- **最终验证准确率**: {final_val_acc:.6f}\n")

                    # 最佳指标
                    f.write(f"\n### 🏆 最佳指标\n\n")
                    best_val_loss = min(history.history['val_loss'])
                    best_val_loss_epoch = history.history['val_loss'].index(best_val_loss) + 1
                    f.write(f"- **最佳验证损失**: {best_val_loss:.6f} (第{best_val_loss_epoch}轮)\n")

                    if 'val_iou_coef' in history.history:
                        best_val_iou = max(history.history['val_iou_coef'])
                        best_val_iou_epoch = history.history['val_iou_coef'].index(best_val_iou) + 1
                        f.write(f"- **最佳验证IoU**: {best_val_iou:.6f} (第{best_val_iou_epoch}轮)\n")

                    if 'val_accuracy' in history.history:
                        best_val_acc = max(history.history['val_accuracy'])
                        best_val_acc_epoch = history.history['val_accuracy'].index(best_val_acc) + 1
                        f.write(f"- **最佳验证准确率**: {best_val_acc:.6f} (第{best_val_acc_epoch}轮)\n")

                # 训练建议
                f.write(f"\n## 💡 训练分析与建议\n\n")
                suggestions = self._analyze_training_results(history)
                for suggestion in suggestions:
                    f.write(f"- {suggestion}\n")

                f.write(f"\n## 📊 图表文件\n\n")
                f.write(f"- 训练曲线图: `training_curves_{self.timestamp}.png`\n")
                f.write(f"- 训练历史数据: `training_history_{self.timestamp}.json`\n")

                f.write(f"\n---\n")
                f.write(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")

            print(f"✅ 详细报告已保存: {report_path}")

        except Exception as e:
            print(f"⚠️  生成详细报告失败: {e}")

    def _generate_html_report(self, history, model_save_path, training_duration):
        """生成HTML格式的训练报告"""
        try:
            html_path = os.path.join(self.save_dir, f'training_report_{self.timestamp}.html')

            # 准备训练数据
            epochs = list(range(1, len(history.history['loss']) + 1))
            train_loss = history.history['loss']
            val_loss = history.history['val_loss']
            train_iou = history.history.get('iou_coef', [])
            val_iou = history.history.get('val_iou_coef', [])
            train_acc = history.history.get('accuracy', [])
            val_acc = history.history.get('val_accuracy', [])

            # 生成HTML内容
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.model_name} 训练报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .content {{
            padding: 30px;
        }}
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .info-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }}
        .info-card h3 {{
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }}
        .info-card p {{
            margin: 5px 0;
            color: #666;
        }}
        .chart-container {{
            margin: 30px 0;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-title {{
            text-align: center;
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
            font-weight: 500;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        .metric-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .metric-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .suggestions {{
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        .suggestions h3 {{
            color: #2e7d32;
            margin-top: 0;
        }}
        .suggestions ul {{
            color: #2e7d32;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛰️ {self.model_name} 训练报告</h1>
            <p>卫星图像建筑物分割模型训练结果</p>
        </div>

        <div class="content">
            <!-- 基本信息 -->
            <div class="info-grid">
                <div class="info-card">
                    <h3>📊 训练信息</h3>
                    <p><strong>开始时间:</strong> {self.training_info.get('start_time', 'Unknown')}</p>
                    <p><strong>结束时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>训练时长:</strong> {self._format_duration(training_duration)}</p>
                    <p><strong>训练轮数:</strong> {len(history.history['loss'])}</p>
                </div>

                <div class="info-card">
                    <h3>⚙️ 模型配置</h3>
                    <p><strong>批次大小:</strong> {self.training_info.get('batch_size', 'Unknown')}</p>
                    <p><strong>学习率:</strong> {self.training_info.get('learning_rate', 'Unknown')}</p>
                    <p><strong>模型文件:</strong> {os.path.basename(model_save_path)}</p>
                </div>

                <div class="info-card">
                    <h3>📁 数据集信息</h3>
                    <p><strong>文件对数量:</strong> {self.dataset_info.get('file_pairs_count', 'Unknown')}</p>
                    <p><strong>训练图像块:</strong> {self.dataset_info.get('train_patches', 'Unknown')}</p>
                    <p><strong>验证图像块:</strong> {self.dataset_info.get('val_patches', 'Unknown')}</p>
                </div>

                <div class="info-card">
                    <h3>🔧 模型参数</h3>
                    <p><strong>总参数:</strong> {self._format_number(self.model_info.get('total_params', 'Unknown'))}</p>
                    <p><strong>可训练参数:</strong> {self._format_number(self.model_info.get('trainable_params', 'Unknown'))}</p>
                    <p><strong>模型大小:</strong> {self.model_info.get('model_size_mb', 'Unknown')} MB</p>
                </div>
            </div>

            <!-- 最终指标 -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{train_loss[-1]:.4f}</div>
                    <div class="metric-label">最终训练损失</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{val_loss[-1]:.4f}</div>
                    <div class="metric-label">最终验证损失</div>
                </div>"""

            if train_iou:
                html_content += f"""
                <div class="metric-card">
                    <div class="metric-value">{train_iou[-1]:.4f}</div>
                    <div class="metric-label">最终训练IoU</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{val_iou[-1]:.4f}</div>
                    <div class="metric-label">最终验证IoU</div>
                </div>"""

            html_content += """
            </div>

            <!-- 训练曲线图表 -->
            <div class="chart-container">
                <div class="chart-title">📈 训练损失曲线</div>
                <canvas id="lossChart" width="400" height="200"></canvas>
            </div>"""

            if train_iou:
                html_content += """
            <div class="chart-container">
                <div class="chart-title">📊 IoU系数曲线</div>
                <canvas id="iouChart" width="400" height="200"></canvas>
            </div>"""

            if train_acc:
                html_content += """
            <div class="chart-container">
                <div class="chart-title">🎯 准确率曲线</div>
                <canvas id="accChart" width="400" height="200"></canvas>
            </div>"""

            # 添加训练建议
            suggestions = self._analyze_training_results(history)
            html_content += f"""
            <div class="suggestions">
                <h3>💡 训练分析与建议</h3>
                <ul>"""

            for suggestion in suggestions:
                html_content += f"<li>{suggestion}</li>"

            html_content += """
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>报告生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
            <p>🛰️ 卫星图像自动偏移校正系统 - UNet训练模块</p>
        </div>
    </div>

    <script>
        // 损失曲线图表
        const lossCtx = document.getElementById('lossChart').getContext('2d');
        new Chart(lossCtx, {
            type: 'line',
            data: {
                labels: """ + str(epochs) + """,
                datasets: [{
                    label: '训练损失',
                    data: """ + str(train_loss) + """,
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: '验证损失',
                    data: """ + str(val_loss) + """,
                    borderColor: 'rgb(118, 75, 162)',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });"""

            if train_iou:
                html_content += """

        // IoU曲线图表
        const iouCtx = document.getElementById('iouChart').getContext('2d');
        new Chart(iouCtx, {
            type: 'line',
            data: {
                labels: """ + str(epochs) + """,
                datasets: [{
                    label: '训练IoU',
                    data: """ + str(train_iou) + """,
                    borderColor: 'rgb(76, 175, 80)',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }, {
                    label: '验证IoU',
                    data: """ + str(val_iou) + """,
                    borderColor: 'rgb(255, 152, 0)',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });"""

            if train_acc:
                html_content += """

        // 准确率曲线图表
        const accCtx = document.getElementById('accChart').getContext('2d');
        new Chart(accCtx, {
            type: 'line',
            data: {
                labels: """ + str(epochs) + """,
                datasets: [{
                    label: '训练准确率',
                    data: """ + str(train_acc) + """,
                    borderColor: 'rgb(233, 30, 99)',
                    backgroundColor: 'rgba(233, 30, 99, 0.1)',
                    tension: 0.4
                }, {
                    label: '验证准确率',
                    data: """ + str(val_acc) + """,
                    borderColor: 'rgb(156, 39, 176)',
                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });"""

            html_content += """
    </script>
</body>
</html>"""

            # 保存HTML文件
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"✅ HTML报告已保存: {html_path}")

        except Exception as e:
            print(f"⚠️  生成HTML报告失败: {e}")

    def _save_training_history(self, history):
        """保存训练历史数据为JSON格式"""
        try:
            history_path = os.path.join(self.save_dir, f'training_history_{self.timestamp}.json')

            # 准备保存的数据
            save_data = {
                'training_info': self._convert_for_json(self.training_info),
                'dataset_info': self._convert_for_json(self.dataset_info),
                'model_info': self._convert_for_json(self.model_info),
                'history': {},
                'timestamp': self.timestamp,
                'export_time': datetime.now().isoformat()
            }

            # 转换numpy数组为列表，处理各种数据类型
            if history and history.history:
                for key, values in history.history.items():
                    if isinstance(values, (list, np.ndarray)):
                        # 转换为Python原生类型
                        converted_values = []
                        for v in values:
                            if isinstance(v, (np.integer, np.int32, np.int64)):
                                converted_values.append(int(v))
                            elif isinstance(v, (np.floating, np.float32, np.float64)):
                                converted_values.append(float(v))
                            else:
                                converted_values.append(float(v))
                        save_data['history'][key] = converted_values
                    else:
                        save_data['history'][key] = self._convert_for_json(values)

            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            print(f"✅ 训练历史已保存: {history_path}")

        except Exception as e:
            print(f"⚠️  保存训练历史失败: {e}")
            import traceback
            traceback.print_exc()

    def _convert_for_json(self, obj):
        """递归转换对象为JSON兼容格式"""
        if isinstance(obj, dict):
            return {k: self._convert_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._convert_for_json(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int32, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float32, np.float64)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            # 对于复杂对象，尝试转换为字符串
            return str(obj)
        else:
            return obj

    def _format_number(self, value):
        """格式化数字，如果是数字则添加千分位分隔符，否则返回原值"""
        if isinstance(value, (int, float)) and value != 'Unknown':
            return f"{value:,}"
        else:
            return str(value)

    def _format_duration(self, seconds):
        """格式化训练时长"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)

        if hours > 0:
            return f"{hours}小时{minutes}分钟{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"

    def _analyze_training_results(self, history):
        """分析训练结果并提供建议"""
        suggestions = []

        try:
            if not history or not history.history:
                suggestions.append("无法分析训练结果：缺少训练历史数据")
                return suggestions

            # 分析损失趋势
            train_loss = history.history['loss']
            val_loss = history.history['val_loss']

            # 检查过拟合
            if len(train_loss) > 10:
                recent_train_loss = np.mean(train_loss[-5:])
                recent_val_loss = np.mean(val_loss[-5:])

                if recent_val_loss > recent_train_loss * 1.5:
                    suggestions.append("⚠️  检测到过拟合：验证损失明显高于训练损失，建议增加正则化或减少模型复杂度")
                elif recent_val_loss < recent_train_loss * 0.8:
                    suggestions.append("✅ 模型泛化良好：验证损失低于训练损失")
                else:
                    suggestions.append("✅ 训练平衡：训练损失和验证损失相近")

            # 检查收敛情况
            if len(val_loss) > 5:
                recent_improvement = val_loss[-5] - val_loss[-1]
                if recent_improvement < 0.001:
                    suggestions.append("📈 模型可能已收敛：最近几轮验证损失变化很小")
                else:
                    suggestions.append("📈 模型仍在改进：验证损失持续下降")

            # 分析IoU指标
            if 'val_iou_coef' in history.history:
                final_iou = history.history['val_iou_coef'][-1]
                if final_iou > 0.8:
                    suggestions.append("🎯 优秀的分割性能：验证IoU > 0.8")
                elif final_iou > 0.6:
                    suggestions.append("🎯 良好的分割性能：验证IoU > 0.6")
                elif final_iou > 0.4:
                    suggestions.append("🎯 中等的分割性能：验证IoU > 0.4，可考虑调整模型或数据")
                else:
                    suggestions.append("⚠️  分割性能较低：验证IoU < 0.4，建议检查数据质量或模型配置")

            # 训练稳定性分析
            loss_std = np.std(train_loss[-10:]) if len(train_loss) > 10 else np.std(train_loss)
            if loss_std < 0.01:
                suggestions.append("📊 训练稳定：损失变化平稳")
            else:
                suggestions.append("📊 训练波动较大：可考虑降低学习率或增加批次大小")

        except Exception as e:
            suggestions.append(f"分析过程出错: {e}")

        return suggestions



# 定义 DataGenerator 类 - 按需加载版本，内存友好
class DataGenerator(keras.utils.Sequence):
    def __init__(self, file_pairs, config, is_training=True, shuffle=True):
        """
        按需加载的数据生成器
        Args:
            file_pairs: [(image_file, mask_file), ...] 文件对列表
            config: 配置字典，包含patch_size, patch_step等参数
            is_training: 是否为训练模式
            shuffle: 是否打乱数据
        """
        self.file_pairs = file_pairs
        self.config = config
        self.is_training = is_training
        self.shuffle = shuffle
        self.batch_size = config.get('batch_size', 4)

        # 生成图像块索引
        self.patch_indices = self._generate_patch_indices()
        self.total_patches = len(self.patch_indices)

        print(f"📊 数据生成器初始化完成:")
        print(f"  文件对数量: {len(file_pairs)}")
        print(f"  总图像块数: {self.total_patches}")
        print(f"  批次大小: {self.batch_size}")
        print(f"  批次数量: {len(self)}")

        self.on_epoch_end()

    def _generate_patch_indices(self):
        """生成所有图像块的索引信息"""
        print("🔍 生成图像块索引...")
        patch_indices = []

        for file_idx, (img_file, mask_file) in enumerate(self.file_pairs):
            try:
                # 读取图像尺寸信息（不加载实际数据）
                with rasterio.open(img_file) as src:
                    height, width = src.height, src.width

                # 计算可能的图像块位置
                patch_size = self.config.get('patch_size', 256)
                step = self.config.get('patch_step', 224)

                patches_per_file = 0
                max_patches = self.config.get('max_patches_per_file', 500)
                use_all_data = self.config.get('use_all_data', False)

                # 检查是否为image+shape模式
                mode = self.config.get('mode', 'image+mask')

                for y in range(0, height - patch_size + 1, step):
                    for x in range(0, width - patch_size + 1, step):
                        if mode == 'image+shape':
                            # image+shape模式：mask_file实际是shape_file
                            patch_indices.append({
                                'file_idx': file_idx,
                                'img_file': img_file,
                                'shape_file': mask_file,  # 在这种模式下，mask_file实际是shape_file
                                'x': x,
                                'y': y,
                                'patch_size': patch_size,
                                'mode': 'image+shape'
                            })
                        else:
                            # image+mask模式
                            patch_indices.append({
                                'file_idx': file_idx,
                                'img_file': img_file,
                                'mask_file': mask_file,
                                'x': x,
                                'y': y,
                                'patch_size': patch_size,
                                'mode': 'image+mask'
                            })
                        patches_per_file += 1

                        # 如果不是使用全部数据模式，则限制每个文件的图像块数量
                        if not use_all_data and max_patches and patches_per_file >= max_patches:
                            break
                    if not use_all_data and max_patches and patches_per_file >= max_patches:
                        break

                print(f"  文件 {file_idx+1}: {os.path.basename(img_file)} -> {patches_per_file} 个图像块")

            except Exception as e:
                print(f"  ❌ 处理文件失败: {img_file} - {e}")
                continue

        return patch_indices

    def __len__(self):
        return int(np.ceil(len(self.patch_indices) / float(self.batch_size)))

    def _augment_data(self, images, masks):
        """简单的数据增强"""
        augmented_images = []
        augmented_masks = []

        for img, mask in zip(images, masks):
            # 随机水平翻转
            if np.random.random() > 0.5:
                img = np.fliplr(img)
                mask = np.fliplr(mask)

            # 随机垂直翻转
            if np.random.random() > 0.5:
                img = np.flipud(img)
                mask = np.flipud(mask)

            # 随机旋转90度
            if np.random.random() > 0.5:
                k = np.random.randint(1, 4)
                img = np.rot90(img, k)
                mask = np.rot90(mask, k)

            augmented_images.append(img)
            augmented_masks.append(mask)

        return np.array(augmented_images), np.array(augmented_masks)

    def __getitem__(self, idx):
        """获取一个批次的数据"""
        # 获取当前批次的索引
        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]

        # 加载批次数据
        batch_x = []
        batch_y = []

        for i in batch_indices:
            if i >= len(self.patch_indices):
                continue

            patch_info = self.patch_indices[i]

            try:
                # 加载图像块
                img_patch, mask_patch = self._load_patch(patch_info)

                # 检查前景比例
                min_foreground_ratio = self.config.get('min_foreground_ratio', 0.01)
                foreground_ratio = np.sum(mask_patch > 0) / mask_patch.size
                if foreground_ratio >= min_foreground_ratio:
                    batch_x.append(img_patch)
                    batch_y.append(mask_patch)
                else:
                    # 如果当前块不符合要求，尝试下一个
                    continue

            except Exception as e:
                # 如果加载失败，跳过这个块
                continue

        # 如果批次数据不足，用零填充
        while len(batch_x) < self.batch_size:
            if len(batch_x) > 0:
                batch_x.append(np.zeros_like(batch_x[0]))
                batch_y.append(np.zeros_like(batch_y[0]))
            else:
                # 如果完全没有有效数据，创建默认形状
                patch_size = self.config.get('patch_size', 256)
                batch_x.append(np.zeros((patch_size, patch_size, 3), dtype=np.float32))
                batch_y.append(np.zeros((patch_size, patch_size, 1), dtype=np.float32))

        # 验证批次数据形状一致性
        if len(batch_x) > 0:
            expected_x_shape = batch_x[0].shape
            expected_y_shape = batch_y[0].shape

            for i, (x_item, y_item) in enumerate(zip(batch_x, batch_y)):
                if x_item.shape != expected_x_shape:
                    print(f"⚠️ 批次{idx}中图像{i}形状不一致: {x_item.shape} vs {expected_x_shape}")
                    # 修复形状不一致的问题
                    if x_item.shape != expected_x_shape:
                        batch_x[i] = np.zeros(expected_x_shape, dtype=np.float32)

                if y_item.shape != expected_y_shape:
                    print(f"⚠️ 批次{idx}中掩膜{i}形状不一致: {y_item.shape} vs {expected_y_shape}")
                    # 修复形状不一致的问题
                    if y_item.shape != expected_y_shape:
                        batch_y[i] = np.zeros(expected_y_shape, dtype=np.float32)

        # 转换为numpy数组
        try:
            X = np.array(batch_x[:self.batch_size], dtype=np.float32)
            y = np.array(batch_y[:self.batch_size], dtype=np.float32)
        except ValueError as e:
            print(f"❌ 批次{idx}数组转换失败: {e}")
            print(f"   batch_x形状: {[x.shape for x in batch_x[:self.batch_size]]}")
            print(f"   batch_y形状: {[y.shape for y in batch_y[:self.batch_size]]}")
            # 创建默认批次
            patch_size = self.config.get('patch_size', 256)
            X = np.zeros((self.batch_size, patch_size, patch_size, 3), dtype=np.float32)
            y = np.zeros((self.batch_size, patch_size, patch_size, 1), dtype=np.float32)

        # 数据预处理
        X = X / 255.0  # 归一化
        y = (y > 0).astype(np.float32)  # 二值化

        # 转换为分类格式
        y_categorical = np.zeros((*y.shape[:3], 2), dtype=np.float32)
        y_categorical[..., 0] = (y[..., 0] == 0)  # 背景
        y_categorical[..., 1] = (y[..., 0] == 1)  # 前景

        # 数据增强（仅训练时）
        if self.is_training and self.config.get('use_data_augmentation', True):
            X, y_categorical = self._augment_batch(X, y_categorical)

        return X, y_categorical

    def _load_patch(self, patch_info):
        """加载单个图像块"""
        x, y = patch_info['x'], patch_info['y']
        patch_size = patch_info['patch_size']
        mode = patch_info.get('mode', 'image+mask')

        # 读取图像块
        with rasterio.open(patch_info['img_file']) as src:
            # 检查边界，确保不超出图像范围
            img_height, img_width = src.height, src.width

            # 调整窗口以确保不超出边界
            actual_x = min(x, img_width - patch_size) if x + patch_size > img_width else x
            actual_y = min(y, img_height - patch_size) if y + patch_size > img_height else y
            actual_x = max(0, actual_x)
            actual_y = max(0, actual_y)

            # 读取指定区域
            window = rasterio.windows.Window(actual_x, actual_y, patch_size, patch_size)
            image = src.read(window=window).transpose(1, 2, 0)

            # 确保图像形状正确
            if image.shape[:2] != (patch_size, patch_size):
                # 如果形状不对，进行填充或裁剪
                target_image = np.zeros((patch_size, patch_size, image.shape[2]), dtype=image.dtype)
                h, w = min(image.shape[0], patch_size), min(image.shape[1], patch_size)
                target_image[:h, :w] = image[:h, :w]
                image = target_image

            if image.shape[2] > 3:
                image = image[:, :, :3]

        if mode == 'image+shape':
            # image+shape模式：从shapefile生成掩膜
            mask = self._generate_mask_from_shape(patch_info, actual_x, actual_y, patch_size)
        else:
            # image+mask模式：直接读取掩膜文件
            with rasterio.open(patch_info['mask_file']) as src:
                # 使用相同的边界检查
                mask_height, mask_width = src.height, src.width
                actual_x = min(x, mask_width - patch_size) if x + patch_size > mask_width else x
                actual_y = min(y, mask_height - patch_size) if y + patch_size > mask_height else y
                actual_x = max(0, actual_x)
                actual_y = max(0, actual_y)

                window = rasterio.windows.Window(actual_x, actual_y, patch_size, patch_size)
                mask = src.read(1, window=window)

                # 确保掩膜形状正确
                if mask.shape != (patch_size, patch_size):
                    target_mask = np.zeros((patch_size, patch_size), dtype=mask.dtype)
                    h, w = min(mask.shape[0], patch_size), min(mask.shape[1], patch_size)
                    target_mask[:h, :w] = mask[:h, :w]
                    mask = target_mask

                mask = np.expand_dims(mask, axis=-1)

        # 最终形状验证
        assert image.shape == (patch_size, patch_size, 3), f"图像形状错误: {image.shape}, 期望: ({patch_size}, {patch_size}, 3)"
        assert mask.shape == (patch_size, patch_size, 1), f"掩膜形状错误: {mask.shape}, 期望: ({patch_size}, {patch_size}, 1)"

        return image.astype(np.float32), mask.astype(np.float32)

    def _generate_mask_from_shape(self, patch_info, x, y, patch_size):
        """从shapefile为指定区域生成掩膜"""
        import geopandas as gpd
        from rasterio.features import rasterize
        from rasterio.transform import from_bounds

        # 读取shapefile
        gdf = gpd.read_file(patch_info['shape_file'])

        # 获取图像的地理变换信息
        with rasterio.open(patch_info['img_file']) as src:
            transform = src.transform
            crs = src.crs

        # 确保shapefile与图像坐标系一致
        if gdf.crs != crs:
            gdf = gdf.to_crs(crs)

        # 计算图像块的地理边界
        left, top = transform * (x, y)
        right, bottom = transform * (x + patch_size, y + patch_size)

        # 创建图像块的变换矩阵
        patch_transform = from_bounds(left, bottom, right, top, patch_size, patch_size)

        # 裁剪shapefile到图像块区域
        from shapely.geometry import box
        patch_bounds = box(left, bottom, right, top)
        clipped_gdf = gdf[gdf.geometry.intersects(patch_bounds)]

        if len(clipped_gdf) == 0:
            # 如果没有几何体相交，返回全零掩膜
            mask = np.zeros((patch_size, patch_size), dtype=np.uint8)
        else:
            # 栅格化几何体
            mask = rasterize(
                [(geom, 1) for geom in clipped_gdf.geometry if geom.is_valid],
                out_shape=(patch_size, patch_size),
                transform=patch_transform,
                fill=0,
                dtype='uint8',
                all_touched=True
            )

        return np.expand_dims(mask, axis=-1)

    def _augment_batch(self, images, masks):
        """批次数据增强"""
        augmented_images = []
        augmented_masks = []

        for img, mask in zip(images, masks):
            # 随机水平翻转
            if np.random.random() > 0.5:
                img = np.fliplr(img)
                mask = np.fliplr(mask)

            # 随机垂直翻转
            if np.random.random() > 0.5:
                img = np.flipud(img)
                mask = np.flipud(mask)

            # 随机旋转90度
            if np.random.random() > 0.5:
                k = np.random.randint(1, 4)
                img = np.rot90(img, k)
                mask = np.rot90(mask, k)

            augmented_images.append(img)
            augmented_masks.append(mask)

        return np.array(augmented_images), np.array(augmented_masks)

    def on_epoch_end(self):
        """每个epoch结束时调用"""
        self.indices = np.arange(len(self.patch_indices))
        if self.shuffle:
            np.random.shuffle(self.indices)

# Monitor GPU memory and set device availability
print("GPU 检测:")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        # 设置GPU内存增长，避免一次性分配所有内存
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"发现 {len(gpus)} 个 GPU，已配置内存增长模式")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu.name}")
    except RuntimeError as e:
        print(f"GPU 配置错误: {e}")
else:
    print("未检测到 GPU，将使用 CPU 进行训练")

# 不再强制使用 CPU 或 GPU，让代码自动选择
# os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # 已删除，不再强制使用 CPU

def load_config(config_path):
    with open(config_path, 'r') as f:
        return json.load(f)

def get_dataset_paths(config):
    """获取数据集路径，支持image+shape模式和image+mask模式"""
    model_args = config.get('model', {}).get('args', {})
    data_mode = model_args.get('data_mode', 'image+shape')

    if data_mode == 'image+mask':
        # image+mask模式：返回image_folder和mask_folder
        image_folder = model_args.get('image_folder', '')
        mask_folder = model_args.get('mask_folder', '')

        if not image_folder or not mask_folder:
            raise ValueError("image+mask模式需要指定image_folder和mask_folder参数")

        return {
            'mode': 'folder',
            'image_folder': image_folder,
            'mask_folder': mask_folder
        }
    else:
        # image+shape模式：使用image_path和shape_path
        if 'image_path' in model_args and 'shape_path' in model_args:
            # 使用新的参数
            image_path = model_args['image_path']
            shape_path = model_args['shape_path']

            # 如果路径是目录，则使用默认文件名
            if os.path.isdir(image_path):
                # 查找目录中的tif文件
                tif_files = [f for f in os.listdir(image_path) if f.endswith('.tif')]
                if tif_files:
                    image_path = os.path.join(image_path, tif_files[0])
                else:
                    image_path = os.path.join(image_path, 'test.tif')

            if os.path.isdir(shape_path):
                # 查找目录中的shp文件
                shp_files = [f for f in os.listdir(shape_path) if f.endswith('.shp')]
                if shp_files:
                    shape_path = os.path.join(shape_path, shp_files[0])
                else:
                    shape_path = os.path.join(shape_path, 'Buildings.shp')

            return {
                'mode': 'single',
                'tif': image_path,
                'shp': shape_path
            }
        else:
            # 使用旧的dataset_path方式
            dataset_path = config['dataset_path']
            return {
                'mode': 'single',
                'tif': os.path.join(dataset_path, 'image', 'test.tif'),
                'shp': os.path.join(dataset_path, 'shape', 'Buildings.shp')
            }

def validate_paths(paths):
    """验证路径，支持image+shape模式和image+mask模式"""
    if paths.get('mode') == 'folder':
        # image+mask模式验证
        required_folders = {
            '图像文件夹': paths['image_folder'],
            '掩膜文件夹': paths['mask_folder']
        }

        missing = []
        for name, folder_path in required_folders.items():
            if not os.path.exists(folder_path):
                missing.append(f"{name}: {folder_path}")
            elif not os.path.isdir(folder_path):
                missing.append(f"{name}: {folder_path} (不是有效的文件夹)")

        if missing:
            raise FileNotFoundError(
                f"缺少必要文件夹:\n" + "\n".join(missing)
            )

        # 检查文件夹中是否有文件
        image_files = [f for f in os.listdir(paths['image_folder'])
                      if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]
        mask_files = [f for f in os.listdir(paths['mask_folder'])
                     if f.lower().endswith(('.tif', '.tiff', '.png'))]

        if not image_files:
            raise FileNotFoundError(f"图像文件夹中没有找到图像文件: {paths['image_folder']}")
        if not mask_files:
            raise FileNotFoundError(f"掩膜文件夹中没有找到掩膜文件: {paths['mask_folder']}")

        print(f"找到 {len(image_files)} 个图像文件，{len(mask_files)} 个掩膜文件")

    else:
        # image+shape模式验证
        required_files = {
            'TIFF文件': paths['tif'],
            'SHP文件': paths['shp']
        }

        missing = []
        for name, path in required_files.items():
            if not os.path.exists(path):
                missing.append(f"{name}: {path}")
            elif name == 'TIFF文件' and not path.endswith('.tif'):
                missing.append(f"{name}: {path} (不是有效的TIFF文件)")
            elif name == 'SHP文件' and not path.endswith('.shp'):
                missing.append(f"{name}: {path} (不是有效的SHP文件)")

        if missing:
            raise FileNotFoundError(
                f"缺少必要文件:\n" + "\n".join(missing)
            )

        # 验证文件可读性（image+shape模式）
        try:
            with rasterio.open(paths['tif']) as src:
                pass
        except Exception as e:
            raise FileNotFoundError(f"无法读取TIFF文件 {paths['tif']}: {str(e)}")

        try:
            gpd.read_file(paths['shp'])
        except Exception as e:
            raise FileNotFoundError(f"无法读取SHP文件 {paths['shp']}: {str(e)}")

class TrainingLogger:
    def __init__(self, log_path="training_log.txt"):
        self.log_file = open(log_path, 'w')

    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.log_file.write(log_message + "\n")
        self.log_file.flush()

# 定义IoU指标 - 数值稳定版本
def iou_coef(y_true, y_pred, smooth=1e-6):
    """计算IoU系数 - 防止NaN的稳定版本"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)

    # 确保预测值在有效范围内
    y_pred_f = tf.clip_by_value(y_pred_f, tf.keras.backend.epsilon(), 1.0 - tf.keras.backend.epsilon())

    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection

    # 防止除零
    union = tf.maximum(union, smooth)
    iou = (intersection + smooth) / (union + smooth)

    # 防止NaN
    iou = tf.where(tf.math.is_nan(iou), tf.constant(0.0), iou)
    iou = tf.clip_by_value(iou, 0.0, 1.0)

    return iou

# 数值稳定性监控回调
class NumericalStabilityCallback(Callback):
    """监控训练过程中的数值稳定性"""

    def __init__(self):
        super().__init__()
        self.nan_count = 0

    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}

        # 检查是否有NaN值
        has_nan = False
        for key, value in logs.items():
            if np.isnan(value) or np.isinf(value):
                print(f"⚠️  警告: Epoch {epoch+1} 中检测到 {key}={value}")
                has_nan = True

        if has_nan:
            self.nan_count += 1
            print(f"🚨 数值不稳定警告: 已连续 {self.nan_count} 个epoch出现NaN/Inf")

            if self.nan_count >= 2:
                print("🛑 检测到严重数值不稳定，建议停止训练并调整参数")
                self.model.stop_training = True
        else:
            self.nan_count = 0  # 重置计数器

        # 打印健康的训练指标
        if not has_nan:
            loss = logs.get('loss', 0)
            val_loss = logs.get('val_loss', 0)
            iou = logs.get('iou_coef', 0)
            val_iou = logs.get('val_iou_coef', 0)

            print(f"✅ Epoch {epoch+1} 数值稳定: loss={loss:.4f}, val_loss={val_loss:.4f}, "
                  f"iou={iou:.4f}, val_iou={val_iou:.4f}")

# 完整的UNet模型结构
def conv_block(inputs, num_filters, dropout_rate=0.1):
    """卷积块：Conv2D -> BatchNorm -> ReLU -> Conv2D -> BatchNorm -> ReLU -> Dropout"""
    conv = keras.layers.Conv2D(num_filters, 3, padding="same", kernel_initializer="he_normal")(inputs)
    conv = keras.layers.BatchNormalization()(conv)
    conv = keras.layers.Activation("relu")(conv)

    conv = keras.layers.Conv2D(num_filters, 3, padding="same", kernel_initializer="he_normal")(conv)
    conv = keras.layers.BatchNormalization()(conv)
    conv = keras.layers.Activation("relu")(conv)

    if dropout_rate > 0:
        conv = keras.layers.Dropout(dropout_rate)(conv)

    return conv

def encoder_block(inputs, num_filters, dropout_rate=0.1):
    """编码器块：卷积块 + 最大池化"""
    conv = conv_block(inputs, num_filters, dropout_rate)
    pool = keras.layers.MaxPooling2D((2, 2))(conv)
    return conv, pool

def decoder_block(inputs, skip_features, num_filters, dropout_rate=0.1):
    """解码器块：上采样 + 跳跃连接 + 卷积块"""
    upsample = keras.layers.Conv2DTranspose(num_filters, (2, 2), strides=2, padding="same")(inputs)
    concat = keras.layers.Concatenate()([upsample, skip_features])
    conv = conv_block(concat, num_filters, dropout_rate)
    return conv

def complete_unet_model(n_classes=2, image_height=256, image_width=256, image_channels=3, dropout_rate=0.1):
    """完整的UNet模型

    Args:
        n_classes: 类别数量
        image_height: 图像高度
        image_width: 图像宽度
        image_channels: 图像通道数
        dropout_rate: Dropout率

    Returns:
        keras.Model: 完整的UNet模型
    """
    inputs = keras.layers.Input((image_height, image_width, image_channels))

    # 编码器路径
    s1, p1 = encoder_block(inputs, 64, dropout_rate)      # 256x256 -> 128x128
    s2, p2 = encoder_block(p1, 128, dropout_rate)         # 128x128 -> 64x64
    s3, p3 = encoder_block(p2, 256, dropout_rate)         # 64x64 -> 32x32
    s4, p4 = encoder_block(p3, 512, dropout_rate)         # 32x32 -> 16x16

    # 瓶颈层（底部）
    b1 = conv_block(p4, 1024, dropout_rate)               # 16x16

    # 解码器路径
    d1 = decoder_block(b1, s4, 512, dropout_rate)         # 16x16 -> 32x32
    d2 = decoder_block(d1, s3, 256, dropout_rate)         # 32x32 -> 64x64
    d3 = decoder_block(d2, s2, 128, dropout_rate)         # 64x64 -> 128x128
    d4 = decoder_block(d3, s1, 64, dropout_rate)          # 128x128 -> 256x256

    # 输出层（使用float32确保数值稳定性）
    outputs = keras.layers.Conv2D(n_classes, 1, padding="same", activation="softmax", dtype='float32')(d4)

    model = keras.models.Model(inputs, outputs, name="Complete_UNet")
    return model



def create_patches(img, mask, size=256, stride=128, filter_empty=True, min_foreground_ratio=0.001):
    """Create patches from image and mask for training with balanced filtering"""
    patches_img = patchify(img, (size, size, img.shape[-1]), step=stride)
    patches_mask = patchify(mask, (size, size, 1), step=stride)

    patches_img = patches_img.reshape(-1, size, size, img.shape[-1])
    patches_mask = patches_mask.reshape(-1, size, size, 1)

    if filter_empty:
        print(f"智能过滤图像块（最小前景比例: {min_foreground_ratio:.3%}）...")

        # 计算每个图像块的前景比例
        foreground_ratios = []
        for i in range(patches_mask.shape[0]):
            patch_mask = patches_mask[i]
            foreground_pixels = np.sum(patch_mask > 0)
            total_pixels = patch_mask.size
            ratio = foreground_pixels / total_pixels
            foreground_ratios.append(ratio)

        foreground_ratios = np.array(foreground_ratios)

        # 分类图像块
        has_foreground = foreground_ratios > 0  # 包含任何前景的块
        pure_background = foreground_ratios == 0  # 纯背景块

        print(f"原始图像块数: {len(patches_img)}")
        print(f"包含前景的块: {np.sum(has_foreground)} ({np.sum(has_foreground)/len(patches_img)*100:.1f}%)")
        print(f"纯背景块: {np.sum(pure_background)} ({np.sum(pure_background)/len(patches_img)*100:.1f}%)")

        # 处理没有前景数据的情况
        if np.sum(has_foreground) == 0:
            print("⚠️  警告: 没有包含前景的图像块，保留部分背景块用于训练")
            # 随机保留一些背景块
            num_to_keep = min(1000, len(patches_img) // 2)  # 保留最多1000个或一半
            if num_to_keep > 0:
                keep_indices = np.random.choice(len(patches_img), num_to_keep, replace=False)
                valid_indices = np.zeros(len(patches_img), dtype=bool)
                valid_indices[keep_indices] = True
            else:
                valid_indices = np.ones(len(patches_img), dtype=bool)  # 保留所有
        else:
            # 保留所有包含前景的块
            valid_indices = has_foreground.copy()

            # 从纯背景块中随机选择一些，保持平衡
            if np.sum(pure_background) > 0:
                num_foreground = np.sum(has_foreground)
                # 保留与前景块数量相等的背景块，但不超过总数的50%
                num_bg_to_keep = min(num_foreground, int(len(patches_img) * 0.5))

                if num_bg_to_keep > 0:
                    bg_indices = np.where(pure_background)[0]
                    if len(bg_indices) > num_bg_to_keep:
                        keep_bg_indices = np.random.choice(bg_indices, num_bg_to_keep, replace=False)
                        valid_indices[keep_bg_indices] = True
                    else:
                        valid_indices[bg_indices] = True

        patches_img = patches_img[valid_indices]
        patches_mask = patches_mask[valid_indices]

        print(f"最终保留的图像块数: {len(patches_img)}")

        # 重新计算最终数据的分布
        if len(patches_mask) > 0:
            final_foreground = np.sum([np.sum(mask > 0) for mask in patches_mask])
            final_total = patches_mask.size
            final_fg_ratio = final_foreground / final_total if final_total > 0 else 0
            print(f"最终数据中前景比例: {final_fg_ratio:.3%}")
        else:
            print("❌ 错误: 没有保留任何图像块")

    return patches_img, patches_mask

# 定义损失函数和指标
def dice_loss(y_true, y_pred, smooth=1e-5):
    # 增强数值稳定性
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)

    # 裁剪预测值以避免极值
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)

    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f)

    # 避免除零
    dice_coef = (2. * intersection + smooth) / (union + smooth)
    dice_coef = tf.clip_by_value(dice_coef, 1e-7, 1.0 - 1e-7)

    return tf.cast(1 - dice_coef, tf.float32)

def focal_loss(gamma=2., alpha=.25):
    def focal_loss_fixed(y_true, y_pred):
        y_true = tf.cast(y_true, tf.float32)
        y_pred = tf.cast(y_pred, tf.float32)

        # 增强数值稳定性：裁剪预测值
        epsilon = 1e-7
        y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)

        pt_1 = tf.where(tf.equal(y_true, 1), y_pred, tf.ones_like(y_pred))
        pt_0 = tf.where(tf.equal(y_true, 0), y_pred, tf.zeros_like(y_pred))

        # 裁剪pt值以避免log(0)
        pt_1 = tf.clip_by_value(pt_1, epsilon, 1.0 - epsilon)
        pt_0 = tf.clip_by_value(pt_0, epsilon, 1.0 - epsilon)

        loss1 = alpha * tf.pow(1. - pt_1, gamma) * tf.math.log(pt_1)
        loss0 = (1 - alpha) * tf.pow(pt_0, gamma) * tf.math.log(1. - pt_0)

        focal_loss_val = -tf.reduce_mean(loss1) - tf.reduce_mean(loss0)

        # 检查并处理NaN/Inf
        focal_loss_val = tf.where(tf.math.is_finite(focal_loss_val), focal_loss_val, tf.constant(0.0))

        return tf.cast(focal_loss_val, tf.float32)
    return focal_loss_fixed

def combined_loss(y_true, y_pred):
    dice = dice_loss(y_true, y_pred)
    focal = focal_loss(gamma=2., alpha=.25)(y_true, y_pred)

    # 检查并处理NaN/Inf
    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))
    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))

    combined = dice + focal
    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))

    return tf.cast(combined, tf.float32)



def train_unet(config_path, model_save_path=None, pretrained_model_path=None):
    """
    训练UNet模型，支持从预训练模型继续训练

    Args:
        config_path: 配置文件路径
        model_save_path: 模型保存路径
        pretrained_model_path: 预训练模型路径（可选，用于继续训练）
    """
    logger = TrainingLogger()
    logger.log("=== 开始训练 ===")
    logger.log(f"使用配置: {config_path}")

    if pretrained_model_path:
        logger.log(f"从预训练模型继续训练: {pretrained_model_path}")

    # 从配置获取所有参数
    config = load_config(config_path)
    model_args = config['model']['args']
    trainer_args = config['trainer']

    # 使用配置参数替换硬编码值
    batch_size = trainer_args['batch_size']
    epochs = trainer_args['epochs']
    learning_rate = trainer_args['learning_rate']
    slice_size = trainer_args['slice_size']
    overlap = trainer_args['overlap']

    # 学习率优化 - 针对瓦片拼接图像的特殊处理，避免过低学习率导致NaN
    if learning_rate >= 0.00002:
        learning_rate = 0.0001  # 提高学习率，避免数值不稳定
        print(f"检测到可能的瓦片拼接图像，使用适中学习率避免NaN: {learning_rate}")
    elif learning_rate >= 0.00005:
        learning_rate = 0.0001  # 提高学习率
        print(f"为防止数值不稳定，提高学习率为: {learning_rate}")
    elif learning_rate >= 0.0001:
        learning_rate = 0.0001  # 保持合理的学习率
        print(f"使用标准学习率: {learning_rate}")

    print(f"最终训练参数: batch_size={batch_size}, lr={learning_rate}, epochs={epochs}")
    print(f"数值稳定性设置: 启用梯度裁剪、NaN检测、稳健归一化、瓦片边界平滑")

    # 内存优化配置 - 从GUI对话框参数中获取或使用默认值
    memory_opt = trainer_args.get('memory_optimization', {})
    use_mixed_precision = memory_opt.get('use_mixed_precision', True)
    reduce_samples = memory_opt.get('reduce_samples', False)
    max_samples = memory_opt.get('max_samples', None)

    # 根据batch_size自动启用内存优化
    if batch_size <= 2:
        print(f"检测到小批次大小 ({batch_size})，自动启用内存优化")
        reduce_samples = True
        if max_samples is None:
            max_samples = 15000  # 为小批次设置合理的样本上限

    # 设置混合精度策略
    policy = tf.keras.mixed_precision.Policy('mixed_float16')
    tf.keras.mixed_precision.set_global_policy(policy)

    paths = get_dataset_paths(config)

    # 使用完整UNet模型
    model_args = config.get('model', {}).get('args', {})
    print("使用完整UNet模型")

    try:
        validate_paths(paths)
    except FileNotFoundError as e:
        print(str(e))
        return False

    # 根据数据模式加载数据
    if paths.get('mode') == 'folder':
        # image+mask模式：使用FolderDatasetLoader
        print("使用image+mask模式加载数据...")
        try:
            from .folder_dataset_loader import FolderDatasetLoader
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            sys.path.insert(0, current_dir)
            from folder_dataset_loader import FolderDatasetLoader

        # 创建临时文件夹用于合并图像
        temp_folder = os.path.join(os.path.dirname(config_path), 'temp_merged')

        try:
            # 创建文件夹数据加载器（禁用自动合并）
            folder_loader = FolderDatasetLoader(
                image_folder=paths['image_folder'],
                mask_folder=paths['mask_folder'],
                slice_size=slice_size,
                overlap=overlap,
                auto_merge=False,  # 禁用自动合并，使用标准1:1对应模式
                temp_folder=temp_folder
            )

            # 创建训练数据集
            image_dataset, label_dataset = folder_loader.create_training_dataset(
                normalize=False,  # 稍后手动归一化
                filter_empty=True,
                min_foreground_ratio=0.001
            )

            print(f"image+mask模式数据加载完成:")
            print(f"  图像数据形状: {image_dataset.shape}")
            print(f"  标签数据形状: {label_dataset.shape}")

            # 清理临时文件
            folder_loader.cleanup_temp_files()

        except Exception as e:
            print(f"image+mask模式数据加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    else:
        # image+shape模式：使用原有逻辑
        print("使用image+shape模式加载数据...")

        with rasterio.open(paths['tif']) as src:
            image = src.read().transpose((1, 2, 0))
            transform = src.transform
            crs = src.crs

        gdf = gpd.read_file(paths['shp'])
        print(f"原始标签坐标系: {gdf.crs}")
        print(f"图像坐标系: {crs}")
        print(f"标签要素数量: {len(gdf)}")

        # 坐标系转换
        if gdf.crs != crs:
            print(f"转换坐标系: {gdf.crs} -> {crs}")
            gdf = gdf.to_crs(crs)

        # 确保图像是3波段的
        if image.shape[2] > 3:
            print(f"原始图像有 {image.shape[2]} 个波段，正在转换为3波段...")
            image = image[:, :, :3]
            print(f"转换后的图像形状: {image.shape}")

        # 改进的掩码创建
        print("开始栅格化...")
        print(f"目标尺寸: {image.shape[:2]}")

        mask = rasterize(
            [(geom, 1) for geom in gdf.geometry if geom.is_valid],
            out_shape=image.shape[:2],
            transform=transform,
            fill=0,
            dtype='uint8',
            all_touched=True  # 包含所有接触的像素
        )

    # 根据数据模式处理标签数据
    if paths.get('mode') == 'folder':
        # 文件夹模式：数据已经预处理好了
        print("文件夹模式：跳过单文件模式的标签处理")
        # image_dataset和label_dataset已经在上面创建好了
        pass
    else:
        # 单文件模式：检查栅格化结果
        unique_values = np.unique(mask)
        foreground_pixels = np.sum(mask > 0)
        total_pixels = mask.size
        print(f"栅格化结果: 唯一值={unique_values}, 前景像素={foreground_pixels}/{total_pixels} ({foreground_pixels/total_pixels:.4%})")

        if foreground_pixels == 0:
            print("⚠️  警告: 栅格化后没有前景像素，创建测试标签...")
            # 创建一些测试标签
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            h, w = mask.shape
            # 在图像中创建一些小的标签区域
            for i in range(3):
                for j in range(3):
                    y = h // 4 * (i + 1)
                    x = w // 4 * (j + 1)
                    mask[y-20:y+20, x-20:x+20] = 1

            foreground_pixels = np.sum(mask > 0)
            print(f"测试标签: 前景像素={foreground_pixels}/{total_pixels} ({foreground_pixels/total_pixels:.4%})")
            print("注意: 这是测试标签，实际使用需要修复原始标签数据")

    # 根据数据模式进行不同的预处理
    if paths.get('mode') == 'folder':
        # 文件夹模式：数据已经预处理好了，直接使用
        print("文件夹模式：使用预处理好的数据")
        print(f"图像数据形状: {image_dataset.shape}")
        print(f"标签数据形状: {label_dataset.shape}")

        # 转换标签为分类格式
        from tensorflow.keras.utils import to_categorical
        labels_categorical_dataset = to_categorical(label_dataset, num_classes=2)

        print(f"转换后标签形状: {labels_categorical_dataset.shape}")

    else:
        # 单文件模式：进行完整的数据预处理
        print("开始高级数据预处理...")

        # 1. 数据质量诊断
        print(f"原始数据诊断:")
        print(f"  数据类型: {image.dtype}")
        print(f"  数据范围: [{image.min():.3f}, {image.max():.3f}]")
        print(f"  数据形状: {image.shape}")
        print(f"  NaN数量: {np.isnan(image).sum()}")
        print(f"  Inf数量: {np.isinf(image).sum()}")
        print(f"  零值比例: {(image == 0).sum() / image.size:.2%}")

        # 2. 检测数据源类型
        max_val = image.max()
        if max_val <= 1.0:
            data_type = "normalized"
        elif max_val <= 255:
            data_type = "8bit"
        elif max_val <= 65535:
            data_type = "16bit"
        else:
            data_type = "high_range"

        print(f"  检测到数据类型: {data_type}")

        # 3. 处理无效值
        original_nan_count = np.isnan(image).sum()
        original_inf_count = np.isinf(image).sum()

        if original_nan_count > 0 or original_inf_count > 0:
            print(f"  处理无效值: NaN={original_nan_count}, Inf={original_inf_count}")
            image = np.nan_to_num(image, nan=0.0, posinf=1.0, neginf=0.0)

        # 4. 数据类型转换
        if image.dtype != np.float32:
            print(f"  转换数据类型: {image.dtype} -> float32")
            image = image.astype(np.float32)

        # 5. 针对瓦片拼接图像的特殊处理
        print("  检测瓦片拼接图像的数值问题...")

        # 检测可能的瓦片边界问题
        def detect_tile_boundaries(img_channel, tile_size=256):
            """检测瓦片边界处的数值不连续性"""
            h, w = img_channel.shape
            boundary_issues = 0

            # 检查垂直边界
            for x in range(tile_size, w, tile_size):
                if x < w:
                    left_col = img_channel[:, x-1]
                    right_col = img_channel[:, x]
                    diff = np.abs(left_col - right_col).mean()
                    if diff > 10:  # 阈值可调
                        boundary_issues += 1

            # 检查水平边界
            for y in range(tile_size, h, tile_size):
                if y < h:
                    top_row = img_channel[y-1, :]
                    bottom_row = img_channel[y, :]
                    diff = np.abs(top_row - bottom_row).mean()
                    if diff > 10:
                        boundary_issues += 1

            return boundary_issues

    # 5. 智能归一化策略（仅在单文件模式下执行）
    if paths.get('mode') != 'folder':
        print("  应用智能归一化...")
        for i in range(image.shape[-1]):
            channel = image[:, :, i].copy()

            # 检测瓦片边界问题
            boundary_issues = detect_tile_boundaries(channel)
            if boundary_issues > 0:
                print(f"    波段{i+1}: 检测到{boundary_issues}个瓦片边界问题")

                # 应用平滑滤波减少边界效应
                from scipy import ndimage
                channel = ndimage.gaussian_filter(channel, sigma=0.5)
                print(f"    已应用高斯平滑处理")

            # 计算统计信息
            mean_val = channel.mean()
            std_val = channel.std()
            min_val = channel.min()
            max_val = channel.max()

            # 选择归一化策略
            if data_type == "high_range" or max_val > 1000:
                # 高动态范围：使用稳健百分位数
                p1, p99 = np.percentile(channel, [1, 99])
                if p99 > p1:
                    channel = np.clip((channel - p1) / (p99 - p1), 0, 1)
                else:
                    channel = np.zeros_like(channel)
            elif std_val > 0:
                # 标准归一化，但使用更稳健的方法
                if max_val > min_val:
                    # 使用稳健的归一化，避免极值影响
                    p2, p98 = np.percentile(channel, [2, 98])
                    if p98 > p2:
                        channel = np.clip((channel - p2) / (p98 - p2), 0, 1)
                    else:
                        channel = (channel - min_val) / (max_val - min_val)
                else:
                    channel = np.zeros_like(channel)
            else:
                # 常数图像
                channel = np.zeros_like(channel)

            # 额外的数值稳定性处理
            channel = np.clip(channel, 0.0, 1.0)

            # 最终的数值清理
            channel = np.nan_to_num(channel, nan=0.0, posinf=1.0, neginf=0.0)

            # 检查归一化后的质量
            if np.isnan(channel).any() or np.isinf(channel).any():
                print(f"    警告: 波段{i+1}归一化后仍有无效值，使用零填充")
                channel = np.zeros_like(channel)

            image[:, :, i] = channel
            print(f"    波段{i+1}: [{channel.min():.6f}, {channel.max():.6f}], std={channel.std():.6f}")

        print(f"处理后 - 图像范围: [{image.min():.3f}, {image.max():.3f}]")

        # 6. 最终安全检查和修复
        image = np.clip(image, 0.0, 1.0)

        # 强制清理任何残留的无效值
        if np.isnan(image).any():
            print("  警告: 发现残留NaN值，强制清理")
            image = np.nan_to_num(image, nan=0.0)

        if np.isinf(image).any():
            print("  警告: 发现残留Inf值，强制清理")
            image = np.nan_to_num(image, posinf=1.0, neginf=0.0)

        # 7. 数据质量验证
        final_nan = np.isnan(image).sum()
        final_inf = np.isinf(image).sum()
        final_range = [image.min(), image.max()]

        print(f"最终验证:")
        print(f"  NaN数量: {final_nan}")
        print(f"  Inf数量: {final_inf}")
        print(f"  数值范围: [{final_range[0]:.6f}, {final_range[1]:.6f}]")

        assert final_nan == 0, f"图像数据仍包含{final_nan}个NaN值"
        assert final_inf == 0, f"图像数据仍包含{final_inf}个Inf值"
        assert 0.0 <= final_range[0] <= final_range[1] <= 1.0, f"数值范围异常: {final_range}"

        print("✅ 数据预处理完成，数值稳定性验证通过")

        # 3. 将掩码扩展为与图像相同的维度
        mask = np.expand_dims(mask, axis=-1)

        print("Image shape:", image.shape)
        print("Mask shape:", mask.shape)

        # 创建图像块 - 智能过滤保持数据平衡
        image_dataset, mask_dataset = create_patches(
            image, mask, slice_size, slice_size-overlap,
            filter_empty=True, min_foreground_ratio=0.001
        )

        print("Image patches shape:", image_dataset.shape)
        print("Mask patches shape:", mask_dataset.shape)

        # 内存优化：转换数据类型为float32以减少内存使用
        print("转换数据类型为float32以优化内存使用...")
        image_dataset = image_dataset.astype(np.float32)
        mask_dataset = mask_dataset.astype(np.float32)

        # 检查数据中是否有 NaN 值
        print("Input data contains NaN:", np.isnan(image_dataset).any())
        print("Label data contains NaN:", np.isnan(mask_dataset).any())

        # 根据模型类型决定标签格式（单文件模式）
        from tensorflow.keras.utils import to_categorical
        labels_categorical_dataset = to_categorical(mask_dataset, num_classes=2)
        print("使用分类标签格式 (适配完整UNet)")

        # 计算类别权重以处理不平衡数据（单文件模式）
        print("计算类别权重...")
        unique_classes = np.unique(mask_dataset)
        print(f"发现的类别: {unique_classes}")

        if len(unique_classes) > 1:
            class_weights = compute_class_weight(
                'balanced',
                classes=unique_classes,
                y=mask_dataset.flatten()
            )
            class_weight_dict = dict(zip(unique_classes, class_weights))
            print(f"类别权重: {class_weight_dict}")
        elif len(unique_classes) == 1:
            print("警告: 只发现一个类别，使用默认权重")
            class_weight_dict = {unique_classes[0]: 1.0}
        else:
            print("错误: 没有发现任何类别，使用默认权重")
            class_weight_dict = {0: 1.0, 1: 1.0}

        # 单文件模式的最终数据准备
        image_dataset = image_dataset.astype(np.float32)
        labels_categorical_dataset = labels_categorical_dataset.astype(np.float32)

    # 统一处理两种模式的数据
    if paths.get('mode') == 'folder':
        # 文件夹模式：使用预处理好的数据
        # image_dataset 和 label_dataset 已经准备好
        labels_categorical_dataset = label_dataset  # 已经转换为分类格式
        # 计算类别权重
        unique_classes = np.unique(np.argmax(labels_categorical_dataset, axis=-1))
        print(f"文件夹模式发现的类别: {unique_classes}")
        if len(unique_classes) > 1:
            class_weights = compute_class_weight(
                'balanced',
                classes=unique_classes,
                y=np.argmax(labels_categorical_dataset, axis=-1).flatten()
            )
            class_weight_dict = dict(zip(unique_classes, class_weights))
            print(f"文件夹模式类别权重: {class_weight_dict}")
        else:
            class_weight_dict = {0: 1.0, 1: 1.0}

    # 内存优化：限制样本数量和使用索引分割
    total_samples = len(image_dataset)
    print(f"原始样本总数: {total_samples}")

    # 确保数据类型一致
    image_dataset = image_dataset.astype(np.float32)
    labels_categorical_dataset = labels_categorical_dataset.astype(np.float32)

    # 如果启用样本减少，限制最大样本数
    if reduce_samples and max_samples and total_samples > max_samples:
        print(f"[内存优化] 启用样本数量限制")
        print(f"[内存优化] 将样本数从 {total_samples} 减少到 {max_samples}")
        # 随机选择样本
        selected_indices = np.random.choice(total_samples, max_samples, replace=False)
        image_dataset = image_dataset[selected_indices]
        labels_categorical_dataset = labels_categorical_dataset[selected_indices]
        total_samples = max_samples
        print(f"[内存优化] 样本减少完成，当前样本数: {total_samples}")
    else:
        print(f"[内存优化] 使用全部样本进行训练: {total_samples}")
        if total_samples > 20000:
            print(f"[警告] 样本数量较大 ({total_samples})，建议在GUI中设置较小的batch_size (1-2)")
            print(f"[警告] 如果遇到内存不足，请考虑减少切片大小或启用样本限制")

    indices = np.arange(total_samples)

    # 划分索引而不是数据 - 固定随机种子确保一致性
    train_indices, test_indices = train_test_split(
        indices, test_size=0.2, random_state=42
    )

    # 确保数据一致性 - 排序索引
    train_indices = np.sort(train_indices)
    test_indices = np.sort(test_indices)

    print(f"最终训练样本数: {len(train_indices)}, 测试样本数: {len(test_indices)}")

    # 不立即创建训练和测试数据，而是在DataGenerator中按需加载

    # 获取图像的高度、宽度、通道数和总类别数
    image_height = image_dataset.shape[1]
    image_width = image_dataset.shape[2]
    image_channels = image_dataset.shape[3]

    # 获取模型参数
    dropout_rate = model_args.get('dropout_rate', 0.1)

    # 检查是否需要从预训练模型继续训练
    if pretrained_model_path and os.path.exists(pretrained_model_path):
        print(f"🔄 从预训练模型继续训练: {pretrained_model_path}")
        try:
            # 加载预训练模型
            model = tf.keras.models.load_model(
                pretrained_model_path,
                custom_objects={
                    'iou_coef': iou_coef,
                    'combined_loss': combined_loss,
                    'dice_loss': dice_loss,
                    'focal_loss': focal_loss
                },
                compile=False  # 不编译，稍后重新编译
            )
            print(f"✅ 成功加载预训练模型，参数量: {model.count_params():,}")

            # 验证模型输入输出形状是否匹配
            expected_input_shape = (None, image_height, image_width, image_channels)
            if model.input_shape != expected_input_shape:
                print(f"⚠️  警告: 模型输入形状不匹配")
                print(f"   预训练模型: {model.input_shape}")
                print(f"   当前数据: {expected_input_shape}")
                print("   将创建新模型而不是加载预训练模型")
                model = None
            else:
                print(f"✅ 模型形状验证通过: {model.input_shape}")

        except Exception as e:
            print(f"❌ 加载预训练模型失败: {e}")
            print("   将创建新模型")
            model = None
    else:
        model = None

    # 如果没有成功加载预训练模型，则创建新模型
    if model is None:
        print("使用完整的UNet模型 (Complete UNet)")
        model = complete_unet_model(
            n_classes=2,
            image_height=image_height,
            image_width=image_width,
            image_channels=image_channels,
            dropout_rate=dropout_rate
        )

    # 使用梯度裁剪的优化器 - 增强数值稳定性
    optimizer = Adam(
        learning_rate=learning_rate,
        clipnorm=1.0,   # 放宽梯度范数裁剪，避免过度限制
        clipvalue=0.5,  # 放宽梯度值裁剪
        epsilon=1e-7    # 防止除零，稍微放宽
    )

    print(f"优化器配置: lr={learning_rate}, clipnorm=1.0, clipvalue=0.5")

    # 完整UNet模型使用组合损失函数
    loss_function = combined_loss
    print("使用组合损失函数 (Dice + Focal)")

    # 编译模型
    model.compile(
        optimizer=optimizer,
        loss=loss_function,
        metrics=["accuracy", iou_coef]
    )

    # 打印模型摘要
    model.summary()

    # 处理模型保存路径，避免覆盖预训练模型
    import os  # 确保os模块可用
    if model_save_path is None:
        save_path = trainer_args['save_dir']
        # 如果save_dir是文件路径，提取目录部分
        if save_path.endswith('.h5'):
            save_dir = os.path.dirname(save_path)
            model_save_path = save_path
        else:
            save_dir = save_path
            model_save_path = os.path.join(save_dir, "final_model.h5")

        if save_dir:
            os.makedirs(save_dir, exist_ok=True)
    else:
        save_dir = os.path.dirname(model_save_path)
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)

    # 检查是否会覆盖预训练模型
    if pretrained_model_path and os.path.abspath(model_save_path) == os.path.abspath(pretrained_model_path):
        print(f"⚠️  警告：保存路径与预训练模型路径相同！")
        print(f"   预训练模型: {pretrained_model_path}")
        print(f"   保存路径: {model_save_path}")

        # 创建备份（只有当备份不存在时才创建）
        backup_path = pretrained_model_path.replace('.h5', '_backup.h5')
        import shutil
        if not os.path.exists(backup_path):
            shutil.copy2(pretrained_model_path, backup_path)
            print(f"✅ 已创建预训练模型备份: {backup_path}")
        else:
            print(f"📁 备份文件已存在: {backup_path}")
            # 检查备份文件是否与原文件相同
            import filecmp
            if filecmp.cmp(pretrained_model_path, backup_path, shallow=False):
                print(f"✅ 备份文件与原文件内容一致")
            else:
                # 创建新的备份文件
                timestamp_backup = backup_path.replace('_backup.h5', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.h5')
                shutil.copy2(pretrained_model_path, timestamp_backup)
                print(f"⚠️  原备份文件内容不同，创建新备份: {timestamp_backup}")

        # 修改保存路径，添加时间戳
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(model_save_path)[0]
        model_save_path = f"{base_name}_continued_{timestamp}.h5"
        print(f"🔄 修改保存路径为: {model_save_path}")

        # 更新保存目录
        save_dir = os.path.dirname(model_save_path)
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)

    # 创建回调函数 - 包含数值稳定性监控
    callbacks = [
        NumericalStabilityCallback(),  # 数值稳定性监控
        ReduceLROnPlateau(
            monitor='val_iou_coef',  # 监控验证IoU而不是loss
            mode='max',  # IoU越大越好
            factor=0.8,  # 更温和的学习率衰减
            patience=5,  # 增加耐心，避免过早降低学习率
            min_lr=1e-6,  # 提高最小学习率
            verbose=1
        ),
        EarlyStopping(
            monitor='val_iou_coef',  # 监控验证IoU
            mode='max',  # IoU越大越好
            patience=15,  # 增加耐心
            restore_best_weights=True,
            verbose=1
        ),
        ModelCheckpoint(
            model_save_path,
            monitor='val_iou_coef',
            mode='max',
            save_best_only=True,
            verbose=1
        ),
        TensorBoard(
            log_dir=os.path.join(save_dir, 'logs'),
            histogram_freq=1
        )
    ]

    # 🔧 检查是否使用image+mask模式（文件夹模式）
    if paths.get('mode') == 'folder':
        print("使用image+mask模式的按需加载数据生成器...")

        # 准备文件对（标准数据集模式）

        # 获取图像和掩膜文件
        image_folder = paths.get('image_folder', '')
        mask_folder = paths.get('mask_folder', '')

        if not image_folder or not mask_folder:
            raise ValueError("image+mask模式需要指定image_folder和mask_folder路径")

        # 获取文件列表
        from pathlib import Path
        image_files = []
        mask_files = []

        for ext in ['.tif', '.tiff']:
            image_files.extend(Path(image_folder).glob(f"*{ext}"))
            mask_files.extend(Path(mask_folder).glob(f"*{ext}"))

        image_files = [str(f) for f in image_files]
        mask_files = [str(f) for f in mask_files]

        print(f"找到 {len(image_files)} 个图像文件，{len(mask_files)} 个掩膜文件")

        # 使用标准数据集模式（1:1对应），不进行任何合并操作
        print("使用标准数据集模式（1:1对应），跳过合并步骤")
        temp_folder = None

        # 文件配对 - 支持多种配对模式
        matched_pairs = []
        import re

        print(f"开始文件配对: {len(image_files)} 个图像文件, {len(mask_files)} 个掩膜文件")

        # 方法1: 基于tile模式的配对（原有逻辑）
        print("尝试方法1: tile模式配对...")
        for img_file in image_files:
            img_name = os.path.basename(img_file)
            tile_base = None
            if 'tile_' in img_name.lower():
                tile_pattern = r'(tile_\d+_\d+)'
                match = re.search(tile_pattern, img_name.lower())
                if match:
                    tile_base = match.group(1)

            if tile_base:
                for mask_file in mask_files:
                    mask_name = os.path.basename(mask_file)
                    if tile_base in mask_name.lower():
                        matched_pairs.append((img_file, mask_file))
                        print(f"配对成功 (tile模式): {os.path.basename(img_file)} <-> {os.path.basename(mask_file)}")
                        break

        # 方法2: 文件名完全匹配（去掉扩展名）
        if not matched_pairs:
            print("方法1失败，尝试方法2: 文件名完全匹配...")
            used_masks = set()

            for img_file in image_files:
                img_name = os.path.basename(img_file)
                img_base = os.path.splitext(img_name)[0]  # 去掉扩展名

                for mask_file in mask_files:
                    if mask_file in used_masks:
                        continue

                    mask_name = os.path.basename(mask_file)
                    mask_base = os.path.splitext(mask_name)[0]  # 去掉扩展名

                    # 完全匹配文件名（不含扩展名）
                    if img_base == mask_base:
                        matched_pairs.append((img_file, mask_file))
                        used_masks.add(mask_file)
                        print(f"配对成功 (完全匹配): {img_name} <-> {mask_name}")
                        break

        # 方法3: 去掉常见后缀的匹配
        if not matched_pairs:
            print("方法2失败，尝试方法3: 去掉常见后缀匹配...")
            used_masks = set()

            for img_file in image_files:
                img_name = os.path.basename(img_file)
                img_base = os.path.splitext(img_name)[0]

                # 去掉常见的图像后缀
                img_clean = img_base
                for suffix in ['_image', '_img', '_sat', '_satellite', '_rgb']:
                    if img_clean.lower().endswith(suffix):
                        img_clean = img_clean[:-len(suffix)]
                        break

                for mask_file in mask_files:
                    if mask_file in used_masks:
                        continue

                    mask_name = os.path.basename(mask_file)
                    mask_base = os.path.splitext(mask_name)[0]

                    # 去掉常见的掩膜后缀
                    mask_clean = mask_base
                    for suffix in ['_mask', '_label', '_gt', '_groundtruth']:
                        if mask_clean.lower().endswith(suffix):
                            mask_clean = mask_clean[:-len(suffix)]
                            break

                    if img_clean.lower() == mask_clean.lower():
                        matched_pairs.append((img_file, mask_file))
                        used_masks.add(mask_file)
                        print(f"配对成功 (后缀匹配): {img_name} <-> {mask_name}")
                        break

        # 方法4: 数字序列匹配
        if not matched_pairs:
            print("方法3失败，尝试方法4: 数字序列匹配...")
            used_masks = set()

            for img_file in image_files:
                img_name = os.path.basename(img_file)
                img_base = os.path.splitext(img_name)[0]
                img_numbers = re.findall(r'\d+', img_base)

                best_match = None
                max_common_numbers = 0

                for mask_file in mask_files:
                    if mask_file in used_masks:
                        continue

                    mask_name = os.path.basename(mask_file)
                    mask_base = os.path.splitext(mask_name)[0]
                    mask_numbers = re.findall(r'\d+', mask_base)

                    # 计算共同的数字序列数量
                    common_numbers = len(set(img_numbers) & set(mask_numbers))

                    if common_numbers > max_common_numbers and common_numbers > 0:
                        max_common_numbers = common_numbers
                        best_match = mask_file

                if best_match:
                    matched_pairs.append((img_file, best_match))
                    used_masks.add(best_match)
                    print(f"配对成功 (数字匹配): {img_name} <-> {os.path.basename(best_match)} (共同数字: {max_common_numbers})")

        # 方法5: 如果文件数量相等，按顺序配对
        if not matched_pairs and len(image_files) == len(mask_files):
            print("方法4失败，尝试方法5: 按文件顺序配对...")
            # 对文件进行排序，确保配对的一致性
            sorted_images = sorted(image_files)
            sorted_masks = sorted(mask_files)

            for img_file, mask_file in zip(sorted_images, sorted_masks):
                matched_pairs.append((img_file, mask_file))
                print(f"配对成功 (顺序配对): {os.path.basename(img_file)} <-> {os.path.basename(mask_file)}")

        if not matched_pairs:
            # 提供更详细的错误信息
            print(f"\n❌ 所有配对方法都失败了，详细信息:")
            print(f"   图像文件数量: {len(image_files)}")
            print(f"   掩膜文件数量: {len(mask_files)}")
            print(f"   图像文件夹: {image_folder}")
            print(f"   掩膜文件夹: {mask_folder}")
            print(f"   图像文件示例: {[os.path.basename(f) for f in image_files[:5]]}")
            print(f"   掩膜文件示例: {[os.path.basename(f) for f in mask_files[:5]]}")

            # 提供解决建议
            print(f"\n💡 解决建议:")
            print(f"   1. 确保图像和掩膜文件名相同（除了扩展名）")
            print(f"   2. 或者使用 'image_name.tif' 对应 'image_name_mask.tif' 的命名模式")
            print(f"   3. 或者使用 tile_x_y 的命名模式")
            print(f"   4. 确保图像和掩膜文件数量相等")

            raise ValueError("未找到任何匹配的图像-掩膜对！请检查文件命名是否一致。")

        # 分割训练和验证数据
        if len(matched_pairs) > 1:
            train_pairs, val_pairs = train_test_split(
                matched_pairs, test_size=0.2, random_state=42
            )
            print(f"数据分割: 训练{len(train_pairs)}对, 验证{len(val_pairs)}对")
        else:
            # 如果只有一个文件对，训练和验证使用相同数据
            train_pairs = matched_pairs
            val_pairs = matched_pairs
            print(f"⚠️  只有{len(matched_pairs)}个文件对，训练和验证使用相同数据")

        # 创建配置 - 支持使用全部数据
        # 从配置文件中获取数据限制设置
        model_args = config.get('model', {}).get('args', {})
        use_all_data = model_args.get('use_all_data', False)
        max_patches_per_file = model_args.get('max_patches_per_file', 1000)
        max_total_patches = model_args.get('max_total_patches', None)

        if use_all_data:
            print("🚀 启用全部数据模式 - 将使用所有可用的图像块")
            max_patches_per_file = None  # 不限制每个文件的图像块数量
            max_total_patches = None     # 不限制总图像块数量
        else:
            print(f"📊 限制数据模式 - 每个文件最多{max_patches_per_file}个图像块")

        data_config = {
            'batch_size': batch_size,
            'patch_size': slice_size,
            'patch_step': slice_size - overlap,
            'min_foreground_ratio': model_args.get('min_foreground_ratio', 0.01),
            'max_patches_per_file': max_patches_per_file,
            'max_total_patches': max_total_patches,
            'use_data_augmentation': model_args.get('use_data_augmentation', True),
            'use_all_data': use_all_data
        }

        # 创建按需加载的数据生成器
        train_generator = DataGenerator(
            train_pairs, data_config, is_training=True, shuffle=True
        )
        val_generator = DataGenerator(
            val_pairs, data_config, is_training=False, shuffle=False
        )

        print(f"按需加载数据生成器创建完成:")
        print(f"  训练文件对: {len(train_pairs)}")
        print(f"  验证文件对: {len(val_pairs)}")
        print(f"  训练批次数: {len(train_generator)}")
        print(f"  验证批次数: {len(val_generator)}")
        print(f"  预计训练图像块数: {train_generator.total_patches}")
        print(f"  预计验证图像块数: {val_generator.total_patches}")

    else:
        # 传统模式（image+shape）：检查是否使用按需加载
        print("使用传统模式（image+shape）的数据生成器...")

        # 从配置文件中获取数据限制设置
        model_args = config.get('model', {}).get('args', {})
        use_all_data = model_args.get('use_all_data', False)
        max_patches_per_file = model_args.get('max_patches_per_file', 1000)

        if use_all_data:
            print("🚀 image+shape模式启用全部数据 - 将使用所有可用的图像块")
            max_patches_limit = None  # 不限制图像块数量
        else:
            print(f"📊 image+shape模式限制数据 - 最多使用{max_patches_per_file}个图像块")
            max_patches_limit = max_patches_per_file

        # 检查是否使用按需加载模式
        use_on_demand_loading = model_args.get('use_on_demand_loading', False)

        if use_on_demand_loading:
            print("🔧 使用按需加载模式处理image+shape数据...")

            # 创建临时文件对，用于按需加载
            image_path = paths.get('tif', paths.get('image_path', ''))
            shape_path = paths.get('shp', paths.get('shape_path', ''))

            if not image_path or not shape_path:
                raise ValueError("image+shape按需加载模式需要指定image_path和shape_path")

            # 创建单个文件对（image+shape模式只有一个图像文件）
            file_pairs = [(image_path, shape_path)]

            # 创建配置
            data_config = {
                'batch_size': batch_size,
                'patch_size': slice_size,
                'patch_step': slice_size - overlap,
                'min_foreground_ratio': model_args.get('min_foreground_ratio', 0.01),
                'max_patches_per_file': max_patches_limit,
                'max_total_patches': model_args.get('max_total_patches', None),
                'use_data_augmentation': model_args.get('use_data_augmentation', True),
                'use_all_data': use_all_data,
                'mode': 'image+shape'  # 标识为image+shape模式
            }

            # 创建按需加载的数据生成器
            train_generator = DataGenerator(
                file_pairs, data_config, is_training=True, shuffle=True
            )
            val_generator = DataGenerator(
                file_pairs, data_config, is_training=False, shuffle=False
            )

            print(f"image+shape按需加载数据生成器创建完成:")
            print(f"  文件对: {len(file_pairs)}")
            print(f"  训练批次数: {len(train_generator)}")
            print(f"  验证批次数: {len(val_generator)}")
            print(f"  预计训练图像块数: {train_generator.total_patches}")
            print(f"  预计验证图像块数: {val_generator.total_patches}")

        else:
            print("🔧 使用传统预加载模式处理image+shape数据...")

            # 传统预加载模式：限制数据量以避免内存问题
            if max_patches_limit:
                print(f"⚠️  为避免内存问题，限制图像块数量为: {max_patches_limit}")

                # 如果数据量超过限制，进行随机采样
                total_patches = len(image_dataset)
                if total_patches > max_patches_limit:
                    print(f"原始数据量: {total_patches}, 采样到: {max_patches_limit}")

                    # 随机采样索引
                    import random
                    random.seed(42)  # 确保可重复性
                    sampled_indices = random.sample(range(total_patches), max_patches_limit)
                    sampled_indices.sort()

                    # 采样数据
                    image_dataset = image_dataset[sampled_indices]
                    labels_categorical_dataset = labels_categorical_dataset[sampled_indices]

                    print(f"数据采样完成: {len(image_dataset)} 个图像块")

            # 创建兼容的配置
            data_config = {
                'batch_size': batch_size,
                'use_data_augmentation': True
            }

        # 创建传统的数据生成器（需要修改以支持预加载数据）
        class LegacyDataGenerator(DataGenerator):
            def __init__(self, image_dataset, label_dataset, indices, batch_size, augment=False):
                self.image_dataset = image_dataset
                self.label_dataset = label_dataset
                self.indices = indices
                self.batch_size = batch_size
                self.augment = augment
                self.total_patches = len(indices)

            def __len__(self):
                return int(np.ceil(len(self.indices) / float(self.batch_size)))

            def __getitem__(self, idx):
                batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]
                batch_x = self.image_dataset[batch_indices]
                batch_y = self.label_dataset[batch_indices]

                if self.augment:
                    batch_x, batch_y = self._augment_data(batch_x, batch_y)

                return batch_x, batch_y

            def _augment_data(self, images, masks):
                """简单的数据增强"""
                augmented_images = []
                augmented_masks = []

                for img, mask in zip(images, masks):
                    # 随机水平翻转
                    if np.random.random() > 0.5:
                        img = np.fliplr(img)
                        mask = np.fliplr(mask)

                    # 随机垂直翻转
                    if np.random.random() > 0.5:
                        img = np.flipud(img)
                        mask = np.flipud(mask)

                    # 随机旋转90度
                    if np.random.random() > 0.5:
                        k = np.random.randint(1, 4)
                        img = np.rot90(img, k)
                        mask = np.rot90(mask, k)

                    augmented_images.append(img)
                    augmented_masks.append(mask)

                return np.array(augmented_images), np.array(augmented_masks)

            def on_epoch_end(self):
                if self.augment:
                    np.random.shuffle(self.indices)

        train_generator = LegacyDataGenerator(
            image_dataset, labels_categorical_dataset, train_indices,
            batch_size=batch_size, augment=True
        )
        val_generator = LegacyDataGenerator(
            image_dataset, labels_categorical_dataset, test_indices,
            batch_size=batch_size
        )

    # 调试验证集
    print(f"验证集调试信息:")
    print(f"  验证集索引数量: {len(test_indices)}")
    print(f"  验证集批次数量: {len(val_generator)}")
    if len(val_generator) > 0:
        try:
            val_batch_x, val_batch_y = val_generator[0]
            print(f"  验证集第一批次形状: x={val_batch_x.shape}, y={val_batch_y.shape}")
            print(f"  验证集标签范围: [{val_batch_y.min():.3f}, {val_batch_y.max():.3f}]")
        except Exception as e:
            print(f"  验证集数据获取失败: {e}")
    else:
        print(f"  警告: 验证集为空！")

    # 🔧 创建训练报告生成器
    print(f"\n📊 初始化训练报告生成器...")
    report_dir = os.path.join(save_dir, 'training_reports')
    reporter = TrainingReporter(report_dir, model_name="UNet")

    # 准备匹配的文件对信息（用于报告）
    if paths.get('mode') == 'folder':
        # image+mask模式
        matched_pairs_for_report = matched_pairs
    else:
        # image+shape模式，创建虚拟的文件对信息
        matched_pairs_for_report = [(paths.get('tif', 'image.tif'), paths.get('shp', 'shape.shp'))]

    # 开始训练记录
    reporter.start_training(config, matched_pairs_for_report, train_generator, val_generator)
    reporter.record_model_info(model)

    # 训练 - 不使用class_weight，因为它不支持3D目标
    # 类别权重已经在损失函数中处理
    print(f"\n🚀 开始模型训练...")
    history = model.fit(
        train_generator,
        validation_data=val_generator,
        epochs=epochs,
        callbacks=callbacks,
        verbose=1
    )

    # 保存最终模型（再次保存，确保最终权重）
    model.save(model_save_path)
    print(f"模型训练和保存完成，模型文件: {model_save_path}")

    # 输出实际保存路径供GUI使用
    print(f"ACTUAL_SAVE_PATH: {model_save_path}")

    # 🔧 生成训练报告
    print(f"\n📈 生成训练报告...")
    reporter.end_training(history, model_save_path)

    # 如果是继续训练，比较模型差异
    if pretrained_model_path:
        print(f"\n📊 模型训练结果对比:")
        print(f"   原始模型: {pretrained_model_path}")
        print(f"   新模型: {model_save_path}")

        # 检查文件大小
        original_size = os.path.getsize(pretrained_model_path) if os.path.exists(pretrained_model_path) else 0
        new_size = os.path.getsize(model_save_path)
        print(f"   文件大小: {original_size:,} bytes → {new_size:,} bytes")

        if original_size == new_size:
            print(f"   ℹ️  文件大小相同（正常，因为模型架构相同）")
        else:
            print(f"   ⚠️  文件大小不同（可能的架构变化）")

        # 检查修改时间
        import time
        original_time = time.ctime(os.path.getmtime(pretrained_model_path)) if os.path.exists(pretrained_model_path) else "N/A"
        new_time = time.ctime(os.path.getmtime(model_save_path))
        print(f"   修改时间: {original_time} → {new_time}")

    # 注意: 训练曲线图表已由TrainingReporter生成到training_reports目录
    # 移除了重复的training_history.png生成代码

    print(f"\n✅ 训练完成！")
    print(f"📁 模型文件: {model_save_path}")
    print(f"📊 训练报告: {reporter.save_dir}")
    print(f"📈 详细报告文件:")
    print(f"   - Markdown报告: {os.path.join(reporter.save_dir, f'training_report_{reporter.timestamp}.md')}")
    print(f"   - HTML报告: {os.path.join(reporter.save_dir, f'training_report_{reporter.timestamp}.html')}")
    print(f"   - 训练曲线: {os.path.join(reporter.save_dir, f'training_curves_{reporter.timestamp}.png')}")
    print(f"   - 训练历史: {os.path.join(reporter.save_dir, f'training_history_{reporter.timestamp}.json')}")
    print(f"\n🌐 在浏览器中打开HTML报告查看详细的交互式训练结果！")

    return True

# 移除了PyTorch版本的SegmentationDataset类
# 主要的数据处理由TensorFlow/Keras的数据生成器处理

# 移除了重复的train_model函数（PyTorch版本）
# 主要的训练功能由train_unet函数提供（TensorFlow版本）

def main():
    # 创建参数解析器
    parser = argparse.ArgumentParser(description='训练分割模型')
    parser.add_argument('--config', type=str, required=False, help='配置文件路径')
    parser.add_argument('config_path', type=str, nargs='?', help='配置文件路径（位置参数）')
    parser.add_argument('--model_save_path', type=str, required=False, help='模型保存文件名（可选）')
    parser.add_argument('--pretrained_path', type=str, required=False, help='预训练模型路径（可选，用于继续训练）')
    args = parser.parse_args()

    # 获取配置文件路径
    config_path = args.config or args.config_path
    if not config_path:
        print("错误：未提供配置文件路径")
        sys.exit(1)

    if not os.path.exists(config_path):
        print(f"错误：配置文件不存在：{config_path}")
        sys.exit(1)

    try:
        print(f"\n=== 开始训练 ===")
        print(f"使用配置文件：{config_path}")
        if args.model_save_path:
            print(f"模型保存文件名：{args.model_save_path}")
        if args.pretrained_path:
            print(f"预训练模型路径：{args.pretrained_path}")
        # 加载配置文件
        # with open(config_path, 'r', encoding='utf-8') as f:
        #     config = json.load(f)
        #     print("\n配置文件内容：")
        #     print(json.dumps(config, indent=2, ensure_ascii=False))
        # 开始训练
        train_unet(config_path, model_save_path=args.model_save_path, pretrained_model_path=args.pretrained_path)
    except Exception as e:
        print(f"\n训练过程中出错：{str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()