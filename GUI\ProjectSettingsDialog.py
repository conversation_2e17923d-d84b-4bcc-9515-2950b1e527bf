from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                           QLineEdit, QDialogButtonBox, QGroupBox, QFormLayout, QFileDialog)
import os

class ProjectSettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("项目设置")
        self.setMinimumWidth(500)
        
        # 设置默认路径
        self.default_paths = {
            'image_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\image",
            'shape_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\shape",
            'model_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\trained_models"
        }
        
        layout = QVBoxLayout(self)
        
        # 数据路径设置组
        data_group = QGroupBox("数据路径设置")
        data_layout = QFormLayout()
        
        # 图像路径
        self.image_path = QLineEdit()
        self.image_path.setText(self.default_paths['image_path'])
        browse_image = QPushButton("浏览...")
        data_layout.addRow("图像路径:", self.create_path_layout(self.image_path, browse_image))
        browse_image.clicked.connect(lambda: self.browse_path(self.image_path))
        
        # Shape文件路径
        self.shape_path = QLineEdit()
        self.shape_path.setText(self.default_paths['shape_path'])
        browse_shape = QPushButton("浏览...")
        data_layout.addRow("Shape文件路径:", self.create_path_layout(self.shape_path, browse_shape))
        browse_shape.clicked.connect(lambda: self.browse_path(self.shape_path))
        
        data_group.setLayout(data_layout)
        layout.addWidget(data_group)
        
        # 模型设置组
        model_group = QGroupBox("模型设置")
        model_layout = QFormLayout()
        
        # 模型保存路径
        self.model_path = QLineEdit()
        self.model_path.setText(self.default_paths['model_path'])
        browse_model = QPushButton("浏览...")
        model_layout.addRow("模型保存路径:", self.create_path_layout(self.model_path, browse_model))
        browse_model.clicked.connect(lambda: self.browse_path(self.model_path))
        
        model_group.setLayout(model_layout)
        layout.addWidget(model_group)
        
        # 添加说明标签
        note_label = QLabel("注意：所有路径都必须是有效的目录路径")
        note_label.setStyleSheet("color: gray;")
        layout.addWidget(note_label)
        
        # 重置按钮
        reset_button = QPushButton("重置为默认路径")
        reset_button.clicked.connect(self.reset_to_defaults)
        layout.addWidget(reset_button)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.validate_and_accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def create_path_layout(self, line_edit, button):
        """创建路径选择布局"""
        layout = QHBoxLayout()
        layout.addWidget(line_edit)
        layout.addWidget(button)
        return layout
    
    def browse_path(self, line_edit):
        """浏览文件夹路径"""
        path = QFileDialog.getExistingDirectory(self, "选择路径", line_edit.text())
        if path:
            line_edit.setText(path)
    
    def reset_to_defaults(self):
        """重置为默认路径"""
        self.image_path.setText(self.default_paths['image_path'])
        self.shape_path.setText(self.default_paths['shape_path'])
        self.model_path.setText(self.default_paths['model_path'])
    
    def validate_and_accept(self):
        """验证路径并接受"""
        # 检查所有路径是否存在，如果不存在则创建
        paths = {
            '图像路径': self.image_path.text(),
            'Shape文件路径': self.shape_path.text(),
            '模型保存路径': self.model_path.text()
        }
        
        for name, path in paths.items():
            if not os.path.exists(path):
                try:
                    os.makedirs(path)
                except Exception as e:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "警告", 
                        f"无法创建{name}：{path}\n错误：{str(e)}\n"
                        "请选择其他路径或确保有足够的权限。")
                    return
        
        self.accept()
    
    def load_settings(self, config):
        """加载现有设置"""
        self.image_path.setText(config.get('image_path', self.default_paths['image_path']))
        self.shape_path.setText(config.get('shape_path', self.default_paths['shape_path']))
        self.model_path.setText(config.get('model_path', self.default_paths['model_path']))
    
    def get_settings(self):
        """获取设置"""
        return {
            'image_path': self.image_path.text(),
            'shape_path': self.shape_path.text(),
            'model_path': self.model_path.text()
        } 