import sys
import os
from rtree import index
import math
import json
from datetime import datetime  # 修改这行
import mercantile
import requests
import traceback
import cv2
import numpy as np
from PIL import Image
from io import BytesIO
from typing import Tuple, Optional, List
from osgeo import gdal, osr
from Core.inference_results_processor import InferenceResultsProcessor
from Core.sam_inference import SAMInference
from GUI.GridGeneratorDialog import GridG<PERSON>atorDialog
from Core.ManualOffsetTool import ManualOffsetTool
from pyproj import CRS, Transformer  # 修改这行，直接导入CRS
import time

# 尝试导入 WebEngine 相关模块
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    from PyQt5.QtWebChannel import QWebChannel
    HAS_WEBENGINE = True
except ImportError:
    HAS_WEBENGINE = False

# 尝试导入 GeoPandas
try:
    import geopandas as gpd
    import pandas as pd
    from shapely.geometry import Point, Polygon
    from shapely import ops
    HAS_GEOPANDAS = True
except ImportError:
    HAS_GEOPANDAS = False

# PyQt5 导入
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QToolBar, QTreeWidget, QTreeWidgetItem,
                            QLabel, QFrame, QSplitter, QStatusBar, QMenuBar,
                            QMenu, QAction, QPushButton, QComboBox, QDialog,
                            QMessageBox, QFileDialog, QStyle, QProgressDialog,
                            QInputDialog, QListWidget, QListWidgetItem, QDialogButtonBox,
                            QCheckBox)
from PyQt5.QtCore import Qt, QUrl, pyqtSlot, QObject, QSize
from PyQt5.QtGui import QIcon

# 本地模块导入
from Core.ShapeDrawer import ShapeDrawer
from Core.WebBridge import WebBridge
from Core.OffsetCommands import CommandManager, MovePointCommand, BatchOffsetCommand
from Core.PointOffsetManager import PointOffsetManager
from GUI.ModelTrainingDialog import ModelTrainingDialog

# AI 相关导入
from deepseek_chat import DeepSeekChatDialog

# 安全导入transformers，处理protobuf兼容性问题
HAS_TRANSFORMERS = False
try:
    from transformers import pipeline
    HAS_TRANSFORMERS = True
except ImportError as e:
    print(f"⚠️ transformers导入失败: {e}")
    print("💡 提示: 如果遇到protobuf错误，请运行: pip install protobuf==3.19.6")
    pipeline = None
except Exception as e:
    print(f"⚠️ transformers导入出现其他错误: {e}")
    pipeline = None

import torch
from segment_anything import sam_model_registry, SamPredictor

# 设置全局变量
HAS_GEOPANDAS = False

try:
    import geopandas as gpd
    import pandas as pd
    HAS_GEOPANDAS = True
except ImportError as e:
    print(f"Warning: {e}")
    print("Shape file functionality will be limited. Please install required packages:")
    print("pip install geopandas pandas")

# 添加新的对话框类
class DeleteFeaturesDialog(QDialog):
    def __init__(self, features, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择要删除的要素")
        self.setMinimumWidth(300)
        self.setMinimumHeight(400)

        # 创建布局
        layout = QVBoxLayout(self)

        # 添加说明标签
        label = QLabel("请选择要删除的要素：")
        layout.addWidget(label)

        # 创建列表控件
        self.list_widget = QListWidget()
        self.list_widget.setSelectionMode(QListWidget.ExtendedSelection)
        layout.addWidget(self.list_widget)

        # 添加要素到列表
        self.features = features
        for idx in range(len(features)):
            item = QListWidgetItem(f"要素 {idx + 1}")
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Unchecked)
            self.list_widget.addItem(item)

        # 添加全选/取消全选复选框
        self.select_all_cb = QCheckBox("全选/取消全选")
        self.select_all_cb.stateChanged.connect(self.toggle_all)
        layout.addWidget(self.select_all_cb)

        # 添加按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 修改事件连接
        self.list_widget.itemClicked.connect(self.on_item_clicked)
        self.list_widget.itemChanged.connect(self.on_item_changed)
        self.list_widget.itemEntered.connect(self.on_item_hover)
        self.list_widget.itemSelectionChanged.connect(self.on_selection_changed)

        # 启用鼠标追踪
        self.list_widget.setMouseTracking(True)

    def toggle_all(self, state):
        """全选/取消全选"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Checked if state == Qt.Checked else Qt.Unchecked)
        self.update_highlight()

    def get_selected_indices(self):
        """获取选中的要素索引"""
        selected = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item.checkState() == Qt.Checked:
                selected.append(i)
        return selected

    def on_item_changed(self, item):
        """当复选框状态改变时"""
        self.update_highlight()

    def on_item_clicked(self, item):
        """当项目被点击时"""
        # 切换复选框状态
        item.setCheckState(Qt.Checked if item.checkState() == Qt.Unchecked else Qt.Unchecked)
        self.update_highlight()

    def on_selection_changed(self):
        """当列表选择改变时"""
        self.update_highlight()

    def on_item_hover(self, item):
        """当鼠标悬停在项目上时"""
        if hasattr(self.parent(), 'highlight_selected_features'):
            # 临时高亮显示悬停的要素
            self.parent().highlight_selected_features([self.list_widget.row(item)], is_hover=True)

    def update_highlight(self):
        """更新高亮显示"""
        if hasattr(self.parent(), 'highlight_selected_features'):
            # 获取选中和勾选的要素
            checked_indices = self.get_selected_indices()
            selected_indices = [self.list_widget.row(item) for item in self.list_widget.selectedItems()]
            # 合并两种选择方式的索引
            all_indices = list(set(checked_indices + selected_indices))
            self.parent().highlight_selected_features(all_indices)

    def leaveEvent(self, event):
        """当鼠标离开对话框时"""
        if hasattr(self.parent(), 'highlight_selected_features'):
            # 恢复显示选中的要素
            self.update_highlight()
        super().leaveEvent(event)

class MainWindow(QMainWindow):
    _instance = None

    @classmethod
    def instance(cls):
        if cls._instance is None:
            # 自动创建实例（如果需要）
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        if MainWindow._instance is not None:
            raise RuntimeError("MainWindow是单例类，请使用instance()方法获取实例")
        super().__init__()
        MainWindow._instance = self
        gdal_path = r'C:\Users\<USER>\AppData\Local\ESRI\conda\envs\autooffset\Library\share\gdal'
        os.environ['GDAL_DATA'] = gdal_path

        # 验证GDAL路径是否存在
        if not os.path.exists(os.path.join(gdal_path, 'gdalvrt.xsd')):
            QMessageBox.warning(self, "GDAL配置错误",
                f"未找到gdalvrt.xsd文件，请检查路径是否正确：\n{gdal_path}")

        # 导入所需模块
        self.math = math  # 保存到实例变量以供后续使用

        self.setWindowTitle("GridSmart")
        self.setWindowState(Qt.WindowMaximized)

        # 初始化属性
        self.shape_drawer = None
        self.is_drawing = False
        self.initial_sizes = [200, 800]
        self.command_manager = CommandManager()  # 初始化命令管理器
        self.point_offset_manager = PointOffsetManager()  # 初始化点位偏移管理器

        # 加载项目配置
        self.load_project_config()

        # 如果没有配置，创建默认配置
        if not hasattr(self, 'project_config') or not self.project_config:
            self.project_config = {
                'image_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\image",
                'shape_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\shape",
                'model_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\trained_models",
                'dataset_path': r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\shape"  # 添加 dataset_path
            }

        # 创建图标目录
        icons_dir = os.path.join(os.path.dirname(__file__), 'icons')
        if not os.path.exists(icons_dir):
            os.makedirs(icons_dir)

        # 创建默认图标
        self.create_default_icons()

        # 初始化坐标系统
        self.init_coordinate_systems()

        # 创建菜单栏
        self.create_menu_bar()

        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 创建水平布局
        hbox = QHBoxLayout(main_widget)
        hbox.setContentsMargins(0, 0, 0, 0)

        # 创建左侧面板
        self.create_left_panel()

        # 创建右侧面板（OpenStreetMap）
        if HAS_WEBENGINE:
            self.create_map_view()
        else:
            self.map_view = QLabel("Map not available.\nPlease install PyQtWebEngine.")
            self.map_view.setStyleSheet("background-color: #f0f0f0; padding: 20px;")

        # 创建主分割器（左右面板）
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.addWidget(self.left_panel)
        self.main_splitter.addWidget(self.map_view)

        # 设置主分割器的初始大小比例 (左:右 = 1:4)
        self.main_splitter.setSizes(self.initial_sizes)

        hbox.addWidget(self.main_splitter)

        # 创建状态栏
        self.create_status_bar()

        # 设置统一的消息框样式
        self.set_uniform_messagebox_style()

        # 在 __init__ 方法中添加（在 self.setWindowTitle 之后）
        self.is_selecting_zone = False

        # 初始化偏移相关的状态
        self.is_dragging_point = False
        self.current_point_id = None
        self.drag_start_pos = None

        # 初始化手动偏移工具相关的变量
        self.manual_offset_tool = None  # 只声明变量，不立即创建实例
        self.manual_offset_action = None  # 初始化手动偏移菜单项的引用

    def init_coordinate_systems(self):
        """初始化坐标系统字典"""
        # 基础坐标系统
        self.coordinate_systems = {
            "WGS 84": "EPSG:4326",
            "Web Mercator": "EPSG:3857"
        }

        # 添加基于International 1924椭球的UTM投影
        self.utm_systems = {
            "UTM Zone 37N (Int 1924)": "EPSG:20437",  # International 1924 UTM Zone 37N
            "UTM Zone 38N (Int 1924)": "EPSG:20438",  # International 1924 UTM Zone 38N
            "UTM Zone 39N (Int 1924)": "EPSG:20439"   # International 1924 UTM Zone 39N
        }

        # 合并所有坐标系统
        self.coordinate_systems.update(self.utm_systems)

    def get_current_crs(self):
        """获取当前选择的坐标系统"""
        if hasattr(self, 'crs_combo'):
            current_text = self.crs_combo.currentText()
            if current_text in self.coordinate_systems:
                return self.coordinate_systems[current_text]
        # 如果没有选择或出现错误，返回默认坐标系统
        return "EPSG:4326"  # WGS 84作为默认值

    def create_left_panel(self):
        """创建左侧面板"""
        self.left_panel = QWidget()
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(2, 2, 2, 2)

        # 创建浏览器树和图层树的垂直分割器
        self.left_splitter = QSplitter(Qt.Vertical)

        # 创建浏览器面板
        self.browser_panel = QWidget()
        browser_layout = QVBoxLayout(self.browser_panel)
        browser_layout.setContentsMargins(2, 2, 2, 2)

        browser_header = QWidget()
        browser_header_layout = QHBoxLayout(browser_header)
        browser_header_layout.setContentsMargins(2, 2, 2, 2)

        browser_label = QLabel("浏览")
        browser_header_layout.addWidget(browser_label)
        browser_header_layout.addStretch()

        self.browser_tree = QTreeWidget()
        self.browser_tree.setHeaderHidden(True)

        # 设置统一的字体样式
        self.set_tree_widget_font(self.browser_tree)

        # 添加文件系统浏览功能
        self.update_file_system_tree()

        browser_layout.addWidget(browser_header)
        browser_layout.addWidget(self.browser_tree)

        # 创建图层面板
        self.layers_panel = QWidget()
        layers_layout = QVBoxLayout(self.layers_panel)
        layers_layout.setContentsMargins(2, 2, 2, 2)

        layers_header = QWidget()
        layers_header_layout = QHBoxLayout(layers_header)
        layers_header_layout.setContentsMargins(2, 2, 2, 2)

        layers_label = QLabel("图层")
        layers_header_layout.addWidget(layers_label)
        layers_header_layout.addStretch()

        self.layers_tree = QTreeWidget()
        self.layers_tree.setHeaderHidden(True)
        self.layers_tree.itemClicked.connect(self.on_layer_clicked)
        self.layers_tree.itemDoubleClicked.connect(self.on_layer_double_clicked)

        # 设置统一的字体样式
        self.set_tree_widget_font(self.layers_tree)

        # 为图层树添加工具提示功能
        self.layers_tree.setMouseTracking(True)
        self.layers_tree.itemEntered.connect(self.show_layer_tooltip)

        layers_layout.addWidget(layers_header)
        layers_layout.addWidget(self.layers_tree)

        # 将面板添加到分割器
        self.left_splitter.addWidget(self.browser_panel)
        self.left_splitter.addWidget(self.layers_panel)

        left_layout.addWidget(self.left_splitter)

        # 移除所有拖放相关的设置
        self.browser_tree.setDragEnabled(False)
        self.layers_tree.setAcceptDrops(False)

        # 为图层树添加右键菜单
        self.layers_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.layers_tree.customContextMenuRequested.connect(self.show_layer_context_menu)

        # 连接双击事件
        self.browser_tree.itemDoubleClicked.connect(self.on_browser_item_double_clicked)

    def update_layer_preview(self):
        """更新图层预览"""
        pass

    def on_layer_clicked(self, item, column):
        """当图层被点击时更新预览"""
        pass

    def on_layer_double_clicked(self, item, column):
        """当图层被双击时缩放到图层位置"""
        try:
            layer_name = item.text(0)
            file_path = item.data(0, Qt.UserRole)

            if not HAS_WEBENGINE:
                return

            # 执行JavaScript缩放到图层
            js_code = f"""
                if (window.layerGroups && window.layerGroups['{layer_name}']) {{
                    try {{
                        var layer = window.layerGroups['{layer_name}'];
                        var bounds = layer.getBounds();
                        map.fitBounds(bounds, {{
                            padding: [20, 20],  // 添加一些边距
                            maxZoom: 18         // 限制最大缩放级别
                        }});
                    }} catch (error) {{
                        // 静默处理错误
                    }}
                }} else {{
                    // 尝试查找相似名称的图层
                    if (window.layerGroups) {{
                        var layerKeys = Object.keys(window.layerGroups);
                        for (var key of layerKeys) {{
                            if (key.includes('{layer_name}') || '{layer_name}'.includes(key)) {{
                                try {{
                                    var layer = window.layerGroups[key];
                                    var bounds = layer.getBounds();
                                    map.fitBounds(bounds, {{
                                        padding: [20, 20],
                                        maxZoom: 18
                                    }});
                                    break;
                                }} catch (error) {{
                                    // 静默处理错误
                                }}
                            }}
                        }}
                    }}
                }}
            """

            self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            pass  # 静默处理错误

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 模型训练菜单
        model_menu = menubar.addMenu("模型训练")

        # 添加项目设置菜单项
        project_settings_action = QAction("项目设置", self)
        project_settings_action.triggered.connect(self.show_project_settings_dialog)
        model_menu.addAction(project_settings_action)

        model_menu.addSeparator()

        # 数据集下载子菜单
        dataset_menu = model_menu.addMenu("数据集下载")

        # 选择区域下载数据集
        select_zone_action = QAction('选择区域下载数据集', self)
        select_zone_action.triggered.connect(self.activate_select_zone)
        dataset_menu.addAction(select_zone_action)

        generate_dataset_action = QAction('生成数据集', self)
        generate_dataset_action.triggered.connect(self.show_generate_dataset_dialog)
        dataset_menu.addAction(generate_dataset_action)

        dataset_menu.addSeparator()

        # Shape文件范围下载
        shape_download_action = QAction('Shape文件范围下载', self)
        shape_download_action.triggered.connect(self.show_shape_download_dialog)
        dataset_menu.addAction(shape_download_action)

        model_menu.addSeparator()

        # 数据集管理
        dataset_management_action = QAction('数据集管理', self)
        dataset_management_action.triggered.connect(self.show_dataset_management_dialog)
        model_menu.addAction(dataset_management_action)

        model_menu.addSeparator()

        # 添加模型训练菜单项
        train_action = QAction("训练模型", self)
        train_action.triggered.connect(self.show_model_training_dialog)
        model_menu.addAction(train_action)

        # 模型推理菜单
        inference_menu = menubar.addMenu("模型推理")

        # 自训练模型推理
        local_custom_model_action = QAction("自训练模型推理", self)
        local_custom_model_action.triggered.connect(self.run_local_custom_model_inference)
        inference_menu.addAction(local_custom_model_action)

        # HuggingFace模型推理
        local_hf_action = QAction("HuggingFace模型", self)
        local_hf_action.triggered.connect(self.run_local_hf_inference)
        inference_menu.addAction(local_hf_action)

        # SAM模型推理
        local_sam_action = QAction("SAM模型", self)
        local_sam_action.triggered.connect(self.run_local_sam_inference)
        inference_menu.addAction(local_sam_action)

        # 添加图层菜单
        layer_menu = menubar.addMenu('图层')

        # 添加加载图层选项
        load_layer_action = QAction('加载图层', self)
        load_layer_action.triggered.connect(self.load_layer_from_menu)
        layer_menu.addAction(load_layer_action)

        layer_menu.addSeparator()

        # 添加新建Polygon Shape文件选项
        new_shape_action = QAction('新建Polygon Shape文件', self)
        new_shape_action.triggered.connect(self.create_new_shape_file)
        layer_menu.addAction(new_shape_action)

        layer_menu.addSeparator()

        # 删除图层
        delete_layer_action = QAction('删除图层', self)
        # delete_layer_action.triggered.connect(self.delete_layer)  # 如果有对应的方法
        layer_menu.addAction(delete_layer_action)

        # 图层属性
        layer_properties_action = QAction('图层属性', self)
        # layer_properties_action.triggered.connect(self.show_layer_properties)  # 如果有对应的方法
        layer_menu.addAction(layer_properties_action)

        # 添加视图菜单
        view_menu = menubar.addMenu('视图')

        # 创建Browser面板动作
        self.browser_action = QAction('Browser', self)
        self.browser_action.setCheckable(True)
        self.browser_action.setChecked(True)
        self.browser_action.triggered.connect(self.toggle_browser_panel)

        # 创建Layer面板动作
        self.layer_action = QAction('Layer', self)
        self.layer_action.setCheckable(True)
        self.layer_action.setChecked(True)
        self.layer_action.triggered.connect(self.toggle_layers_panel)

        # 添加动作到视图菜单
        view_menu.addAction(self.browser_action)
        view_menu.addAction(self.layer_action)

        # 添加点位偏移菜单
        point_offset_menu = menubar.addMenu('点位偏移')

        # 加载障碍物
        load_obstacles_action = QAction("加载障碍物", self)
        load_obstacles_action.triggered.connect(self.load_obstacles)
        point_offset_menu.addAction(load_obstacles_action)

        # 加载网格
        load_grid_action = QAction("加载网格/网格点", self)
        load_grid_action.triggered.connect(self.load_grid)
        point_offset_menu.addAction(load_grid_action)

        # 搜索障碍物内的点
        search_points_action = QAction("搜索障碍物内的点", self)
        search_points_action.triggered.connect(self.search_points_in_obstacles)
        point_offset_menu.addAction(search_points_action)

        # 自动偏移设置
        offset_settings_action = QAction("偏移设置", self)
        offset_settings_action.triggered.connect(self.show_offset_settings)
        point_offset_menu.addAction(offset_settings_action)

        # 执行自动偏移
        auto_offset_action = QAction("执行自动偏移", self)
        auto_offset_action.triggered.connect(self.execute_auto_offset)
        point_offset_menu.addAction(auto_offset_action)

        # 添加分隔符
        point_offset_menu.addSeparator()

        # 撤销/重做
        undo_action = QAction("撤销", self)
        undo_action.triggered.connect(self.command_manager.undo)
        point_offset_menu.addAction(undo_action)

        redo_action = QAction("重做", self)
        redo_action.triggered.connect(self.command_manager.redo)
        point_offset_menu.addAction(redo_action)

        # 添加生成网格动作
        generate_grid_action = QAction('生成网格', self)
        generate_grid_action.triggered.connect(self.show_grid_generator)
        point_offset_menu.addAction(generate_grid_action)

        # 添加帮助菜单
        help_menu = menubar.addMenu("帮助")
        deepseek_action = QAction("DeepSeek查询", self)
        deepseek_action.triggered.connect(self.show_deepseek_chat)
        help_menu.addAction(deepseek_action)

        # 在点位偏移菜单中添加手动偏移选项
        manual_offset_action = QAction("手动偏移", self)
        manual_offset_action.setCheckable(True)
        manual_offset_action.setChecked(False)  # 确保初始状态为关闭
        manual_offset_action.triggered.connect(self.toggle_manual_offset)
        point_offset_menu.addAction(manual_offset_action)
        self.manual_offset_action = manual_offset_action  # 保存引用以便后续使用

        # 添加导出偏移结果菜单项
        export_offset_action = QAction("导出偏移结果", self)
        export_offset_action.triggered.connect(self.export_offset_results_to_csv)
        point_offset_menu.addAction(export_offset_action)

        # 设置统一的菜单字体样式
        self.set_uniform_menu_font(menubar)

    def set_uniform_menu_font(self, menubar):
        """设置统一的菜单字体样式"""
        try:
            from PyQt5.QtGui import QFont

            # 创建统一的字体
            menu_font = QFont()
            menu_font.setFamily("Microsoft YaHei UI")  # 使用微软雅黑字体
            menu_font.setPointSize(9)  # 设置字体大小
            menu_font.setBold(False)   # 确保不加粗

            # 应用到菜单栏
            menubar.setFont(menu_font)

            # 递归应用到所有菜单和子菜单
            for action in menubar.actions():
                if action.menu():
                    self.apply_font_to_menu(action.menu(), menu_font)
                else:
                    action.setFont(menu_font)

        except Exception as e:
            print(f"设置菜单字体失败: {e}")

    def apply_font_to_menu(self, menu, font):
        """递归应用字体到菜单及其子菜单"""
        try:
            menu.setFont(font)

            for action in menu.actions():
                if action.menu():
                    # 递归处理子菜单
                    self.apply_font_to_menu(action.menu(), font)
                else:
                    # 应用字体到菜单项
                    action.setFont(font)

        except Exception as e:
            print(f"应用菜单字体失败: {e}")

    def set_tree_widget_font(self, tree_widget):
        """设置树形控件的统一字体样式"""
        try:
            from PyQt5.QtGui import QFont

            # 创建统一的字体
            tree_font = QFont()
            tree_font.setFamily("Microsoft YaHei UI")  # 使用微软雅黑字体
            tree_font.setPointSize(9)  # 设置字体大小
            tree_font.setBold(False)   # 确保不加粗

            # 应用到树形控件
            tree_widget.setFont(tree_font)

            # 设置样式表确保所有项目都使用相同字体
            tree_widget.setStyleSheet("""
                QTreeWidget {
                    font-family: "Microsoft YaHei UI";
                    font-size: 9pt;
                    font-weight: normal;
                }
                QTreeWidget::item {
                    font-family: "Microsoft YaHei UI";
                    font-size: 9pt;
                    font-weight: normal;
                    padding: 2px;
                }
                QTreeWidget::item:selected {
                    font-family: "Microsoft YaHei UI";
                    font-size: 9pt;
                    font-weight: normal;
                }
            """)

        except Exception as e:
            print(f"设置树形控件字体失败: {e}")

    def set_status_bar_font(self, status_bar):
        """设置状态栏的统一字体样式"""
        try:
            from PyQt5.QtGui import QFont

            # 创建统一的字体
            status_font = QFont()
            status_font.setFamily("Microsoft YaHei UI")  # 使用微软雅黑字体
            status_font.setPointSize(9)  # 设置字体大小
            status_font.setBold(False)   # 确保不加粗

            # 应用到状态栏
            status_bar.setFont(status_font)

            # 设置样式表确保状态栏所有元素都使用相同字体
            status_bar.setStyleSheet("""
                QStatusBar {
                    font-family: "Microsoft YaHei UI";
                    font-size: 9pt;
                    font-weight: normal;
                }
                QStatusBar QLabel {
                    font-family: "Microsoft YaHei UI";
                    font-size: 9pt;
                    font-weight: normal;
                }
                QStatusBar QComboBox {
                    font-family: "Microsoft YaHei UI";
                    font-size: 9pt;
                    font-weight: normal;
                }
            """)

        except Exception as e:
            print(f"设置状态栏字体失败: {e}")

    def set_context_menu_font(self, menu):
        """设置右键菜单的统一字体样式"""
        try:
            from PyQt5.QtGui import QFont

            # 创建统一的字体
            menu_font = QFont()
            menu_font.setFamily("Microsoft YaHei UI")  # 使用微软雅黑字体
            menu_font.setPointSize(9)  # 设置字体大小
            menu_font.setBold(False)   # 确保不加粗

            # 应用到菜单
            menu.setFont(menu_font)

            # 递归应用到所有菜单项和子菜单
            for action in menu.actions():
                if action.menu():
                    # 递归处理子菜单
                    self.set_context_menu_font(action.menu())
                else:
                    # 应用字体到菜单项
                    action.setFont(menu_font)

        except Exception as e:
            print(f"设置右键菜单字体失败: {e}")

    def set_uniform_messagebox_style(self):
        """设置统一的消息框样式"""
        try:
            # 设置全局样式表，统一消息框字体
            app_style = """
            QMessageBox {
                font-family: "Microsoft YaHei UI";
                font-size: 9pt;
                font-weight: normal;
            }
            QMessageBox QLabel {
                font-family: "Microsoft YaHei UI";
                font-size: 9pt;
                font-weight: normal;
            }
            QMessageBox QPushButton {
                font-family: "Microsoft YaHei UI";
                font-size: 9pt;
                font-weight: normal;
                min-width: 60px;
                min-height: 25px;
            }
            """
            QApplication.instance().setStyleSheet(app_style)
        except Exception as e:
            print(f"设置消息框样式失败: {e}")

    def show_uniform_message(self, title, message, msg_type="information"):
        """显示统一样式的消息框"""
        try:
            from PyQt5.QtGui import QFont

            # 创建消息框
            if msg_type == "information":
                msg_box = QMessageBox(QMessageBox.Information, title, message, QMessageBox.Ok, self)
            elif msg_type == "warning":
                msg_box = QMessageBox(QMessageBox.Warning, title, message, QMessageBox.Ok, self)
            elif msg_type == "critical":
                msg_box = QMessageBox(QMessageBox.Critical, title, message, QMessageBox.Ok, self)
            else:
                msg_box = QMessageBox(QMessageBox.Information, title, message, QMessageBox.Ok, self)

            # 设置统一字体
            font = QFont("Microsoft YaHei UI", 9)
            font.setBold(False)
            msg_box.setFont(font)

            # 设置按钮字体
            for button in msg_box.buttons():
                button.setFont(font)

            return msg_box.exec_()

        except Exception as e:
            print(f"显示消息框失败: {e}")
            # 回退到标准消息框
            if msg_type == "information":
                return QMessageBox.information(self, title, message)
            elif msg_type == "warning":
                return QMessageBox.warning(self, title, message)
            elif msg_type == "critical":
                return QMessageBox.critical(self, title, message)

    def load_layer_from_menu(self):
        """从菜单加载图层"""
        try:
            # 设置文件对话框
            file_dialog = QFileDialog()
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            file_dialog.setNameFilter("所有支持的格式 (*.shp *.geojson *.kml *.gpkg *.json);;Shapefile (*.shp);;GeoJSON (*.geojson *.json);;KML (*.kml);;GeoPackage (*.gpkg)")
            file_dialog.setWindowTitle("选择要加载的图层文件")

            # 设置默认目录为项目目录
            default_dir = os.path.dirname(os.path.abspath(__file__))
            file_dialog.setDirectory(default_dir)

            if file_dialog.exec_() == QFileDialog.Accepted:
                file_paths = file_dialog.selectedFiles()
                if file_paths:
                    file_path = file_paths[0]

                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        QMessageBox.warning(self, "错误", f"文件不存在：{file_path}")
                        return

                    # 检查文件格式
                    file_ext = os.path.splitext(file_path)[1].lower()
                    supported_formats = ['.shp', '.geojson', '.json', '.kml', '.gpkg']

                    if file_ext not in supported_formats:
                        QMessageBox.warning(self, "错误", f"不支持的文件格式：{file_ext}\n支持的格式：{', '.join(supported_formats)}")
                        return

                    # 加载图层到地图
                    if self.add_layer_to_map(file_path):
                        layer_name = os.path.basename(file_path)
                        QMessageBox.information(self, "成功", f"图层 '{layer_name}' 已成功加载到地图中")
                    else:
                        QMessageBox.warning(self, "错误", "加载图层失败，请检查文件格式和内容")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载图层时发生错误：{str(e)}")
            import traceback
            traceback.print_exc()
    def export_offset_results_to_csv(self):
        """导出偏移结果到CSV文件"""
        try:
            # 选择要导出的.shp文件
            input_file, _ = QFileDialog.getOpenFileName(
                self, "选择要导出的Shape文件", "", "Shape文件 (*.shp)"
            )

            if not input_file:
                return

            # 选择保存CSV的位置
            output_file, _ = QFileDialog.getSaveFileName(
                self, "保存CSV文件", "", "CSV文件 (*.csv)"
            )

            if not output_file:
                return

            # 确保文件有.csv扩展名
            if not output_file.lower().endswith('.csv'):
                output_file += '.csv'

            # 读取Shape文件
            import geopandas as gpd
            import pandas as pd

            try:
                # 读取shape文件
                gdf = gpd.read_file(input_file)

                # 提取需要的数据
                result_df = pd.DataFrame()

                # 检查必要的字段是否存在
                required_fields = ['line_numbe', 'stake_numb']
                missing_fields = [field for field in required_fields if field not in gdf.columns]

                if missing_fields:
                    QMessageBox.warning(
                        self,
                        "字段缺失",
                        f"所选文件缺少必要的字段: {', '.join(missing_fields)}"
                    )
                    return

                # 提取坐标
                result_df['X'] = gdf.geometry.x
                result_df['Y'] = gdf.geometry.y

                # 提取桩号信息
                result_df['LineNumber'] = gdf['line_numbe']
                result_df['StakeNumber'] = gdf['stake_numb']

                # 添加偏移状态信息（如果存在）
                if 'offset_sta' in gdf.columns:
                    result_df['OffsetStatus'] = gdf['offset_sta']

                # 保存为CSV
                result_df.to_csv(output_file, index=False)

                # 显示成功消息
                QMessageBox.information(
                    self,
                    "导出成功",
                    f"偏移结果已成功导出到:\n{output_file}"
                )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "导出错误",
                    f"导出偏移结果时出错:\n{str(e)}"
                )
                traceback.print_exc()

        except Exception as e:
            QMessageBox.critical(
                self,
                "操作错误",
                f"导出操作失败:\n{str(e)}"
            )
            traceback.print_exc()
    def toggle_manual_offset(self, checked):
        """切换手动偏移模式"""
        try:
            if checked:
                # 如果工具还没有创建，创建它
                if self.manual_offset_tool is None:
                    self.manual_offset_tool = ManualOffsetTool(self)
                    self.manual_offset_tool.point_moved.connect(self.on_point_manually_moved)
                self.manual_offset_tool.activate()

                # 确保Python回调函数已注册
                def handle_point_drag_complete(result):
                    try:
                        if not result:
                            print("警告：收到空的拖动结果")
                            return

                        print(f"收到原始拖动结果数据: {result}")
                        data = json.loads(result)
                        point_index = data.get('pointIndex')
                        new_position = data.get('newPosition')  # [lng, lat]

                        print(f"接收到点位拖动完成事件: 索引={point_index}, 新位置={new_position}")

                        # 如果在手动偏移模式下
                        if hasattr(self, 'manual_offset_tool') and self.manual_offset_tool.is_active:
                            print(f"手动偏移工具已激活，发送点位移动信号")
                            # 触发点位移动信号
                            self.manual_offset_tool.point_moved.emit(point_index, tuple(new_position))
                        else:
                            print(f"警告：手动偏移工具未激活或不存在")
                    except Exception as e:
                        print(f"处理点位拖动回调时出错: {str(e)}")
                        traceback.print_exc()

                # 注册Python回调到JavaScript
                js_callback = """
                    window.pyPointDragComplete = function(jsonData) {
                        console.log('Python回调函数被调用，数据: ' + jsonData);
                        return jsonData;  // 返回数据给Python
                    }
                """
                self.web_view.page().runJavaScript(js_callback)
                self.web_view.page().runJavaScript("pyPointDragComplete", handle_point_drag_complete)
                print("已设置Python回调函数")

                # 禁用地图拖动
                self.web_view.page().runJavaScript("""
                    // 禁用地图拖动
                    map.dragging.disable();
                    // 改变鼠标指针样式
                    document.getElementById('map').style.cursor = 'grab';
                    // 更新手动模式状态
                    window.manualOffsetMode = true;
                    console.log('已启用手动偏移模式');

                    // 确保保持点的原始颜色
                    if (window.circleElements && window.circleElements.length > 0) {
                        for (var i = 0; i < window.circleElements.length; i++) {
                            var circle = window.circleElements[i];
                            var originalColor = circle.originalColor || circle.options.fillColor;
                            circle.setStyle({
                                color: '#000',
                                fillColor: originalColor,
                                fillOpacity: 0.7,
                                weight: 1
                            });
                        }
                        console.log('已保持 ' + window.circleElements.length + ' 个点的原始颜色');
                    }

                    // 测试回调函数是否可用
                    if (typeof window.pyPointDragComplete === 'function') {
                        console.log('Python回调函数已正确设置');
                        try {
                            window.pyPointDragComplete('{"test": true, "message": "回调函数测试"}');
                        } catch(err) {
                            console.error('测试回调函数失败:', err);
                        }
                    } else {
                        console.error('警告：Python回调函数未设置');
                    }
                """)
            else:
                if self.manual_offset_tool is not None:
                    self.manual_offset_tool.deactivate()

                # 保存手动偏移的结果
                self.save_manual_offset_results()

                # 启用地图拖动
                self.web_view.page().runJavaScript("""
                    // 启用地图拖动
                    map.dragging.enable();
                    // 恢复默认鼠标指针
                    document.getElementById('map').style.cursor = '';
                    // 更新手动模式状态
                    window.manualOffsetMode = false;
                    console.log('已禁用手动偏移模式');

                    // 保持点的原始颜色
                    if (window.circleElements && window.circleElements.length > 0) {
                        for (var i = 0; i < window.circleElements.length; i++) {
                            var circle = window.circleElements[i];
                            var originalColor = circle.originalColor || circle.options.fillColor;
                            circle.setStyle({
                                color: '#000',
                                fillColor: originalColor,
                                fillOpacity: 0.7,
                                weight: 1
                            });
                        }
                    }
                """)

        except Exception as e:
            print(f"切换手动偏移模式时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def save_manual_offset_results(self):
        """保存手动偏移结果到自动偏移的结果文件中"""
        try:
            # 检查是否有自动偏移的结果文件
            if hasattr(self.point_offset_manager, 'last_offset_shapefile'):
                auto_offset_file = self.point_offset_manager.last_offset_shapefile
                print(f"找到自动偏移结果文件: {auto_offset_file}")

                # 检查文件是否存在
                if not os.path.exists(auto_offset_file):
                    print(f"警告：自动偏移结果文件不存在: {auto_offset_file}")
                    QMessageBox.warning(self, "警告", "找不到自动偏移的结果文件，无法保存手动偏移结果。")
                    return

                # 使用列表封装GeoDataFrame，解决作用域问题
                auto_offset_data = [None]
                original_crs = [None]

                # 读取自动偏移结果文件
                try:
                    auto_offset_data[0] = gpd.read_file(auto_offset_file)
                    original_crs[0] = auto_offset_data[0].crs
                    print(f"读取自动偏移结果文件成功，坐标系统: {original_crs[0]}")
                except Exception as e:
                    print(f"读取自动偏移结果文件失败: {str(e)}")
                    QMessageBox.warning(self, "错误", f"读取自动偏移结果文件失败: {str(e)}")
                    return

                # 获取当前点位数据（手动偏移后的）
                if hasattr(self, 'current_points') and len(self.current_points) > 0:
                    # 使用JavaScript获取当前所有点位的位置和颜色
                    get_points_js = """
                        (function() {
                            var result = [];
                            if (window.circleElements && window.circleElements.length > 0) {
                                for (var i = 0; i < window.circleElements.length; i++) {
                                    var circle = window.circleElements[i];
                                    var pos = circle.getLatLng();
                                    var color = circle.originalColor || circle.options.fillColor;
                                    var gridData = circle.gridData || {};
                                    result.push({
                                        index: i,
                                        position: [pos.lng, pos.lat],
                                        color: color,
                                        line: gridData.line,
                                        stake: gridData.stake
                                    });
                                }
                            }
                            return JSON.stringify(result);
                        })();
                    """

                    def handle_get_points_result(result):
                        try:
                            if not result:
                                print("警告：获取点位数据为空")
                                return

                            points_data = json.loads(result)
                            print(f"获取到 {len(points_data)} 个点位数据")

                            # 创建新的GeoDataFrame，与自动偏移结果结合
                            from shapely.geometry import Point
                            import pandas as pd
                            import geopandas as gpd

                            # 使用列表中的GeoDataFrame
                            auto_offset_gdf = auto_offset_data[0]

                            # 确保auto_offset_gdf有足够的行数
                            if len(points_data) > len(auto_offset_gdf):
                                print(f"警告：手动偏移点数 ({len(points_data)}) 超过了自动偏移结果点数 ({len(auto_offset_gdf)})")
                                QMessageBox.warning(self, "警告", f"手动偏移点数超过了自动偏移结果点数，部分点位可能无法保存。")

                            # 创建临时的GeoDataFrame来存储JavaScript获取的点位（WGS84坐标系）
                            temp_geometries = []
                            temp_indices = []
                            temp_colors = []
                            temp_lines = []
                            temp_stakes = []

                            for point_data in points_data:
                                idx = point_data['index']
                                if idx < len(auto_offset_gdf):
                                    pos = point_data['position']  # [lng, lat]
                                    color = point_data['color']
                                    line = point_data.get('line', None)
                                    stake = point_data.get('stake', None)

                                    # 创建几何对象（WGS84坐标系）
                                    new_point = Point(pos[0], pos[1])
                                    temp_geometries.append(new_point)
                                    temp_indices.append(idx)
                                    temp_colors.append(color)
                                    temp_lines.append(line)
                                    temp_stakes.append(stake)

                            # 创建临时GeoDataFrame
                            temp_df = pd.DataFrame({
                                'index': temp_indices,
                                'color': temp_colors,
                                'line': temp_lines,
                                'stake': temp_stakes
                            })
                            temp_gdf = gpd.GeoDataFrame(temp_df, geometry=temp_geometries, crs='EPSG:4326')

                            print(f"创建临时GeoDataFrame，包含 {len(temp_gdf)} 个点位（WGS84坐标系）")

                            # 将临时GeoDataFrame转换为原始坐标系统
                            print(f"将WGS84坐标转换为原始坐标系统: {original_crs[0]}")
                            transformed_gdf = temp_gdf.to_crs(original_crs[0])

                            # 更新auto_offset_gdf中的几何对象和状态
                            for idx, row in transformed_gdf.iterrows():
                                original_idx = row['index']
                                color = row['color']
                                #line = row['line']
                                #stake = row['stake']

                                # 更新几何对象
                                auto_offset_gdf.loc[original_idx, 'geometry'] = row.geometry
                                # 更新line和stake号
                                # 根据新位置计算网格编号
                                new_x, new_y = row.geometry.x, row.geometry.y
                                new_grid_info = self.point_offset_manager.calculate_grid_numbers(new_x, new_y)
                                if new_grid_info:
                                    line_number, stake_number = new_grid_info
                                    # 更新网格编号
                                    auto_offset_gdf.loc[original_idx, 'line_numbe'] = line_number
                                    auto_offset_gdf.loc[original_idx, 'stake_numb'] = stake_number
                                # 保留原有代码以支持从JavaScript获取的网格信息
                                # if line is not None:
                                #     auto_offset_gdf.loc[original_idx, 'line'] = line
                                # if stake is not None:
                                #     auto_offset_gdf.loc[original_idx, 'stake'] = stake

                                # 更新状态（根据颜色）
                                if color.lower() == '#ffff00' or color.lower() == 'yellow':
                                    auto_offset_gdf.loc[original_idx, 'offset_sta'] = 'manual_offset'
                                elif color.lower() == '#ff0000' or color.lower() == 'red':
                                    auto_offset_gdf.loc[original_idx, 'offset_sta'] = 'in_obstacle'
                                else:
                                    # 检查当前状态
                                    current_status = auto_offset_gdf.loc[original_idx, 'offset_sta'] if 'offset_sta' in auto_offset_gdf.columns else 'not_needed'
                                    # 只有当当前状态不是 auto_offset 时，才设置为 not_needed
                                    if current_status != 'auto_offset' and current_status != 'skipped' and current_status != 'failed' and current_status != 'error':
                                        auto_offset_gdf.loc[original_idx, 'offset_sta'] = 'not_needed'

                                # 保存原始坐标
                                # 保存原始坐标 - 避免重复字段
                                # 首先检查是否存在不同名称的状态字段
                                if 'status' in auto_offset_gdf.columns and 'offset_status' in auto_offset_gdf.columns:
                                    # 如果同时存在两个状态字段，删除status字段
                                    auto_offset_gdf = auto_offset_gdf.drop(columns=['status'])
                                    print("删除重复的状态字段 'status'")

                                # 保存原始坐标
                                if 'original_x' not in auto_offset_gdf.columns:
                                    auto_offset_gdf['original_x'] = auto_offset_gdf.geometry.x
                                if 'original_y' not in auto_offset_gdf.columns:
                                    auto_offset_gdf['original_y'] = auto_offset_gdf.geometry.y
                            # 在保存前检查字段名
                            field_names = auto_offset_gdf.columns.tolist()
                            status_fields = [f for f in field_names if 'offset_s' in f.lower()]
                            if len(status_fields) > 1:
                                print(f"发现多个状态字段: {status_fields}")
                                # 选择一个主要状态字段
                                main_status_field = 'offset_sta'
                                for field in status_fields:
                                    if field != main_status_field and field in auto_offset_gdf.columns:
                                        # 合并状态到主要字段
                                        for idx in range(len(auto_offset_gdf)):
                                            if auto_offset_gdf.loc[idx, field] in ['manual_offset', 'in_obstacle', 'auto_offset', 'failed', 'skipped', 'error', 'not_needed']:
                                                auto_offset_gdf.loc[idx, main_status_field] = auto_offset_gdf.loc[idx, field]
                                        # 删除其他状态字段
                                        auto_offset_gdf = auto_offset_gdf.drop(columns=[field])
                                        print(f"已删除多余状态字段 {field}，保留 {main_status_field}")
                            # 保存结果到原文件
                            auto_offset_gdf.to_file(auto_offset_file)
                            print(f"已成功保存手动偏移结果到: {auto_offset_file}")
                            print(f"保存后的坐标系统: {auto_offset_gdf.crs}")

                            # 显示成功消息
                            QMessageBox.information(self, "成功", f"已保存手动偏移结果到:\n{auto_offset_file}")

                        except Exception as e:
                            print(f"处理点位数据时出错: {str(e)}")
                            traceback.print_exc()
                            QMessageBox.critical(self, "错误", f"保存手动偏移结果时出错: {str(e)}")

                    # 执行JavaScript获取当前点位数据
                    self.web_view.page().runJavaScript(get_points_js, handle_get_points_result)
                else:
                    print("警告：没有找到当前点位数据")
                    QMessageBox.warning(self, "警告", "没有找到当前点位数据，无法保存手动偏移结果。")
            else:
                print("警告：未找到自动偏移的结果文件路径")
                # 检查是否需要创建新文件保存结果
                reply = QMessageBox.question(self, "保存手动偏移结果",
                                           "未找到自动偏移的结果文件，是否需要创建新文件保存手动偏移结果？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.Yes:
                    # 创建新文件保存手动偏移结果
                    self.save_manual_offset_to_new_file()
        except Exception as e:
            print(f"保存手动偏移结果时出错: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"保存手动偏移结果时出错: {str(e)}")

    def save_manual_offset_to_new_file(self):
        """创建新文件保存手动偏移结果"""
        try:
            # 获取当前点位数据
            if not hasattr(self, 'current_points') or len(self.current_points) == 0:
                print("警告：没有找到当前点位数据")
                QMessageBox.warning(self, "警告", "没有找到当前点位数据，无法保存手动偏移结果。")
                return

            # 使用JavaScript获取当前所有点位的位置和颜色
            get_points_js = """
                (function() {
                    var result = [];
                    if (window.circleElements && window.circleElements.length > 0) {
                        for (var i = 0; i < window.circleElements.length; i++) {
                            var circle = window.circleElements[i];
                            var pos = circle.getLatLng();
                            var color = circle.originalColor || circle.options.fillColor;
                            result.push({
                                index: i,
                                position: [pos.lng, pos.lat],
                                color: color
                            });
                        }
                    }
                    return JSON.stringify(result);
                })();
            """

            def handle_get_points_result(result):
                try:
                    if not result:
                        print("警告：获取点位数据为空")
                        return

                    points_data = json.loads(result)
                    print(f"获取到 {len(points_data)} 个点位数据")

                    # 创建新的GeoDataFrame
                    from shapely.geometry import Point
                    import pandas as pd
                    import geopandas as gpd
                    import os
                    from datetime import datetime

                    # 创建几何和属性数据
                    geometries = []
                    attributes = []

                    for point_data in points_data:
                        idx = point_data['index']
                        pos = point_data['position']  # [lng, lat]
                        color = point_data['color']

                        # 创建几何对象
                        point_geom = Point(pos[0], pos[1])
                        geometries.append(point_geom)

                        # 确定状态
                        # 确定状态
                        if color.lower() == '#00ff00' or color.lower() == 'green':
                            status = 'manual_offset'
                        elif color.lower() == '#ff0000' or color.lower() == 'red':
                            status = 'in_obstacle'
                        else:
                            status = 'unknown'

                        # 添加属性
                        attributes.append({
                            'id': idx,
                            'offset_sta': status,  # 使用更短的名称以避免Shapefile截断
                            'x': pos[0],
                            'y': pos[1]
                        })

                    # 创建GeoDataFrame
                    gdf = gpd.GeoDataFrame(attributes, geometry=geometries, crs='EPSG:4326')

                    # 处理保存路径
                    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'output')
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)

                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    output_file = os.path.join(output_dir, f"manual_offset_{timestamp}.shp")

                    # 保存到shapefile
                    gdf.to_file(output_file)
                    print(f"已成功保存手动偏移结果到新文件: {output_file}")

                    # 显示成功消息
                    QMessageBox.information(self, "成功", f"已保存手动偏移结果到新文件:\n{output_file}")

                    # 保存路径以便后续使用
                    self.point_offset_manager.last_offset_shapefile = output_file

                except Exception as e:
                    print(f"保存点位数据到新文件时出错: {str(e)}")
                    traceback.print_exc()
                    QMessageBox.critical(self, "错误", f"保存手动偏移结果到新文件时出错: {str(e)}")

            # 执行JavaScript获取当前点位数据
            self.web_view.page().runJavaScript(get_points_js, handle_get_points_result)

        except Exception as e:
            print(f"创建新文件保存手动偏移结果时出错: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"创建新文件保存手动偏移结果时出错: {str(e)}")

    def on_point_manually_moved(self, point_id, new_pos):
        """处理点位手动移动"""
        from Core.OffsetCommands import MovePointCommand

        try:
            print(f"开始处理点位手动移动: ID={point_id}, 新位置={new_pos}")

            # 确保坐标格式正确
            if isinstance(new_pos, (list, tuple)):
                old_pos = None
                if hasattr(self.manual_offset_tool, 'drag_start_pos'):
                    old_pos = [self.manual_offset_tool.drag_start_pos[0],
                             self.manual_offset_tool.drag_start_pos[1]]
                    print(f"使用手动偏移工具记录的起始位置: {old_pos}")
                else:
                    # 如果没有记录起始位置，则从当前点位数据中获取
                    if hasattr(self, 'grid_label_gdf') and point_id < len(self.grid_label_gdf):
                        point_geom = self.grid_label_gdf.iloc[point_id].geometry
                        old_pos = [point_geom.x, point_geom.y]
                        print(f"从网格标签数据中获取起始位置: {old_pos}")
                    else:
                        print(f"警告：无法获取点 {point_id} 的起始位置")
                        old_pos = new_pos  # 使用新位置作为起始位置（没有变化）

                # 确保新位置的格式正确
                new_pos = [new_pos[0], new_pos[1]]
                print(f"点位移动: ID={point_id}, 从 {old_pos} 到 {new_pos}")
            else:
                print(f"错误：无效的坐标格式: {new_pos}")
                return

            # 直接使用JavaScript强制设置点为绿色
            js_code = """
                // 查找并更改点的颜色
                if (window.circleElements && window.circleElements[%d]) {
                    console.log('正在更新点 #%d 的颜色为黄色');
                    // 设置新的原始颜色
                    window.circleElements[%d].originalColor = '#FFFF00';
                    // 应用新的样式
                    window.circleElements[%d].setStyle({
                        color: '#000',
                        fillColor: '#FFFF00',
                        fillOpacity: 0.7,
                        weight: 1
                    });
                    console.log('点 #%d 的颜色已更新为黄色');
                }
            """ % (point_id, point_id, point_id, point_id, point_id)

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)
            print(f"已执行JavaScript代码更新点 {point_id} 的颜色为黄色")

            # 创建移动点位的命令
            command = MovePointCommand(
                point_id,
                old_pos,
                new_pos,
                self.update_point_position
            )
            self.command_manager.execute_command(command)
            print(f"已执行移动点位命令: 点 {point_id} 从 {old_pos} 到 {new_pos}")

            # 同步更新原始点位的坐标
            if hasattr(self, 'grid_label_gdf') and point_id < len(self.grid_label_gdf):
                # 将WGS84坐标转换为当前CRS
                from shapely.geometry import Point
                wgs84_point = Point(new_pos[0], new_pos[1])

                if self.grid_label_gdf.crs != 'EPSG:4326':
                    from geopandas import GeoDataFrame
                    import pandas as pd
                    temp_df = GeoDataFrame([{'geometry': wgs84_point}], crs='EPSG:4326')
                    transformed_df = temp_df.to_crs(self.grid_label_gdf.crs)
                    transformed_point = transformed_df.geometry[0]
                    # 更新点位
                    self.grid_label_gdf.loc[point_id, 'geometry'] = transformed_point
                    print(f"已更新点 {point_id} 在地理数据中的位置（坐标系转换）")
                else:
                    # 如果已经是WGS84，直接更新
                    self.grid_label_gdf.loc[point_id, 'geometry'] = wgs84_point
                    print(f"已更新点 {point_id} 在地理数据中的位置（直接WGS84）")

                print(f"点位更新完成: ID={point_id}, 新位置={new_pos}")
            else:
                print(f"警告：无法在地理数据中找到点 {point_id}")

        except Exception as e:
            print(f"处理点位移动时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_point_position(self, point_id: int, new_pos):
        """更新点位位置"""
        try:
            print(f"开始更新点位位置: ID={point_id}, 新位置={new_pos}")

            # 确保输入格式正确
            if isinstance(new_pos, (list, tuple)):
                # 坐标格式为 [lon, lat]
                lon, lat = new_pos[0], new_pos[1]
            elif isinstance(new_pos, dict) and 'lng' in new_pos and 'lat' in new_pos:
                # 坐标格式为 {lat: y, lng: x}
                lon, lat = new_pos['lng'], new_pos['lat']
            elif isinstance(new_pos, dict) and 'lat' in new_pos and 'lon' in new_pos:
                # 坐标格式为 {lat: y, lon: x}
                lon, lat = new_pos['lon'], new_pos['lat']
            else:
                print(f"错误：无效的坐标格式: {new_pos}")
                return

            # 最简化版本：直接在JavaScript中更新点位位置和颜色为绿色
            js_code = f"""
                // 更新点位位置和颜色
                if (window.circleElements && window.circleElements[{point_id}]) {{
                    console.log('正在更新点 #{point_id} 的位置和颜色');

                    // 获取当前属性
                    var radius = window.circleElements[{point_id}].options.radius;
                    var originalPointIndex = window.circleElements[{point_id}].pointIndex || {point_id};

                    // 从地图上移除旧点
                    map.removeLayer(window.circleElements[{point_id}]);

                    // 创建新的绿色点
                    window.circleElements[{point_id}] = L.circle([{lat}, {lon}], {{
                        radius: radius,
                        fillColor: '#FFFF00',  // 黄色
                        color: '#000',
                        weight: 1,
                        opacity: 1,
                        fillOpacity: 0.7
                    }});

                    // 设置原始颜色和点索引
                    window.circleElements[{point_id}].originalColor = '#FFFF00';  // 黄色
                    window.circleElements[{point_id}].pointIndex = originalPointIndex;

                    // 添加到地图
                    window.circleElements[{point_id}].addTo(map);

                    // 如果存在pointLayerGroup，也添加到点图层组
                    if (window.pointLayerGroup) {{
                        window.pointLayerGroup.addLayer(window.circleElements[{point_id}]);
                    }}

                    console.log('点 #{point_id} 已更新位置到 [{lat}, {lon}] 并变为黄色');
                }} else {{
                    console.error('找不到点 #{point_id}');
                }}
            """

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)
            print(f"已执行JavaScript代码更新点 {point_id} 的位置到 [{lat}, {lon}] 并设置为绿色")

        except Exception as e:
            print(f"更新点位时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def clear_selection_area(self):
        """清除地图上的选择区域"""
        js_code = """
            // 清除选择区域多边形
            if (map.selectArea && map.selectArea.polygon) {
                map.removeLayer(map.selectArea.polygon);
                map.selectArea.polygon = null;
            }

            // 清除临时线条
            if (map.selectArea && map.selectArea.polyline) {
                map.removeLayer(map.selectArea.polyline);
                map.selectArea.polyline = null;
            }

            // 重置选择状态
            if (map.selectArea) {
                map.selectArea.enabled = false;
                map.selectArea.points = [];
            }

            // 移除地图样式
            document.getElementById('map').classList.remove('selecting');
        """
        self.web_view.page().runJavaScript(js_code)

        # 重置选择状态
        self.is_selecting_zone = False
        self.selected_area_points = []

    def activate_select_zone(self):
        """激活选择图像区域模式"""
        self.is_selecting_zone = not self.is_selecting_zone

        # 显示提示信息
        if self.is_selecting_zone:
            QMessageBox.information(self, "区域选择", "请点击地图创建选择区域的顶点，双击完成选择")

        # 执行JavaScript来启用区域选择模式
        js_code = """
        // 添加样式
        if (!document.getElementById('map-cursor-style')) {
            var style = document.createElement('style');
            style.id = 'map-cursor-style';
            style.textContent = '#map.selecting { cursor: crosshair !important; }';
            document.head.appendChild(style);
        }

        if (typeof L.selectArea === 'undefined') {
            // 初始化选择区域功能
            map.selectArea = {
                enabled: false,
                points: [],
                polygon: null,
                polyline: null
            };

            // 添加鼠标点击事件监听器
            map.on('click', function(e) {
                if (!map.selectArea.enabled) return;

                // 添加新的点
                map.selectArea.points.push(e.latlng);

                // 如果是第一个点
                if (map.selectArea.points.length === 1) {
                    // 创建多边形
                    map.selectArea.polygon = L.polygon([e.latlng], {
                        color: 'red',
                        weight: 2,
                        fillOpacity: 0.2
                    }).addTo(map);

                    // 创建临时线条（用于显示正在绘制的线）
                    map.selectArea.polyline = L.polyline([e.latlng], {
                        color: 'red',
                        weight: 2,
                        dashArray: '5, 10'
                    }).addTo(map);
                } else {
                    // 更新多边形和线条
                    map.selectArea.polygon.setLatLngs(map.selectArea.points);
                    map.selectArea.polyline.setLatLngs(map.selectArea.points);
                }
            });

            // 添加鼠标移动事件监听器（用于显示预览线）
            map.on('mousemove', function(e) {
                if (!map.selectArea.enabled || map.selectArea.points.length === 0) return;

                let points = [...map.selectArea.points];
                points.push(e.latlng);
                map.selectArea.polyline.setLatLngs(points);
            });

            // 添加双击事件监听器（完成绘制）
            map.on('dblclick', function(e) {
                if (!map.selectArea.enabled || map.selectArea.points.length < 3) return;

                // 移除最后一个点（双击会触发一个额外的点击事件）
                map.selectArea.points.pop();

                // 更新最终的多边形
                map.selectArea.polygon.setLatLngs(map.selectArea.points);

                // 移除临时线条
                if (map.selectArea.polyline) {
                    map.removeLayer(map.selectArea.polyline);
                    map.selectArea.polyline = null;
                }

                // 计算边界并缩放
                var bounds = map.selectArea.polygon.getBounds();
                map.fitBounds(bounds, {
                    padding: [50, 50]
                });

                // 保存选定的区域坐标
                if (window.bridge) {
                    var points = map.selectArea.points.map(function(point) {
                        return {lat: point.lat, lng: point.lng};
                    });
                    window.bridge.saveSelectedArea(JSON.stringify(points));
                }

                // 禁用选择模式
                map.selectArea.enabled = false;
                document.getElementById('map').classList.remove('selecting');
                map.selectArea.points = [];
            });
        }

        // 切换选择模式
        map.selectArea.enabled = !map.selectArea.enabled;
        if (map.selectArea.enabled) {
            // 清理之前的选择（如果有）
            if (map.selectArea.polygon) {
                map.removeLayer(map.selectArea.polygon);
                map.selectArea.polygon = null;
            }
            if (map.selectArea.polyline) {
                map.removeLayer(map.selectArea.polyline);
                map.selectArea.polyline = null;
            }
            map.selectArea.points = [];

            // 启用选择模式的鼠标样式
            document.getElementById('map').classList.add('selecting');
        } else {
            // 禁用选择模式的鼠标样式
            document.getElementById('map').classList.remove('selecting');
        }
        """
        self.web_view.page().runJavaScript(js_code)

    @pyqtSlot(str)
    def saveSelectedArea(self, points_json):
        """保存选定的区域坐标并根据当前推理类型进行处理"""
        # 解析选择的区域坐标
        points = json.loads(points_json)

        # 如果没有选择区域，直接返回
        if not points or len(points) < 3:  # 至少需要3个点才能形成一个有效的多边形
            QMessageBox.warning(self, "警告", "请选择一个有效的区域！")
            return

        # 保存坐标点到txt文件
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            txt_filename = os.path.join(self.project_config['dataset_path'], f'selected_area_coords_{timestamp}.txt')

            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write("# 选择区域的坐标点列表 (经度,纬度)\n")
                for point in points:
                    f.write(f"{point['lng']},{point['lat']}\n")

            QMessageBox.information(self, "成功", f"已将坐标点保存到文件：\n{txt_filename}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存坐标文件时发生错误：{str(e)}")

        # 保存选择区域为shape文件
        if HAS_GEOPANDAS:
            try:
                # 创建一个临时文件名
                shape_filename = os.path.join(self.project_config['dataset_path'], f'selected_area_{timestamp}.shp')

                # 创建一个包含多边形的GeoDataFrame
                # 注意：points中的坐标是[lng, lat]格式，需要创建一个闭合的多边形
                coords = [[p['lng'], p['lat']] for p in points]
                if coords[0] != coords[-1]:  # 确保多边形是闭合的
                    coords.append(coords[0])

                from shapely.geometry import Polygon
                polygon = Polygon(coords)
                gdf = gpd.GeoDataFrame(geometry=[polygon], crs='EPSG:4326')

                # 保存为shape文件
                gdf.to_file(shape_filename)

                # 将新创建的shape文件添加到图层列表
                self.add_layer_to_list(shape_filename, shape_filename)

                QMessageBox.information(self, "成功", f"已将选择区域保存为Shape文件：\n{shape_filename}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存Shape文件时发生错误：{str(e)}")

        # 根据当前推理类型进行处理
        if hasattr(self, 'current_inference_type'):
            if self.current_inference_type == "local_custom":
                self.process_local_custom_inference(points)
            elif self.current_inference_type == "local_hf":
                self.process_local_hf_inference(points)
            elif self.current_inference_type == "local_sam":
                self.process_local_sam_inference(points)
            elif self.current_inference_type.startswith("cloud_"):
                self.process_cloud_inference(points)

            # 清除当前推理类型
            delattr(self, 'current_inference_type')
            if hasattr(self, 'current_model_path'):
                delattr(self, 'current_model_path')

        else:
            # 如果不是模型推理操作，则保存为生成数据集使用的区域
            self.selected_area_points = points
            print(f"已保存选定区域坐标：{points}")

    def toggle_browser_panel(self):
        """切换浏览器面板的显示状态"""
        visible = not self.browser_panel.isVisible()
        self.browser_panel.setVisible(visible)
        self.browser_action.setChecked(visible)
        self.adjust_panel_sizes()

    def toggle_layers_panel(self):
        """切换图层面板的显示状态"""
        visible = not self.layers_panel.isVisible()
        self.layers_panel.setVisible(visible)
        self.layer_action.setChecked(visible)
        self.adjust_panel_sizes()

    def adjust_panel_sizes(self):
        """调整面板大小"""
        # 获取当前左侧面板的总宽度
        left_width = self.initial_sizes[0]
        right_width = self.initial_sizes[1]
        total_width = sum(self.initial_sizes)

        # 检查面板的可见性
        browser_visible = self.browser_panel.isVisible()
        layers_visible = self.layers_panel.isVisible()

        if browser_visible and layers_visible:
            # 两个面板都可见，恢复原始大小
            self.left_splitter.setSizes([left_width/2, left_width/2])
            self.main_splitter.setSizes([left_width, right_width])
        elif not browser_visible and not layers_visible:
            # 两个面板都隐藏，地图占据全部空间
            self.main_splitter.setSizes([0, total_width])
        else:
            # 只有一个面板可见，调整左侧面板大小
            self.main_splitter.setSizes([left_width, right_width])
            if browser_visible:
                self.left_splitter.setSizes([left_width, 0])
            else:
                self.left_splitter.setSizes([0, left_width])

    @pyqtSlot(float, float, float)
    def updateCoordinates(self, lat, lng, zoom):
        """更新状态栏中的坐标显示"""
        try:
            # 导入必要的库
            from pyproj import CRS, Transformer

            # 获取当前选择的坐标系统
            current_crs = self.crs_combo.currentText()
            if "UTM" in current_crs:
                # 使用当前选择的UTM投影
                utm_proj4 = self.utm_systems[current_crs]
                utm_crs = CRS.from_proj4(utm_proj4)

                # 创建从WGS84到UTM的转换器
                transformer = Transformer.from_crs("EPSG:4326", utm_crs, always_xy=True)

                # 转换坐标（注意：lng在前，lat在后）
                x, y = transformer.transform(lng, lat)

                # 更新状态栏
                self.coordinate_label.setText(f"坐标: E {x:.2f}m, N {y:.2f}m | 经度: {lng:.6f}°, 纬度: {lat:.6f}° | 缩放: {zoom:.1f}")
            else:
                # 如果不是UTM系统，只显示经纬度
                self.coordinate_label.setText(f"经度: {lng:.6f}°, 纬度: {lat:.6f}° | 缩放: {zoom:.1f}")

        except Exception as e:
            print(f"更新坐标时出错: {str(e)}")
            # 发生错误时显示原始经纬度
            self.coordinate_label.setText(f"经度: {lng:.6f}°, 纬度: {lat:.6f}° | 缩放: {zoom:.1f}")

    def create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # 设置状态栏统一字体样式
        self.set_status_bar_font(status_bar)

        # 创建坐标标签
        self.coordinate_label = QLabel("")
        self.coordinate_label.setMinimumWidth(250)

        # 创建坐标系统选择下拉框
        crs_label = QLabel("坐标系统: ")
        self.crs_combo = QComboBox()

        # 设置控件字体
        from PyQt5.QtGui import QFont
        status_font = QFont("Microsoft YaHei UI", 9)
        status_font.setBold(False)

        self.coordinate_label.setFont(status_font)
        crs_label.setFont(status_font)
        self.crs_combo.setFont(status_font)

        # 添加所有坐标系统
        crs_items = list(self.coordinate_systems.keys())
        self.crs_combo.addItems(crs_items)
        self.crs_combo.setMinimumWidth(200)

        # 设置默认坐标系统为 WGS 84
        default_crs = "WGS 84"
        index = self.crs_combo.findText(default_crs)
        if index >= 0:
            self.crs_combo.setCurrentIndex(index)
            print(f"已设置默认坐标系统: {default_crs}")
            # 手动触发一次坐标系统改变事件
            self.change_coordinate_system(index)
        else:
            print(f"警告：未找到默认坐标系统 {default_crs}")
            if crs_items:
                self.crs_combo.setCurrentIndex(0)
                print(f"使用第一个可用的坐标系统: {crs_items[0]}")
                self.change_coordinate_system(0)

        # 连接信号
        self.crs_combo.currentIndexChanged.connect(self.change_coordinate_system)

        # 添加到状态栏
        status_bar.addPermanentWidget(self.coordinate_label)
        status_bar.addPermanentWidget(crs_label)
        status_bar.addPermanentWidget(self.crs_combo)

        # 打印当前状态
        print(f"状态栏初始化完成")
        print(f"当前选择的坐标系统: {self.crs_combo.currentText()}")
        print(f"对应的EPSG/PROJ: {self.coordinate_systems.get(self.crs_combo.currentText(), 'None')}")

    def change_coordinate_system(self, index):
        """当坐标系统改变时更新坐标显示和地图"""
        try:
            current_crs = self.coordinate_systems[self.crs_combo.currentText()]

            # 更新当前显示的图层
            for i in range(self.layers_tree.topLevelItemCount()):
                item = self.layers_tree.topLevelItem(i)
                file_path = item.data(0, Qt.UserRole)
                if file_path and os.path.exists(file_path):
                    self.update_layer_crs(file_path, current_crs)

            # 如果有当前坐标，则更新显示
            if hasattr(self, 'current_lat') and hasattr(self, 'current_lng'):
                self.updateCoordinates(self.current_lat, self.current_lng, self.current_zoom)

        except Exception as e:
            print(f"Error changing coordinate system: {e}")

    def update_layer_crs(self, file_path, target_crs):
        """更新图层的坐标系统"""
        try:
            if HAS_WEBENGINE:
                import geopandas as gpd
                gdf = gpd.read_file(file_path)

                # 转换到目标坐标系统
                if gdf.crs and str(gdf.crs) != target_crs:
                    gdf = gdf.to_crs(target_crs)

                # 如果需要在地图上显示，需要转换到WGS84
                if target_crs != "EPSG:4326":
                    display_gdf = gdf.to_crs("EPSG:4326")
                else:
                    display_gdf = gdf

                # 更新地图显示
                geojson_data = display_gdf.to_json()
                js_code = f"""
                    if (typeof layerGroup !== 'undefined') {{
                        layerGroup.clearLayers();
                        var geoJsonLayer = L.geoJSON({geojson_data}, {{
                            style: {{
                                color: 'red',
                                weight: 2,
                                fillOpacity: 0.2
                            }}
                        }}).addTo(layerGroup);
                        map.fitBounds(geoJsonLayer.getBounds());
                    }}
                """
                self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"Error updating layer CRS: {e}")

    @pyqtSlot(float, float)
    def locationUpdated(self, lat, lng):
        """当获取到用户位置时被调用"""
        print(f"获取到用户位置：纬度 {lat}, 经度 {lng}")

    def show_model_training_dialog(self):
        """显示模型训练对话框"""
        try:
            # 检查是否已配置数据集
            if not self.project_config or 'dataset_path' not in self.project_config:
                QMessageBox.warning(self, "警告", "请先配置项目数据集！")
                return

            dataset_info = {
                'path': self.project_config['dataset_path']
            }

            # 检查并修复环境
            if not self.check_and_fix_environment():
                return

            dialog = ModelTrainingDialog(self, self.project_config)
            if dialog.exec_() == QDialog.Accepted:
                settings = dialog.get_settings()

                # 检查是否是LoGCAN模型，如果是则直接返回（LoGCAN有自己的处理逻辑）
                if settings['model'] == "LoGCAN":
                    print("[信息] LoGCAN训练已在独立窗口中启动")
                    return

                # 生成最终配置
                config = {
                    "dataset_path": self.project_config['dataset_path'],
                    "model": {
                        "name": settings['model'],
                        "args": settings['config']['model']['args']
                    },
                    "trainer": {
                        "epochs": settings['epochs'],
                        "batch_size": settings['batch_size'],
                        "learning_rate": settings['learning_rate'],
                        "slice_size": settings['slice_size'],
                        "overlap": settings['overlap'],
                        "save_dir": settings['save_path'],
                        "device": settings['device']
                    }
                }

                # 获取模型名称并生成配置文件名
                model_name = settings['model']
                # 保持与ModelTrainingDialog.py一致的命名规则
                if model_name == "DeepLabV3+":
                    config_filename = "deeplabv3+_config.json"
                elif model_name == "UNet":
                    config_filename = "unet_config.json"
                else:
                    config_filename = model_name.lower().replace("+", "plus").replace(" ", "_") + "_config.json"
                config_path = os.path.join("config", config_filename)

                # 保存配置文件
                os.makedirs("config", exist_ok=True)
                with open(config_path, 'w') as f:
                    json.dump(config, f, indent=4)

                try:
                    # 根据不同模型选择不同的训练脚本
                    script_map = {
                        "UNet": "Shape_Segmentation_Model_UNET.py",
                        "DeepLabV3+": "Shape_Segmentation_Model_DeepLabV3Plus.py",
                        "ResNet": "Shape_Segmentation_Model_ResNet.py",
                        "FCN": "Shape_Segmentation_Model_FCN.py"
                        # LoGCAN已移除，使用新的独立训练系统
                    }

                    # 获取训练脚本路径
                    script_file = script_map.get(model_name)
                    if script_file is None:
                        QMessageBox.critical(self, "错误", f"不支持的模型类型：{model_name}")
                        return

                    train_script = os.path.join("Core", script_file)

                    # 检查训练脚本是否存在
                    if not os.path.exists(train_script):
                        QMessageBox.critical(self, "错误", f"找不到训练脚本：{train_script}")
                        return

                    # 获取conda环境信息
                    conda_path = os.path.join(os.path.dirname(os.path.dirname(sys.executable)), "Scripts", "activate.bat")
                    conda_env = "autooffset"

                    # 获取 CUDA 路径
                    cuda_path = r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.2\bin"

                    # 构建设备相关的环境变量
                    device_env = ""
                    if settings['device']['use_gpu']:
                        device_env = f'set CUDA_VISIBLE_DEVICES={settings["device"]["gpu_id"]} && '
                    else:
                        device_env = 'set CUDA_VISIBLE_DEVICES=-1 && '

                    # 构建命令 - 设置环境变量并运行脚本
                    cmd = (
                        f'cmd /c "'
                        f'set CUDA_PATH=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.2 && '
                        f'set CUDA_HOME=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.2 && '
                        f'set PATH={cuda_path};%PATH% && '
                        f'{device_env}'
                        f'"{conda_path}" {conda_env} && '
                        f'python "{train_script}" "{config_path}" && '
                        f'pause"'
                    )

                    # 导入 subprocess
                    import subprocess

                    # 使用 subprocess.Popen 启动新窗口
                    subprocess.Popen(
                        cmd,
                        creationflags=subprocess.CREATE_NEW_CONSOLE,
                        shell=True
                    )

                    # 显示设备信息
                    device_info = "GPU " + str(settings['device']['gpu_id']) if settings['device']['use_gpu'] else "CPU"

                    QMessageBox.information(self, "提示",
                        f"训练已在新窗口中启动！\n"
                        f"使用设备: {device_info}\n"
                        f"使用数据集: {dataset_info['path']}\n"
                        f"请在终端窗口中查看训练进度。\n"
                    )
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"启动训练失败：{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示训练对话框失败：{str(e)}")

    def check_and_fix_environment(self):
        """检查并修复训练环境"""
        try:
            # 检查 PyTorch 安装
            try:
                import torch
                print(f"PyTorch 版本: {torch.__version__}")

                # 检查 CUDA 可用性
                if torch.cuda.is_available():
                    cuda_version = torch.version.cuda
                    print(f"CUDA 可用: {cuda_version}")
                    print(f"GPU: {torch.cuda.get_device_name(0)}")


                else:
                    print("CUDA 不可用，将使用 CPU 进行训练")

                # 不管是否有GPU都继续，只要PyTorch能正常导入
                return True

            except ImportError as e:
                # PyTorch 导入失败
                reply = QMessageBox.question(
                    self,
                    "环境检查",
                    "未找到 PyTorch。\n是否要安装 PyTorch？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # 获取conda环境信息
                    conda_path = os.path.join(os.path.dirname(os.path.dirname(sys.executable)), "Scripts", "activate.bat")
                    conda_env = "autooffset"

                    # 检查是否有NVIDIA GPU
                    has_nvidia_gpu = False
                    try:
                        import subprocess
                        result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        has_nvidia_gpu = result.returncode == 0
                    except:
                        has_nvidia_gpu = False

                    # 根据是否有NVIDIA GPU选择安装命令
                    if has_nvidia_gpu:
                        install_cmd = 'conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia'
                    else:
                        install_cmd = 'conda install pytorch torchvision torchaudio cpuonly -c pytorch'

                    # 构建安装命令
                    cmd = (
                        f'cmd /c "'
                        f'"{conda_path}" {conda_env} && '
                        f'{install_cmd} && '
                        f'echo 安装完成，请按任意键继续... && '
                        f'pause"'
                    )

                    process = subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
                    process.wait()  # 等待安装完成

                    return True
                else:
                    return False

            return True

        except Exception as e:
            QMessageBox.critical(self, "错误", f"环境检查失败：{str(e)}")
            return False

    def show_generate_dataset_dialog(self):
        """显示生成数据集对话框"""
        if not hasattr(self, 'selected_area_points') or not self.selected_area_points:
            # 如果没有选择区域，先激活选择区域模式
            self.activate_select_zone()
            QMessageBox.information(self, "提示", "请先使用'选择图像区域'工具选择要处理的区域，然后再次点击'生成数据集'按钮。")
            return

        from GUI.GenerateDatasetDialog import GenerateDatasetDialog
        dialog = GenerateDatasetDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            settings = dialog.get_settings()
            self.start_generate_dataset(settings)

    def start_generate_dataset(self, settings):
        """开始生成数据集"""
        try:
            import sys
            import os
            from PIL import Image
            import numpy as np
            import requests
            from io import BytesIO
            import mercantile
            from PIL import Image
            from osgeo import gdal, osr

            # 获取保存设置
            save_dir = settings['save_dir']
            filename = settings['filename']
            image_path = settings['save_path']  # 完整的文件路径

            # 创建保存目录
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 获取选定区域的边界框
            lats = [point['lat'] for point in self.selected_area_points]
            lngs = [point['lng'] for point in self.selected_area_points]
            min_lat, max_lat = min(lats), max(lats)
            min_lng, max_lng = min(lngs), max(lngs)

            # 设置缩放级别和图像参数
            zoom_level = 18  # 高分辨率

            try:
                # 注意：mercantile.tile 函数参数顺序是 (lng, lat, zoom)
                min_tile = mercantile.tile(min_lng, min_lat, zoom_level)
                max_tile = mercantile.tile(max_lng, max_lat, zoom_level)

                # 确保正确的顺序
                x_start = min(min_tile.x, max_tile.x)
                x_end = max(min_tile.x, max_tile.x)
                y_start = min(min_tile.y, max_tile.y)
                y_end = max(min_tile.y, max_tile.y)

                # 计算总瓦片数
                tile_width = x_end - x_start + 1
                tile_height = y_end - y_start + 1

                # 创建大图
                total_width = tile_width * 256  # 每个瓦片是256x256像素
                total_height = tile_height * 256
                merged_image = Image.new('RGB', (total_width, total_height))

                # 设置请求头
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Referer': 'https://www.arcgis.com/'
                }

                # 创建进度对话框
                total_tiles = tile_width * tile_height
                progress = QProgressDialog("正在下载卫星图像...", "取消", 0, total_tiles, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.setMinimumDuration(0)  # 立即显示进度条

                print(f"📡 开始下载卫星图像瓦片...")
                print(f"📊 瓦片范围: {tile_width}×{tile_height} = {total_tiles}个瓦片")
                print(f"🗺️ 缩放级别: {zoom_level}")

                # 设置调试模式标志
                self._debug_tile_download = False  # 关闭详细瓦片下载信息

                # 下载和拼接瓦片
                session = requests.Session()
                processed_tiles = 0

                for y in range(y_start, y_end + 1):
                    for x in range(x_start, x_end + 1):
                        if progress.wasCanceled():
                            return

                        # 使用智能双源切换下载瓦片
                        tile_img = self.download_tile_with_fallback(x, y, zoom_level, session, "google")
                        if tile_img is None:
                            continue

                        try:
                            # 瓦片已经通过智能切换下载

                            # 计算瓦片在大图中的位置
                            paste_x = (x - x_start) * 256
                            paste_y = (y - y_start) * 256

                            # 将瓦片粘贴到大图上
                            merged_image.paste(tile_img, (paste_x, paste_y))

                            processed_tiles += 1
                            progress.setValue(processed_tiles)
                            progress.setLabelText(f"正在下载卫星图像... ({processed_tiles}/{total_tiles})")

                            # 每下载50个瓦片显示一次进度信息
                            if processed_tiles % 50 == 0 or processed_tiles == total_tiles:
                                percent = (processed_tiles / total_tiles) * 100
                                print(f"📊 下载进度: {processed_tiles}/{total_tiles} ({percent:.1f}%)")

                        except Exception as e:
                            continue

                # 将PIL图像转换为numpy数组，并进行数值稳定性处理
                print("开始训练图像数值稳定性处理...")
                image_array = np.array(merged_image, dtype=np.float32)

                # 数值稳定性处理
                print(f"原始图像范围: [{image_array.min():.1f}, {image_array.max():.1f}]")

                # 1. 检测和修复瓦片边界问题
                def smooth_tile_boundaries_training(img_array, tile_size=256):
                    """为训练图像平滑瓦片边界以减少数值不连续性"""
                    try:
                        from scipy import ndimage
                        h, w, c = img_array.shape
                        boundary_fixes = 0

                        print(f"检查图像瓦片边界: {w}x{h}, 瓦片大小: {tile_size}")

                        # 对每个通道分别处理
                        for channel in range(c):
                            channel_data = img_array[:, :, channel].copy()

                            # 检测垂直边界
                            for x in range(tile_size, w, tile_size):
                                if x < w - 1:
                                    # 计算边界两侧的平均值差异
                                    left_col = channel_data[:, x-1]
                                    right_col = channel_data[:, x]
                                    diff = np.abs(left_col - right_col).mean()

                                    if diff > 8:  # 如果差异较大
                                        # 应用边界平滑
                                        for y_idx in range(h):
                                            if x-1 >= 0 and x+1 < w:
                                                # 使用三点平均
                                                avg_val = (channel_data[y_idx, x-1] +
                                                          channel_data[y_idx, x] +
                                                          channel_data[y_idx, x+1]) / 3
                                                channel_data[y_idx, x] = avg_val
                                        boundary_fixes += 1

                            # 检测水平边界
                            for y in range(tile_size, h, tile_size):
                                if y < h - 1:
                                    # 计算边界两侧的平均值差异
                                    top_row = channel_data[y-1, :]
                                    bottom_row = channel_data[y, :]
                                    diff = np.abs(top_row - bottom_row).mean()

                                    if diff > 8:  # 如果差异较大
                                        # 应用边界平滑
                                        for x_idx in range(w):
                                            if y-1 >= 0 and y+1 < h:
                                                # 使用三点平均
                                                avg_val = (channel_data[y-1, x_idx] +
                                                          channel_data[y, x_idx] +
                                                          channel_data[y+1, x_idx]) / 3
                                                channel_data[y, x_idx] = avg_val
                                        boundary_fixes += 1

                            img_array[:, :, channel] = channel_data

                        print(f"修复了 {boundary_fixes} 个瓦片边界问题")
                        return img_array

                    except ImportError:
                        print("警告: scipy不可用，跳过高级边界平滑")
                        return img_array
                    except Exception as e:
                        print(f"边界平滑处理出错: {e}")
                        return img_array

                # 应用瓦片边界平滑
                image_array = smooth_tile_boundaries_training(image_array)

                # 2. 数值范围标准化和稳定性处理
                image_array = np.clip(image_array, 0, 255)

                # 3. 检查并处理异常值
                for i in range(image_array.shape[2]):
                    channel = image_array[:, :, i]
                    # 使用稳健统计量检测异常值
                    q1, q99 = np.percentile(channel, [1, 99])
                    if q99 > q1:
                        # 将极端值限制在合理范围内
                        channel = np.clip(channel, q1, q99)
                        image_array[:, :, i] = channel

                # 4. 转换回uint8，确保数值稳定性
                image_array = image_array.astype(np.uint8)

                print(f"处理后图像范围: [{image_array.min()}, {image_array.max()}]")
                print("训练图像数值稳定性处理完成")

                # 计算地理变换参数
                # 获取左上角瓦片的地理坐标
                ul_bounds = mercantile.bounds(x_start, y_start, zoom_level)
                # 获取右下角瓦片的地理坐标
                lr_bounds = mercantile.bounds(x_end, y_end, zoom_level)

                # 计算地理分辨率
                x_res = (lr_bounds.east - ul_bounds.west) / total_width
                y_res = (ul_bounds.north - lr_bounds.south) / total_height

                # 创建地理变换参数
                geotransform = (
                    ul_bounds.west,    # 左上角x坐标
                    x_res,             # 水平分辨率
                    0,                 # 旋转, 0表示图像"北方朝上"
                    ul_bounds.north,   # 左上角y坐标
                    0,                 # 旋转, 0表示图像"北方朝上"
                    -y_res            # 垂直分辨率（负值因为y轴向下）
                )

                # 保存为GeoTIFF（使用用户指定的文件名）
                # image_path 已经在前面设置为完整路径

                # 创建GDAL数据集
                driver = gdal.GetDriverByName('GTiff')
                dataset = driver.Create(
                    image_path,
                    total_width,
                    total_height,
                    3,  # 3个波段（RGB）
                    gdal.GDT_Byte,  # 数据类型
                    ['COMPRESS=LZW']  # 使用LZW压缩
                )

                # 设置地理变换参数
                dataset.SetGeoTransform(geotransform)

                # 设置投影（WGS84）
                srs = osr.SpatialReference()
                srs.ImportFromEPSG(4326)  # WGS84
                dataset.SetProjection(srs.ExportToWkt())

                # 写入图像数据
                for i in range(3):  # 对RGB三个波段
                    band = dataset.GetRasterBand(i + 1)
                    band.WriteArray(image_array[:, :, i])

                # 生成图像金字塔（.ovr文件）
                overview_list = [2, 4, 8, 16]  # 金字塔层级
                dataset.BuildOverviews("NEAREST", overview_list)

                # 创建XML元数据文件
                xml_path = os.path.join(save_dir, f"{filename}.xml")
                with open(xml_path, 'w', encoding='utf-8') as xml_file:
                    xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<metadata>
    <image>
        <name>{filename}.tif</name>
        <width>{total_width}</width>
        <height>{total_height}</height>
        <bands>3</bands>
        <pixel_type>Byte</pixel_type>
        <compression>LZW</compression>
    </image>
    <spatial_reference>
        <projection>WGS84</projection>
        <epsg_code>4326</epsg_code>
    </spatial_reference>
    <extent>
        <west>{ul_bounds.west}</west>
        <east>{lr_bounds.east}</east>
        <north>{ul_bounds.north}</north>
        <south>{lr_bounds.south}</south>
    </extent>
    <resolution>
        <x_res>{x_res}</x_res>
        <y_res>{y_res}</y_res>
    </resolution>
    <creation_time>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</creation_time>
</metadata>"""
                    xml_file.write(xml_content)

                # 关闭数据集
                dataset = None

                progress.close()
                QMessageBox.information(self, "完成",
                    f"卫星图像下载完成！\n"
                    f"图像大小: {total_width}x{total_height}\n"
                    f"已生成文件：\n"
                    f"- {image_path} (图像文件)\n"
                    f"- {image_path}.ovr (金字塔文件)\n"
                    f"- {xml_path} (元数据文件)")

            except Exception as e:
                progress.close()
                raise e

        except Exception as e:
            QMessageBox.critical(self, "错误", f"下载卫星图像时出错：{str(e)}")

        # 数据集生成完成后
        QMessageBox.information(self, "完成", "数据集生成完成！")

        # 清除选择的区域
        self.selected_area_points = []

        # 清除地图上的选择区域 - 使用正确的变量名
        self.clear_selection_area()

    def show_project_config_dialog(self):
        """显示项目配置对话框"""
        try:
            from GUI.ProjectConfigDialog import ProjectConfigDialog
            dialog = ProjectConfigDialog(self)

            # 如果已有配置，加载现有设置
            if hasattr(self, 'project_config'):
                dialog.load_settings(self.project_config)

            if dialog.exec_() == QDialog.Accepted:
                settings = dialog.get_settings()
                # 确保设置包含所有必要的路径
                if 'image_path' not in settings:
                    settings['image_path'] = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\image"
                if 'shape_path' not in settings:
                    settings['shape_path'] = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\shape"
                if 'model_path' not in settings:
                    settings['model_path'] = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\trained_models"

                self.save_project_config(settings)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示项目设置对话框时出错：{str(e)}")

    def save_project_config(self, settings):
        """保存项目配置"""
        try:
            # 确保包含 dataset_path
            if 'shape_path' in settings and 'dataset_path' not in settings:
                settings['dataset_path'] = settings['shape_path']  # 使用 shape_path 作为 dataset_path

            # 更新配置
            self.project_config = settings

            # 创建配置文件目录
            config_dir = os.path.join(os.path.dirname(__file__), 'config')
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 保存配置到文件
            config_file = os.path.join(config_dir, 'project_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)

            QMessageBox.information(self, "成功", "项目设置已保存")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存项目设置时出错：{str(e)}")

    def load_project_config(self):
        """加载项目配置"""
        try:
            import json
            config_file = os.path.join(os.path.dirname(__file__), 'config', 'project_config.json')
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.project_config = json.load(f)
            else:
                self.project_config = None
        except Exception:
            self.project_config = None

    def check_project_config(self):
        """检查项目配置是否已设置"""
        if not hasattr(self, 'project_config') or self.project_config is None:
            QMessageBox.warning(self, "警告", "请先在'模型训练->项目配置'中设置项目路径！")
            return False
        return True

    def create_new_shape_file(self):
        """创建新的Polygon Shape文件"""
        if not self.check_project_config():
            return

        # 确保有 dataset_path
        if 'dataset_path' not in self.project_config:
            if 'shape_path' in self.project_config:
                self.project_config['dataset_path'] = self.project_config['shape_path']
            else:
                QMessageBox.warning(self, "警告", "请先在'模型训练->项目设置'中设置项目路径！")
                return

        from GUI.CreateShapeFileDialog import CreateShapeFileDialog
        dialog = CreateShapeFileDialog(self, self.project_config['dataset_path'])
        if dialog.exec_() == QDialog.Accepted:
            file_name = dialog.get_file_name()
            file_path = os.path.join(self.project_config['dataset_path'], file_name)
            if not file_path.endswith('.shp'):
                file_path += '.shp'

            if self.create_shape_file(file_name):
                # 创建ShapeDrawer实例
                self.shape_drawer = ShapeDrawer(file_path, self.coordinate_systems[self.crs_combo.currentText()])
                # 开始绘制
                self.create_shape_polygon()

    def create_shape_file(self, file_name):
        """创建新的Shape文件"""
        try:
            import geopandas as gpd
            from shapely.geometry import Polygon

            # 构建完整的文件路径
            file_path = os.path.join(self.project_config['dataset_path'], file_name)
            if not file_path.endswith('.shp'):
                file_path += '.shp'

            # 创建空的GeoDataFrame
            gdf = gpd.GeoDataFrame(columns=['geometry'], geometry='geometry')

            # 获取当前选择的坐标系统
            current_crs = self.coordinate_systems[self.crs_combo.currentText()]

            # 设置坐标系统
            if current_crs.startswith("+proj="):
                # 对于 International 1924，使用 EPSG:4230
                if self.crs_combo.currentText() == "International 1924":
                    gdf.set_crs(epsg=4230, inplace=True, allow_override=True)
                else:
                    gdf.set_crs(current_crs, inplace=True, allow_override=True)
            else:
                # 使用EPSG代码设置CRS
                epsg = int(current_crs.split(':')[1])
                gdf.set_crs(epsg=epsg, inplace=True, allow_override=True)

            # 保存为shape文件
            gdf.to_file(file_path)

            # 添加到图层列表和地图
            self.add_layer_to_list(file_name, file_path)

            # 保存当前文件路径到ShapeDrawer
            if self.shape_drawer:
                self.shape_drawer.file_path = file_path
                self.shape_drawer.layer_name = os.path.basename(file_path)

            return True

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建Shape文件时出错：{str(e)}")
            return False

    def add_layer_to_list(self, layer_name, file_path):
        """添加图层到图层列表"""
        # 创建图层项
        layer_item = QTreeWidgetItem(self.layers_tree)
        layer_item.setText(0, layer_name)
        layer_item.setData(0, Qt.UserRole, file_path)  # 保存文件路径

        # 设置统一的字体样式
        from PyQt5.QtGui import QFont
        item_font = QFont("Microsoft YaHei UI", 9)
        item_font.setBold(False)
        layer_item.setFont(0, item_font)

        # 设置图标
        icon = QIcon("icons/polygon.png")  # 确保这个图标文件存在
        layer_item.setIcon(0, icon)

        # 展开图层树
        self.layers_tree.expandAll()

        # 更新图层显示
        self.update_layer_preview()

    def create_default_icons(self):
        """创建默认图标"""
        icons_dir = os.path.join(os.path.dirname(__file__), 'icons')
        polygon_icon_path = os.path.join(icons_dir, 'polygon.png')

        if not os.path.exists(polygon_icon_path):
            # 创建一个简单的多边形图标
            from PIL import Image, ImageDraw

            # 创建一个16x16的透明图像
            img = Image.new('RGBA', (16, 16), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # 绘制一个简单的多边形
            points = [(3, 13), (7, 3), (13, 7), (11, 13), (3, 13)]
            draw.polygon(points, outline=(0, 0, 0), fill=(200, 200, 200, 180))

            # 保存图标
            img.save(polygon_icon_path, 'PNG')

    def create_map_view(self):
        """创建地图视图"""
        self.web_view = QWebEngineView()
        self.map_view = self.web_view  # 保存为map_view属性

        # 创建WebChannel和Bridge
        self.channel = QWebChannel()
        self.bridge = WebBridge()
        self.channel.registerObject("bridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)

        # 连接信号
        self.bridge.locationChanged.connect(self.locationUpdated)
        self.bridge.pointCreated.connect(self.point_added)
        self.bridge.polygonFinished.connect(self.polygon_completed)
        self.bridge.drawingCanceled.connect(self.drawingCanceled)
        self.bridge.coordinatesChanged.connect(self.updateCoordinates)
        self.bridge.areaSelected.connect(self.saveSelectedArea)

        self.initMap()

    def initMap(self):
        """初始化地图"""
        # 创建HTML内容
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>OpenStreetMap</title>
            <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
            <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
            <style>
                #map { height: 100%; width: 100%; }
                html, body { height: 100%; margin: 0; padding: 0; }
            </style>
            <script>
                var map;
                var satellite;

                function initMap() {
                    // 初始化地图，设置Rafha的坐标
                    map = L.map('map').setView([29.6174, 43.5068], 11);

                    // 添加Google卫星图层
                    satellite = L.tileLayer('https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
                        maxZoom: 21,  // 增加最大缩放级别
                        attribution: 'Imagery &#169; Google'
                    }).addTo(map);

                    // 添加备用图层（当Google图层加载失败时使用）
                    var backup = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        maxZoom: 19,
                        attribution: 'Tiles &#169; Esri'
                    });

                    // 监听图层加载错误
                    satellite.on('tileerror', function(e) {
                        if (!map.hasLayer(backup)) {
                            satellite.remove();
                            backup.addTo(map);
                        }
                    });

                    // 等待 QWebChannel 建立
                    new QWebChannel(qt.webChannelTransport, function(channel) {
                        window.bridge = channel.objects.bridge;

                        // 添加鼠标移动事件监听器
                        map.on('mousemove', function(e) {
                            if (window.bridge) {
                                window.bridge.handleCoordinatesUpdated(
                                    e.latlng.lat,
                                    e.latlng.lng,
                                    map.getZoom()
                                );
                            }
                        });
                    });

                    // 尝试获取用户位置
                    if ("geolocation" in navigator) {
                        navigator.geolocation.getCurrentPosition(function(position) {
                            var lat = position.coords.latitude;
                            var lng = position.coords.longitude;
                            map.setView([lat, lng], 11);
                            if (window.bridge) {
                                window.bridge.handleLocationUpdated(lat, lng);
                            }
                        });
                    }
                }
            </script>
            <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
        </head>
        <body onload="initMap()">
            <div id="map"></div>
        </body>
        </html>
        """

        # 直接设置HTML内容
        self.web_view.setHtml(html_content, QUrl("file://"))

    def create_shape_polygon(self):
        """创建新的Shape文件多边形"""
        if not self.shape_drawer:
            return

        self.is_drawing = True

        # 添加JavaScript代码处理多边形绘制
        js_code = """
            // 移除现有的事件监听器
            if (window.drawingClickHandler) {
                map.off('click', window.drawingClickHandler);
            }
            if (window.drawingDblClickHandler) {
                map.off('dblclick', window.drawingDblClickHandler);
            }

            // 清除现有的绘制图层
            if (window.drawingLayer) {
                map.removeLayer(window.drawingLayer);
            }

            // 创建新的绘制图层
            window.drawingLayer = L.layerGroup().addTo(map);
            window.points = [];

            // 添加点击事件处理器
            window.drawingClickHandler = function(e) {
                var point = [e.latlng.lat, e.latlng.lng];
                window.points.push(point);

                // 添加标记
                L.circleMarker(e.latlng, {
                    radius: 5,
                    color: 'red',
                    fillColor: '#f03',
                    fillOpacity: 0.5
                }).addTo(window.drawingLayer);

                // 如果有多个点，绘制线段
                if (window.points.length > 1) {
                    var lastPoint = window.points[window.points.length - 2];
                    L.polyline([lastPoint, point], {
                        color: 'red'
                    }).addTo(window.drawingLayer);
                }

                // 通知Python添加点
                if (window.bridge) {
                    window.bridge.handlePointAdded(point[0], point[1]);
                }
            };

            // 添加双击事件处理器完成绘制
            window.drawingDblClickHandler = function(e) {
                if (window.points.length >= 3) {
                    // 闭合多边形
                    var firstPoint = window.points[0];
                    var lastPoint = window.points[window.points.length - 1];
                    L.polyline([lastPoint, firstPoint], {
                        color: 'red'
                    }).addTo(window.drawingLayer);

                    // 填充多边形
                    L.polygon(window.points, {
                        color: 'red',
                        fillColor: '#f03',
                        fillOpacity: 0.2
                    }).addTo(window.drawingLayer);

                    // 通知Python完成绘制
                    if (window.bridge) {
                        window.bridge.handlePolygonCompleted();
                    }

                    // 清除临时变量
                    window.points = [];
                }
            };

            // 添加ESC键事件处理器
            window.escHandler = function(e) {
                if (e.key === 'Escape') {
                    // 清除绘制层
                    if (window.drawingLayer) {
                        window.drawingLayer.clearLayers();
                    }
                    window.points = [];

                    // 通知Python取消绘制
                    if (window.bridge) {
                        window.bridge.handleDrawingCanceled();
                    }
                }
            };

            // 添加事件监听器
            map.on('click', window.drawingClickHandler);
            map.on('dblclick', window.drawingDblClickHandler);
            document.addEventListener('keydown', window.escHandler);

            // 更改鼠标样式
            map.getContainer().style.cursor = 'crosshair';
        """

        self.web_view.page().runJavaScript(js_code)
        QMessageBox.information(self, "提示", "请点击地图添加多边形顶点，双击完成绘制，按ESC取消。")

    def point_added(self, lat, lng):
        """
        当在JavaScript中添加点时调用
        """
        if self.shape_drawer and self.is_drawing:
            self.shape_drawer.add_point(lat, lng)

    def polygon_completed(self):
        """当多边形绘制完成时调用"""
        if self.shape_drawer and self.is_drawing:
            if not self.shape_drawer.complete_polygon():
                QMessageBox.warning(self, "错误", "创建多边形失败，请重试。")
                return

            # 更新地图显示
            if HAS_WEBENGINE:
                # 清除绘制层
                js_code = """
                    if (window.drawingLayer) {
                        window.drawingLayer.clearLayers();
                    }
                    // 重置绘制状态
                    window.points = [];
                """
                self.web_view.page().runJavaScript(js_code)

                # 重新加载图层
                file_path = self.shape_drawer.file_path
                layer_name = os.path.basename(file_path)

                # 先从地图上移除旧图层
                js_code = f"""
                    if (window.layerGroups && window.layerGroups['{layer_name}']) {{
                        map.removeLayer(window.layerGroups['{layer_name}']);
                        delete window.layerGroups['{layer_name}'];
                    }}
                """
                self.web_view.page().runJavaScript(js_code)

                # 重新加载并显示图层
                self.refresh_layer_display(file_path, layer_name)

                # 提示用户可以继续添加新要素，并重新开始绘制模式
                reply = QMessageBox.question(self, "提示",
                    "要素已保存。是否继续添加新的要素？",
                    QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    # 重新启动绘制模式
                    self.create_shape_polygon()
                else:
                    # 如果用户选择不继续，才清理绘制状态
                    self.is_drawing = False
                    js_code = """
                        // 恢复默认鼠标样式
                        map.getContainer().style.cursor = 'default';
                        // 移除事件监听器
                        if (window.drawingClickHandler) {
                            map.off('click', window.drawingClickHandler);
                        }
                        if (window.drawingDblClickHandler) {
                            map.off('dblclick', window.drawingDblClickHandler);
                        }
                        document.removeEventListener('keydown', window.escHandler);
                    """
                    self.web_view.page().runJavaScript(js_code)

    def update_file_system_tree(self):
        """更新文件系统树"""
        self.browser_tree.clear()

        # 显示所有驱动器
        import string
        from PyQt5.QtGui import QFont

        # 创建统一的字体
        item_font = QFont("Microsoft YaHei UI", 9)
        item_font.setBold(False)

        for letter in string.ascii_uppercase:
            drive = f"{letter}:\\"
            if os.path.exists(drive):
                drive_item = QTreeWidgetItem(self.browser_tree)
                drive_item.setText(0, drive)
                drive_item.setData(0, Qt.UserRole, drive)
                drive_item.setFont(0, item_font)  # 设置统一字体
                icon = self.style().standardIcon(QStyle.SP_DirIcon)
                drive_item.setIcon(0, icon)
                QTreeWidgetItem(drive_item)

        # 连接展开事件
        self.browser_tree.itemExpanded.connect(self.on_item_expanded)

        # 添加右键菜单
        self.browser_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.browser_tree.customContextMenuRequested.connect(self.show_browser_context_menu)

        # 延迟自动定位到当前项目文件夹（等待UI完全加载）
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(1000, self.auto_navigate_to_project_folder)

        # 如果有项目配置，也展开到默认路径的父目录
        if hasattr(self, 'project_config') and self.project_config:
            default_paths = []
            for key in ['image_path', 'shape_path', 'model_path']:
                path = self.project_config.get(key)
                if path and os.path.exists(path):
                    # 获取父目录路径
                    parent_path = os.path.dirname(path)
                    if parent_path not in default_paths and os.path.exists(parent_path):
                        default_paths.append(parent_path)

            # 展开所有默认路径
            for path in default_paths:
                self.expand_to_path(path)

    def auto_navigate_to_project_folder(self):
        """自动导航到项目文件夹"""
        try:
            project_root = os.path.dirname(os.path.abspath(__file__))

            # 确保路径存在
            if not os.path.exists(project_root):
                return

            # 展开到项目根目录
            self.expand_to_path(project_root)

            # 稍等一下再选中
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(200, lambda: self.select_project_folder(project_root))

        except Exception as e:
            pass  # 静默处理错误

    def select_project_folder(self, project_root):
        """选中项目文件夹"""
        try:
            # 查找并选中项目根目录
            project_item = self.find_item_by_path(project_root)
            if project_item:
                self.browser_tree.setCurrentItem(project_item)
                self.browser_tree.scrollToItem(project_item)
        except Exception as e:
            pass  # 静默处理错误

    def find_item_by_path(self, path):
        """根据路径查找树项"""
        if not path:
            return None

        parts = os.path.normpath(path).split(os.sep)
        current_item = None

        # 找到对应的驱动器项
        drive = parts[0] + os.sep
        for i in range(self.browser_tree.topLevelItemCount()):
            item = self.browser_tree.topLevelItem(i)
            if item.text(0).startswith(drive):
                current_item = item
                break

        if current_item:
            # 遍历路径的每一部分
            for part in parts[1:]:
                found = False
                for i in range(current_item.childCount()):
                    child = current_item.child(i)
                    if child.text(0) == part:
                        current_item = child
                        found = True
                        break
                if not found:
                    break

        return current_item

    def show_browser_context_menu(self, position):
        """显示浏览器右键菜单"""
        menu = QMenu()

        # 设置右键菜单统一字体样式
        self.set_context_menu_font(menu)

        # 添加刷新选项
        refresh_action = QAction("刷新", self)
        refresh_action.triggered.connect(self.refresh_browser_tree)
        menu.addAction(refresh_action)

        # 显示菜单
        menu.exec_(self.browser_tree.viewport().mapToGlobal(position))

    def show_layer_tooltip(self, item, column):
        """显示图层工具提示"""
        try:
            file_path = item.data(0, Qt.UserRole)
            if file_path and os.path.exists(file_path):
                import geopandas as gpd
                gdf = gpd.read_file(file_path)

                # 获取坐标系统信息
                crs_name = "未知"
                if gdf.crs:
                    if isinstance(gdf.crs, str) and gdf.crs.startswith("+proj="):
                        # 处理proj4字符串
                        for name, code in self.coordinate_systems.items():
                            if code == gdf.crs:
                                crs_name = name
                                break
                    else:
                        # 处理EPSG代码
                        try:
                            epsg_code = gdf.crs.to_epsg()
                            if epsg_code == 4230:  # 特别处理 International 1924
                                crs_name = "International 1924"
                            elif epsg_code:
                                for name, code in self.coordinate_systems.items():
                                    if code == f"EPSG:{epsg_code}":
                                        crs_name = name
                                        break
                            if crs_name == "未知":
                                crs_name = str(gdf.crs)
                        except Exception:
                            crs_name = str(gdf.crs)

                # 构建工具提示文本
                tooltip = f"图层名称: {item.text(0)}\n"
                tooltip += f"要素数量: {len(gdf)}\n"
                tooltip += f"几何类型: {gdf.geom_type.iloc[0] if len(gdf) > 0 else 'Polygon'}\n"
                tooltip += f"坐标系统: {crs_name}"

                item.setToolTip(0, tooltip)
        except Exception as e:
            item.setToolTip(0, f"无法加载图层信息：{str(e)}")

    def on_browser_item_double_clicked(self, item, column):
        """处理浏览器树的双击事件"""
        file_path = item.data(0, Qt.UserRole)
        if os.path.isfile(file_path) and file_path.lower().endswith(('.shp', '.geojson', '.kml', '.gpkg')):
            self.add_layer_to_map(file_path)

    def add_layer_to_map(self, file_path):
        """添加图层到地图"""
        try:
            # 读取文件
            gdf = gpd.read_file(file_path)
            print(f"开始加载图层: {file_path}")
            print(f"原始坐标系统: {gdf.crs}")
            print(f"几何类型: {gdf.geometry.iloc[0].geom_type}")

            # 转换坐标系统到WGS84
            if gdf.crs != "EPSG:4326":
                print(f"转换坐标系统从 {gdf.crs} 到 WGS84")
                gdf = gdf.to_crs("EPSG:4326")
                print("坐标转换完成")

            # 检查几何类型，如果是点类型，使用特殊的显示方式
            geom_type = gdf.geometry.iloc[0].geom_type
            if geom_type == 'Point':
                print("检测到点类型图层，使用圆形显示方式")
                self._add_point_layer_to_map(gdf, file_path)
                return True

            # 如果没有图层组，创建一个新的
            js_init_layer_group = """
                if (typeof layerGroups === 'undefined') {
                    window.layerGroups = {};
                }
            """
            self.web_view.page().runJavaScript(js_init_layer_group)

            # 处理时间戳字段，转换为GeoJSON
            # 复制GeoDataFrame以避免修改原始数据
            gdf_copy = gdf.copy()

            # 将所有不兼容JSON的字段转换为字符串
            for col in gdf_copy.columns:
                if col != 'geometry':  # 跳过几何字段
                    try:
                        # 检查是否包含时间戳或日期类型
                        if (gdf_copy[col].dtype == 'datetime64[ns]' or
                            str(gdf_copy[col].dtype).startswith('datetime') or
                            'timestamp' in str(gdf_copy[col].dtype).lower()):
                            gdf_copy[col] = gdf_copy[col].astype(str)
                        # 检查是否有Timestamp对象
                        elif len(gdf_copy) > 0:
                            sample_value = gdf_copy[col].iloc[0]
                            if hasattr(sample_value, 'timestamp') or 'Timestamp' in str(type(sample_value)):
                                gdf_copy[col] = gdf_copy[col].astype(str)
                    except Exception as e:
                        print(f"处理字段 {col} 时出错: {e}")
                        # 如果出错，尝试转换为字符串
                        try:
                            gdf_copy[col] = gdf_copy[col].astype(str)
                        except:
                            # 如果还是出错，删除这个字段
                            gdf_copy = gdf_copy.drop(columns=[col])
                            print(f"删除有问题的字段: {col}")

            # 转换为GeoJSON
            geojson_data = gdf_copy.to_json()

            # 根据文件类型确定图层样式和ID
            if 'grid' in file_path.lower():
                print("正在处理网格文件")
                layer_id = f"grid_{os.path.basename(file_path)}"
                layer_style = {
                    'color': '#808080',
                    'weight': 1,
                    'fillOpacity': 0.1,
                    'fillColor': '#808080'
                }
            else:
                # 默认作为障碍物处理（包括所有其他shapefile）
                print("正在处理障碍物文件")
                layer_id = f"obstacle_{os.path.basename(file_path)}"
                layer_style = {
                    'color': '#FF0000',
                    'weight': 2,
                    'fillOpacity': 0.2,
                    'fillColor': '#FF0000'
                }

            # 创建图层的JavaScript代码
            js_code = f"""
                // 移除旧的图层（如果存在）
                if (window.layerGroups['{layer_id}']) {{
                    map.removeLayer(window.layerGroups['{layer_id}']);
                }}

                // 创建新的图层
                window.layerGroups['{layer_id}'] = L.geoJSON({geojson_data}, {{
                    style: {{
                        color: '{layer_style['color']}',
                        weight: {layer_style['weight']},
                        fillOpacity: {layer_style['fillOpacity']},
                        fillColor: '{layer_style['fillColor']}'
                    }}
                }}).addTo(map);

                // 调整地图视图以适应所有图层
                var allBounds = [];
                for (var key in window.layerGroups) {{
                    if (window.layerGroups[key]) {{
                        allBounds.push(window.layerGroups[key].getBounds());
                    }}
                }}
                if (allBounds.length > 0) {{
                    var bounds = L.latLngBounds(allBounds);
                    map.fitBounds(bounds);
                }}
            """

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)
            print("图层添加成功")

            # 更新图层列表
            self.add_layer_to_list(os.path.basename(file_path), file_path)

            return True

        except Exception as e:
            print(f"添加图层时出错: {str(e)}")
            traceback.print_exc()
            return False

    def _add_point_layer_to_map(self, gdf, file_path):
        """专门处理点类型图层的添加"""
        try:
            layer_name = os.path.basename(file_path)
            layer_id = f"point_layer_{layer_name.replace('.', '_')}"

            # 计算圆形半径（米），使用与点位偏移相同的方法
            radius = self.point_offset_manager.offset_settings['surface_bin_size'] / 4

            # 提取点坐标
            point_coords = [[p.x, p.y] for p in gdf.geometry]  # [lng, lat]
            point_ids = gdf.index.tolist()

            # 使用JavaScript在地图上显示点，采用与点位偏移相同的样式
            js_code = f"""
                // 初始化图层组
                if (typeof window.layerGroups === 'undefined') {{
                    window.layerGroups = {{}};
                }}

                // 移除旧的图层（如果存在）
                if (window.layerGroups['{layer_id}']) {{
                    map.removeLayer(window.layerGroups['{layer_id}']);
                }}

                // 初始化circleElements数组（如果不存在）
                if (!window.circleElements) {{
                    window.circleElements = [];
                }}

                // 创建新的图层组
                var pointLayerGroup = L.layerGroup();
                window.layerGroups['{layer_id}'] = pointLayerGroup;

                var points = {point_coords};
                var pointIds = {point_ids};
                var radius = {radius};

                // 创建所有圆点并添加到地图
                for (var i = 0; i < points.length; i++) {{
                    var point = points[i];
                    var circle = L.circle([point[1], point[0]], {{
                        color: 'blue',
                        fillColor: '#3388ff',
                        fillOpacity: 0.5,
                        weight: 1,
                        radius: radius
                    }});

                    // 存储索引和原始颜色
                    circle.pointIndex = pointIds[i];
                    circle.originalColor = '#3388ff';

                    // 添加到图层
                    pointLayerGroup.addLayer(circle);

                    // 存储引用到全局数组（用于拖动功能）
                    window.circleElements.push(circle);
                }}

                // 添加图层到地图
                pointLayerGroup.addTo(map);

                // 缩放到图层范围
                if (points.length > 0) {{
                    var bounds = pointLayerGroup.getBounds();
                    map.fitBounds(bounds);
                }}

                console.log('添加了 ' + points.length + ' 个点到地图（圆形显示）');
            """

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)
            print(f"点图层添加成功: {layer_name}")

            # 更新图层列表
            self.add_layer_to_list(layer_name, file_path)

            return True

        except Exception as e:
            print(f"添加点图层时出错: {str(e)}")
            traceback.print_exc()
            return False

    def show_layer_context_menu(self, position):
        """显示图层右键菜单"""
        item = self.layers_tree.itemAt(position)
        if item:
            menu = QMenu()

            # 设置右键菜单统一字体样式
            self.set_context_menu_font(menu)

            # 添加查看属性表选项
            attribute_table_action = QAction("查看属性表", self)
            menu.addAction(attribute_table_action)
            menu.addSeparator()

            # 创建编辑子菜单
            edit_menu = menu.addMenu("编辑")
            add_feature_action = QAction("添加要素", self)
            delete_feature_action = QAction("删除要素", self)
            edit_menu.addAction(add_feature_action)
            edit_menu.addAction(delete_feature_action)

            menu.addSeparator()
            info_action = QAction("图层信息", self)
            menu.addAction(info_action)
            menu.addSeparator()
            delete_action = QAction("删除图层", self)
            menu.addAction(delete_action)

            # 显示菜单并获取选择的动作
            action = menu.exec_(self.layers_tree.viewport().mapToGlobal(position))

            if action == delete_action:
                self.delete_layer(item)
            elif action == info_action:
                self.show_layer_info(item)
            elif action == attribute_table_action:
                self.show_attribute_table(item)
            elif action == add_feature_action:
                self.start_add_feature(item)
            elif action == delete_feature_action:
                self.start_delete_feature(item)
    def delete_layer(self, item):
        """删除图层"""
        try:
            # 获取要删除的图层名称和文件路径
            layer_name = item.text(0)
            file_path = item.data(0, Qt.UserRole)

            # 确认删除操作
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除图层 '{layer_name}' 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 从地图上移除特定图层
            if HAS_WEBENGINE:
                # 生成可能的点图层ID
                point_layer_id = f"point_layer_{layer_name.replace('.', '_')}"

                js_code = f"""
                    console.log('Attempting to remove layer:', '{layer_name}');
                    console.log('Available layers:', window.layerGroups ? Object.keys(window.layerGroups) : 'No layerGroups');

                    // 尝试删除标准图层
                    if (window.layerGroups && window.layerGroups['{layer_name}']) {{
                        map.removeLayer(window.layerGroups['{layer_name}']);
                        delete window.layerGroups['{layer_name}'];
                        console.log('Successfully removed layer:', '{layer_name}');
                    }}

                    // 尝试删除点图层（如果是点类型）
                    if (window.layerGroups && window.layerGroups['{point_layer_id}']) {{
                        // 清理该图层中的圆点引用
                        if (window.layerGroups['{point_layer_id}'].circles) {{
                            for (var i = 0; i < window.layerGroups['{point_layer_id}'].circles.length; i++) {{
                                var circle = window.layerGroups['{point_layer_id}'].circles[i];
                                var idx = window.circleElements ? window.circleElements.indexOf(circle) : -1;
                                if (idx > -1) {{
                                    window.circleElements.splice(idx, 1);
                                }}
                            }}
                        }}
                        map.removeLayer(window.layerGroups['{point_layer_id}']);
                        delete window.layerGroups['{point_layer_id}'];
                        console.log('Successfully removed point layer:', '{point_layer_id}');
                    }}

                    // 如果上述都没找到，尝试查找并删除可能的变体名称
                    if (!window.layerGroups || (!window.layerGroups['{layer_name}'] && !window.layerGroups['{point_layer_id}'])) {{
                        console.log('Layer not found, searching for similar names...');
                        var layerKeys = window.layerGroups ? Object.keys(window.layerGroups) : [];
                        for (var key of layerKeys) {{
                            if (key.includes('{layer_name}') || '{layer_name}'.includes(key)) {{
                                // 如果是点图层，清理圆点引用
                                if (window.layerGroups[key].circles) {{
                                    for (var i = 0; i < window.layerGroups[key].circles.length; i++) {{
                                        var circle = window.layerGroups[key].circles[i];
                                        var idx = window.circleElements ? window.circleElements.indexOf(circle) : -1;
                                        if (idx > -1) {{
                                            window.circleElements.splice(idx, 1);
                                        }}
                                    }}
                                }}
                                map.removeLayer(window.layerGroups[key]);
                                delete window.layerGroups[key];
                                console.log('Removed similar layer:', key);
                                break;
                            }}
                        }}
                    }}

                    console.log('Remaining layers after deletion:', window.layerGroups ? Object.keys(window.layerGroups) : 'No layerGroups');
                """
                self.web_view.page().runJavaScript(js_code)

            # 从图层树中移除
            index = self.layers_tree.indexOfTopLevelItem(item)
            if index >= 0:
                self.layers_tree.takeTopLevelItem(index)
                QMessageBox.information(self, "成功", f"图层 '{layer_name}' 已删除")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除图层失败: {str(e)}")

    def show_layer_info(self, item):
        """显示图层详细信息"""
        if not HAS_GEOPANDAS:
            QMessageBox.warning(self, "警告", "未安装 geopandas，无法读取图层信息！")
            return

        try:
            file_path = item.data(0, Qt.UserRole)
            if file_path and os.path.exists(file_path):
                import geopandas as gpd
                gdf = gpd.read_file(file_path)

                # 获取坐标系统信息
                crs_name = "未知"
                if gdf.crs:
                    if isinstance(gdf.crs, str) and gdf.crs.startswith("+proj="):
                        # 处理proj4字符串
                        for name, code in self.coordinate_systems.items():
                            if code == gdf.crs:
                                crs_name = name
                                break
                    else:
                        # 处理EPSG代码
                        try:
                            epsg_code = gdf.crs.to_epsg()
                            if epsg_code == 4230:  # 特别处理 International 1924
                                crs_name = "International 1924"
                            elif epsg_code:
                                for name, code in self.coordinate_systems.items():
                                    if code == f"EPSG:{epsg_code}":
                                        crs_name = name
                                        break
                            if crs_name == "未知":
                                crs_name = str(gdf.crs)
                        except Exception:
                            crs_name = str(gdf.crs)

                # 构建信息文本
                info = f"图层名称: {item.text(0)}\n"
                info += f"存储路径: {file_path}\n"
                info += f"要素数量: {len(gdf)}\n"
                info += f"几何类型: {gdf.geom_type.iloc[0] if len(gdf) > 0 else 'Polygon'}\n"
                info += f"坐标系统: {crs_name}"

                QMessageBox.information(self, "图层信息", info)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法获取图层信息: {str(e)}")

    @pyqtSlot()
    def drawingCanceled(self):
        """当绘制被取消时调用"""
        if self.shape_drawer:
            self.shape_drawer.cancel_drawing()
            self.is_drawing = False

            # 清除绘制状态和图层
            js_code = """
                // 清除绘制层
                if (window.drawingLayer) {
                    window.drawingLayer.clearLayers();
                }
                // 重置绘制状态
                window.points = [];
                // 恢复默认鼠标样式
                map.getContainer().style.cursor = 'default';
                // 移除事件监听器
                if (window.drawingClickHandler) {
                    map.off('click', window.drawingClickHandler);
                }
                if (window.drawingDblClickHandler) {
                    map.off('dblclick', window.drawingDblClickHandler);
                }
                document.removeEventListener('keydown', window.escHandler);
            """
            self.web_view.page().runJavaScript(js_code)

    def show_attribute_table(self, item):
        """显示属性表"""
        try:
            file_path = item.data(0, Qt.UserRole)
            if not file_path or not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", "图层文件不存在！")
                return

            # 检查是否安装了geopandas
            if not HAS_GEOPANDAS:
                QMessageBox.warning(self, "警告", "未安装 geopandas，无法显示属性表！")
                return

            # 导入属性表对话框
            try:
                from GUI.AttributeTableDialog import AttributeTableDialog
            except ImportError as e:
                QMessageBox.critical(self, "错误", f"无法导入属性表对话框:\n{str(e)}")
                return

            # 创建并显示属性表对话框
            dialog = AttributeTableDialog(file_path, self)

            # 连接信号（如果需要与地图交互）
            dialog.feature_selected.connect(self.on_feature_selected_from_table)
            dialog.feature_highlighted.connect(self.on_feature_highlighted_from_table)

            # 显示对话框
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示属性表时出错:\n{str(e)}")
            print(f"属性表错误详情: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_feature_selected_from_table(self, feature_index):
        """处理从属性表选择要素的信号"""
        try:
            print(f"从属性表选择要素: {feature_index}")
            # 这里可以添加在地图上高亮显示选中要素的代码
            # 例如：缩放到要素位置、改变要素样式等
        except Exception as e:
            print(f"处理要素选择时出错: {str(e)}")

    def on_feature_highlighted_from_table(self, feature_index):
        """处理从属性表高亮要素的信号"""
        try:
            print(f"从属性表高亮要素: {feature_index}")
            # 这里可以添加在地图上临时高亮显示要素的代码
        except Exception as e:
            print(f"处理要素高亮时出错: {str(e)}")

    def start_add_feature(self, item):
        """开始添加要素到现有图层"""
        try:
            file_path = item.data(0, Qt.UserRole)
            if file_path and os.path.exists(file_path):
                # 创建ShapeDrawer实例
                self.shape_drawer = ShapeDrawer(file_path, self.coordinate_systems[self.crs_combo.currentText()])
                # 开始绘制
                self.create_shape_polygon()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法开始编辑图层: {str(e)}")

    def refresh_browser_tree(self):
        """刷新文件系统树"""
        try:
            # 保存当前选中的项目路径
            current_item = self.browser_tree.currentItem()
            current_path = current_item.data(0, Qt.UserRole) if current_item else None

            # 清空并重新加载文件系统树
            self.browser_tree.clear()
            self.update_file_system_tree()

            # 如果有之前选中的路径，重新展开到该路径
            if current_path:
                self.expand_to_path(current_path)

            # 如果有项目配置的路径，确保它们的父目录是展开的
            if hasattr(self, 'project_config') and self.project_config:
                for key in ['image_path', 'shape_path', 'model_path']:
                    path = self.project_config.get(key)
                    if path and os.path.exists(path):
                        parent_path = os.path.dirname(path)
                        if os.path.exists(parent_path):
                            self.expand_to_path(parent_path)

        except Exception as e:
            print(f"刷新文件系统树时出错: {str(e)}")

    def refresh_layer_display(self, file_path, layer_name):
        """刷新指定图层的显示"""
        if not HAS_GEOPANDAS:
            QMessageBox.warning(self, "警告", "未安装 geopandas，无法显示 Shape 文件！")
            return

        try:
            if HAS_WEBENGINE:
                import geopandas as gpd
                gdf = gpd.read_file(file_path)

                # 如果需要，转换坐标系统到WGS84
                if gdf.crs and gdf.crs != "EPSG:4326":
                    gdf = gdf.to_crs("EPSG:4326")

                # 转换为GeoJSON格式
                geojson_data = gdf.to_json()

                # 使用JavaScript在地图上显示图层
                js_code = f"""
                    if (typeof window.layerGroups === 'undefined') {{
                        window.layerGroups = {{}};
                    }}

                    // 移除已存在的同名图层
                    if (window.layerGroups['{layer_name}']) {{
                        map.removeLayer(window.layerGroups['{layer_name}']);
                    }}

                    // 创建新的图层组
                    var layerGroup = L.layerGroup();
                    window.layerGroups['{layer_name}'] = layerGroup;

                    var geoJsonLayer = L.geoJSON({geojson_data}, {{
                        style: function(feature) {{
                            return {{
                                color: 'red',
                                weight: 2,
                                fillOpacity: 0.2
                            }};
                        }}
                    }}).addTo(layerGroup);

                    // 将图层组添加到地图
                    layerGroup.addTo(map);

                    // 缩放到图层范围
                    map.fitBounds(geoJsonLayer.getBounds());
                """
                self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"刷新图层显示时出错: {str(e)}")

    def start_delete_feature(self, item):
        """开始删除要素"""
        if not HAS_GEOPANDAS:
            QMessageBox.warning(self, "警告", "未安装 geopandas，无法编辑 Shape 文件！")
            return

        try:
            file_path = item.data(0, Qt.UserRole)
            if not file_path or not os.path.exists(file_path):
                return

            # 读取图层数据
            gdf = gpd.read_file(file_path)
            if len(gdf) == 0:
                QMessageBox.warning(self, "警告", "该图层没有要素可以删除！")
                return

            # 如果需要，将整个GeoDataFrame转换到WGS84
            if gdf.crs and str(gdf.crs) != "EPSG:4326":
                gdf = gdf.to_crs("EPSG:4326")

            # 首先清除任何现有的删除图层
            js_code = """
                if (window.deleteLayer) {
                    map.removeLayer(window.deleteLayer);
                }
                window.deleteLayer = L.featureGroup().addTo(map);
                window.featureIndices = [];  // 用于存储要素索引
            """
            self.web_view.page().runJavaScript(js_code)

            # 将所有要素转换为GeoJSON并一次性添加到地图
            features_js = []
            for idx, row in gdf.iterrows():
                geometry = row.geometry
                centroid = geometry.centroid

                feature_js = {
                    'type': 'Feature',
                    'geometry': geometry.__geo_interface__,
                    'properties': {'index': idx}
                }
                features_js.append(feature_js)

            # 创建GeoJSON集合
            geojson_collection = {
                'type': 'FeatureCollection',
                'features': features_js
            }

            # 添加要素到地图
            js_code = f"""
                var geojsonLayer = L.geoJSON({json.dumps(geojson_collection)}, {{
                    style: function(feature) {{
                        return {{
                            color: 'red',
                            weight: 2,
                            fillOpacity: 0.2
                        }};
                    }},
                    onEachFeature: function(feature, layer) {{
                        window.featureIndices.push(feature.properties.index);

                        // 添加标签
                        var centroid = layer.getBounds().getCenter();
                        L.marker([centroid.lat, centroid.lng], {{
                            icon: L.divIcon({{
                                html: '<div style="background-color: white; border: 1px solid black; padding: 2px;">' +
                                      (feature.properties.index + 1) + '</div>',
                                className: 'feature-index'
                            }})
                        }}).addTo(window.deleteLayer);
                    }}
                }}).addTo(window.deleteLayer);

                // 缩放到所有要素的范围
                map.fitBounds(window.deleteLayer.getBounds());
            """
            self.web_view.page().runJavaScript(js_code)

            # 显示删除对话框
            dialog = DeleteFeaturesDialog(gdf, self)
            if dialog.exec_() == QDialog.Accepted:
                indices = dialog.get_selected_indices()
                if indices:
                    # 读取原始数据（使用原始坐标系统）
                    original_gdf = gpd.read_file(file_path)
                    # 删除选中的要素
                    original_gdf = original_gdf.drop(indices)
                    # 保存更新后的文件
                    original_gdf.to_file(file_path)

                    # 刷新图层显示
                    self.refresh_layer_display(file_path, os.path.basename(file_path))

                    QMessageBox.information(self, "成功", f"已删除 {len(indices)} 个要素")

            # 清除选择图层
            js_code = """
                if (window.deleteLayer) {
                    map.removeLayer(window.deleteLayer);
                    window.deleteLayer = null;
                }
            """
            self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"删除要素时出错：{str(e)}")
            QMessageBox.critical(self, "错误", f"删除要素时出错：{str(e)}")

    def highlight_selected_features(self, indices, is_hover=False):
        """高亮显示选中的要素"""
        try:
            # 构建一个完整的 JavaScript 函数来处理高亮显示
            js_code = f"""
                (function() {{
                    if (!window.deleteLayer) return;

                    // 存储所有的 GeoJSON 图层
                    var geoJSONLayers = [];
                    window.deleteLayer.eachLayer(function(layer) {{
                        if (layer instanceof L.GeoJSON) {{
                            geoJSONLayers.push(layer);
                            // 重置样式
                            layer.setStyle({{
                                color: 'red',
                                weight: 2,
                                fillOpacity: 0.2
                            }});
                        }}
                    }});

                    // 高亮显示选中的要素
                    var selectedIndices = {str(indices)};
                    selectedIndices.forEach(function(idx) {{
                        if (idx >= 0 && idx < geoJSONLayers.length) {{
                            var layer = geoJSONLayers[idx];
                            layer.setStyle({{
                                color: '{('blue' if is_hover else 'yellow')}',
                                weight: 3,
                                fillOpacity: {0.3 if is_hover else 0.4}
                            }});
                            layer.bringToFront();
                        }}
                    }});

                    // 如果不是悬停状态且有选中的要素，调整地图视图
                    if (!{str(is_hover).lower()} && selectedIndices.length > 0) {{
                        var bounds = null;
                        selectedIndices.forEach(function(idx) {{
                            if (idx >= 0 && idx < geoJSONLayers.length) {{
                                var layerBounds = geoJSONLayers[idx].getBounds();
                                if (!bounds) {{
                                    bounds = layerBounds;
                                }} else {{
                                    bounds.extend(layerBounds);
                                }}
                            }}
                        }});
                        if (bounds) {{
                            map.fitBounds(bounds, {{padding: [50, 50]}});
                        }}
                    }}
                }})();
            """
            self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"高亮显示要素时出错：{str(e)}")

    def show_project_settings_dialog(self):
        """显示项目设置对话框"""
        try:
            from GUI.ProjectSettingsDialog import ProjectSettingsDialog
            dialog = ProjectSettingsDialog(self)

            # 如果已有配置，加载现有设置
            if hasattr(self, 'project_config'):
                dialog.load_settings(self.project_config)

            if dialog.exec_() == QDialog.Accepted:
                settings = dialog.get_settings()
                # 确保设置包含所有必要的路径
                if 'image_path' not in settings:
                    settings['image_path'] = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\image"
                if 'shape_path' not in settings:
                    settings['shape_path'] = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\dataset\shape"
                if 'model_path' not in settings:
                    settings['model_path'] = r"D:\Satellite_Image_Auto_Offset\Autooffset_Test\trained_models"

                self.save_project_config(settings)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示项目设置对话框时出错：{str(e)}")

    def save_project_config(self, settings):
        """保存项目配置"""
        try:
            # 确保包含 dataset_path
            if 'shape_path' in settings and 'dataset_path' not in settings:
                settings['dataset_path'] = settings['shape_path']  # 使用 shape_path 作为 dataset_path

            # 更新配置
            self.project_config = settings

            # 创建配置文件目录
            config_dir = os.path.join(os.path.dirname(__file__), 'config')
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 保存配置到文件
            config_file = os.path.join(config_dir, 'project_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)

            QMessageBox.information(self, "成功", "项目设置已保存")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存项目设置时出错：{str(e)}")

    def show_deepseek_chat(self):
        chat_dialog = DeepSeekChatDialog(self)
        chat_dialog.exec_()

    def run_hf_inference(self):
        if not HAS_TRANSFORMERS:
            self.show_uniform_message("错误", "transformers库未正确安装或导入失败。\n\n请运行以下命令修复：\npip install protobuf==3.19.6", "critical")
            return

        if not hasattr(self, 'hf_model'):
            # 加载HuggingFace分割模型
            self.hf_model = pipeline(
                "image-segmentation",
                model="facebook/detr-resnet-50-panoptic",
                device=0 if torch.cuda.is_available() else -1
            )

        # 获取当前选择的区域图像
        if hasattr(self, 'current_image'):
            # 执行推理
            results = self.hf_model(self.current_image)

            # 显示结果
            self.show_segmentation_results(results)

    def run_sam_inference(self):
        if not hasattr(self, 'sam_predictor'):
            # 加载SAM模型
            sam = sam_model_registry["vit_h"](checkpoint="sam_vit_h_4b8939.pth")
            self.sam_predictor = SamPredictor(sam)

        # 获取当前选择的区域图像
        if hasattr(self, 'current_image'):
            # 设置输入图像
            self.sam_predictor.set_image(self.current_image)

            # 获取图像中心点作为提示点
            h, w = self.current_image.shape[:2]
            input_point = np.array([[w//2, h//2]])
            input_label = np.array([1])

            # 生成掩码
            masks, _, _ = self.sam_predictor.predict(
                point_coords=input_point,
                point_labels=input_label,
                multimask_output=True
            )

            # 显示结果
            self.show_segmentation_results(masks)

    def show_segmentation_results(self, results):
        """显示分割结果"""
        try:
            from PIL import Image, ImageDraw
            import numpy as np
            import os
            import geopandas as gpd
            from shapely.geometry import Polygon

            # 解包SAM模型的结果
            image = results['image']
            masks = results['masks']
            scores = results['scores']
            input_points = results['input_points']

            # 创建结果目录
            result_dir = os.path.join(self.project_config['model_path'], 'Model')
            if not os.path.exists(result_dir):
                os.makedirs(result_dir)

            # 将numpy图像转换为PIL图像
            original_image = Image.fromarray(image)
            result_image = original_image.copy()
            draw = ImageDraw.Draw(result_image, 'RGBA')

            # 为每个检测到的对象绘制半透明遮罩
            colors = [
                (255, 0, 0, 128),   # 红色
                (0, 255, 0, 128),   # 绿色
                (0, 0, 255, 128),   # 蓝色
                (255, 255, 0, 128), # 黄色
                (255, 0, 255, 128), # 紫色
                (0, 255, 255, 128)  # 青色
            ]

            print("处理分割结果...")
            print(f"检测到 {len(masks)} 个目标")

            # 处理分割结果
            polygons = []
            mask_scores = []

            for i, (mask, score) in enumerate(zip(masks, scores)):
                try:
                    print(f"\n处理第 {i+1} 个目标:")
                    print(f"- 置信度: {score:.2f}")
                    print(f"- 掩码形状: {mask.shape}")

                    # 将掩码转换为PIL图像
                    mask_image = Image.fromarray((mask.astype(np.uint8) * 255))

                    # 选择颜色
                    color = colors[i % len(colors)]

                    # 创建彩色遮罩
                    colored_mask = Image.new('RGBA', mask_image.size, color)

                    # 将遮罩应用到结果图像
                    result_image.paste(colored_mask, (0, 0), mask=mask_image)

                    # 从掩码中提取边界点
                    contours = cv2.findContours(
                        mask.astype(np.uint8),
                        cv2.RETR_EXTERNAL,
                        cv2.CHAIN_APPROX_SIMPLE
                    )[0]

                    # 处理每个轮廓
                    for contour in contours:
                        # 简化轮廓点
                        if len(contour) > 3:  # 确保至少有3个点形成多边形
                            # 将轮廓点转换为多边形
                            coords = [(point[0][0], point[0][1]) for point in contour]
                            if coords[0] != coords[-1]:  # 确保多边形闭合
                                coords.append(coords[0])
                            polygons.append(Polygon(coords))
                            mask_scores.append(score)

                except Exception as segment_error:
                    print(f"处理第 {i+1} 个目标时出错: {str(segment_error)}")
                    continue

            # 保存结果图像
            result_path = os.path.join(result_dir, 'segmentation_result.png')
            result_image.save(result_path)
            print(f"\n结果图像已保存到: {result_path}")

            # 创建并保存shapefile
            if polygons:
                # 创建GeoDataFrame
                gdf = gpd.GeoDataFrame({
                    'geometry': polygons,
                    'score': mask_scores
                })

                # 设置坐标系统为WGS84
                gdf.set_crs(epsg=4326, inplace=True)

                # 保存为shape文件
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                shape_file = os.path.join(self.project_config['shape_path'], f'inference_result_{timestamp}.shp')
                gdf.to_file(shape_file)
                print(f"\nShape文件已保存到: {shape_file}")

                # 将结果添加到地图上
                self.add_layer_to_map(shape_file)

                # 添加到图层列表
                layer_name = os.path.basename(shape_file)
                self.add_layer_to_list(layer_name, shape_file)

                print("结果已添加到地图和图层列表")

            # 显示结果对话框
            self.show_result_dialog(original_image, result_image, [
                {'label': f'Segment {i+1}', 'score': score}
                for i, score in enumerate(scores)
            ])

            # 更新进度为100%
            if hasattr(self, 'inference_dialog'):
                self.inference_dialog.update_progress("推理完成", 100)
            # 清除地图上的选择区域
            self.web_bridge.clearSelection()

        except Exception as e:
            error_msg = f"显示分割结果时出错：{str(e)}\n{traceback.format_exc()}"
            print(f"\n错误: {error_msg}")
            print("详细信息:")
            print(f"- 结果类型: {type(results)}")
            print(f"- 结果长度: {len(results) if results else 0}")
            raise Exception(error_msg)

    def run_local_custom_model_inference(self):
        """运行本地自训练模型推理"""
        # 创建模型类型选择对话框
        type_dialog = QDialog(self)
        type_dialog.setWindowTitle("选择模型类型")
        type_dialog.setMinimumWidth(350)

        # 设置对话框布局
        layout = QVBoxLayout(type_dialog)

        # 模型类型选择
        type_label = QLabel("请选择模型类型:")
        layout.addWidget(type_label)

        model_type_combo = QComboBox()
        model_type_combo.addItems(["UNet模型", "LoGCAN模型"])
        layout.addWidget(model_type_combo)

        # 模型类型说明
        info_label = QLabel("UNet模型: 使用.h5格式模型文件\nLoGCAN模型: 使用.ckpt格式模型文件")
        info_label.setStyleSheet("color: #666;")
        layout.addWidget(info_label)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(type_dialog.accept)
        button_box.rejected.connect(type_dialog.reject)
        layout.addWidget(button_box)

        # 显示对话框
        if type_dialog.exec_() != QDialog.Accepted:
            return

        # 获取选择的模型类型
        selected_type = model_type_combo.currentText()
        model_type = "UNet" if "UNet" in selected_type else "LoGCAN"

        # 根据选择的模型类型设置文件过滤器
        if model_type == "UNet":
            file_filter = "UNet模型文件 (*.h5);;所有文件 (*.*)"
            default_dir = self.project_config.get('model_path', '')
        else:  # LoGCAN
            file_filter = "LoGCAN模型文件 (*.ckpt);;PyTorch模型 (*.pth *.pt);;所有文件 (*.*)"
            model_path = self.project_config.get('model_path', '')
            if model_path:
                default_dir = os.path.join(os.path.dirname(model_path), 'trained_models')
            else:
                default_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trained_models')

        # 打开文件选择对话框
        model_path, _ = QFileDialog.getOpenFileName(
            self,
            f"选择{model_type}模型文件",
            default_dir,
            file_filter
        )

        if not model_path:
            return

        # 保存选择的模型类型和路径
        self.current_inference_type = "local_custom"
        self.current_model_path = model_path
        self.current_model_type = model_type

        # 显示选择信息
        QMessageBox.information(
            self,
            "模型已选择",
            f"已选择{model_type}模型：\n{model_path}\n\n请在地图上框选要进行推理的区域。"
        )

        # 激活选择区域模式
        self.activate_select_zone()

    def run_local_hf_inference(self):
        """运行本地HuggingFace模型推理"""
        # 创建模型选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择HuggingFace模型")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # 添加模型选择下拉框
        model_label = QLabel("选择模型:")
        layout.addWidget(model_label)

        model_combo = QComboBox()
        model_combo.addItems([
            # 语义分割模型
            "facebook/mask2former-swin-large-ade-semantic",  # 通用场景分割
            "facebook/mask2former-swin-base-coco",          # 目标检测和分割
            "openmmlab/upernet-convnext-base",             # 通用场景分割
            # 遥感专用模型
            "nvidia/mit-b0",                               # 适用于卫星图像分割
            "microsoft/beit-base-finetuned-ade-640-512",   # 地物特征提取
            # 专门用于地物识别的模型
            "DAMO-YOLO/DAMO-YOLO-S",                      # 建筑物检测
            "whu-lmars/levir-unet-plus",                  # 地物变化检测
            "alibaba-damo/image-segmentation-landcover",   # 土地覆盖分类
            # 水体检测模型
            "ThomasSimonini/Water-Segmentation",          # 水体分割
            # 道路提取模型
            "cswin-t/road-extraction",                    # 道路网络提取
            # 建筑物检测模型
            "microsoft/swin-base-building-detection",      # 建筑物检测
            # 植被分析模型
            "nvidia/segformer-b0-vegetation",             # 植被覆盖分析
            "nvidia/segformer-b5-finetuned-ade-640-640",  # 高精度场景分割
        ])
        layout.addWidget(model_combo)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            selected_model = model_combo.currentText()

            # 激活选择区域模式
            self.activate_select_zone()

            # 等待用户选择区域
            # 在saveSelectedArea中处理推理
            self.current_inference_type = "local_hf"
            self.current_model_path = selected_model

    def run_local_sam_inference(self):
        """运行本地SAM模型推理"""
        # 创建模型选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择SAM模型")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # 添加模型选择下拉框
        model_label = QLabel("选择模型:")
        layout.addWidget(model_label)

        model_combo = QComboBox()
        model_combo.addItems([
            "vit_h",  # ViT-H SAM model
            "vit_l",  # ViT-L SAM model
            "vit_b",  # ViT-B SAM model
        ])
        layout.addWidget(model_combo)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            selected_model = model_combo.currentText()

            # 激活选择区域模式
            self.activate_select_zone()

            # 等待用户选择区域
            # 在saveSelectedArea中处理推理
            self.current_inference_type = "local_sam"
            self.current_model_path = selected_model

    def process_local_custom_inference(self, points):
     """处理本地自训练模型推理"""
     try:
        # 获取模型类型（如果未定义则默认为UNet）
        model_type = getattr(self, 'current_model_type', 'UNet')

        # 1. 下载选定区域的卫星图像
        image_path = self.download_satellite_image_for_inference(points)
        if not image_path:
            return

        # 2. 创建推理实例并加载模型
        from Core.custom_model_inference import CustomModelInference
        inference = CustomModelInference(self.current_model_path, model_type=model_type, parent=self)
        inference.load_model()

        # 3. 运行推理
        result_path, shape_path = inference.run_inference(image_path)

        # 4. 显示结果
        if result_path and os.path.exists(result_path):
            # 添加结果到地图
            if shape_path and os.path.exists(shape_path):
                self.add_layer_to_map(shape_path)
                result_type = "矢量"
            else:
                # 如果没有矢量结果，则添加栅格结果
                self.add_raster_to_map(result_path)
                result_type = "栅格"

            # 清除选择区域
            self.clear_selection_area()

            QMessageBox.information(self, "完成", f"{model_type}模型推理完成，{result_type}结果已添加到地图中。\n\n结果文件保存位置:\n{shape_path if shape_path else result_path}")
        else:
            QMessageBox.warning(self, "警告", "推理完成，但没有生成有效的结果文件。")

     except Exception as e:
        # 显示详细错误信息
        import traceback
        error_details = traceback.format_exc()
        error_message = f"推理过程发生错误：\n{str(e)}\n\n详细信息：\n{error_details}"

        QMessageBox.critical(self, "错误", error_message)
        print(error_message)

    def process_local_sam_inference(self, points):
        """处理本地SAM模型推理"""
        from GUI.InferenceProgressDialog import InferenceProgressDialog
        import cv2
        import torch
        import numpy as np
        from segment_anything import sam_model_registry, SamPredictor

        # 创建进度对话框
        progress_dialog = InferenceProgressDialog(self)
        progress_dialog.show()

        try:
            # 检测可用设备并显示信息
            if torch.cuda.is_available():
                device = "cuda"
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                device_info = f"GPU: {gpu_name} ({gpu_memory:.2f} GB)"
                print(f"检测到GPU，将使用GPU进行SAM模型推理: {device_info}")
                progress_dialog.log_message(f"使用GPU进行推理: {device_info}")

                # SAM模型在GPU上内存占用较大，尝试优化
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.enabled = True
            else:
                device = "cpu"
                import platform
                import multiprocessing
                cpu_info = platform.processor()
                cpu_cores = multiprocessing.cpu_count()
                device_info = f"CPU: {cpu_info} ({cpu_cores}核)"
                print(f"未检测到可用的GPU，将使用CPU进行SAM模型推理: {device_info}")
                progress_dialog.log_message(f"使用CPU进行推理: {device_info}")
                progress_dialog.log_message("注意: SAM模型在CPU上运行可能会非常慢，请耐心等待")

                # 为CPU优化线程数
                torch.set_num_threads(multiprocessing.cpu_count())
                print(f"已设置PyTorch使用 {multiprocessing.cpu_count()} 个CPU线程进行推理")

            # 定义模型文件映射
            model_files = {
                'vit_h': 'sam_vit_h_4b8939.pth',
                'vit_l': 'sam_vit_l_0b3195.pth',
                'vit_b': 'sam_vit_b_01ec64.pth'
            }

            progress_dialog.progress_updated.emit("正在检查模型文件...", 10)

            model_type = self.current_model_path
            model_file = os.path.join(self.project_config['model_path'],
                                    model_files.get(model_type, 'sam_vit_h_4b8939.pth'))

            if not os.path.exists(model_file):
                error_msg = (f"找不到SAM模型文件：{model_file}\n"
                           f"请确保模型文件已放置在正确位置：\n"
                           f"{self.project_config['model_path']}")
                progress_dialog.log_message(f"错误: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                progress_dialog.reject()
                return

            print(f"使用模型：{model_type}，文件路径：{model_file}")
            progress_dialog.log_message(f"模型类型: {model_type}")
            progress_dialog.log_message(f"模型文件: {os.path.basename(model_file)}")

            # 下载并预处理选定区域的图像
            progress_dialog.progress_updated.emit("正在下载卫星图像...", 20)
            image_path = self.download_satellite_image_for_inference(points)
            if not image_path:
                progress_dialog.log_message("下载卫星图像失败")
                progress_dialog.reject()
                return

            progress_dialog.log_message(f"已下载图像: {os.path.basename(image_path)}")

            # 加载图像并进行预处理
            image = cv2.imread(image_path)
            if image is None:
                error_msg = "无法加载图像"
                progress_dialog.log_message(f"错误: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                progress_dialog.reject()
                return

            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            progress_dialog.log_message(f"图像尺寸: {image.shape[1]}x{image.shape[0]}")

            # 加载选定的SAM模型
            progress_dialog.progress_updated.emit(f"正在加载SAM模型 ({model_type})...", 40)
            progress_dialog.log_message("正在加载模型，这可能需要一些时间...")

            # 初始化模型，设置为接近半精度以优化GPU内存使用
            sam = sam_model_registry[model_type](checkpoint=model_file)

            # 移动模型到正确的设备
            sam.to(device=device)

            if device == "cuda":
                # 查看模型加载后的GPU内存使用情况
                memory_allocated = torch.cuda.memory_allocated(0) / (1024**3)
                memory_reserved = torch.cuda.memory_reserved(0) / (1024**3)
                progress_dialog.log_message(f"GPU内存使用: 已分配={memory_allocated:.2f}GB, 已预留={memory_reserved:.2f}GB")

            # 创建预测器
            predictor = SamPredictor(sam)

            # 设置图像
            progress_dialog.progress_updated.emit("正在预处理图像...", 50)
            predictor.set_image(image)

            # 准备输入点
            input_points = np.array([[p['lng'], p['lat']] for p in points])
            input_labels = np.ones(len(points))

            # 执行推理
            progress_dialog.progress_updated.emit("正在进行分割推理...", 60)
            progress_dialog.log_message("正在执行模型推理，请耐心等待...")

            # 使用自适应策略，根据点的数量决定是否使用批处理
            if len(points) > 10:
                progress_dialog.log_message(f"点数较多 ({len(points)}个)，使用分批处理")

                # 分批处理点
                batch_size = 5
                all_masks = []
                all_scores = []
                all_logits = []

                for i in range(0, len(points), batch_size):
                    batch_points = input_points[i:i+batch_size]
                    batch_labels = input_labels[i:i+batch_size]

                    progress_percent = 60 + (i / len(points)) * 20
                    progress_dialog.progress_updated.emit(f"正在处理第 {i+1}-{min(i+batch_size, len(points))} 个点 (共{len(points)}个)...", progress_percent)

                    batch_masks, batch_scores, batch_logits = predictor.predict(
                        point_coords=batch_points,
                        point_labels=batch_labels,
                        multimask_output=True
                    )

                    all_masks.append(batch_masks)
                    all_scores.append(batch_scores)
                    all_logits.append(batch_logits)

                # 合并结果
                masks = np.vstack([m for m in all_masks])
                scores = np.vstack([s for s in all_scores])
                logits = np.vstack([l for l in all_logits])
            else:
                # 直接处理所有点
                masks, scores, logits = predictor.predict(
                    point_coords=input_points,
                    point_labels=input_labels,
                    multimask_output=True
                )

            # 获取最佳掩码
            progress_dialog.progress_updated.emit("正在处理分割结果...", 80)

            # 对于每个点，选择最佳掩码
            if len(masks.shape) == 4:  # 如果是多掩码输出
                # 选择每个点的最高分数掩码
                best_masks = []
                for i in range(len(scores)):
                    best_idx = np.argmax(scores[i])
                    best_masks.append(masks[i, best_idx])

                final_mask = np.any(best_masks, axis=0).astype(np.uint8)
            else:
                # 单掩码输出
                final_mask = masks.astype(np.uint8)

            # 处理结果
            results = {
                'original_image': image,
                'masks': [final_mask],
                'scores': [np.max(scores)],
                'classes': ['segmented_object'],
                'input_points': input_points
            }

            progress_dialog.log_message(f"分割完成，检测到 {len(results['masks'])} 个对象")

            # 显示分割结果
            progress_dialog.progress_updated.emit("正在显示结果...", 90)
            self.show_segmentation_results(results)

            # 完成
            progress_dialog.progress_updated.emit("推理完成！", 100)
            progress_dialog.accept()

            # 清理资源
            del predictor
            del sam

            # 清理GPU内存
            if device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()
                print("已清理GPU缓存")

                # 显示清理后的内存使用
                memory_allocated_after = torch.cuda.memory_allocated(0) / (1024**3)
                memory_reserved_after = torch.cuda.memory_reserved(0) / (1024**3)
                progress_dialog.log_message(f"清理后GPU内存使用: 已分配={memory_allocated_after:.2f}GB, 已预留={memory_reserved_after:.2f}GB")

            return True

        except Exception as e:
            error_msg = f"SAM模型推理过程中出错：{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()

            progress_dialog.log_message(f"错误：{str(e)}")
            QMessageBox.critical(self, "错误", error_msg)

            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            progress_dialog.reject()
            return False

    def saveSelectedArea(self, points_json):
        """保存选定的区域坐标并根据当前推理类型进行处理"""
        # 解析选择的区域坐标
        points = json.loads(points_json)

        # 如果没有选择区域，直接返回
        if not points or len(points) < 3:  # 至少需要3个点才能形成一个有效的多边形
            QMessageBox.warning(self, "警告", "请选择一个有效的区域！")
            return

        # 保存坐标点到txt文件
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            txt_filename = os.path.join(self.project_config['dataset_path'], f'selected_area_coords_{timestamp}.txt')

            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write("# 选择区域的坐标点列表 (经度,纬度)\n")
                for point in points:
                    f.write(f"{point['lng']},{point['lat']}\n")

            QMessageBox.information(self, "成功", f"已将坐标点保存到文件：\n{txt_filename}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存坐标文件时发生错误：{str(e)}")

        # 保存选择区域为shape文件
        if HAS_GEOPANDAS:
            try:
                # 创建一个临时文件名
                shape_filename = os.path.join(self.project_config['dataset_path'], f'selected_area_{timestamp}.shp')

                # 创建一个包含多边形的GeoDataFrame
                # 注意：points中的坐标是[lng, lat]格式，需要创建一个闭合的多边形
                coords = [[p['lng'], p['lat']] for p in points]
                if coords[0] != coords[-1]:  # 确保多边形是闭合的
                    coords.append(coords[0])

                from shapely.geometry import Polygon
                polygon = Polygon(coords)
                gdf = gpd.GeoDataFrame(geometry=[polygon], crs='EPSG:4326')

                # 保存为shape文件
                gdf.to_file(shape_filename)

                # 将新创建的shape文件添加到图层列表
                self.add_layer_to_map(shape_filename)

                QMessageBox.information(self, "成功", f"已将选择区域保存为Shape文件：\n{shape_filename}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存Shape文件时发生错误：{str(e)}")

        # 根据当前推理类型进行处理
        if hasattr(self, 'current_inference_type'):
            if self.current_inference_type == "local_custom":
                self.process_local_custom_inference(points)
            elif self.current_inference_type == "local_hf":
                self.process_local_hf_inference(points)
            elif self.current_inference_type == "local_sam":
                self.process_local_sam_inference(points)
            elif self.current_inference_type.startswith("cloud_"):
                self.process_cloud_inference(points)

            # 清除当前推理类型
            delattr(self, 'current_inference_type')
            if hasattr(self, 'current_model_path'):
                delattr(self, 'current_model_path')

        else:
            # 如果不是模型推理操作，则保存为生成数据集使用的区域
            self.selected_area_points = points
            print(f"已保存选定区域坐标：{points}")

    def mousePressEvent(self, event):
        """处理鼠标按下事件"""
        if not self.manual_offset_tool or not self.manual_offset_tool.is_active:
          super().mousePressEvent(event)
          return

        # 获取鼠标点击位置对应的地图坐标
        map_pos = self.get_map_coordinates(event.pos())
        if not map_pos:
            return

        # 在手动偏移模式下，尝试查找并选中最近的点
        if self.manual_offset_tool.handle_mouse_press(event, map_pos):
            # 如果成功选中点，阻止事件继续传播
            event.accept()
            # 高亮显示选中的点
            if self.manual_offset_tool.dragging_point_id is not None:
                self.highlight_points([(self.manual_offset_tool.dragging_point_id, map_pos)])
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        if not self.manual_offset_tool or not self.manual_offset_tool.is_active:
            super().mouseMoveEvent(event)
            return

        # 获取鼠标当前位置对应的地图坐标
        map_pos = self.get_map_coordinates(event.pos())
        if not map_pos:
            # 如果获取不到坐标，清除高亮
            self.highlight_points([])
            return

            # 查找最近的点
            point_id = self.find_nearest_point(map_pos)
            if point_id is not None:
            # 找到点，高亮显示
             self.highlight_points([(point_id, map_pos)], color='#FFFF00')
        else:
            # 没有找到点，清除高亮
            self.highlight_points([])

        event.accept()

    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件"""
        if not self.manual_offset_tool or not self.manual_offset_tool.is_active:
           super().mouseReleaseEvent(event)
           return

        # 获取鼠标释放位置对应的地图坐标
        map_pos = self.get_map_coordinates(event.pos())
        if not map_pos:
            return

        # 在手动偏移模式下，完成点的拖动
        if self.manual_offset_tool.handle_mouse_release(event, map_pos):
            # 如果成功完成拖动，阻止事件继续传播
            event.accept()
            # 清除高亮显示
            self.highlight_points([])
        else:
            super().mouseReleaseEvent(event)

    def get_map_coordinates(self, screen_pos):
        """将屏幕坐标转换为地图坐标"""
        if not hasattr(self, 'web_view'):
            return None

        # 执行JavaScript来获取地图坐标
        js_code = """
        (function() {
            var containerPoint = L.point(%d, %d);
            var layerPoint = map.containerPointToLayerPoint(containerPoint);
            var latlng = map.layerPointToLatLng(layerPoint);
            return [latlng.lng, latlng.lat];
        })();
        """ % (screen_pos.x(), screen_pos.y())

        result = self.web_view.page().runJavaScript(js_code)
        if result:
            return tuple(result)  # 返回 (x, y) 坐标
        return None

    def find_nearest_point(self, map_pos, max_distance=10):
        """查找距离给定位置最近的点"""
        if not hasattr(self, 'current_points') or self.current_points is None:
            return None

        # 创建点对象
        click_point = Point(map_pos[0], map_pos[1])  # 使用经度和纬度创建点

        # 获取圆形半径
        circle_radius = self.point_offset_manager.offset_settings['surface_bin_size'] / 4

        # 计算每个点到点击位置的距离
        min_distance = float('inf')
        nearest_point_id = None

        for idx, geometry in enumerate(self.current_points.geometry):
            # 创建以点为中心的圆
            circle = geometry.buffer(circle_radius)
            # 计算点击位置到圆的距离
            distance = click_point.distance(circle)
            if distance < min_distance:
                min_distance = distance
                nearest_point_id = idx

        return nearest_point_id

    def load_obstacles(self):
        """加载障碍物Shapefile"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择障碍物Shapefile",
            self.project_config.get('shape_path', ''),
            "Shapefile (*.shp)"
        )

        if file_path:
            try:
                # 读取障碍物数据
                self.obstacles_gdf = gpd.read_file(file_path)
                # 加载到点位偏移管理器
                if self.point_offset_manager.load_obstacles(file_path):
                    self.add_layer_to_map(file_path)  # 在地图上显示障碍物
                    QMessageBox.information(self, "成功", "已加载障碍物数据")
                else:
                    QMessageBox.warning(self, "错误", "加载障碍物数据失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载障碍物文件时出错：{str(e)}")

    def load_grid(self):
        """加载网格文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择网格文件",
                self.project_config.get('shape_path', ''),
                "Shapefile (*.shp)"
            )

            if not file_path:
                return

            # 读取网格文件
            grid_data = gpd.read_file(file_path)

            # 输出调试信息
            print(f"网格文件几何类型: {grid_data.geometry.iloc[0].geom_type}")
            print(f"网格文件坐标系统: {grid_data.crs}")

            # 检查几何类型
            geom_type = grid_data.geometry.iloc[0].geom_type
            if geom_type == 'Point':
                # 如果是点文件，作为网格点加载
                print("加载点类型网格")
                self.grid_label_gdf = grid_data
                self.current_points = grid_data

                # 转换到WGS84
                if self.grid_label_gdf.crs != "EPSG:4326":
                    self.grid_label_gdf = self.grid_label_gdf.to_crs("EPSG:4326")

                # 提取点坐标
                points = np.array([[p.x, p.y] for p in self.grid_label_gdf.geometry])

                # 添加到地图
                self.add_grid_points_to_map(points)
                QMessageBox.information(self, "成功", "已加载网格点数据")

            elif geom_type == 'Polygon':
                # 如果是多边形类型，作为网格加载
                print("加载多边形网格到地图")
                self.grid_gdf = grid_data
                self.grid_path = file_path

                # 提取网格中心点
                centers = []
                for geom in grid_data.geometry:
                    center = geom.centroid
                    centers.append(Point(center.x, center.y))

                # 创建中心点GeoDataFrame
                self.grid_centers_gdf = gpd.GeoDataFrame(geometry=centers, crs=grid_data.crs)

                # 转换到WGS84
                if self.grid_centers_gdf.crs != "EPSG:4326":
                    self.grid_centers_gdf = self.grid_centers_gdf.to_crs("EPSG:4326")

                print(f"提取了 {len(centers)} 个网格中心点")

                # 加载到地图上
                self.add_layer_to_map(file_path)

                # 将中心点数据传递给point_offset_manager
                if hasattr(self, 'point_offset_manager'):
                    center_points = np.array([[p.x, p.y] for p in self.grid_centers_gdf.geometry])
                    self.point_offset_manager.load_grid_points(center_points)

                QMessageBox.information(self, "成功", "已加载网格数据")
            else:
                QMessageBox.warning(self, "警告", f"不支持的几何类型: {geom_type}")

        except Exception as e:
            print(f"加载网格文件失败: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"加载网格文件时出错：{str(e)}")

    def add_grid_points_to_map(self, points, layer_name="网格点", clear_existing=True):
        """添加网格点到地图"""
        try:
            # 清除现有点位
            if clear_existing:
                self.clear_point_layers()

            # 计算圆形半径（米）
            radius = self.point_offset_manager.offset_settings['surface_bin_size'] / 4
            print(f"圆形半径: {radius}米")

            # 从网格数据提取中心点用于吸附功能
            all_grid_centers = []

            # 首先尝试从自定义的网格中心点数据中获取
            if hasattr(self, 'grid_centers_gdf') and not self.grid_centers_gdf.empty:
                print("使用专门存储的网格中心点数据进行吸附")
                all_grid_centers = [[p.x, p.y] for p in self.grid_centers_gdf.geometry]
                print(f"从grid_centers_gdf提取的网格中心点总数: {len(all_grid_centers)}")
            # 然后尝试从point_offset_manager中获取
            elif hasattr(self.point_offset_manager, 'grid_centers') and len(self.point_offset_manager.grid_centers) > 0:
                print("使用point_offset_manager中的网格中心点数据进行吸附")
                all_grid_centers = [[center[0], center[1]] for center in self.point_offset_manager.grid_centers]
                print(f"从point_offset_manager提取的网格中心点总数: {len(all_grid_centers)}")
            # 最后尝试从输入点数据中获取
            else:
                print("没有找到存储的网格中心点数据，尝试从输入参数中提取")
                # 检查points的类型，处理不同格式的输入
                if hasattr(points, 'geometry'):
                    # 如果是GeoDataFrame或有geometry属性
                    all_grid_centers = [[p.x, p.y] for p in points.geometry]
                elif isinstance(points, np.ndarray):
                    # 如果已经是numpy数组，直接使用
                    all_grid_centers = points.tolist()
                else:
                    # 其他情况，尝试转换
                    all_grid_centers = np.array(points).tolist()

                print(f"从输入参数提取的网格中心点总数: {len(all_grid_centers)}")

            # 确保至少有一些网格中心点
            if len(all_grid_centers) == 0:
                print("警告：未能找到任何网格中心点，吸附功能可能无法正常工作")

            # 确保正确加载网格中心点
            print(f"最终用于吸附的网格中心点总数: {len(all_grid_centers)}")
            if len(all_grid_centers) > 0:
                print(f"第一个网格中心点示例: {all_grid_centers[0]}")

            # 提取点坐标 - 处理不同类型的points
            if hasattr(points, 'geometry'):
                # 如果是GeoDataFrame
                points_array = np.array([[p.x, p.y] for p in points.geometry])
            elif isinstance(points, np.ndarray):
                # 如果已经是numpy数组
                points_array = points
            else:
                # 其他情况，尝试转换
                points_array = np.array(points)

            # 保存当前点位数据
            self.current_points = points_array

            # 生成JavaScript代码
            js_code = f"""
                // 拖动状态变量
                window.isDragging = false;
                window.selectedCircleIndex = -1;
                window.dragStartPos = null;
                window.circleElements = [];

                // 网格中心点数据（用于吸附功能）- 优先级高
                window.gridCenters = {all_grid_centers};
                console.log('已加载 ' + window.gridCenters.length + ' 个网格中心点用于吸附');

                // 记录已有点位 - 优先级低
                window.existingPoints = [];

                // 清除之前的图层
                if (typeof pointLayerGroup !== 'undefined') {{
                    pointLayerGroup.clearLayers();
                }}

                // 清除网格中心点高亮层
                if (typeof gridCenterLayer !== 'undefined') {{
                    map.removeLayer(gridCenterLayer);
                }}

                // 清除网格中心点可视化层（如果存在）
                if (typeof allGridCentersLayer !== 'undefined') {{
                    map.removeLayer(allGridCentersLayer);
                }}

                // 创建新图层
                var pointLayerGroup = L.layerGroup();
                var points = {points_array.tolist()};
                var radius = {radius}

                // 创建所有圆点并添加到地图
                for (var i = 0; i < points.length; i++) {{
                    var point = points[i];
                    var circle = L.circle([point[1], point[0]], {{
                        color: 'blue',
                        fillColor: '#3388ff',
                        fillOpacity: 0.5,
                        weight: 1,
                        radius: radius
                    }});

                    // 存储索引
                    circle.pointIndex = i;

                    // 存储原始颜色
                    circle.originalColor = '#3388ff';

                    // 添加到图层
                    pointLayerGroup.addLayer(circle);

                    // 存储引用
                    window.circleElements.push(circle);

                    // 记录已有点位
                    window.existingPoints.push([point[0], point[1]]);
                }}

                // 添加图层到地图
                pointLayerGroup.addTo(map);
                window.pointLayerGroup = pointLayerGroup;  // 保存全局引用
                console.log('添加了 ' + points.length + ' 个网格点到地图');

                // 测试打印部分网格中心点
                if (window.gridCenters && window.gridCenters.length > 0) {{
                    console.log('部分网格中心点示例:');
                    for (var i = 0; i < Math.min(5, window.gridCenters.length); i++) {{
                        console.log(' - 网格中心点 #' + i + ': [' + window.gridCenters[i][0] + ', ' + window.gridCenters[i][1] + ']');
                    }}
                }}

                // 查找最近网格中心点函数
                function findNearestGridCenter(latlng) {{
                    if (!window.gridCenters || window.gridCenters.length === 0) {{
                        console.log("无可用的网格中心点数据");
                        return null;
                    }}

                    console.log("开始查找最近网格中心点，总数：" + window.gridCenters.length + "，当前位置: [" + latlng.lng.toFixed(6) + ", " + latlng.lat.toFixed(6) + "]");

                    var minDist = Infinity;
                    var nearestCenter = null;
                    var nearestIdx = -1;

                    // 遍历所有网格中心点
                    for (var i = 0; i < window.gridCenters.length; i++) {{
                        var center = window.gridCenters[i];
                        if (!center || center.length < 2) continue;  // 跳过无效的中心点

                        var centerLatLng = L.latLng(center[1], center[0]);  // [lng, lat] -> L.latLng
                        var dist = latlng.distanceTo(centerLatLng);

                        if (dist < minDist) {{
                            minDist = dist;
                            nearestCenter = centerLatLng;
                            nearestIdx = i;
                        }}
                    }}

                    if (nearestCenter) {{
                        console.log("找到最近的网格中心点 #" + nearestIdx + "，位置: [" + window.gridCenters[nearestIdx][0].toFixed(6) + ", " + window.gridCenters[nearestIdx][1].toFixed(6) + "]，距离: " + minDist.toFixed(2) + "米");
                        return nearestCenter;
                    }} else {{
                        console.log("未找到最近的网格中心点");
                        return null;
                    }}
                }}

                // 高亮显示网格中心点
                function highlightNearestGridCenter(latlng) {{
                    // 查找最近的网格中心
                    var nearestCenter = findNearestGridCenter(latlng);
                    if (!nearestCenter) {{
                        console.log("未找到可用的网格中心点");
                        return;
                    }}

                    // 移除旧的高亮
                    if (typeof gridCenterLayer !== 'undefined') {{
                        map.removeLayer(gridCenterLayer);
                    }}

                    // 创建新高亮
                    gridCenterLayer = L.circleMarker(nearestCenter, {{
                        radius: 8,
                        color: '#00FF00',
                        fillColor: '#00FF00',
                        fillOpacity: 0.7,
                        weight: 3
                    }}).addTo(map);

                    console.log("已高亮显示网格中心点，位置: [" + nearestCenter.lng.toFixed(6) + ", " + nearestCenter.lat.toFixed(6) + "]");
                }}

                // Tab键处理
                window.tabPressed = false;
                document.addEventListener('keydown', function(e) {{
                    if (e.key === 'Tab') {{
                        e.preventDefault(); // 阻止Tab键的默认行为
                        window.tabPressed = true;
                        console.log('Tab键按下 - 激活网格吸附');

                        // 显示视觉提示
                        if (!window.snapInfoDiv) {{
                            window.snapInfoDiv = document.createElement('div');
                            window.snapInfoDiv.style.position = 'absolute';
                            window.snapInfoDiv.style.top = '10px';
                            window.snapInfoDiv.style.left = '50%';
                            window.snapInfoDiv.style.transform = 'translateX(-50%)';
                            window.snapInfoDiv.style.backgroundColor = 'rgba(0,255,0,0.8)';
                            window.snapInfoDiv.style.color = 'white';
                            window.snapInfoDiv.style.padding = '8px 15px';
                            window.snapInfoDiv.style.borderRadius = '5px';
                            window.snapInfoDiv.style.zIndex = '9999';
                            window.snapInfoDiv.style.fontWeight = 'bold';
                            window.snapInfoDiv.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
                            window.snapInfoDiv.textContent = '✓ 网格吸附已激活';
                            document.body.appendChild(window.snapInfoDiv);
                        }}

                        // 如果正在拖动，立即吸附
                        if (window.isDragging && window.selectedCircleIndex >= 0) {{
                            var circle = window.circleElements[window.selectedCircleIndex];
                            var pos = circle.getLatLng();
                            console.log('正在拖动的点位置: [' + pos.lng.toFixed(6) + ', ' + pos.lat.toFixed(6) + ']');

                            var nearestCenter = findNearestGridCenter(pos);
                            if (nearestCenter) {{
                                console.log('发现最近的网格中心点，移动点到该位置');
                                circle.setLatLng(nearestCenter);
                                highlightNearestGridCenter(pos);
                            }}
                        }}
                    }}
                }});

                document.addEventListener('keyup', function(e) {{
                    if (e.key === 'Tab') {{
                        window.tabPressed = false;
                        console.log('Tab键释放 - 关闭网格吸附');

                        // 移除视觉提示
                        if (window.snapInfoDiv) {{
                            document.body.removeChild(window.snapInfoDiv);
                            window.snapInfoDiv = null;
                        }}

                        // 移除网格中心点高亮
                        if (typeof gridCenterLayer !== 'undefined') {{
                            map.removeLayer(gridCenterLayer);
                        }}
                    }}
                }});

                // 点击事件处理
                function onMapClick(e) {{
                    console.log('地图点击事件触发，手动偏移模式:', window.manualOffsetMode);
                    if (!window.manualOffsetMode) return;

                    // 查找最近的点
                    var minDist = Infinity;
                    var nearestCircle = null;
                    var nearestIndex = -1;

                    for (var i = 0; i < window.circleElements.length; i++) {{
                        var circle = window.circleElements[i];
                        var dist = e.latlng.distanceTo(circle.getLatLng());
                        if (dist < minDist) {{
                            minDist = dist;
                            nearestCircle = circle;
                            nearestIndex = i;
                        }}
                    }}

                    // 如果点击位置靠近某个点，开始拖动
                    if (minDist <= radius * 2) {{
                        console.log('开始拖动点 #' + nearestIndex + '，距离: ' + minDist.toFixed(2) + '米');
                        window.isDragging = true;
                        window.selectedCircleIndex = nearestIndex;
                        window.dragStartPos = nearestCircle.getLatLng();

                        // 高亮显示选中的点，提高不透明度但保持原始颜色
                        var originalColor = nearestCircle.originalColor || nearestCircle.options.fillColor;
                        console.log('选中点的原始颜色:', originalColor);
                        nearestCircle.setStyle({{
                            color: '#000',
                            fillColor: originalColor,
                            fillOpacity: 0.9,
                            weight: 2
                        }});

                        // 如果Tab键被按下，显示最近的网格中心点
                        if (window.tabPressed) {{
                            var nearestCenter = findNearestGridCenter(nearestCircle.getLatLng());
                            if (nearestCenter) {{
                                nearestCircle.setLatLng(nearestCenter);
                                highlightNearestGridCenter(nearestCircle.getLatLng());
                            }}
                        }}

                        L.DomEvent.stopPropagation(e);
                    }}
                }}

                // 移动事件处理
                function onMapMouseMove(e) {{
                    if (!window.manualOffsetMode) return;

                    // 如果正在拖动点
                    if (window.isDragging && window.selectedCircleIndex >= 0) {{
                        var circle = window.circleElements[window.selectedCircleIndex];

                        // 处理吸附逻辑
                        if (window.tabPressed) {{
                            var nearestCenter = findNearestGridCenter(e.latlng);
                            if (nearestCenter) {{
                                circle.setLatLng(nearestCenter);
                                highlightNearestGridCenter(e.latlng);
                            }} else {{
                                circle.setLatLng(e.latlng);
                            }}
                        }} else {{
                            circle.setLatLng(e.latlng);
                        }}

                        // 拖动时使用原始颜色
                        var originalColor = circle.originalColor || circle.options.fillColor;
                        circle.setStyle({{
                            color: '#000',
                            fillColor: originalColor,
                            fillOpacity: 0.9,
                            weight: 2
                        }});

                        L.DomEvent.stopPropagation(e);
                        return;
                    }}

                    // 鼠标悬停效果
                    var minDist = Infinity;
                    var nearestCircle = null;

                    for (var i = 0; i < window.circleElements.length; i++) {{
                        var circle = window.circleElements[i];
                        var dist = e.latlng.distanceTo(circle.getLatLng());
                        if (dist < minDist) {{
                            minDist = dist;
                            nearestCircle = circle;
                        }}

                        // 重置样式到原始状态
                        if (i !== window.selectedCircleIndex) {{
                            // 如果点有存储的原始颜色，使用它；否则使用蓝色默认值
                            var originalColor = circle.originalColor || '#3388ff';
                            circle.setStyle({{
                                color: '#000',
                                fillColor: originalColor,
                                fillOpacity: 0.7,
                                weight: 1
                            }});
                        }}
                    }}

                    // 高亮显示最近的点，但保持其原始颜色
                    if (minDist <= radius * 2 && nearestCircle && nearestCircle.pointIndex !== window.selectedCircleIndex) {{
                        var originalColor = nearestCircle.originalColor || nearestCircle.options.fillColor;
                        nearestCircle.setStyle({{
                            color: '#000',
                            fillColor: originalColor,
                            fillOpacity: 0.9,
                            weight: 2
                        }});
                        document.body.style.cursor = 'pointer';
                    }} else if (!window.isDragging) {{
                        document.body.style.cursor = '';
                    }}
                }}

                // 鼠标释放事件处理
                function onMapMouseUp(e) {{
                    console.log('鼠标释放事件触发，手动偏移模式:', window.manualOffsetMode, '，正在拖动:', window.isDragging, '，选中点索引:', window.selectedCircleIndex);
                    if (!window.manualOffsetMode) return;

                    if (window.isDragging && window.selectedCircleIndex >= 0) {{
                        var circle = window.circleElements[window.selectedCircleIndex];
                        var finalPos = circle.getLatLng();

                        // 如果Tab键被按下，确保使用吸附后的位置
                        if (window.tabPressed) {{
                            var nearestCenter = findNearestGridCenter(finalPos);
                            if (nearestCenter) {{
                                finalPos = nearestCenter;
                                circle.setLatLng(finalPos);

                                // 立即更新颜色为绿色表示已经吸附到网格中心
                                circle.originalColor = '#FFFF00';  // 黄色
                                circle.setStyle({{
                                    color: '#000',
                                    fillColor: '#FFFF00',  // 黄色
                                    fillOpacity: 1.0,
                                    weight: 2
                                }});
                            }}
                        }}

                        // 发送拖动结果到Python
                        var data = {{
                            pointIndex: window.selectedCircleIndex,
                            newPosition: [finalPos.lng, finalPos.lat]
                        }};

                        // 临时增加透明度表示成功，保持绿色或原始颜色
                        var originalColor = window.tabPressed ? '#00FF00' : (circle.originalColor || circle.options.fillColor);
                        circle.setStyle({{
                            color: '#000',
                            fillColor: originalColor,
                            fillOpacity: 1.0,
                            weight: 2
                        }});

                        console.log('完成拖动，点 #' + window.selectedCircleIndex + ' 移动到: [' + finalPos.lng.toFixed(6) + ', ' + finalPos.lat.toFixed(6) + ']', '颜色:', originalColor);

                        // 如果是障碍物内的点（红色），现在将颜色设置为黄色
                        if (originalColor === '#FF0000' || originalColor === 'red' || originalColor.toLowerCase() === '#ff0000') {{
                            console.log('检测到红色点被拖动，将其更新为黄色');
                            circle.originalColor = '#FFFF00';  // 黄色
                            circle.setStyle({{
                                color: '#000',
                                fillColor: '#FFFF00',  // 黄色
                                fillOpacity: 1.0,
                                weight: 2
                            }});
                        }}

                        // 通知Python
                        try {{
                            console.log('尝试调用Python回调: pyPointDragComplete，数据:', JSON.stringify(data));
                            if (typeof window.pyPointDragComplete === 'function') {{
                                window.pyPointDragComplete(JSON.stringify(data));
                                console.log('Python回调调用成功');
                            }} else {{
                                console.error('Python回调函数未定义');
                            }}
                        }} catch(err) {{
                            console.error('调用Python回调失败:', err);
                        }}

                        // 延迟恢复样式，但保持新的颜色
                        setTimeout(function() {{
                            if (window.circleElements[window.selectedCircleIndex]) {{
                                var color = window.circleElements[window.selectedCircleIndex].originalColor || '#3388ff';
                                window.circleElements[window.selectedCircleIndex].setStyle({{
                                    color: '#000',
                                    fillColor: color,
                                    fillOpacity: 0.7,
                                    weight: 1
                                }});
                            }}
                        }}, 500);

                        // 清除高亮和状态
                        if (typeof gridCenterLayer !== 'undefined') {{
                            map.removeLayer(gridCenterLayer);
                        }}

                        window.isDragging = false;
                        window.selectedCircleIndex = -1;
                        document.body.style.cursor = '';
                    }}
                }}

                // 更新手动偏移模式状态，使用当前菜单项的状态
                window.manualOffsetMode = {str(self.manual_offset_action.isChecked()).lower() if self.manual_offset_action else 'false'};
                console.log('手动偏移模式状态:', window.manualOffsetMode);

                // 注册事件处理函数
                map.on('click', onMapClick);
                map.on('mousemove', onMapMouseMove);
                map.on('mouseup', onMapMouseUp);

                // 显示网格中心点的可视化调试功能 - 保留函数但默认不调用
                function showAllGridCenters() {{
                    if (!window.gridCenters || window.gridCenters.length === 0) {{
                        console.log("无可用的网格中心点数据进行可视化");
                        return;
                    }}

                    // 清除旧的可视化
                    if (typeof allGridCentersLayer !== 'undefined') {{
                        map.removeLayer(allGridCentersLayer);
                    }}

                    // 创建新的图层组
                    var allGridCentersLayer = L.layerGroup();

                    // 为每个网格中心点创建一个小圆点
                    for (var i = 0; i < window.gridCenters.length; i++) {{
                        var center = window.gridCenters[i];
                        if (!center || center.length < 2) continue;  // 跳过无效的中心点

                        // 创建一个很小的圆点表示网格中心
                        var dot = L.circleMarker([center[1], center[0]], {{
                            radius: 2,
                            color: '#FFFF00',
                            fillColor: '#FFFF00',
                            fillOpacity: 0.7,
                            weight: 1
                        }});

                        allGridCentersLayer.addLayer(dot);
                    }}

                    // 添加到地图
                    allGridCentersLayer.addTo(map);
                    window.allGridCentersLayer = allGridCentersLayer;

                    console.log('已显示所有 ' + window.gridCenters.length + ' 个网格中心点');
                }}

                // 添加快捷键来切换显示/隐藏网格中心点（仅用于调试）
                document.addEventListener('keydown', function(e) {{
                    // 按Ctrl+G组合键显示/隐藏网格中心点
                    if (e.ctrlKey && e.key === 'g') {{
                        e.preventDefault();
                        if (typeof allGridCentersLayer === 'undefined' || !map.hasLayer(allGridCentersLayer)) {{
                            console.log('显示网格中心点调试视图');
                            showAllGridCenters();
                        }} else {{
                            console.log('隐藏网格中心点调试视图');
                            map.removeLayer(allGridCentersLayer);
                        }}
                    }}
                }});

                // 注意：默认不自动显示所有网格中心点，但可以通过Ctrl+G显示
                // showAllGridCenters();  // 已注释掉，不再默认显示

                console.log('网格吸附功能已初始化，当前有 ' + window.gridCenters.length + ' 个网格中心点可用于吸附');
            """

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)

            # 定义Python回调函数，处理从JavaScript发来的拖动完成事件
            def handle_point_drag_complete(result):
                try:
                    if not result:
                        print("警告：收到空的拖动结果")
                        return

                    print(f"收到原始拖动结果数据: {result}")
                    data = json.loads(result)
                    point_index = data.get('pointIndex')
                    new_position = data.get('newPosition')  # [lng, lat]

                    print(f"接收到点位拖动完成事件: 索引={point_index}, 新位置={new_position}")

                    # 如果在手动偏移模式下
                    if hasattr(self, 'manual_offset_tool') and self.manual_offset_tool.is_active:
                        print(f"手动偏移工具已激活，发送点位移动信号")
                        # 触发点位移动信号
                        self.manual_offset_tool.point_moved.emit(point_index, tuple(new_position))
                    else:
                        print(f"警告：手动偏移工具未激活或不存在")
                except Exception as e:
                    print(f"处理点位拖动回调时出错: {str(e)}")
                    traceback.print_exc()

            # 注册Python回调到JavaScript
            js_callback = """
                window.pyPointDragComplete = function(jsonData) {
                    console.log('Python回调函数被调用，数据: ' + jsonData);
                    return jsonData;  // 返回数据给Python
                }
            """
            self.web_view.page().runJavaScript(js_callback)
            self.web_view.page().runJavaScript("pyPointDragComplete", handle_point_drag_complete)

            # 打印测试点的数据
            if len(points_array) > 0:
                print(f"测试点坐标: {points_array[0]}")

            print(f"成功添加 {len(points_array)} 个网格点到地图")

            # 添加到图层列表
            self.add_layer_to_list(layer_name, "grid_points")
            return True

        except Exception as e:
            print(f"添加网格点到地图时出错: {str(e)}")
            traceback.print_exc()
            return False
    def search_points_in_obstacles(self):
        """搜索障碍物内的点"""
        try:
            # 检查是否已加载障碍物和网格点
            if not hasattr(self, 'obstacles_gdf') or not hasattr(self, 'grid_label_gdf'):
                QMessageBox.warning(self, "警告", "请先加载障碍物和网格点图层！")
                return

            print("开始搜索障碍物内的点...")
            print(f"障碍物坐标系统: {self.obstacles_gdf.crs}")
            print(f"网格点坐标系统: {self.grid_label_gdf.crs}")

            # 确保两个图层使用相同的坐标系统
            target_crs = self.get_current_crs()
            print(f"目标坐标系统: {target_crs}")

            # 转换障碍物图层的坐标系统
            if str(self.obstacles_gdf.crs) != str(target_crs):
                print(f"转换障碍物坐标系统从 {self.obstacles_gdf.crs} 到 {target_crs}")
                self.obstacles_gdf = self.obstacles_gdf.to_crs(target_crs)

            # 转换网格点图层的坐标系统
            if str(self.grid_label_gdf.crs) != str(target_crs):
                print(f"转换网格点坐标系统从 {self.grid_label_gdf.crs} 到 {target_crs}")
                self.grid_label_gdf = self.grid_label_gdf.to_crs(target_crs)

            # 使用空间索引加速搜索
            print("构建空间索引...")
            obstacles_idx = index.Index()
            for idx, geom in enumerate(self.obstacles_gdf.geometry):
                obstacles_idx.insert(idx, geom.bounds)

            # 找出落在障碍物内的点
            points_in_obstacles = []
            total_points = len(self.grid_label_gdf)
            print(f"开始检查 {total_points} 个点...")

            for idx, point in enumerate(self.grid_label_gdf.geometry):
                if idx % 100 == 0:  # 每处理100个点输出一次进度
                    print(f"已处理 {idx}/{total_points} 个点...")
                # 获取可能与点相交的障碍物
                possible_obstacles = list(obstacles_idx.intersection(point.bounds))
                if any(self.obstacles_gdf.iloc[i].geometry.contains(point) for i in possible_obstacles):
                    points_in_obstacles.append(idx)
                    print(f"找到一个落在障碍物内的点: {idx}")

            print(f"搜索完成，共找到 {len(points_in_obstacles)} 个点在障碍物内")

            if not points_in_obstacles:
                QMessageBox.information(self, "结果", "未找到落在障碍物内的点位")
                return

            # 清除之前的所有点位图层
            self.clear_point_layers()

            # 获取所有点的索引集合
            all_points_indices = set(range(len(self.grid_label_gdf)))

            # 获取不需要偏移的点的索引集合（不在障碍物内的点）
            points_not_in_obstacles = all_points_indices - set(points_in_obstacles)

            # 创建一个只包含在障碍物内的点的GeoDataFrame
            highlighted_points = self.grid_label_gdf.iloc[points_in_obstacles].copy()
            # 确保设置了正确的CRS
            highlighted_points.crs = self.grid_label_gdf.crs

            # 高亮显示障碍物内的点（使用红色）
            self.highlight_points(highlighted_points, color='#FF0000')

            # 显示障碍物外的点（蓝色）
            if points_not_in_obstacles:
                normal_points_gdf = self.grid_label_gdf.iloc[list(points_not_in_obstacles)].copy()
                normal_points_gdf.crs = self.grid_label_gdf.crs
                # 使用highlight_points方法统一点的显示方式，使所有点都能手动移动
                self.highlight_points(normal_points_gdf, color='#0000FF')

            # 保存找到的点供后续使用
            self.points_in_obstacles = points_in_obstacles

            QMessageBox.information(self, "结果", f"找到 {len(points_in_obstacles)} 个落在障碍物内的点位，已用红色标记\n障碍物外的点以蓝色标记")

        except Exception as e:
            print(f"搜索障碍物内的点时出错：{str(e)}")  # 添加详细的错误输出
            traceback.print_exc()  # 打印完整的错误堆栈
            QMessageBox.critical(self, "错误", f"搜索障碍物内的点时出错：{str(e)}")

    def execute_auto_offset(self):
        """执行自动偏移"""
        try:
            # 清空PointOffsetManager中的已占用网格点集合
            if hasattr(self.point_offset_manager, 'occupied_grid_points'):
                self.point_offset_manager.occupied_grid_points.clear()
                print("已清空已占用网格点集合")

                # 获取所有不需要偏移的点（在障碍物外的点）
                all_points_indices = set(range(len(self.grid_label_gdf)))
                points_not_in_obstacles = all_points_indices - set(self.points_in_obstacles)

                # 将不需要偏移的点的网格位置标记为已占用
                if points_not_in_obstacles:
                    print(f"初始化已占用网格点集合，标记 {len(points_not_in_obstacles)} 个不需要偏移的点位")
                    # 判断坐标系统类型
                    is_utm = False
                    if hasattr(self.grid_label_gdf, 'crs'):
                        crs_str = str(self.grid_label_gdf.crs).lower()
                        is_utm = 'utm' in crs_str or '+proj=utm' in crs_str

                    for idx in points_not_in_obstacles:
                        point_geom = self.grid_label_gdf.iloc[idx].geometry
                        # 计算并标记网格中心点
                        grid_center = self.point_offset_manager.calculate_grid_center(
                            point_geom.x, point_geom.y, is_utm)
                        self.point_offset_manager.occupied_grid_points.add(grid_center)

                    print(f"已将 {len(self.point_offset_manager.occupied_grid_points)} 个网格点标记为已占用")

            # 检查必要的图层是否已加载
            if not hasattr(self, 'grid_gdf') or self.grid_gdf is None:
                QMessageBox.warning(self, "警告", "请先加载网格图层！")
                return

            if not hasattr(self, 'obstacles_gdf') or self.obstacles_gdf is None:
                QMessageBox.warning(self, "警告", "请先加载障碍物图层！")
                return

            # 检查是否已找到需要偏移的点
            if not hasattr(self, 'points_in_obstacles') or not self.points_in_obstacles:
                QMessageBox.warning(self, "警告", "请先搜索障碍物内的点位！")
                return

            # 打印当前所有图层的坐标系统信息
            print(f"网格图层坐标系统: {self.grid_gdf.crs}")
            print(f"障碍物图层坐标系统: {self.obstacles_gdf.crs}")
            print(f"网格点图层坐标系统: {self.grid_label_gdf.crs}")

            # 检查是否所有图层都处于投影坐标系统(UTM)中
            is_grid_utm = ('utm' in str(self.grid_gdf.crs).lower() or
                           '+proj=utm' in str(self.grid_gdf.crs).lower() or
                           'epsg:20438' in str(self.grid_gdf.crs).lower() or
                           '20438' in str(self.grid_gdf.crs).lower())

            is_obstacles_utm = ('utm' in str(self.obstacles_gdf.crs).lower() or
                                '+proj=utm' in str(self.obstacles_gdf.crs).lower() or
                                'epsg:20438' in str(self.obstacles_gdf.crs).lower() or
                                '20438' in str(self.obstacles_gdf.crs).lower())

            is_grid_label_utm = ('utm' in str(self.grid_label_gdf.crs).lower() or
                                 '+proj=utm' in str(self.grid_label_gdf.crs).lower() or
                                 'epsg:20438' in str(self.grid_label_gdf.crs).lower() or
                                 '20438' in str(self.grid_label_gdf.crs).lower())

            is_all_utm = is_grid_utm and is_obstacles_utm and is_grid_label_utm

            if not is_all_utm:
                # 如果不是UTM坐标系统，提示用户
                result = QMessageBox.question(
                    self, "坐标系统警告",
                    "部分图层不是UTM投影坐标系统。为了准确计算偏移距离，建议在UTM坐标系统中进行计算。\n\n"
                    "是否继续执行自动偏移？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if result == QMessageBox.No:
                    return

            # 创建进度对话框
            progress = QProgressDialog("正在执行自动偏移...", "取消", 0, len(self.points_in_obstacles), self)
            progress.setWindowModality(Qt.WindowModal)

            # 显示正在使用的偏移策略
            strategy = self.point_offset_manager.offset_settings['strategy']
            if strategy == 'auto':
                if self.point_offset_manager.offset_settings['surface_bin_size'] >= 20.0:
                    strategy_desc = "选项1 (面元尺寸≥20m)"
                else:
                    strategy_desc = "选项2 (面元尺寸<20m)"
            else:
                strategy_map = {
                    'option1': "选项1: 震源点在半个面元范围内偏移",
                    'option2': "选项2: 震源点在面元范围内偏移",
                    'option3': "选项3: 沿接收线方向偏移震源点",
                    'option4': "选项4: 在接收线方向上的最大震源点偏移",
                    'option5': "选项5: 补充震源点",
                    'option6': "选项6: 在相邻测线区重新定位震源点",
                    'option7': "选项7: 结合接收线方向移动的相邻测线区重定位",
                    'option8': "选项8: 跳过震源点",
                    'option9': "选项9: 障碍物边界优先偏移"
                }
                strategy_desc = strategy_map.get(strategy, strategy)

            progress.setLabelText(f"正在执行自动偏移...\n使用策略: {strategy_desc}")

            # 导入自动偏移模块
            from Core.autooffset import create_spatial_index

            # 在运算坐标系统(UTM)中创建数据副本
            if not is_all_utm:
                # 确定一个UTM坐标系统
                utm_crs = None
                if is_grid_utm:
                    utm_crs = self.grid_gdf.crs
                elif is_obstacles_utm:
                    utm_crs = self.obstacles_gdf.crs
                elif is_grid_label_utm:
                    utm_crs = self.grid_label_gdf.crs

                # 如果没有找到UTM坐标系统，尝试转换到UTM坐标系统
                if utm_crs is None:
                    # 确定中心点经纬度以选择合适的UTM区带
                    if 'EPSG:4326' in str(self.grid_label_gdf.crs):
                        # 如果是WGS84，直接获取中心点
                        center_point = self.grid_label_gdf.geometry.unary_union.centroid
                        lon, lat = center_point.x, center_point.y
                    else:
                        # 转换到WGS84再获取中心点
                        temp_gdf = self.grid_label_gdf.to_crs('EPSG:4326')
                        center_point = temp_gdf.geometry.unary_union.centroid
                        lon, lat = center_point.x, center_point.y

                    # 根据经度确定UTM区带
                    zone_number = int((lon + 180) / 6) + 1

                    # 构建UTM EPSG代码
                    if lat >= 0:
                        # 北半球UTM
                        utm_epsg = f"EPSG:{32600 + zone_number}"
                    else:
                        # 南半球UTM
                        utm_epsg = f"EPSG:{32700 + zone_number}"

                    utm_crs = utm_epsg
                    print(f"自动选择UTM坐标系统: {utm_crs}")

                print(f"创建UTM坐标系统({utm_crs})副本用于计算...")

                # 转换前记录每个图层的边界
                grid_bounds_before = self.grid_gdf.total_bounds if hasattr(self.grid_gdf, 'total_bounds') else None
                obstacles_bounds_before = self.obstacles_gdf.total_bounds if hasattr(self.obstacles_gdf, 'total_bounds') else None
                grid_label_bounds_before = self.grid_label_gdf.total_bounds if hasattr(self.grid_label_gdf, 'total_bounds') else None

                print(f"转换前 - 网格图层边界: {grid_bounds_before}")
                print(f"转换前 - 障碍物图层边界: {obstacles_bounds_before}")
                print(f"转换前 - 网格点图层边界: {grid_label_bounds_before}")

                # 转换各图层到UTM坐标系统
                try:
                    if self.grid_gdf.crs != utm_crs:
                        print(f"转换网格图层从 {self.grid_gdf.crs} 到 {utm_crs}")
                        calc_grid_gdf = self.grid_gdf.to_crs(utm_crs)
                        print(f"网格图层转换完成，包含 {len(calc_grid_gdf)} 个要素")
                    else:
                        calc_grid_gdf = self.grid_gdf.copy()
                        print(f"网格图层已经是 {utm_crs}，直接复制")

                    if self.obstacles_gdf.crs != utm_crs:
                        print(f"转换障碍物图层从 {self.obstacles_gdf.crs} 到 {utm_crs}")
                        calc_obstacles_gdf = self.obstacles_gdf.to_crs(utm_crs)
                        print(f"障碍物图层转换完成，包含 {len(calc_obstacles_gdf)} 个要素")
                    else:
                        calc_obstacles_gdf = self.obstacles_gdf.copy()
                        print(f"障碍物图层已经是 {utm_crs}，直接复制")

                    if self.grid_label_gdf.crs != utm_crs:
                        print(f"转换网格点图层从 {self.grid_label_gdf.crs} 到 {utm_crs}")
                        calc_grid_label_gdf = self.grid_label_gdf.to_crs(utm_crs)
                        print(f"网格点图层转换完成，包含 {len(calc_grid_label_gdf)} 个要素")
                    else:
                        calc_grid_label_gdf = self.grid_label_gdf.copy()
                        print(f"网格点图层已经是 {utm_crs}，直接复制")
                except Exception as e:
                    print(f"坐标系统转换出错: {str(e)}")
                    traceback.print_exc()

                    # 如果转换失败，使用原始数据
                    calc_grid_gdf = self.grid_gdf.copy()
                    calc_obstacles_gdf = self.obstacles_gdf.copy()
                    calc_grid_label_gdf = self.grid_label_gdf.copy()
                    print("坐标系统转换失败，使用原始数据")

                # 转换后检查各图层的边界，确保转换正确
                grid_bounds_after = calc_grid_gdf.total_bounds if hasattr(calc_grid_gdf, 'total_bounds') else None
                obstacles_bounds_after = calc_obstacles_gdf.total_bounds if hasattr(calc_obstacles_gdf, 'total_bounds') else None
                grid_label_bounds_after = calc_grid_label_gdf.total_bounds if hasattr(calc_grid_label_gdf, 'total_bounds') else None

                print(f"转换后 - 网格图层边界: {grid_bounds_after}")
                print(f"转换后 - 障碍物图层边界: {obstacles_bounds_after}")
                print(f"转换后 - 网格点图层边界: {grid_label_bounds_after}")

                # 检查各图层是否还有有效的几何形状
                print(f"转换后 - 网格图层几何有效性: {all(calc_grid_gdf.geometry.is_valid)}")
                print(f"转换后 - 障碍物图层几何有效性: {all(calc_obstacles_gdf.geometry.is_valid)}")
                print(f"转换后 - 网格点图层几何有效性: {all(calc_grid_label_gdf.geometry.is_valid)}")

                # 如果几何形状无效，尝试修复
                if not all(calc_obstacles_gdf.geometry.is_valid):
                    print("检测到障碍物图层中存在无效几何形状，尝试修复...")
                    from shapely.geometry import mapping, shape
                    calc_obstacles_gdf.geometry = calc_obstacles_gdf.geometry.apply(lambda g: shape(mapping(g)) if not g.is_valid else g)
                    print(f"修复后的几何有效性: {all(calc_obstacles_gdf.geometry.is_valid)}")
            else:
                # 使用原始数据
                calc_grid_gdf = self.grid_gdf.copy()
                calc_obstacles_gdf = self.obstacles_gdf.copy()
                calc_grid_label_gdf = self.grid_label_gdf.copy()
                print("所有图层已经是UTM坐标系统，使用原始数据")

            # 创建障碍物的空间索引
            obstacles_idx = create_spatial_index(calc_obstacles_gdf)

            # 记录成功偏移的点
            successful_offsets = []
            failed_offsets = []
            skipped_offsets = []

            # 创建一个新的GeoDataFrame来存储偏移后的点
            offset_gdf = calc_grid_label_gdf.copy()

            if 'color' not in offset_gdf.columns:
                offset_gdf['color'] = 'blue'  # 默认蓝色（不需要偏移）
            # 添加offset_sta字段，默认为'not_needed'
            if 'offset_sta' not in offset_gdf.columns:
                offset_gdf['offset_sta'] = 'not_needed'

            # 将在障碍物内的点标记为failed（等待处理）
            for idx in self.points_in_obstacles:  # 只处理需要偏移的点
                offset_gdf.loc[idx, 'offset_sta'] = 'failed'
            for point_idx in self.points_in_obstacles:
                offset_gdf.loc[point_idx, 'color'] = 'red'
            # 检查哪些点在障碍物内，将它们标记为in_obstacle
            # for idx in range(len(offset_gdf)):
            #     point = offset_gdf.iloc[idx].geometry
            #     if any(calc_obstacles_gdf.geometry.intersects(point)):
            #         offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'
            # 将所有不需要偏移的点（不在障碍物内的点）的网格位置添加到occupied_grid_points集合中
            # 这样可以避免偏移点与原始点重叠
            grid_size = self.point_offset_manager.offset_settings['surface_bin_size']
            print(f"正在处理网格中已有的原始点，网格大小: {grid_size}")

            # 创建所有点的索引集合，方便快速查找
            all_points_indices = set(range(len(calc_grid_label_gdf)))
            # 计算需要偏移的点的索引集合
            points_to_offset = set(self.points_in_obstacles)
            # 计算不需要偏移的点的索引集合
            points_not_in_obstacles = all_points_indices - points_to_offset

            # 为不在障碍物内的点添加到已占用网格点集合
            for idx in points_not_in_obstacles:
                try:
                    # 获取点的几何对象
                    point_geom = calc_grid_label_gdf.iloc[idx].geometry

                    # 计算点所在的网格中心坐标
                    if hasattr(point_geom, 'x') and hasattr(point_geom, 'y'):
                        # 如果是点类型
                        point_x, point_y = point_geom.x, point_geom.y

                        # 检查坐标系统类型
                        is_utm = False
                        if hasattr(calc_grid_label_gdf, 'crs'):
                            crs_str = str(calc_grid_label_gdf.crs).lower()
                            is_utm = ('utm' in crs_str or
                                      '+proj=utm' in crs_str or
                                      'epsg:20438' in crs_str or
                                      '20438' in crs_str)

                        # 使用统一的方法计算网格中心坐标
                        grid_point_key = self.point_offset_manager.calculate_grid_center(point_x, point_y, is_utm)

                        # 将网格中心点添加到已占用集合
                        self.point_offset_manager.occupied_grid_points.add(grid_point_key)
                except Exception as e:
                    print(f"处理原始点 {idx} 时出错: {str(e)}")
                    traceback.print_exc()

            print(f"已将 {len(self.point_offset_manager.occupied_grid_points)} 个原始点所在的网格标记为已占用")

            # 定义进度回调函数
            def update_progress(message):
                current_text = progress.labelText()
                base_text = current_text.split('\n')[0]  # 保留第一行的基本信息
                progress.setLabelText(f"{base_text}\n{message}\n"
                                   f"已处理: {len(successful_offsets) + len(failed_offsets) + len(skipped_offsets)}/{len(self.points_in_obstacles)}\n"
                                   f"成功: {len(successful_offsets)}\n"
                                   f"失败: {len(failed_offsets)}\n"
                                   f"跳过: {len(skipped_offsets)}")

            # 遍历需要偏移的点
            for i, point_idx in enumerate(self.points_in_obstacles):
                if progress.wasCanceled():
                    break

                progress.setValue(i)

                try:
                    # 获取当前点的几何对象
                    current_point = calc_grid_label_gdf.iloc[point_idx].geometry

                    # 记录原始位置用于调试
                    original_x, original_y = current_point.x, current_point.y

                    # 应用偏移策略
                    new_point = self.point_offset_manager.apply_offset_strategy(
                        current_point,
                        calc_obstacles_gdf,
                        calc_grid_gdf,
                        update_progress
                    )

                    if new_point is None:
                        print(f"点 {point_idx} 无法找到合适的偏移位置")
                        failed_offsets.append(point_idx)
                        continue

                    # 计算偏移距离
                    offset_distance = current_point.distance(new_point)

                    # 检查是否是原始点（未偏移）
                    is_original_point = offset_distance < 0.001  # 考虑浮点精度，如果距离极小则认为是相同点

                    # 检查偏移距离是否超过最大限制
                    if offset_distance > self.point_offset_manager.max_offset_distance:
                        print(f"警告：点 {point_idx} 的偏移距离 {offset_distance:.2f}m 超过最大限制")
                        failed_offsets.append(point_idx)
                        continue

                    # 如果点没有移动且仍在障碍物内，标记为失败
                    if is_original_point and any(calc_obstacles_gdf.geometry.intersects(new_point)):
                        print(f"点 {point_idx} 未移动且仍在障碍物内")
                        failed_offsets.append(point_idx)
                        continue

                    # 检查新位置是否在障碍物内
                    if not any(calc_obstacles_gdf.geometry.intersects(new_point)):
                        # 更新点位置
                        offset_gdf.loc[point_idx, 'geometry'] = new_point
                        offset_gdf.loc[point_idx, 'offset_sta'] = 'auto_offset'
                        # 设置颜色为绿色（自动偏移成功）
                        offset_gdf.loc[point_idx, 'color'] = 'green'
                        successful_offsets.append(point_idx)

                        # 在这里添加计算并更新网格编号的代码
                        # 根据新位置计算网格编号
                        new_x, new_y = new_point.x, new_point.y
                        new_grid_info = self.point_offset_manager.calculate_grid_numbers(new_x, new_y)
                        if new_grid_info:
                            line_number, stake_number = new_grid_info
                            # 更新网格编号
                            offset_gdf.loc[point_idx, 'line_numbe'] = line_number
                            offset_gdf.loc[point_idx, 'stake_numb'] = stake_number
                        #successful_offsets.append(point_idx)
                        print(f"点 {point_idx} 成功偏移到新位置: ({new_point.x}, {new_point.y})")
                        print(f"偏移距离: {offset_distance:.2f}m")
                    else:
                        print(f"点 {point_idx} 的新位置仍在障碍物内或与障碍物相交，标记为失败")
                        print(f"原始位置: ({original_x}, {original_y}), 新位置: ({new_point.x}, {new_point.y})")
                        offset_gdf.loc[point_idx, 'offset_sta'] = 'failed'
                        offset_gdf.loc[point_idx, 'color'] = 'red'
                        failed_offsets.append(point_idx)

                except Exception as e:
                    print(f"处理点 {point_idx} 时出错: {str(e)}")
                    traceback.print_exc()
                    offset_gdf.loc[point_idx, 'offset_sta'] = 'failed'
                    failed_offsets.append(point_idx)

            progress.setValue(len(self.points_in_obstacles))

            # 保存偏移结果
            try:
                # 获取输出目录
                if hasattr(self.grid_label_gdf, 'filename'):
                    output_dir = os.path.dirname(self.grid_label_gdf.filename)
                    base_name = os.path.splitext(os.path.basename(self.grid_label_gdf.filename))[0]
                else:
                    # 如果grid_label_gdf没有filename属性，使用默认输出目录
                    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'output')
                    base_name = 'grid_offset'

                # 确保输出目录存在
                os.makedirs(output_dir, exist_ok=True)

                # 构建输出文件路径
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = os.path.join(output_dir, f"{base_name}_offset_{timestamp}.shp")

                # 将偏移后的点转换回原始坐标系统并保存
                original_crs = self.grid_label_gdf.crs
                current_crs = offset_gdf.crs
                print(f"原始坐标系统: {original_crs}")
                print(f"当前坐标系统: {current_crs}")

                if str(original_crs) != str(current_crs):
                    print("转换回原始坐标系统...")
                    save_gdf = offset_gdf.to_crs(original_crs)
                else:
                    print("保持当前坐标系统...")
                    save_gdf = offset_gdf
                # 保存前检查并合并状态字段
                # field_names = offset_gdf.columns.tolist()
                # status_fields = [f for f in field_names if 'offset_s' in f.lower()]
                # if len(status_fields) > 1:
                #    print(f"发现多个状态字段: {status_fields}")
                #    # 选择一个主要状态字段
                #    main_status_field = 'offset_sta'

                #    # 如果主要字段不存在，创建它
                #    if main_status_field not in offset_gdf.columns:
                #        offset_gdf[main_status_field] = 'not_needed'  # 默认值

                #    # 合并其他状态字段到主字段
                #    for field in status_fields:
                #       if field != main_status_field and field in offset_gdf.columns:
                #           # 合并状态到主要字段
                #           for idx in range(len(offset_gdf)):
                #               try:
                #                   if offset_gdf.loc[idx, field] in ['manual_offset', 'in_obstacle', 'auto_offset', 'failed', 'skipped', 'error']:
                #                        offset_gdf.loc[idx, main_status_field] = offset_gdf.loc[idx, field]
                #               except KeyError:
                #                   continue  # 忽略不存在的索引

                # 保存前检查状态
                # for idx in range(len(offset_gdf)):
                #     current_status = offset_gdf.loc[idx, 'offset_sta']

                #     # 如果是手动偏移或自动偏移成功的点，保持不变
                #     if current_status in ['manual_offset', 'auto_offset']:
                #         continue

                #     # 其他所有状态，根据点是否在障碍物内判断
                #     point = offset_gdf.iloc[idx].geometry
                #     if any(calc_obstacles_gdf.geometry.intersects(point)):
                #         offset_gdf.loc[idx, 'offset_sta'] = 'failed'
                #     else:
                #         offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'
                # 根据颜色设置状态
                # for idx in range(len(offset_gdf)):
                #     color = offset_gdf.loc[idx, 'color'].lower() if isinstance(offset_gdf.loc[idx, 'color'], str) else 'blue'

                #     if color == 'green':
                #         offset_gdf.loc[idx, 'offset_sta'] = 'auto_offset'
                #     elif color == 'yellow':
                #         offset_gdf.loc[idx, 'offset_sta'] = 'manual_offset'
                #     elif color == 'red':
                #         # 检查点是否真的在障碍物内
                #         point = offset_gdf.iloc[idx].geometry
                #         if any(calc_obstacles_gdf.geometry.intersects(point)):
                #             offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'  # 真在障碍物内的设为in_obstacle
                #         else:
                #             offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'  # 不在障碍物内的设为not_needed
                #     else:  # 蓝色或其他颜色
                #         offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'

                # # 对所有点再次检查位置与状态的一致性
                # for idx in range(len(offset_gdf)):
                #     current_status = offset_gdf.loc[idx, 'offset_sta']
                #     point = offset_gdf.iloc[idx].geometry
                #     is_in_obstacle = any(calc_obstacles_gdf.geometry.intersects(point))

                #     # 保留manual_offset状态（手动偏移）
                #     if current_status == 'manual_offset':
                #         continue

                #     # auto_offset的点应该不在障碍物内
                #     if current_status == 'auto_offset' and is_in_obstacle:
                #         print(f"警告：将点{idx}从auto_offset修正为in_obstacle (在障碍物内)")
                #         offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'
                #         continue

                #     # in_obstacle的点应该在障碍物内
                #     if current_status == 'in_obstacle' and not is_in_obstacle:
                #         print(f"警告：将点{idx}从in_obstacle修正为not_needed (不在障碍物内)")
                #         offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'
                #         continue

                #     # failed的点位置检查并修正
                #     if current_status == 'failed':
                #         if is_in_obstacle:
                #             print(f"警告：将点{idx}从failed修正为in_obstacle (在障碍物内)")
                #             offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'
                #         else:
                #             print(f"警告：将点{idx}从failed修正为not_needed (不在障碍物内)")
                #             offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'
                #         continue

                #     # not_needed的点应该不在障碍物内
                #     if current_status == 'not_needed' and is_in_obstacle:
                #         print(f"警告：将点{idx}从not_needed修正为in_obstacle (在障碍物内)")
                #         offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'
                #         continue

                # 直接根据颜色设置状态，不再进行额外检查
                for idx in range(len(offset_gdf)):
                    color = offset_gdf.loc[idx, 'color'].lower() if isinstance(offset_gdf.loc[idx, 'color'], str) else 'blue'

                    if color == 'green' or color=='#00ff00':
                        offset_gdf.loc[idx, 'offset_sta'] = 'auto_offset'
                    elif color == 'yellow' or color=='#ffff00':
                        offset_gdf.loc[idx, 'offset_sta'] = 'manual_offset'
                    elif color == 'red' or color=='#ff0000':
                        offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'
                    else:  # 蓝色或其他颜色
                        offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'
                # 删除color字段
                if 'color' in offset_gdf.columns:
                     offset_gdf = offset_gdf.drop(columns=['color'])
                # 删除offset_sta字段
                # if 'offset_sta' in offset_gdf.columns:
                #      offset_gdf = offset_gdf.drop(columns=['offset_sta'])

                # 强制修正所有状态
                # for idx in range(len(offset_gdf)):
                #     current_status = offset_gdf.loc[idx, 'offset_sta']
                #     point = offset_gdf.iloc[idx].geometry

                    # 保留manual_offset状态（手动偏移）
                    # if current_status == 'manual_offset':
                    #     continue

                    # 保留auto_offset状态（自动偏移成功）
                    # if current_status == 'auto_offset':
                    #     # 额外检查确保auto_offset的点确实不在障碍物内
                    #     if any(calc_obstacles_gdf.geometry.intersects(point)):
                    #         print(f"警告：发现标记为auto_offset但实际在障碍物内的点，索引={idx}")
                    #         offset_gdf.loc[idx, 'offset_sta'] = 'in_obstacle'
                    #     continue

                    # 对于in_obstacle状态，统一改为failed
                    # if current_status == 'in_obstacle':
                    #     offset_gdf.loc[idx, 'offset_sta'] = 'failed'
                    #     continue

                    # 对于failed状态，检查点是否确实在障碍物内
                    # if current_status == 'failed':
                    #     if not any(calc_obstacles_gdf.geometry.intersects(point)):
                    #         print(f"警告：发现标记为failed但实际不在障碍物内的点，索引={idx}")
                    #         offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'
                    #     continue

                    # 对于not_needed状态，检查点是否确实不在障碍物内
                    # if current_status == 'not_needed':
                    #     if any(calc_obstacles_gdf.geometry.intersects(point)):
                    #         print(f"警告：发现标记为not_needed但实际在障碍物内的点，索引={idx}")
                    #         offset_gdf.loc[idx, 'offset_sta'] = 'failed'
                    #     continue

                    # 对于其他任何状态，根据点是否在障碍物内判断
                    # if any(calc_obstacles_gdf.geometry.intersects(point)):
                    #     offset_gdf.loc[idx, 'offset_sta'] = 'failed'
                    # else:
                    #     offset_gdf.loc[idx, 'offset_sta'] = 'not_needed'
                # 删除其他可能存在的状态字段
                # field_names = offset_gdf.columns.tolist()
                # status_fields = [f for f in field_names if 'offset_s' in f.lower() and f != 'offset_sta']
                # if status_fields:
                #     offset_gdf = offset_gdf.drop(columns=status_fields)
                #     print(f"已删除多余状态字段: {status_fields}")

                # 删除其他状态字段
                # for field in status_fields:
                #     if field != main_status_field and field in offset_gdf.columns:
                #         offset_gdf = offset_gdf.drop(columns=[field])
                #         print(f"已删除多余状态字段 {field}，保留 {main_status_field}")



                # 直接保存偏移后的点位（保持原始坐标系统）
                save_gdf = offset_gdf
                save_gdf.to_file(output_path)
                print(f"已保存偏移结果到: {output_path}")
                print(f"偏移结果坐标系统: {save_gdf.crs}")

                # 保存偏移结果文件路径，以便后续手动偏移使用
                self.point_offset_manager.last_offset_shapefile = output_path

                # 更新主窗口中的数据 - 转换为WGS84用于显示
                map_crs = 'EPSG:4326'

                if str(save_gdf.crs) != map_crs:
                    display_gdf = save_gdf.to_crs(map_crs)
                else:
                    display_gdf = save_gdf

                # 更新数据
                self.grid_label_gdf = save_gdf.copy()

                # 清除地图上的所有点位图层
                js_clear_code = """
                    // 清除所有点位图层
                    if (typeof pointLayerGroup !== 'undefined') {
                        pointLayerGroup.clearLayers();
                        map.removeLayer(pointLayerGroup);
                    }
                    if (typeof highlightLayer !== 'undefined') {
                        map.removeLayer(highlightLayer);
                    }
                    if (typeof gridCenterLayer !== 'undefined') {
                        map.removeLayer(gridCenterLayer);
                    }
                """
                self.web_view.page().runJavaScript(js_clear_code)

                # 重新显示所有点位到地图上 - 使用WGS84坐标
                points = np.array([[p.x, p.y] for p in display_gdf.geometry])
                self.add_grid_points_to_map(points, "偏移后的网格点")

                # 等待一小段时间确保点位加载完成
                time.sleep(0.5)

                # 对成功偏移、失败和跳过的点进行相应的WGS84格式转换
                # 高亮显示不同状态的点位
                if successful_offsets:
                    successful_points = display_gdf.iloc[successful_offsets].copy()
                    self.highlight_points(successful_points, color='#00FF00')  # 使用绿色标记成功偏移的点

                if failed_offsets:
                    failed_points = display_gdf.iloc[failed_offsets].copy()
                    self.highlight_points(failed_points, color='#FF0000')  # 使用红色标记失败的点

                if skipped_offsets:
                    skipped_points = display_gdf.iloc[skipped_offsets].copy()
                    self.highlight_points(skipped_points, color='#FFA500')  # 使用橙色标记跳过的点

                # 调整地图视图以显示所有点位
                js_fit_bounds = """
                    if (typeof pointLayerGroup !== 'undefined' && pointLayerGroup.getLayers().length > 0) {
                        var bounds = L.latLngBounds([]);
                        pointLayerGroup.eachLayer(function(layer) {
                            bounds.extend(layer.getLatLng());
                        });
                        map.fitBounds(bounds);
                    }
                """
                self.web_view.page().runJavaScript(js_fit_bounds)

            except Exception as e:
                print(f"保存偏移结果时出错：{str(e)}")
                traceback.print_exc()
                QMessageBox.critical(self, "错误", f"保存偏移结果时出错：{str(e)}")
                return

            # 保存结果后，清除所有原有图层
            self.clear_point_layers()

            # 在地图上显示所有点位，包括偏移后的点和原始的不需要偏移的点
            try:
                # 确保所有点的坐标系统为WGS84
                if str(save_gdf.crs) != 'EPSG:4326':
                    display_gdf = save_gdf.to_crs('EPSG:4326')
                else:
                    display_gdf = save_gdf

                # 获取所有点的索引集合
                all_points_indices = set(range(len(self.grid_label_gdf)))

                # 获取不需要偏移的点的索引集合
                points_not_in_obstacles = all_points_indices - set(self.points_in_obstacles)

                # 获取不需要偏移的原始点
                if points_not_in_obstacles:
                    # 创建只包含不需要偏移点的GeoDataFrame
                    normal_points_gdf = self.grid_label_gdf.iloc[list(points_not_in_obstacles)].copy()

                    # 转换为WGS84坐标系
                    if str(normal_points_gdf.crs) != 'EPSG:4326':
                        normal_points_gdf = normal_points_gdf.to_crs('EPSG:4326')

                    # 使用highlight_points方法添加蓝色点，以便能够手动移动
                    self.highlight_points(normal_points_gdf, color='#0000FF')

                # 显示结果消息
                message = (f"自动偏移完成：\n"
                          f"成功：{len(successful_offsets)} 个点（绿色标记）\n"
                          f"失败：{len(failed_offsets)} 个点（红色标记）\n"
                          f"跳过：{len(skipped_offsets)} 个点（橙色标记）")

                if failed_offsets or skipped_offsets:
                    message += "\n\n部分点位无法找到合适的偏移位置，建议：\n"
                    message += "1. 尝试调整偏移策略\n"
                    message += "2. 增加最大偏移距离\n"
                    message += "3. 对于无法自动处理的点，使用手动偏移"

                QMessageBox.information(self, "完成", message)

                # 高亮显示不同状态的点位
                if successful_offsets:
                    successful_points = display_gdf.iloc[successful_offsets].copy()
                    self.highlight_points(successful_points, color='#00FF00')  # 使用绿色标记成功偏移的点

                if failed_offsets:
                    failed_points = display_gdf.iloc[failed_offsets].copy()
                    self.highlight_points(failed_points, color='#FF0000')  # 使用红色标记失败的点

                if skipped_offsets:
                    skipped_points = display_gdf.iloc[skipped_offsets].copy()
                    self.highlight_points(skipped_points, color='#FFA500')  # 使用橙色标记跳过的点

            except Exception as e:
                print(f"显示偏移结果时出错：{str(e)}")
                traceback.print_exc()
                QMessageBox.critical(self, "错误", f"显示偏移结果时出错：{str(e)}")

        except Exception as e:
            print(f"执行自动偏移时出错：{str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"执行自动偏移时出错：{str(e)}")

    def clear_point_layers(self):
        """清除地图上的点图层"""
        try:
            # 清除原始的蓝色点图层和所有高亮图层
            js_code = """
                // 清除原始的蓝色点图层
                if (typeof pointLayerGroup !== 'undefined') {
                    map.removeLayer(pointLayerGroup);
                    pointLayerGroup = undefined;
                }

                // 清空全局circleElements数组
                window.circleElements = [];

                // 清除所有以"highlight_layer_"开头的图层
                for (var key in window) {
                    if (key.startsWith('highlight_layer_')) {
                        if (window[key]) {
                            map.removeLayer(window[key]);
                            window[key] = undefined;
                        }
                    }
                }

                console.log('已清除所有点位图层');
            """
            self.web_view.page().runJavaScript(js_code)
            print("已清除所有点位图层")
        except Exception as e:
            print(f"清除点位图层时出错：{str(e)}")
            traceback.print_exc()

    def highlight_points(self, points, color='red'):
        """在地图上高亮显示点位，并支持手动偏移"""
        try:
            if points is None or (isinstance(points, (list, tuple)) and len(points) == 0):
                return

            # 计算圆形半径（米），使用与add_grid_points_to_map相同的方法
            radius = self.point_offset_manager.offset_settings['surface_bin_size'] / 4

            # 准备点位数据
            point_coords = []

            # 处理不同格式的输入
            if isinstance(points, list) and len(points) > 0 and isinstance(points[0], tuple):
                # 如果输入是(point_id, pos)格式的列表
                point_ids = [point_id for point_id, _ in points]
                point_coords = [[pos[0], pos[1]] for _, pos in points]  # [[lng, lat], ...]
            else:
                # GeoDataFrame处理逻辑
                if hasattr(points, 'crs'):
                    if str(points.crs) != 'EPSG:4326':
                        display_points = points.to_crs('EPSG:4326')
                    else:
                        display_points = points

                    # 获取点坐标和索引
                    point_ids = display_points.index.tolist()
                    point_coords = [[p.x, p.y] for p in display_points.geometry]
                else:
                    raise ValueError("输入的points既不是列表也不是GeoDataFrame")

            # 为每种颜色创建单独的图层ID
            layer_id = f"highlight_layer_{color.replace('#', '')}"

            # 使用JavaScript在地图上高亮显示这些点，并支持拖动
            js_code = f"""
                // 如果存在同色图层，先移除
                if (window['{layer_id}']) {{
                    // 从circleElements中移除该图层的点
                    if (window['{layer_id}'].circles && window['{layer_id}'].circles.length > 0) {{
                        for (var i = 0; i < window['{layer_id}'].circles.length; i++) {{
                            var idx = window.circleElements.indexOf(window['{layer_id}'].circles[i]);
                            if (idx > -1) {{
                                window.circleElements.splice(idx, 1);
                            }}
                        }}
                    }}
                    map.removeLayer(window['{layer_id}']);
                }}

                // 初始化circleElements数组（如果不存在）
                if (!window.circleElements) {{
                    window.circleElements = [];
                }}

                // 创建新的高亮图层
                window['{layer_id}'] = L.layerGroup();
                window['{layer_id}'].circles = [];  // 存储该图层中的圆点引用

                var points = {point_coords};
                var pointIds = {point_ids};

                // 为每个点创建圆并添加到图层
                for (var i = 0; i < points.length; i++) {{
                    var point = points[i];
                    var circle = L.circle([point[1], point[0]], {{
                        color: '#000',
                        fillColor: '{color}',
                        fillOpacity: 0.8,
                        weight: 1,
                        radius: {radius}
                    }});

                    // 存储点的原始ID（用于回传给Python）
                    circle.pointIndex = pointIds[i];

                    // 存储点的原始颜色（用于在手动偏移时保持颜色）
                    circle.originalColor = '{color}';

                    // 添加到图层
                    window['{layer_id}'].addLayer(circle);

                    // 存储到图层的circles数组
                    window['{layer_id}'].circles.push(circle);

                    // 也添加到全局的circleElements数组中（用于拖动功能）
                    window.circleElements.push(circle);
                }}

                // 添加图层到地图
                window['{layer_id}'].addTo(map);
                console.log('添加了 ' + points.length + ' 个高亮点到地图：' + '{layer_id}');
            """

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"高亮显示点位时出错：{str(e)}")
            traceback.print_exc()

    def on_item_expanded(self, item):
        """当树项被展开时加载子目录"""
        try:
            # 检查是否有子项
            path = item.data(0, Qt.UserRole)
            if not path or not os.path.exists(path):
                return

            # 获取目录下的所有项
            entries = list(os.scandir(path))

            # 清除现有的子项
            item.takeChildren()

            # 创建统一的字体
            from PyQt5.QtGui import QFont
            item_font = QFont("Microsoft YaHei UI", 9)
            item_font.setBold(False)

            # 先添加目录
            for entry in sorted(entries, key=lambda x: x.name.lower()):
                try:
                    if entry.is_dir():
                        child = QTreeWidgetItem(item)
                        child.setText(0, entry.name)
                        child.setData(0, Qt.UserRole, entry.path)
                        child.setFont(0, item_font)  # 设置统一字体
                        icon = self.style().standardIcon(QStyle.SP_DirIcon)
                        child.setIcon(0, icon)

                        # 添加一个临时子项，以显示展开箭头
                        QTreeWidgetItem(child)
                except Exception as e:
                    print(f"处理目录 {entry.name} 时出错: {str(e)}")
                    continue

            # 再添加文件
            for entry in sorted(entries, key=lambda x: x.name.lower()):
                try:
                    if entry.is_file():
                        # 获取文件扩展名
                        _, ext = os.path.splitext(entry.name)
                        ext = ext.lower()

                        # 根据文件类型设置不同的图标
                        if ext in ['.shp', '.geojson', '.kml', '.gpkg']:
                            icon = self.style().standardIcon(QStyle.SP_FileIcon)
                        elif ext in ['.tif', '.jpg', '.jpeg', '.png']:
                            icon = self.style().standardIcon(QStyle.SP_FileIcon)  # 可以为图像文件设置特殊图标
                        else:
                            # 跳过不支持的文件类型
                            continue

                        child = QTreeWidgetItem(item)
                        child.setText(0, entry.name)
                        child.setData(0, Qt.UserRole, entry.path)
                        child.setIcon(0, icon)

                except Exception as e:
                    print(f"处理文件 {entry.name} 时出错: {str(e)}")
                    continue

        except Exception as e:
            print(f"展开目录时出错: {str(e)}")
            # 添加错误提示项
            error_item = QTreeWidgetItem(item)
            error_item.setText(0, f"加载出错: {str(e)}")
            error_item.setForeground(0, Qt.red)

    def expand_to_path(self, path):
        """展开文件树到指定路径"""
        if not path or not os.path.exists(path):
            return

        parts = os.path.normpath(path).split(os.sep)
        current_item = None

        # 找到对应的驱动器项
        drive = parts[0] + os.sep if len(parts) > 0 else ""

        for i in range(self.browser_tree.topLevelItemCount()):
            item = self.browser_tree.topLevelItem(i)
            item_text = item.text(0)
            # 使用大小写不敏感的比较
            if item_text.upper() == drive.upper() or item_text.upper().startswith(drive.upper()):
                current_item = item
                break

        if not current_item:
            return

        # 展开每一级目录
        current_path = drive  # 保持驱动器路径完整，包含分隔符
        for part_index, part in enumerate(parts[1:], 1):
            if not part:  # 跳过空部分
                continue

            current_path = os.path.join(current_path, part)

            if not os.path.exists(current_path):
                break

            # 展开当前项
            self.browser_tree.expandItem(current_item)

            # 等待子项加载完成
            QApplication.processEvents()

            # 查找下一级目录
            found = False
            child_count = current_item.childCount()

            for i in range(child_count):
                child = current_item.child(i)
                child_text = child.text(0)
                if child_text == part:
                    current_item = child
                    found = True
                    break

            if not found:
                break

        # 展开并选中最后一项
        if current_item:
            self.browser_tree.expandItem(current_item)
            self.browser_tree.setCurrentItem(current_item)
            self.browser_tree.scrollToItem(current_item)


    def get_hf_model_cache_dir(self):
        """获取HuggingFace模型缓存目录"""
        cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trained_models', 'Model', 'huggingface_cache')
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        # 设置环境变量，强制 HuggingFace 使用我们的缓存目录
        os.environ['TRANSFORMERS_CACHE'] = cache_dir
        os.environ['HF_HOME'] = cache_dir
        return cache_dir

    def get_tile_url_with_fallback(self, x, y, zoom_level, primary_source="google"):
        """
        获取瓦片URL，支持主备源切换

        Args:
            x, y: 瓦片坐标
            zoom_level: 缩放级别
            primary_source: 主要图片源 ("google" 或 "arcgis")

        Returns:
            list: [(url, source_name), ...] 按优先级排序的URL列表
        """
        sources = {
            "google": f"https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={zoom_level}",
            "arcgis": f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom_level}/{y}/{x}"
        }

        # 返回主要源和备用源
        if primary_source == "google":
            return [(sources["google"], "Google"), (sources["arcgis"], "ArcGIS")]
        else:
            return [(sources["arcgis"], "ArcGIS"), (sources["google"], "Google")]

    def download_tile_with_fallback(self, x, y, zoom_level, session=None, primary_source="google"):
        """
        下载瓦片，支持主备源自动切换

        Args:
            x, y: 瓦片坐标
            zoom_level: 缩放级别
            session: requests会话
            primary_source: 主要图片源

        Returns:
            PIL.Image or None: 下载的图像
        """
        if session is None:
            session = requests.Session()

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # 获取主备源URL
        url_sources = self.get_tile_url_with_fallback(x, y, zoom_level, primary_source)

        for url, source_name in url_sources:
            try:
                response = session.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                # 成功下载
                image = Image.open(BytesIO(response.content))
                # 记录成功的下载源（用于统计）
                if not hasattr(self, '_download_source_stats'):
                    self._download_source_stats = {}
                if source_name not in self._download_source_stats:
                    self._download_source_stats[source_name] = 0
                self._download_source_stats[source_name] += 1

                # 只在调试模式下显示详细信息，否则通过进度条显示
                if hasattr(self, '_debug_tile_download') and self._debug_tile_download:
                    print(f"✅ 使用{source_name}源下载瓦片 {x}/{y}/{zoom_level}")
                return image

            except Exception as e:
                print(f"⚠️ {source_name}源下载失败: {e}")
                continue

        print(f"❌ 所有源都无法下载瓦片 {x}/{y}/{zoom_level}")
        return None

    def download_satellite_image_for_inference(self, points, save_dir=None):
        """下载用于推理的卫星图像

        Args:
            points: 选择的区域坐标点
            save_dir: 保存图像的目录，如果为None则使用默认目录

        Returns:
            str: 下载的图像路径
        """
        print(f"开始下载卫星图像，保存目录: {save_dir}")

        if save_dir is None:
            save_dir = os.path.join(self.project_config['model_path'], 'Model', 'inference_temp')
            print(f"使用默认保存目录: {save_dir}")

        if not os.path.exists(save_dir):
            print(f"创建保存目录: {save_dir}")
            os.makedirs(save_dir)

        # 获取选定区域的边界框
        lats = [point['lat'] for point in points]
        lngs = [point['lng'] for point in points]
        min_lat, max_lat = min(lats), max(lats)
        min_lng, max_lng = min(lngs), max(lngs)
        print(f"图像边界框: lat[{min_lat}, {max_lat}], lng[{min_lng}, {max_lng}]")

        # 设置缩放级别
        zoom_level = 18  # 高分辨率

        try:
            # 注意：mercantile.tile 函数参数顺序是 (lng, lat, zoom)
            min_tile = mercantile.tile(min_lng, min_lat, zoom_level)
            max_tile = mercantile.tile(max_lng, max_lat, zoom_level)
            print(f"瓦片范围: min_tile={min_tile}, max_tile={max_tile}")

            # 确保正确的顺序
            x_start = min(min_tile.x, max_tile.x)
            x_end = max(min_tile.x, max_tile.x)
            y_start = min(min_tile.y, max_tile.y)
            y_end = max(min_tile.y, max_tile.y)

            # 计算总瓦片数和图像大小
            tile_width = x_end - x_start + 1
            tile_height = y_end - y_start + 1
            total_width = tile_width * 256  # 每个瓦片是256x256像素
            total_height = tile_height * 256
            print(f"最终图像大小: {total_width}x{total_height} 像素")

            # 导入 BytesIO
            from io import BytesIO

            # 创建大图
            merged_image = Image.new('RGB', (total_width, total_height))
            print("创建了空白图像")

            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.arcgis.com/'
            }

            # 创建进度对话框
            total_tiles = tile_width * tile_height
            progress = QProgressDialog("正在下载卫星图像...", "取消", 0, total_tiles, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)  # 立即显示进度条

            print(f"📡 开始下载推理用卫星图像瓦片...")
            print(f"📊 瓦片范围: {tile_width}×{tile_height} = {total_tiles}个瓦片")
            print(f"🗺️ 缩放级别: {zoom_level}")

            # 下载和拼接瓦片
            session = requests.Session()
            processed_tiles = 0
            successful_downloads = 0
            failed_downloads = 0

            for y in range(y_start, y_end + 1):
                for x in range(x_start, x_end + 1):
                    if progress.wasCanceled():
                        return

                    try:
                        # 使用智能双源切换下载瓦片
                        tile_img = self.download_tile_with_fallback(x, y, zoom_level, session, "google")
                        if tile_img is None:
                            failed_downloads += 1
                            continue

                        # 计算瓦片在大图中的位置
                        paste_x = (x - x_start) * 256
                        paste_y = (y - y_start) * 256

                        # 将瓦片粘贴到大图上
                        merged_image.paste(tile_img, (paste_x, paste_y))
                        successful_downloads += 1
                        processed_tiles += 1
                        progress.setValue(processed_tiles)
                        progress.setLabelText(f"正在下载卫星图像... ({processed_tiles}/{total_tiles})")

                        # 每下载50个瓦片显示一次进度信息
                        if processed_tiles % 50 == 0 or processed_tiles == total_tiles:
                            percent = (processed_tiles / total_tiles) * 100
                            print(f"📊 下载进度: {processed_tiles}/{total_tiles} ({percent:.1f}%) - 成功:{successful_downloads}, 失败:{failed_downloads}")

                    except Exception as e:
                        failed_downloads += 1
                        processed_tiles += 1
                        progress.setValue(processed_tiles)
                        continue

            # 显示下载统计信息
            print(f"📊 瓦片下载完成: 成功 {successful_downloads} 个, 失败 {failed_downloads} 个")
            if hasattr(self, '_download_source_stats') and self._download_source_stats:
                print("📡 下载源统计:")
                for source, count in self._download_source_stats.items():
                    percent = (count / successful_downloads) * 100 if successful_downloads > 0 else 0
                    print(f"   - {source}: {count} 个瓦片 ({percent:.1f}%)")
                # 清空统计信息
                self._download_source_stats = {}

            # 将PIL图像转换为numpy数组，并进行数值稳定性处理
            print("开始图像数值稳定性处理...")
            image_array = np.array(merged_image, dtype=np.float32)

            # 数值稳定性处理
            print(f"原始图像范围: [{image_array.min():.1f}, {image_array.max():.1f}]")

            # 1. 检测和修复瓦片边界问题
            def smooth_tile_boundaries(img_array, tile_size=256):
                """平滑瓦片边界以减少数值不连续性"""
                from scipy import ndimage
                h, w, c = img_array.shape

                # 对每个通道分别处理
                for channel in range(c):
                    channel_data = img_array[:, :, channel]

                    # 检测垂直边界
                    for x in range(tile_size, w, tile_size):
                        if x < w - 1:
                            # 在边界附近应用轻微的高斯平滑
                            left_region = channel_data[:, max(0, x-2):x+1]
                            right_region = channel_data[:, x:min(w, x+3)]

                            # 计算边界差异
                            if left_region.size > 0 and right_region.size > 0:
                                left_mean = left_region.mean()
                                right_mean = right_region.mean()
                                diff = abs(left_mean - right_mean)

                                if diff > 5:  # 如果差异较大，应用平滑
                                    boundary_region = channel_data[:, max(0, x-1):min(w, x+2)]
                                    smoothed = ndimage.gaussian_filter(boundary_region, sigma=0.5)
                                    channel_data[:, max(0, x-1):min(w, x+2)] = smoothed

                    # 检测水平边界
                    for y in range(tile_size, h, tile_size):
                        if y < h - 1:
                            # 在边界附近应用轻微的高斯平滑
                            top_region = channel_data[max(0, y-2):y+1, :]
                            bottom_region = channel_data[y:min(h, y+3), :]

                            # 计算边界差异
                            if top_region.size > 0 and bottom_region.size > 0:
                                top_mean = top_region.mean()
                                bottom_mean = bottom_region.mean()
                                diff = abs(top_mean - bottom_mean)

                                if diff > 5:  # 如果差异较大，应用平滑
                                    boundary_region = channel_data[max(0, y-1):min(h, y+2), :]
                                    smoothed = ndimage.gaussian_filter(boundary_region, sigma=0.5)
                                    channel_data[max(0, y-1):min(h, y+2), :] = smoothed

                    img_array[:, :, channel] = channel_data

                return img_array

            # 应用瓦片边界平滑
            image_array = smooth_tile_boundaries(image_array)

            # 2. 数值范围标准化
            image_array = np.clip(image_array, 0, 255)

            # 3. 转换回uint8，确保数值稳定性
            image_array = image_array.astype(np.uint8)

            print(f"处理后图像范围: [{image_array.min()}, {image_array.max()}]")
            print("图像数值稳定性处理完成")

            # 计算地理变换参数
            # 获取左上角瓦片的地理坐标
            ul_bounds = mercantile.bounds(x_start, y_start, zoom_level)
            # 获取右下角瓦片的地理坐标
            lr_bounds = mercantile.bounds(x_end, y_end, zoom_level)

            # 计算地理分辨率
            x_res = (lr_bounds.east - ul_bounds.west) / total_width
            y_res = (ul_bounds.north - lr_bounds.south) / total_height

            # 创建地理变换参数
            geotransform = (
                ul_bounds.west,    # 左上角x坐标
                x_res,             # 水平分辨率
                0,                 # 旋转, 0表示图像"北方朝上"
                ul_bounds.north,   # 左上角y坐标
                0,                 # 旋转, 0表示图像"北方朝上"
                -y_res            # 垂直分辨率（负值因为y轴向下）
            )

            # 保存为GeoTIFF
            image_path = os.path.join(save_dir, "inference_image.tif")

            # 创建GDAL数据集
            driver = gdal.GetDriverByName('GTiff')
            dataset = driver.Create(
                image_path,
                total_width,
                total_height,
                3,  # 3个波段（RGB）
                gdal.GDT_Byte,  # 数据类型
                ['COMPRESS=LZW']  # 使用LZW压缩
            )

            # 设置地理变换参数
            dataset.SetGeoTransform(geotransform)

            # 设置投影（WGS84）
            srs = osr.SpatialReference()
            srs.ImportFromEPSG(4326)  # WGS84
            dataset.SetProjection(srs.ExportToWkt())

            # 写入图像数据
            for i in range(3):  # 对RGB三个波段
                band = dataset.GetRasterBand(i + 1)
                band.WriteArray(image_array[:, :, i])

            # 生成图像金字塔（.ovr文件）
            overview_list = [2, 4, 8, 16]  # 金字塔层级
            dataset.BuildOverviews("NEAREST", overview_list)

            # 创建XML元数据文件
            xml_path = os.path.join(save_dir, "inference_image.xml")
            with open(xml_path, 'w', encoding='utf-8') as xml_file:
                xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<metadata>
    <image>
        <name>inference_image.tif</name>
        <width>{total_width}</width>
        <height>{total_height}</height>
        <bands>3</bands>
        <pixel_type>Byte</pixel_type>
        <compression>LZW</compression>
    </image>
    <spatial_reference>
        <projection>WGS84</projection>
        <epsg_code>4326</epsg_code>
    </spatial_reference>
    <extent>
        <west>{ul_bounds.west}</west>
        <east>{lr_bounds.east}</east>
        <north>{ul_bounds.north}</north>
        <south>{lr_bounds.south}</south>
    </extent>
    <resolution>
        <x_res>{x_res}</x_res>
        <y_res>{y_res}</y_res>
    </resolution>
    <creation_time>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</creation_time>
</metadata>"""
                xml_file.write(xml_content)

            # 关闭数据集
            dataset = None

            progress.close()
            print(f"图像保存成功，文件大小: {os.path.getsize(image_path)} 字节")

            return image_path

        except Exception as e:
            progress.close()
            print(f"下载卫星图像失败: {str(e)}")
            raise

    def process_local_hf_inference(self, points):
        """处理本地HuggingFace模型推理"""
        from GUI.InferenceProgressDialog import InferenceProgressDialog
        from transformers import pipeline, SegformerForSemanticSegmentation, SegformerImageProcessor
        import torch
        from PIL import Image

        # 创建进度对话框
        progress_dialog = InferenceProgressDialog(self)
        progress_dialog.show()

        try:
            # 检测可用设备并显示信息
            if torch.cuda.is_available():
                device = 0  # 使用GPU
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                device_info = f"GPU: {gpu_name} ({gpu_memory:.2f} GB)"
                print(f"检测到GPU，将使用GPU进行推理: {device_info}")
                progress_dialog.log_message(f"使用GPU进行推理: {device_info}")
            else:
                device = -1  # 使用CPU
                import platform
                import multiprocessing
                cpu_info = platform.processor()
                cpu_cores = multiprocessing.cpu_count()
                device_info = f"CPU: {cpu_info} ({cpu_cores}核)"
                print(f"未检测到可用的GPU，将使用CPU进行推理: {device_info}")
                progress_dialog.log_message(f"使用CPU进行推理: {device_info}")
                progress_dialog.log_message("注意: 使用CPU进行推理可能会较慢，请耐心等待")

            # 1. 准备工作目录
            model_base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trained_models')
            inference_dir = os.path.join(model_base_dir, 'Model', 'inference_temp')

            # 添加调试日志
            print(f"创建目录: {inference_dir}")
            os.makedirs(inference_dir, exist_ok=True)
            print(f"目录创建状态: {os.path.exists(inference_dir)}")

            # 2. 检查模型缓存
            progress_dialog.progress_updated.emit("正在检查模型缓存...", 10)
            cache_dir = self.get_hf_model_cache_dir()

            # 3. 下载卫星图像
            progress_dialog.progress_updated.emit("正在下载卫星图像...", 20)
            print("开始下载卫星图像...")
            image_path = self.download_satellite_image_for_inference(points, inference_dir)
            print(f"卫星图像保存路径: {image_path}")

            if not os.path.exists(image_path):
                print(f"错误：图像文件不存在: {image_path}")
                raise Exception("卫星图像下载失败，请检查网络连接")
            else:
                print(f"图像文件大小: {os.path.getsize(image_path)} 字节")

            # 4. 加载模型和处理推理
            model_name = self.current_model_path
            progress_dialog.progress_updated.emit(f"正在加载模型 {model_name}...", 40)

            # 设置推理显存优化选项(对于GPU推理)
            if device == 0:
                # 尝试启用混合精度推理以减少GPU内存使用
                torch.backends.cudnn.benchmark = True
                if hasattr(torch.cuda, 'amp') and hasattr(torch.cuda.amp, 'autocast'):
                    use_amp = True
                    print("已启用自动混合精度(AMP)推理以优化性能")
                else:
                    use_amp = False
                    print("当前PyTorch版本不支持自动混合精度推理")
            else:
                use_amp = False
                # 对于CPU，设置线程数优化
                torch.set_num_threads(multiprocessing.cpu_count())
                print(f"已设置PyTorch使用 {multiprocessing.cpu_count()} 个CPU线程进行推理")

            # 处理Segformer模型
            if "segformer" in model_name.lower():
                try:
                    print(f"加载SegFormer模型: {model_name}")
                    processor = SegformerImageProcessor.from_pretrained(model_name, cache_dir=cache_dir)
                    model = SegformerForSemanticSegmentation.from_pretrained(model_name, cache_dir=cache_dir)

                    # 将模型移至适当的设备
                    model.to(f"cuda:{device}" if device >= 0 else "cpu")

                    # 加载和预处理图像
                    image = Image.open(image_path).convert("RGB")
                    inputs = processor(images=image, return_tensors="pt").to(f"cuda:{device}" if device >= 0 else "cpu")

                    # 使用适当的方式运行推理
                    progress_dialog.progress_updated.emit("正在进行推理...", 60)

                    if use_amp and device >= 0:
                        with torch.cuda.amp.autocast():
                            with torch.no_grad():
                                outputs = model(**inputs)
                    else:
                        with torch.no_grad():
                            outputs = model(**inputs)

                    logits = outputs.logits

                    # 获取掩码并转换为分割结果
                    upsampled_logits = torch.nn.functional.interpolate(
                        logits,
                        size=image.size[::-1],
                        mode="bilinear",
                        align_corners=False
                    )

                    # 获取掩码标签
                    mask = upsampled_logits.argmax(dim=1)[0]
                    mask_cpu = mask.cpu().numpy()

                    # 创建结果字典
                    results = {
                        'original_image': np.array(image),
                        'masks': [mask_cpu],
                        'classes': list(model.config.id2label.values()),
                        'scores': [1.0]  # 简化处理
                    }

                except Exception as segformer_error:
                    print(f"SegFormer模型处理失败: {str(segformer_error)}")
                    progress_dialog.log_message(f"模型处理失败: {str(segformer_error)}")
                    QMessageBox.warning(self, "错误", f"推理过程中出错：{str(segformer_error)}")
                    progress_dialog.reject()
                    return

            else:
                try:
                    print(f"使用标准 pipeline 处理方式: {model_name}")
                    progress_dialog.log_message(f"正在加载模型: {model_name}")

                    # 检查transformers是否可用
                    if not HAS_TRANSFORMERS:
                        progress_dialog.log_message("错误: transformers库未正确安装")
                        QMessageBox.critical(self, "错误", "transformers库未正确安装或导入失败。\n\n请运行以下命令修复：\npip install protobuf==3.19.6")
                        progress_dialog.reject()
                        return

                    # 其他模型使用 pipeline
                    model = pipeline("image-segmentation",
                                   model=model_name,
                                   cache_dir=cache_dir,
                                   device=device,
                                   trust_remote_code=True,
                                   token="*************************************")

                    # 运行推理
                    progress_dialog.progress_updated.emit("正在进行推理...", 60)
                    progress_dialog.log_message("开始执行模型推理，请耐心等待...")

                    results = model(image_path)
                    print("获取到的类别:", model.model.config.id2label)

                except Exception as model_error:
                    print(f"Pipeline 处理失败: {str(model_error)}")
                    progress_dialog.log_message(f"推理失败: {str(model_error)}")
                    QMessageBox.warning(self, "错误", f"推理过程中出错：{str(model_error)}")
                    progress_dialog.reject()
                    return

            # 5. 处理结果
            progress_dialog.progress_updated.emit("正在处理结果...", 80)
            self.show_segmentation_results(results)

            # 6. 完成
            progress_dialog.progress_updated.emit("推理完成！", 100)
            progress_dialog.accept()

            # 7. 清理临时文件，但保留结果
            self.cleanup_inference_files(keep_results=True)

            # 8. 释放GPU内存(如果使用了GPU)
            if device >= 0 and torch.cuda.is_available():
                torch.cuda.empty_cache()
                print("已清理GPU缓存")

        except Exception as e:
            print(f"发生错误: {str(e)}")
            progress_dialog.log_message(f"错误：{str(e)}")
            QMessageBox.critical(self, "错误", f"推理过程中出错：{str(e)}")
            # 发生错误时也清理临时文件
            self.cleanup_inference_files(keep_results=False)
            # 释放GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

    def show_result_dialog(self, original_image, result_image, results):
        """显示结果对话框"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                                    QLabel, QPushButton, QScrollArea, QWidget)
        from PyQt5.QtGui import QPixmap, QImage
        from PyQt5.QtCore import Qt
        import numpy as np

        dialog = QDialog(self)
        dialog.setWindowTitle("分割结果")
        dialog.setMinimumSize(800, 600)

        # 创建布局
        layout = QHBoxLayout(dialog)

        # 左侧：原始图像和结果图像
        images_layout = QVBoxLayout()

        # 添加原始图像
        original_label = QLabel("原始图像：")
        images_layout.addWidget(original_label)

        # 将PIL图像转换为QPixmap
        if isinstance(original_image, np.ndarray):
            height, width, channel = original_image.shape
            bytes_per_line = 3 * width
            q_img = QImage(original_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            original_pixmap = QPixmap.fromImage(q_img)
        else:
            original_pixmap = self.pil_to_pixmap(original_image)

        original_image_label = QLabel()
        original_image_label.setPixmap(original_pixmap.scaled(
            400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation
        ))
        images_layout.addWidget(original_image_label)

        # 添加结果图像
        result_label = QLabel("分割结果：")
        images_layout.addWidget(result_label)

        if isinstance(result_image, np.ndarray):
            height, width, channel = result_image.shape
            bytes_per_line = 3 * width
            q_img = QImage(result_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            result_pixmap = QPixmap.fromImage(q_img)
        else:
            result_pixmap = self.pil_to_pixmap(result_image)

        result_image_label = QLabel()
        result_image_label.setPixmap(result_pixmap.scaled(
            400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation
        ))
        images_layout.addWidget(result_image_label)

        layout.addLayout(images_layout)

        # 右侧：检测结果列表
        results_layout = QVBoxLayout()
        results_label = QLabel("检测到的对象：")
        results_layout.addWidget(results_label)

        # 创建滚动区域显示结果
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 添加每个检测结果
        for i, result in enumerate(results):
            # 使用字典的get方法安全地获取值
            label = result.get('label', f'Segment {i+1}')
            score = result.get('score', None)

            # 构建显示文本
            if score is not None:
                result_text = QLabel(f"{i+1}. {label} (置信度: {score:.2f})")
            else:
                result_text = QLabel(f"{i+1}. {label}")

            scroll_layout.addWidget(result_text)

        scroll_widget.setLayout(scroll_layout)
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        results_layout.addWidget(scroll)

        layout.addLayout(results_layout)

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        results_layout.addWidget(close_button)

        dialog.exec_()

    def pil_to_pixmap(self, pil_image):
        """将PIL图像转换为QPixmap"""
        from PyQt5.QtGui import QImage, QPixmap
        import numpy as np

        # 将PIL图像转换为numpy数组
        if pil_image.mode == 'RGBA':
            data = pil_image.convert('RGBA')
            data = np.array(data)
            qimage = QImage(data.tobytes(), data.shape[1], data.shape[0],
                           QImage.Format_RGBA8888)
        else:
            data = pil_image.convert('RGB')
            data = np.array(data)
            qimage = QImage(data.tobytes(), data.shape[1], data.shape[0],
                           QImage.Format_RGB888)

        return QPixmap.fromImage(qimage)

    def show_segmentation_results(self, results):
     """显示分割结果"""
     try:
        # 创建结果处理器
        processor = InferenceResultsProcessor(self.project_config)

        # 处理结果
        original_image, result_image, shape_file = processor.process_results(results, self)

        # 如果成功创建了shape文件，将其添加到地图上
        if shape_file and os.path.exists(shape_file):
            self.add_layer_to_map(shape_file)

        # 显示结果对话框
        self.show_result_dialog(original_image, result_image, results)

     except Exception as e:
        print(f"显示分割结果时出错：{str(e)}")
        QMessageBox.critical(
            self,
            "错误",
            f"显示分割结果时发生错误:\n{str(e)}"
        )
        raise

    def run_local_custom_model_inference(self):
        """运行本地自训练模型推理"""
        # 创建模型类型选择对话框
        type_dialog = QDialog(self)
        type_dialog.setWindowTitle("选择模型类型")
        type_dialog.setMinimumWidth(350)

        # 设置对话框布局
        layout = QVBoxLayout(type_dialog)

        # 模型类型选择
        type_label = QLabel("请选择模型类型:")
        layout.addWidget(type_label)

        model_type_combo = QComboBox()
        model_type_combo.addItems(["UNet模型", "LoGCAN模型"])
        layout.addWidget(model_type_combo)

        # 模型类型说明
        info_label = QLabel("UNet模型: 使用.h5格式模型文件\nLoGCAN模型: 使用.ckpt格式模型文件")
        info_label.setStyleSheet("color: #666;")
        layout.addWidget(info_label)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(type_dialog.accept)
        button_box.rejected.connect(type_dialog.reject)
        layout.addWidget(button_box)

        # 显示对话框
        if type_dialog.exec_() != QDialog.Accepted:
            return

        # 获取选择的模型类型
        selected_type = model_type_combo.currentText()
        model_type = "UNet" if "UNet" in selected_type else "LoGCAN"

        # 根据选择的模型类型设置文件过滤器
        if model_type == "UNet":
            file_filter = "UNet模型文件 (*.h5);;所有文件 (*.*)"
            default_dir = self.project_config.get('model_path', '')
        else:  # LoGCAN
            file_filter = "LoGCAN模型文件 (*.ckpt);;PyTorch模型 (*.pth *.pt);;所有文件 (*.*)"
            model_path = self.project_config.get('model_path', '')
            if model_path:
                default_dir = os.path.join(os.path.dirname(model_path), 'trained_models')
            else:
                default_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trained_models')

        # 打开文件选择对话框
        model_path, _ = QFileDialog.getOpenFileName(
            self,
            f"选择{model_type}模型文件",
            default_dir,
            file_filter
        )

        if not model_path:
            return

        # 保存选择的模型类型和路径
        self.current_inference_type = "local_custom"
        self.current_model_path = model_path
        self.current_model_type = model_type

        # 显示选择信息
        QMessageBox.information(
            self,
            "模型已选择",
            f"已选择{model_type}模型：\n{model_path}\n\n请在地图上框选要进行推理的区域。"
        )

        # 激活选择区域模式
        self.activate_select_zone()

    def run_local_hf_inference(self):
        """运行本地HuggingFace模型推理"""
        # 创建模型选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择HuggingFace模型")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # 添加模型选择下拉框
        model_label = QLabel("选择模型:")
        layout.addWidget(model_label)

        model_combo = QComboBox()
        model_combo.addItems([
            # 语义分割模型
            "facebook/mask2former-swin-large-ade-semantic",  # 通用场景分割
            "facebook/mask2former-swin-base-coco",          # 目标检测和分割
            "openmmlab/upernet-convnext-base",             # 通用场景分割
            # 遥感专用模型
            "nvidia/mit-b0",                               # 适用于卫星图像分割
            "microsoft/beit-base-finetuned-ade-640-512",   # 地物特征提取
            # 专门用于地物识别的模型
            "DAMO-YOLO/DAMO-YOLO-S",                      # 建筑物检测
            "whu-lmars/levir-unet-plus",                  # 地物变化检测
            "alibaba-damo/image-segmentation-landcover",   # 土地覆盖分类
            # 水体检测模型
            "ThomasSimonini/Water-Segmentation",          # 水体分割
            # 道路提取模型
            "cswin-t/road-extraction",                    # 道路网络提取
            # 建筑物检测模型
            "microsoft/swin-base-building-detection",      # 建筑物检测
            # 植被分析模型
            "nvidia/segformer-b0-vegetation",             # 植被覆盖分析
            "nvidia/segformer-b5-finetuned-ade-640-640",  # 高精度场景分割
        ])
        layout.addWidget(model_combo)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            selected_model = model_combo.currentText()

            # 激活选择区域模式
            self.activate_select_zone()

            # 等待用户选择区域
            # 在saveSelectedArea中处理推理
            self.current_inference_type = "local_hf"
            self.current_model_path = selected_model

    def run_local_sam_inference(self):
        """运行本地SAM模型推理"""
        # 创建模型选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择SAM模型")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # 添加模型选择下拉框
        model_label = QLabel("选择模型:")
        layout.addWidget(model_label)

        model_combo = QComboBox()
        model_combo.addItems([
            "vit_h",  # ViT-H SAM model
            "vit_l",  # ViT-L SAM model
            "vit_b",  # ViT-B SAM model
        ])
        layout.addWidget(model_combo)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            selected_model = model_combo.currentText()

            # 激活选择区域模式
            self.activate_select_zone()

            # 等待用户选择区域
            # 在saveSelectedArea中处理推理
            self.current_inference_type = "local_sam"
            self.current_model_path = selected_model

    def process_local_custom_inference(self, points):
     """处理本地自训练模型推理"""
     try:
        # 获取模型类型（如果未定义则默认为UNet）
        model_type = getattr(self, 'current_model_type', 'UNet')

        # 1. 下载选定区域的卫星图像
        image_path = self.download_satellite_image_for_inference(points)
        if not image_path:
            return

        # 2. 创建推理实例并加载模型
        from Core.custom_model_inference import CustomModelInference
        inference = CustomModelInference(self.current_model_path, model_type=model_type, parent=self)
        inference.load_model()

        # 3. 运行推理
        result_path, shape_path = inference.run_inference(image_path)

        # 4. 显示结果
        if result_path and os.path.exists(result_path):
            # 添加结果到地图
            if shape_path and os.path.exists(shape_path):
                self.add_layer_to_map(shape_path)
                result_type = "矢量"
            else:
                # 如果没有矢量结果，则添加栅格结果
                self.add_raster_to_map(result_path)
                result_type = "栅格"

            # 清除选择区域
            self.clear_selection_area()

            QMessageBox.information(self, "完成", f"{model_type}模型推理完成，{result_type}结果已添加到地图中。\n\n结果文件保存位置:\n{shape_path if shape_path else result_path}")
        else:
            QMessageBox.warning(self, "警告", "推理完成，但没有生成有效的结果文件。")

     except Exception as e:
        # 显示详细错误信息
        import traceback
        error_details = traceback.format_exc()
        error_message = f"推理过程发生错误：\n{str(e)}\n\n详细信息：\n{error_details}"

        QMessageBox.critical(self, "错误", error_message)
        print(error_message)

    def process_local_sam_inference(self, points):
        """处理本地SAM模型推理"""
        try:
            # 定义模型文件映射
            model_files = {
                'vit_h': 'sam_vit_h_4b8939.pth',
                'vit_l': 'sam_vit_l_0b3195.pth',
                'vit_b': 'sam_vit_b_01ec64.pth'
            }

            model_file = os.path.join(self.project_config['model_path'],
                                    model_files.get(self.current_model_path, 'sam_vit_h_4b8939.pth'))

            if not os.path.exists(model_file):
                QMessageBox.critical(self, "错误",
                                   f"找不到SAM模型文件：{model_file}\n"
                                   f"请确保模型文件已放置在正确位置：\n"
                                   f"{self.project_config['model_path']}")
                return

            print(f"使用模型：{self.current_model_path}，文件路径：{model_file}")

            # 加载选定的SAM模型
            sam = sam_model_registry[self.current_model_path](checkpoint=model_file)
            sam.to(device="cuda" if torch.cuda.is_available() else "cpu")
            predictor = SamPredictor(sam)

            # 下载并预处理选定区域的图像
            image_path = self.download_satellite_image_for_inference(points)
            if not image_path:
                return

            # 加载图像并进行预处理
            image = cv2.imread(image_path)
            if image is None:
                QMessageBox.critical(self, "错误", "无法加载图像")
                return
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            predictor.set_image(image)

            # 准备输入点
            input_points = np.array([[p['lng'], p['lat']] for p in points])
            input_labels = np.ones(len(points))  # 所有点都标记为前景

            # 运行推理
            masks, scores, logits = predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True
            )

            # 显示结果
            self.show_segmentation_results({
                'image': image,
                'masks': masks,
                'scores': scores,
                'input_points': input_points
            })

        except Exception as e:
            error_msg = f"推理过程中出错：{str(e)}\n{traceback.format_exc()}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"详细错误信息：\n{error_msg}")
            raise

    def saveSelectedArea(self, points_json):
        """保存选定的区域坐标并根据当前推理类型进行处理"""
        # 解析选择的区域坐标
        points = json.loads(points_json)

        # 如果没有选择区域，直接返回
        if not points or len(points) < 3:  # 至少需要3个点才能形成一个有效的多边形
            QMessageBox.warning(self, "警告", "请选择一个有效的区域！")
            return

        # 保存坐标点到txt文件
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            txt_filename = os.path.join(self.project_config['dataset_path'], f'selected_area_coords_{timestamp}.txt')

            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write("# 选择区域的坐标点列表 (经度,纬度)\n")
                for point in points:
                    f.write(f"{point['lng']},{point['lat']}\n")

            QMessageBox.information(self, "成功", f"已将坐标点保存到文件：\n{txt_filename}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存坐标文件时发生错误：{str(e)}")

        # 保存选择区域为shape文件
        if HAS_GEOPANDAS:
            try:
                # 创建一个临时文件名
                shape_filename = os.path.join(self.project_config['dataset_path'], f'selected_area_{timestamp}.shp')

                # 创建一个包含多边形的GeoDataFrame
                # 注意：points中的坐标是[lng, lat]格式，需要创建一个闭合的多边形
                coords = [[p['lng'], p['lat']] for p in points]
                if coords[0] != coords[-1]:  # 确保多边形是闭合的
                    coords.append(coords[0])

                from shapely.geometry import Polygon
                polygon = Polygon(coords)
                gdf = gpd.GeoDataFrame(geometry=[polygon], crs='EPSG:4326')

                # 保存为shape文件
                gdf.to_file(shape_filename)

                # 将新创建的shape文件添加到图层列表
                self.add_layer_to_map(shape_filename)

                QMessageBox.information(self, "成功", f"已将选择区域保存为Shape文件：\n{shape_filename}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存Shape文件时发生错误：{str(e)}")

        # 根据当前推理类型进行处理
        if hasattr(self, 'current_inference_type'):
            if self.current_inference_type == "local_custom":
                self.process_local_custom_inference(points)
            elif self.current_inference_type == "local_hf":
                self.process_local_hf_inference(points)
            elif self.current_inference_type == "local_sam":
                self.process_local_sam_inference(points)
            elif self.current_inference_type.startswith("cloud_"):
                self.process_cloud_inference(points)

            # 清除当前推理类型
            delattr(self, 'current_inference_type')
            if hasattr(self, 'current_model_path'):
                delattr(self, 'current_model_path')

        else:
            # 如果不是模型推理操作，则保存为生成数据集使用的区域
            self.selected_area_points = points
            print(f"已保存选定区域坐标：{points}")

    def mousePressEvent(self, event):
        """处理鼠标按下事件"""
        if not self.manual_offset_tool or not self.manual_offset_tool.is_active:
            super().mousePressEvent(event)
            return

        # 获取鼠标点击位置对应的地图坐标
            map_pos = self.get_map_coordinates(event.pos())
        if not map_pos:
            return

        # 在手动偏移模式下，尝试查找并选中最近的点
        if self.manual_offset_tool.handle_mouse_press(event, map_pos):
            # 如果成功选中点，阻止事件继续传播
            event.accept()
            # 高亮显示选中的点
            if self.manual_offset_tool.dragging_point_id is not None:
                self.highlight_points([(self.manual_offset_tool.dragging_point_id, map_pos)])
        else:
            super().mousePressEvent(event)



    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件"""
        if not self.manual_offset_tool or not self.manual_offset_tool.is_active:
           super().mouseReleaseEvent(event)
           return

        # 获取鼠标释放位置对应的地图坐标
        map_pos = self.get_map_coordinates(event.pos())
        if not map_pos:
            return

        # 在手动偏移模式下，完成点的拖动
        if self.manual_offset_tool.handle_mouse_release(event, map_pos):
            # 如果成功完成拖动，阻止事件继续传播
            event.accept()
            # 清除高亮显示
            self.highlight_points([])
        else:
            super().mouseReleaseEvent(event)

    def get_map_coordinates(self, screen_pos):
        """将屏幕坐标转换为地图坐标"""
        if not hasattr(self, 'web_view'):
            return None

        # 执行JavaScript来获取地图坐标
        js_code = """
        (function() {
            var containerPoint = L.point(%d, %d);
            var layerPoint = map.containerPointToLayerPoint(containerPoint);
            var latlng = map.layerPointToLatLng(layerPoint);
            return [latlng.lng, latlng.lat];
        })();
        """ % (screen_pos.x(), screen_pos.y())

        result = self.web_view.page().runJavaScript(js_code)
        if result:
            return tuple(result)  # 返回 (x, y) 坐标
        return None



    def show_grid_generator(self):
        """显示网格生成对话框"""
        dialog = GridGeneratorDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            if values:
                try:
                    # 生成网格
                    grid_gdf, point_gdf = dialog.generate_grid(values)

                    # 获取保存路径
                    grid_file_path, _ = QFileDialog.getSaveFileName(
                        self,
                        "保存网格文件",
                        "",
                        "Shapefile (*.shp)"
                    )

                    if grid_file_path:
                        # 保存为shapefile
                        grid_gdf.to_file(grid_file_path)

                        # 添加到图层列表和地图
                        self.add_layer_to_map(grid_file_path)

                        # 构造点文件的路径（在同一目录下，添加 _points 后缀）
                        base_path = os.path.splitext(grid_file_path)[0]
                        point_file_path = f"{base_path}_points.shp"

                        # 保存网格点
                        point_gdf.to_file(point_file_path)

                        # 添加点到图层列表和地图
                        if self.point_offset_manager.load_grid(point_file_path):
                            layer_name = os.path.splitext(os.path.basename(point_file_path))[0]
                            self.add_grid_points_to_map(self.point_offset_manager.grid_points, layer_name)
                            QMessageBox.information(self, "成功", "网格和点生成成功！")
                        else:
                            QMessageBox.warning(self, "错误", "网格点加载失败")
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"生成网格时发生错误：{str(e)}")

    def add_raster_to_map(self, raster_path):
        """将栅格图像添加到地图上显示"""
        try:
            # 检查文件是否存在
            if not os.path.exists(raster_path):
                QMessageBox.warning(self, "错误", f"找不到栅格文件: {raster_path}")
                return False

            # 获取文件名作为图层名称
            layer_name = os.path.basename(raster_path)

            # 获取文件类型
            file_ext = os.path.splitext(raster_path)[1].lower()

            # 将图像转换为Base64编码以通过JavaScript加载
            import base64
            with open(raster_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode('utf-8')

            # 获取图像URL前缀
            if file_ext in ['.png', '.jpg', '.jpeg']:
                prefix = f"data:image/{file_ext[1:]};base64,"
            else:
                # 对于其他类型，假设为png
                prefix = "data:image/png;base64,"

            # 完整的图像URL
            image_url = prefix + encoded_image

            # 使用JavaScript添加图像覆盖层
            js_code = """
                // 如果图层已存在，先移除
                if (window.layerGroups && window.layerGroups['%s']) {
                    map.removeLayer(window.layerGroups['%s']);
                    delete window.layerGroups['%s'];
                }

                // 初始化 layerGroups 对象（如果不存在）
                if (!window.layerGroups) {
                    window.layerGroups = {};
                }

                // 创建图像覆盖层
                var imageBounds = map.getBounds();
                var imageOverlay = L.imageOverlay('%s', imageBounds, {
                    opacity: 0.7,
                    interactive: true
                });

                // 创建图层并添加到地图
                window.layerGroups['%s'] = imageOverlay;
                imageOverlay.addTo(map);

                // 添加图层控制
                if (!window.layerControl) {
                    window.layerControl = L.control.layers({}, {}).addTo(map);
                }

                // 添加到图层控制
                window.layerControl.addOverlay(imageOverlay, '%s');

                console.log('已添加栅格图像: %s');
            """ % (layer_name, layer_name, layer_name, image_url, layer_name, layer_name, layer_name)

            # 执行JavaScript代码
            self.web_view.page().runJavaScript(js_code)
            return True

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加栅格图像到地图时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def show_offset_settings(self):
        """显示偏移设置对话框"""
        try:
            from GUI.OffsetSettingsDialog import OffsetSettingsDialog
            dialog = OffsetSettingsDialog(self.point_offset_manager, self)

            if dialog.exec_() == QDialog.Accepted:
                # 对话框中已经将设置应用到point_offset_manager
                QMessageBox.information(self, "设置成功", "震源点偏移设置已更新！")
        except Exception as e:
            print(f"显示偏移设置对话框时出错：{str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"显示偏移设置对话框时出错：{str(e)}")

            # 作为备选，使用简单的输入对话框
            distance, ok = QInputDialog.getDouble(
                self, "偏移设置",
                "请输入最大偏移距离（米）：",
                self.point_offset_manager.max_offset_distance,
                0.1, 1000.0, 1
            )

            if ok:
                self.point_offset_manager.set_max_offset_distance(distance)

    def show_shape_download_dialog(self):
        """显示Shape文件范围下载对话框"""
        try:
            from GUI.ShapeBasedDownloadDialog import ShapeBasedDownloadDialog

            dialog = ShapeBasedDownloadDialog(self)
            dialog.exec_()

        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法导入Shape下载对话框：{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示Shape下载对话框时出错：{str(e)}")

    def show_dataset_management_dialog(self):
        """显示数据集管理对话框"""
        try:
            from GUI.DatasetManagementDialog import DatasetManagementDialog

            dialog = DatasetManagementDialog(self)
            dialog.exec_()

        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法导入数据集管理对话框：{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示数据集管理对话框时出错：{str(e)}")

def main():
    app = QApplication(sys.argv)
    # 提前创建MainWindow实例
    window = MainWindow.instance()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()

