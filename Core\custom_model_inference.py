import os
import sys
import shutil
try:
    import cv2
except ImportError:
    pass
try:
    from osgeo import gdal, ogr
except ImportError:
    pass
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import load_model

# 定义自定义损失函数和指标（与训练时保持一致）
def dice_loss(y_true, y_pred, smooth=1e-5):
    """Dice损失函数"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)
    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f)
    dice_coef = (2. * intersection + smooth) / (union + smooth)
    dice_coef = tf.clip_by_value(dice_coef, 1e-7, 1.0 - 1e-7)
    return tf.cast(1 - dice_coef, tf.float32)

def focal_loss(gamma=2., alpha=.25):
    """Focal损失函数"""
    def focal_loss_fixed(y_true, y_pred):
        epsilon = tf.keras.backend.epsilon()
        y_pred = tf.clip_by_value(y_pred, epsilon, 1. - epsilon)
        p_t = tf.where(tf.equal(y_true, 1), y_pred, 1 - y_pred)
        alpha_factor = tf.ones_like(y_true) * alpha
        alpha_t = tf.where(tf.equal(y_true, 1), alpha_factor, 1 - alpha_factor)
        cross_entropy = -tf.math.log(p_t)
        weight = alpha_t * tf.pow((1 - p_t), gamma)
        focal_loss_val = weight * cross_entropy
        focal_loss_val = tf.reduce_mean(tf.reduce_sum(focal_loss_val, axis=-1))
        focal_loss_val = tf.where(tf.math.is_finite(focal_loss_val), focal_loss_val, tf.constant(0.0))
        return tf.cast(focal_loss_val, tf.float32)
    return focal_loss_fixed

def combined_loss(y_true, y_pred):
    """组合损失函数"""
    dice = dice_loss(y_true, y_pred)
    focal = focal_loss(gamma=2., alpha=.25)(y_true, y_pred)
    dice = tf.where(tf.math.is_finite(dice), dice, tf.constant(0.0))
    focal = tf.where(tf.math.is_finite(focal), focal, tf.constant(0.0))
    combined = dice + focal
    combined = tf.where(tf.math.is_finite(combined), combined, tf.constant(1.0))
    return tf.cast(combined, tf.float32)

def iou_coef(y_true, y_pred, smooth=1e-5):
    """IoU系数"""
    y_true_f = tf.cast(tf.reshape(y_true, [-1]), tf.float32)
    y_pred_f = tf.cast(tf.reshape(y_pred, [-1]), tf.float32)
    y_pred_f = tf.clip_by_value(y_pred_f, 1e-7, 1.0 - 1e-7)
    intersection = tf.reduce_sum(y_true_f * y_pred_f)
    union = tf.reduce_sum(y_true_f) + tf.reduce_sum(y_pred_f) - intersection
    iou = (intersection + smooth) / (union + smooth)
    iou = tf.clip_by_value(iou, 1e-7, 1.0 - 1e-7)
    return tf.cast(iou, tf.float32)
import rasterio
from rasterio.merge import merge
from rasterio.transform import Affine
from PyQt5.QtWidgets import QProgressDialog, QApplication, QMessageBox
from .predict_obstacles import predict_and_save_patches, merge_predictions
from .unet_inference import preprocess_image_for_unet, optimized_raster_to_polygon
import traceback
import importlib.util

# 注意：旧的rsseg框架已移除，LogCAN现在使用独立的训练和推理系统

# 注意：LoGCAN现在使用独立的推理系统，不再需要rsseg环境检查
def check_logcan_environment():
    """检查LoGCAN推理环境是否可用（已废弃）"""
    return False, "LoGCAN已迁移到独立系统，请使用Core/LogCAN_Training_New.py中的推理功能"

# 尝试动态导入LoGCAN推理模型
LoGCANInferenceModel = None
is_logcan_available, logcan_error = check_logcan_environment()

if is_logcan_available:
    try:
        # 使用importlib动态导入LoGCANInferenceModel类
        spec = importlib.util.spec_from_file_location(
            "logcan_inference",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "logcan_inference.py")
        )
        logcan_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(logcan_module)
        LoGCANInferenceModel = logcan_module.LoGCANInferenceModel
        print("成功导入LoGCAN推理模型")
    except Exception as e:
        is_logcan_available = False
        logcan_error = f"导入LoGCAN推理模型失败: {str(e)}"
        print(f"警告: {logcan_error}")
else:
    print(f"警告: LoGCAN推理环境不可用 - {logcan_error}")

class CustomModelInference:
    """自训练模型推理类"""

    def __init__(self, model_path, model_type="UNet", parent=None):
        """
        初始化推理类

        Args:
            model_path: 模型文件路径
            model_type: 模型类型，"UNet"或"LoGCAN"
            parent: 父窗口，用于显示进度对话框
        """
        self.model_path = model_path
        self.model_type = model_type
        self.model = None
        self.parent = parent
        print(f"初始化自训练模型推理: 类型={model_type}, 路径={model_path}")

        # 针对LoGCAN模型进行环境检查
        if self.model_type == "LoGCAN" and not is_logcan_available:
            error_msg = f"LoGCAN推理环境不可用: {logcan_error}"
            print(f"警告: {error_msg}")
            if self.parent:
                QMessageBox.warning(self.parent, "警告", error_msg)

    def load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"找不到模型文件：{self.model_path}")

        try:
            if self.model_type == "UNet":
                # UNet模型加载 (TensorFlow)
                # 设置GPU内存增长
                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus:
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    print(f"检测到 {len(gpus)} 个 GPU，已配置内存增长")
                else:
                    print("未检测到 GPU，将使用 CPU 进行 UNet 推理")

                # 定义自定义对象字典
                custom_objects = {
                    'combined_loss': combined_loss,
                    'dice_loss': dice_loss,
                    'focal_loss': focal_loss,
                    'iou_coef': iou_coef
                }

                try:
                    self.model = load_model(self.model_path, custom_objects=custom_objects, compile=False)
                    print(f"✅ 成功加载UNet模型（包含自定义损失函数）：{self.model_path}")
                except Exception as e:
                    print(f"⚠️ 使用自定义对象加载失败，尝试不编译加载: {e}")
                    try:
                        self.model = load_model(self.model_path, compile=False)
                        print(f"✅ 成功加载UNet模型（不编译）：{self.model_path}")
                    except Exception as e2:
                        raise Exception(f"UNet模型加载失败: {e2}")

            elif self.model_type == "LoGCAN":
                # LoGCAN模型加载 (PyTorch)
                # 确保LoGCAN环境可用
                if not is_logcan_available:
                    raise ImportError(f"LoGCAN推理环境不可用: {logcan_error}")

                if LoGCANInferenceModel is None:
                    raise ImportError("无法导入LoGCANInferenceModel类")

                # 注意：LoGCAN配置文件路径已更改，现在使用独立系统
                raise NotImplementedError("LoGCAN推理已迁移到独立系统，请使用Core/LogCAN_Training_New.py")

                # 检测设备
                try:
                    import torch
                    if torch.cuda.is_available():
                        device_info = f"GPU: {torch.cuda.get_device_name(0)}"
                        print(f"检测到GPU，将使用GPU进行LoGCAN推理: {device_info}")
                    else:
                        import platform
                        import multiprocessing
                        device_info = f"CPU: {platform.processor()} ({multiprocessing.cpu_count()}核)"
                        print(f"未检测到GPU，将使用CPU进行LoGCAN推理: {device_info}")
                        # 设置CPU线程数
                        torch.set_num_threads(multiprocessing.cpu_count())
                except Exception as e:
                    print(f"检测设备出错: {str(e)}")

                # 初始化LoGCAN推理模型
                print(f"开始加载LoGCAN模型，配置文件: {config_path}")
                print(f"模型检查点路径: {self.model_path}")

                self.model = LoGCANInferenceModel(config_path, self.model_path)
                print(f"成功加载LoGCAN模型：{self.model_path}")

            else:
                raise ValueError(f"不支持的模型类型：{self.model_type}，当前仅支持UNet和LoGCAN")

        except Exception as e:
            error_msg = f"加载模型失败：{str(e)}\n模型路径：{self.model_path}"
            print(error_msg)
            traceback.print_exc()
            if self.parent:
                QMessageBox.critical(self.parent, "错误", error_msg)
            raise Exception(error_msg)

    def run_inference(self, image_path):
        """
        运行推理

        Args:
            image_path: 输入图像路径

        Returns:
            tuple: (merged_output_path, output_shape_path) 栅格预测结果路径和矢量结果路径
        """
        # 1. 创建临时输出目录
        temp_output_dir = os.path.join(os.path.dirname(image_path), 'temp_predictions')
        os.makedirs(temp_output_dir, exist_ok=True)

        # 2. 创建进度对话框
        progress = QProgressDialog("正在进行模型推理...", "取消", 0, 100, self.parent)
        progress.setWindowTitle("推理进度")
        progress.setMinimumDuration(0)
        progress.setModal(True)
        progress.show()
        QApplication.processEvents()

        try:
            # 加载模型（如果尚未加载）
            if self.model is None:
                progress.setLabelText("正在加载模型...")
                progress.setValue(5)
                QApplication.processEvents()
                self.load_model()

            # 根据模型类型执行不同的推理流程
            if self.model_type == "UNet":
                # UNet推理流程（原有实现）
                progress.setValue(10)
                progress.setLabelText("正在读取和预处理图像...")
                QApplication.processEvents()

                with rasterio.open(image_path) as src:
                    image = src.read()
                    image = np.transpose(image, (1, 2, 0))  # 转换为(H, W, C)格式
                    image = preprocess_image_for_unet(image)  # 使用改进的UNet预处理函数

                    # 4. 进行分块预测
                    progress.setValue(30)
                    progress.setLabelText("正在进行分块预测...")
                    QApplication.processEvents()

                    predict_and_save_patches(self.model, image, temp_output_dir, src.transform, src.crs)

                    # 5. 合并预测结果
                    progress.setValue(60)
                    progress.setLabelText("正在合并预测结果...")
                    QApplication.processEvents()

                    # 创建结果保存目录
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    result_dir = os.path.join(os.path.dirname(image_path), 'inference_results', timestamp)
                    os.makedirs(result_dir, exist_ok=True)

                    merged_output_path = os.path.join(result_dir, 'merged_prediction.tif')
                    merge_predictions(temp_output_dir, merged_output_path, image.shape[:2], src.transform, src.crs)

                    # 6. 转换为矢量数据 - 使用优化后的形状保持算法
                    progress.setValue(80)
                    progress.setLabelText("正在转换为矢量数据...")
                    QApplication.processEvents()

                    output_shape_path = os.path.join(result_dir, 'predicted_buildings.shp')
                    optimized_raster_to_polygon(merged_output_path, output_shape_path, threshold=None, min_area=15, max_area=50000)  # 最小15平方米，最大5万平方米

                    print(f"\n✅ 推理结果已保存到: {result_dir}")
                    print(f"   - 栅格结果: {merged_output_path}")
                    print(f"   - 矢量结果: {output_shape_path}")

                    progress.setValue(100)
                    return merged_output_path, output_shape_path

            elif self.model_type == "LoGCAN":
                # LoGCAN推理流程（新实现）
                progress.setValue(10)
                progress.setLabelText("正在准备LoGCAN推理...")
                QApplication.processEvents()

                # 获取输出文件路径
                base_name = os.path.basename(image_path)
                name_without_ext = os.path.splitext(base_name)[0]
                output_path = os.path.join(os.path.dirname(image_path), f"{name_without_ext}_logcan_pred.png")

                # 直接使用LoGCAN模型进行预测
                progress.setValue(30)
                progress.setLabelText("正在使用LoGCAN模型进行推理...")
                QApplication.processEvents()

                try:
                    # 调用LoGCAN推理方法，直接预测并保存结果
                    self.model.predict_and_save(image_path, output_path)

                    progress.setValue(70)
                    progress.setLabelText("正在处理推理结果...")
                    QApplication.processEvents()

                    # 将PNG分割结果转换为GeoTIFF格式，保留所有类别
                    geotiff_path = os.path.join(os.path.dirname(image_path), f"{name_without_ext}_logcan_pred.tif")

                    # 获取LoGCAN的颜色映射（从模型中）
                    color_map = self.model.color_map

                    # 读取原始卫星图像以获取地理参考信息
                    with rasterio.open(image_path) as src:
                        # 打开预测结果PNG
                        pred_img = cv2.imread(output_path)

                        # 如果需要，调整大小以匹配原始图像
                        if pred_img.shape[:2] != (src.height, src.width):
                            pred_img = cv2.resize(pred_img, (src.width, src.height),
                                                interpolation=cv2.INTER_NEAREST)

                        # 创建分类图像（保留所有类别）
                        # 初始化每个像素为背景类别（0）
                        class_img = np.zeros((src.height, src.width), dtype=np.uint8)

                        # 遍历颜色映射，为每个类别赋值
                        for class_idx, color in color_map.items():
                            # 创建颜色掩码（BGR格式）
                            mask = np.all(pred_img == np.array([color[2], color[1], color[0]]), axis=2)
                            # 将匹配的像素设置为当前类别索引
                            class_img[mask] = class_idx

                        # 使用原始图像的元数据创建GeoTIFF
                        with rasterio.open(
                            geotiff_path,
                            'w',
                            driver='GTiff',
                            height=src.height,
                            width=src.width,
                            count=1,
                            dtype=np.uint8,
                            crs=src.crs,
                            transform=src.transform,
                        ) as dst:
                            dst.write(class_img, 1)

                    progress.setValue(85)
                    progress.setLabelText("正在转换为矢量数据...")
                    QApplication.processEvents()

                    # 转换为矢量数据，保留所有类别信息
                    output_shape_path = os.path.join(os.path.dirname(image_path), f"{name_without_ext}_logcan_classes.shp")

                    # 使用修改后的raster_to_polygon函数，保留所有类别值
                    gdal.Polygonize(band, None, layer, 0, [], callback=None)

                    progress.setValue(100)
                    return output_path, output_shape_path

                except Exception as e:
                    error_msg = f"LoGCAN推理失败：{str(e)}"
                    print(error_msg)
                    traceback.print_exc()
                    raise Exception(error_msg)

        except Exception as e:
            error_msg = f"推理过程中出错：{str(e)}"
            print(error_msg)
            traceback.print_exc()
            if self.parent:
                QMessageBox.critical(self.parent, "错误", error_msg)
            raise Exception(error_msg)
        finally:
            # 清理临时文件夹
            progress.close()
            if os.path.exists(temp_output_dir):
                shutil.rmtree(temp_output_dir)
