# 深度学习框架
tensorflow>=2.8.0
torch>=1.9.0
torchvision>=0.10.0
pytorch-lightning>=1.5.0
segmentation-models>=1.0.1

# 图像处理
opencv-python>=4.5.0
Pillow>=8.0.0
albumentations>=1.0.0
rasterio>=1.2.0
GDAL>=3.4.0

# 地理数据处理
rtree>=1.0.0
mercantile>=1.2.0
shapely>=1.8.0
geopandas>=0.10.0

# GUI
PyQt5>=5.15.0
PyQtWebEngine>=5.15.0

# 数据处理
numpy>=1.19.0,<2.0  # 固定为1.x版本，避免与PyTorch/TensorFlow冲突
pandas>=1.3.0
scikit-learn>=0.24.0

# 网络请求
requests>=2.26.0
aiohttp>=3.8.0

# 工具包
tqdm>=4.62.0
matplotlib>=3.4.0
seaborn>=0.11.0

# 其他可能需要的依赖
tqdm
pyyaml
mercantile  # 用于地图瓦片坐标处理
requests  # 用于HTTP请求