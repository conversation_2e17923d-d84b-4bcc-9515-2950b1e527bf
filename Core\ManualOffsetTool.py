from PyQt5.QtCore import Qt, pyqtSignal, QObject
from shapely.geometry import Point
import pyproj
from pyproj import Transformer

class ManualOffsetTool(QObject):
    """手动偏移工具类"""
    
    # 信号定义
    point_moved = pyqtSignal(int, object)  # 点ID, 新位置
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.is_active = False
        self.dragging_point_id = None
        self.drag_start_pos = None
        self.grid_size = 25.0  # 默认网格大小
        
        # 创建坐标转换器
        self.wgs84 = pyproj.CRS("EPSG:4326")  # WGS84
        self.utm = pyproj.CRS("+proj=utm +zone=38 +ellps=intl +towgs84=-143,-236,7,0,0,0,0 +units=m +no_defs")  # UTM38N with International 1924
        self.to_utm = Transformer.from_crs(self.wgs84, self.utm, always_xy=True)
        self.to_wgs84 = Transformer.from_crs(self.utm, self.wgs84, always_xy=True)
        
    def activate(self):
        """激活手动偏移工具"""
        self.is_active = True
        self.main_window.setStatusTip("手动偏移模式：点击并拖动点位到新位置，按Tab键吸附到最近的网格单元中心")
        # 获取当前的网格大小设置
        if hasattr(self.main_window, 'point_offset_manager'):
            self.grid_size = self.main_window.point_offset_manager.offset_settings['surface_bin_size']
            print(f"当前网格大小: {self.grid_size}")
        
    def deactivate(self):
        """停用手动偏移工具"""
        self.is_active = False
        self.dragging_point_id = None
        self.drag_start_pos = None
        self.main_window.setStatusTip("")
        
    def normalize_coordinates(self, pos):
        """统一坐标格式为 [lon, lat]"""
        try:
            if hasattr(pos, 'lat') and hasattr(pos, 'lng'):
                return [pos.lng, pos.lat]
            elif isinstance(pos, (list, tuple)) and len(pos) >= 2:
                return list(pos[:2])
            else:
                raise ValueError(f"无效的坐标格式: {pos}")
        except Exception as e:
            print(f"坐标格式转换错误: {str(e)}")
            return None
            
    def snap_to_grid(self, pos):
        """将坐标吸附到网格单元中心"""
        try:
            # 确保输入坐标格式正确
            coords = self.normalize_coordinates(pos)
            if coords is None:
                return pos
                
            lon, lat = coords[0], coords[1]
            print(f"原始WGS84坐标: {lon}, {lat}")
            
            # 转换到UTM坐标
            utm_x, utm_y = self.to_utm.transform(lon, lat)
            print(f"UTM坐标: {utm_x}, {utm_y}")
            
            # 计算网格单元索引（使用地板除法）
            grid_x_index = utm_x // self.grid_size
            grid_y_index = utm_y // self.grid_size
            
            # 计算网格单元中心UTM坐标
            center_x = (grid_x_index + 0.5) * self.grid_size
            center_y = (grid_y_index + 0.5) * self.grid_size
            print(f"网格中心UTM坐标: {center_x}, {center_y}")
            
            # 转换回WGS84坐标
            lon, lat = self.to_wgs84.transform(center_x, center_y)
            print(f"吸附后WGS84坐标: {lon}, {lat}")
            
            # 返回符合地图期望的格式 {lat, lng}
            return {'lat': lat, 'lng': lon}
            
        except Exception as e:
            print(f"网格吸附错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return pos
            
    def handle_mouse_press(self, event, map_pos):
        """处理鼠标按下事件"""
        if not self.is_active:
            return False
            
        # 查找最近的点
        point_id = self.main_window.find_nearest_point(map_pos)
        if point_id is not None:
            self.dragging_point_id = point_id
            self.drag_start_pos = self.normalize_coordinates(map_pos)
            print(f"开始拖动点 {point_id}, 起始位置: {self.drag_start_pos}")
            return True
        return False
        
    def handle_mouse_move(self, event, map_pos):
        """处理鼠标移动事件"""
        if not self.is_active or self.dragging_point_id is None:
            return False
            
        try:
            pos = self.normalize_coordinates(map_pos)
            if pos is None:
                return False
                
            print(f"移动前坐标: {pos}")
            
            # 直接更新点位位置，网格吸附功能由JavaScript端处理（通过Tab键）
            self.main_window.update_point_position(self.dragging_point_id, pos)
            return True
            
        except Exception as e:
            print(f"鼠标移动处理错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    def handle_mouse_release(self, event, map_pos):
        """处理鼠标释放事件"""
        if not self.is_active or self.dragging_point_id is None:
            return False
            
        try:
            pos = self.normalize_coordinates(map_pos)
            if pos is None:
                return False
                
            print(f"释放前坐标: {pos}")
            
            if pos != self.drag_start_pos:
                # 发出点位移动信号
                self.point_moved.emit(self.dragging_point_id, pos)
            
            self.dragging_point_id = None
            self.drag_start_pos = None
            return True
            
        except Exception as e:
            print(f"鼠标释放处理错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False 