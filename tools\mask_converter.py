#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
掩码格式转换工具 - 简化版
将GeoTIFF掩码转换为Windows可查看的PNG格式

使用方法：
1. 直接运行：python tools/mask_converter.py
2. 指定路径：python tools/mask_converter.py dataset/mask
3. 指定输出：python tools/mask_converter.py dataset/mask output_folder

功能：
- 将GeoTIFF二值掩码转换为标准PNG格式
- 创建彩色版本便于查看
- 生成缩略图和统计信息
- 支持批量处理
"""

import os
import sys
import glob
import rasterio
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from pathlib import Path

def convert_single_mask(tif_path, output_dir):
    """转换单个掩码文件"""
    try:
        # 读取GeoTIFF
        with rasterio.open(tif_path) as src:
            mask = src.read(1)
        
        base_name = Path(tif_path).stem
        
        # 1. 创建标准灰度PNG (0->黑色, 1->白色)
        mask_255 = (mask * 255).astype(np.uint8)
        gray_image = Image.fromarray(mask_255, mode='L')
        gray_path = os.path.join(output_dir, f"{base_name}_gray.png")
        gray_image.save(gray_path)
        
        # 2. 创建彩色PNG (更容易区分)
        colored_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
        colored_mask[mask == 0] = [25, 25, 112]   # 深蓝色 - 背景
        colored_mask[mask == 1] = [255, 165, 0]   # 橙色 - 建筑
        
        colored_image = Image.fromarray(colored_mask, mode='RGB')
        colored_path = os.path.join(output_dir, f"{base_name}_colored.png")
        colored_image.save(colored_path)
        
        # 3. 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 原始灰度
        ax1.imshow(mask, cmap='gray', vmin=0, vmax=1)
        ax1.set_title('原始掩码 (灰度)')
        ax1.axis('off')
        
        # 彩色版本
        ax2.imshow(colored_mask)
        ax2.set_title('彩色增强版')
        ax2.axis('off')
        
        # 添加说明
        fig.suptitle(f'掩码对比: {base_name}', fontsize=14, fontweight='bold')
        
        # 统计信息
        total_pixels = mask.size
        building_pixels = np.sum(mask == 1)
        background_pixels = np.sum(mask == 0)
        
        stats_text = f"""
统计信息:
• 图像尺寸: {mask.shape[0]} × {mask.shape[1]}
• 总像素数: {total_pixels:,}
• 背景像素: {background_pixels:,} ({background_pixels/total_pixels:.1%})
• 建筑像素: {building_pixels:,} ({building_pixels/total_pixels:.1%})

颜色说明:
🔵 深蓝色 = 背景区域
🟠 橙色 = 建筑区域
        """
        
        plt.figtext(0.02, 0.02, stats_text, fontsize=9, 
                   verticalalignment='bottom', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        plt.tight_layout()
        comparison_path = os.path.join(output_dir, f"{base_name}_comparison.png")
        plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return {
            'success': True,
            'gray_path': gray_path,
            'colored_path': colored_path,
            'comparison_path': comparison_path,
            'stats': {
                'total_pixels': total_pixels,
                'building_pixels': building_pixels,
                'building_ratio': building_pixels/total_pixels
            }
        }
        
    except Exception as e:
        return {'success': False, 'error': str(e)}

def batch_convert(mask_folder, output_folder=None, max_files=None):
    """批量转换掩码文件"""
    
    # 设置输出文件夹
    if output_folder is None:
        output_folder = os.path.join(mask_folder, "viewable_masks")
    
    os.makedirs(output_folder, exist_ok=True)
    
    # 查找所有TIF文件
    tif_files = glob.glob(os.path.join(mask_folder, "*.tif"))
    tif_files.extend(glob.glob(os.path.join(mask_folder, "*.tiff")))
    
    if not tif_files:
        print(f"❌ 在 {mask_folder} 中没有找到TIF文件")
        return False
    
    # 限制处理文件数量（避免处理太多文件）
    if max_files and len(tif_files) > max_files:
        print(f"📊 找到 {len(tif_files)} 个文件，限制处理前 {max_files} 个")
        tif_files = tif_files[:max_files]
    else:
        print(f"📊 找到 {len(tif_files)} 个TIF文件")
    
    print(f"📁 输出目录: {output_folder}")
    print("=" * 60)
    
    # 批量处理
    success_count = 0
    failed_count = 0
    stats_summary = []
    
    for i, tif_file in enumerate(tif_files, 1):
        print(f"[{i}/{len(tif_files)}] 处理: {os.path.basename(tif_file)}")
        
        result = convert_single_mask(tif_file, output_folder)
        
        if result['success']:
            success_count += 1
            stats_summary.append(result['stats'])
            print(f"  ✅ 成功 - 建筑占比: {result['stats']['building_ratio']:.1%}")
        else:
            failed_count += 1
            print(f"  ❌ 失败: {result['error']}")
    
    # 生成总结报告
    print("\n" + "=" * 60)
    print("📋 转换完成总结")
    print("=" * 60)
    print(f"✅ 成功转换: {success_count} 个文件")
    print(f"❌ 转换失败: {failed_count} 个文件")
    
    if stats_summary:
        avg_building_ratio = np.mean([s['building_ratio'] for s in stats_summary])
        print(f"📊 平均建筑占比: {avg_building_ratio:.1%}")
        
        print(f"\n📁 输出文件说明:")
        print(f"  • *_gray.png      - 标准灰度图 (Windows可直接查看)")
        print(f"  • *_colored.png   - 彩色增强图 (推荐查看)")
        print(f"  • *_comparison.png - 对比图 (包含统计信息)")
        
        print(f"\n💡 使用建议:")
        print(f"  1. 用Windows图片查看器打开 *_colored.png 文件")
        print(f"  2. 查看 *_comparison.png 了解详细统计")
        print(f"  3. 深蓝色区域 = 背景，橙色区域 = 建筑")
    
    return success_count > 0

def main():
    """主函数"""
    print("🚀 掩码格式转换工具")
    print("=" * 60)
    
    # 默认设置
    default_mask_folder = "dataset/mask"
    max_files = 10  # 默认只处理前10个文件，避免生成太多文件
    
    # 解析命令行参数
    if len(sys.argv) >= 2:
        mask_folder = sys.argv[1]
    else:
        mask_folder = default_mask_folder
    
    if len(sys.argv) >= 3:
        output_folder = sys.argv[2]
    else:
        output_folder = None
    
    print(f"📁 掩码文件夹: {mask_folder}")
    
    # 检查文件夹是否存在
    if not os.path.exists(mask_folder):
        print(f"❌ 文件夹不存在: {mask_folder}")
        print("\n💡 使用方法:")
        print("  python tools/mask_converter.py                    # 使用默认路径")
        print("  python tools/mask_converter.py dataset/mask       # 指定输入路径")
        print("  python tools/mask_converter.py dataset/mask output # 指定输入和输出路径")
        return
    
    # 询问是否处理所有文件
    tif_count = len(glob.glob(os.path.join(mask_folder, "*.tif")))
    if tif_count > max_files:
        print(f"⚠️  发现 {tif_count} 个TIF文件")
        response = input(f"是否只处理前 {max_files} 个文件？(y/n，默认y): ").strip().lower()
        if response in ['n', 'no']:
            max_files = None
            print("将处理所有文件...")
    
    # 开始转换
    success = batch_convert(mask_folder, output_folder, max_files)
    
    if success:
        print("\n🎉 转换完成！现在可以用Windows图片查看器查看PNG文件了")
    else:
        print("\n❌ 转换失败")

if __name__ == "__main__":
    main()
