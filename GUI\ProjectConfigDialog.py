from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QGroupBox, QLineEdit, QFileDialog,
                            QMessageBox)
from PyQt5.QtCore import Qt
import os

class ProjectConfigDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("项目配置")
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 数据集路径组
        dataset_group = QGroupBox("数据集路径设置")
        dataset_layout = QHBoxLayout()
        
        self.dataset_path = QLineEdit()
        # 设置默认路径
        default_dataset_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dataset')
        self.dataset_path.setText(default_dataset_path)
        
        dataset_browse_button = QPushButton("浏览...")
        dataset_browse_button.clicked.connect(lambda: self.browse_path(self.dataset_path, "选择数据集保存路径"))
        
        dataset_layout.addWidget(QLabel("数据集路径:"))
        dataset_layout.addWidget(self.dataset_path)
        dataset_layout.addWidget(dataset_browse_button)
        dataset_group.setLayout(dataset_layout)
        
        # 模型路径组
        model_group = QGroupBox("模型路径设置")
        model_layout = QHBoxLayout()
        
        self.model_path = QLineEdit()
        # 设置默认路径
        default_model_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'trained_models')
        self.model_path.setText(default_model_path)
        
        model_browse_button = QPushButton("浏览...")
        model_browse_button.clicked.connect(lambda: self.browse_path(self.model_path, "选择模型保存路径"))
        
        model_layout.addWidget(QLabel("模型路径:"))
        model_layout.addWidget(self.model_path)
        model_layout.addWidget(model_browse_button)
        model_group.setLayout(model_layout)
        
        # 添加所有组到主布局
        layout.addWidget(dataset_group)
        layout.addWidget(model_group)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.ok_button.clicked.connect(self.validate_and_accept)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

    def browse_path(self, line_edit, title):
        """打开文件夹选择对话框"""
        current_path = line_edit.text()
        folder_path = QFileDialog.getExistingDirectory(
            self,
            title,
            current_path,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )
        if folder_path:
            line_edit.setText(folder_path)

    def validate_and_accept(self):
        """验证路径并接受对话框"""
        dataset_path = self.dataset_path.text()
        model_path = self.model_path.text()
        
        # 检查并创建路径
        try:
            for path in [dataset_path, model_path]:
                if not os.path.exists(path):
                    os.makedirs(path)
                if not os.access(path, os.W_OK):
                    raise Exception(f"路径不可写：{path}")
            self.accept()
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"路径设置错误：{str(e)}")
    
    def get_settings(self):
        """获取所有设置参数"""
        return {
            "dataset_path": self.dataset_path.text(),
            "model_path": self.model_path.text()
        } 