# 🌐 UNet训练通用平台使用指南

本指南详细说明如何在各种平台上使用`UNet_Training_Universal.ipynb`进行卫星图像建筑物分割模型训练。

## 📋 支持的平台

| 平台 | 状态 | 特点 | 推荐指数 |
|------|------|------|----------|
| **Jupyter Lab/Notebook** | ✅ 完全支持 | 本地运行，完全控制 | ⭐⭐⭐⭐⭐ |
| **Google Colab** | ✅ 完全支持 | 免费GPU，云端运行 | ⭐⭐⭐⭐⭐ |
| **Kaggle Notebooks** | ✅ 完全支持 | 免费GPU，数据集管理 | ⭐⭐⭐⭐ |
| **Azure ML Studio** | ✅ 支持 | 企业级，集成MLOps | ⭐⭐⭐⭐ |
| **AWS SageMaker** | ✅ 支持 | 企业级，可扩展 | ⭐⭐⭐⭐ |
| **其他Jupyter环境** | ✅ 通用支持 | 根据具体环境调整 | ⭐⭐⭐ |

## 🚀 快速开始

### 第一步：准备数据
确保您的数据按以下结构组织：
```
your_dataset/
├── images/          # 图像文件夹
│   ├── tile_0_0_img-*.tif
│   └── ...
└── masks/           # 掩膜文件夹
    ├── tile_0_0_mask.tif
    └── ...
```

### 第二步：选择平台并配置路径
在notebook的**数据路径配置**部分，根据您的平台取消对应行的注释：

## 📍 各平台配置详解

### 1️⃣ Jupyter本地环境

**适用场景：** 有本地GPU，需要完全控制训练过程

**配置方法：**
```python
# 取消注释以下行
DATA_ROOT = "./data"  # 当前目录下的data文件夹
# 或使用绝对路径
# DATA_ROOT = "/path/to/your/dataset"
WORK_DIR = "./training_output"
```

**数据准备：**
1. 将数据放在notebook同级目录的`data`文件夹中
2. 或修改`DATA_ROOT`为您的数据绝对路径

**优势：**
- 完全控制训练环境
- 数据隐私性好
- 可自定义硬件配置

**注意事项：**
- 需要本地GPU支持
- 需要安装CUDA和相关依赖

---

### 2️⃣ Kaggle平台

**适用场景：** 需要免费GPU，有公开数据集

**配置方法：**
```python
# 取消注释以下行
DATA_ROOT = "/kaggle/input/your-dataset-name"
WORK_DIR = "/kaggle/working"
```

**数据准备：**
1. 将数据上传为Kaggle Dataset
2. 在notebook中添加该数据集
3. 修改`your-dataset-name`为实际数据集名称

**优势：**
- 免费GPU使用（每周30小时）
- 数据集版本管理
- 社区分享和讨论

**注意事项：**
- 数据集需要公开或私有共享
- 有时间限制（9小时/session）
- 网络访问受限

**详细步骤：**
1. 登录Kaggle，创建新的Dataset
2. 上传您的图像和掩膜文件
3. 创建新的Notebook，添加数据集
4. 修改配置中的数据集名称

---

### 3️⃣ Google Colab

**适用场景：** 需要免费GPU，数据在Google Drive

**配置方法：**
```python
# 取消注释以下行
from google.colab import drive
drive.mount('/content/drive')
DATA_ROOT = "/content/drive/MyDrive/your_dataset_folder"
WORK_DIR = "/content/drive/MyDrive/UNet_Training"
```

**数据准备：**
1. 将数据上传到Google Drive
2. 按标准结构组织文件夹
3. 修改路径为实际的Drive路径

**优势：**
- 免费GPU使用
- 与Google Drive集成
- 易于分享和协作

**注意事项：**
- 需要Google账户
- 有使用时间限制
- 数据传输可能较慢

---

### 4️⃣ Azure ML Studio

**适用场景：** 企业环境，需要MLOps集成

**配置方法：**
```python
# 取消注释以下行
from azureml.core import Run
run = Run.get_context()
DATA_ROOT = run.input_datasets['dataset_name'].as_mount()
WORK_DIR = "./outputs"
```

**数据准备：**
1. 在Azure ML中创建数据集
2. 在计算实例中注册数据集
3. 修改`dataset_name`为实际名称

**优势：**
- 企业级安全和合规
- 完整的MLOps流程
- 可扩展的计算资源

**注意事项：**
- 需要Azure订阅
- 相对复杂的设置
- 成本考虑

---

### 5️⃣ AWS SageMaker

**适用场景：** 企业环境，需要大规模训练

**配置方法：**
```python
# 取消注释以下行
DATA_ROOT = "/opt/ml/input/data/training"
WORK_DIR = "/opt/ml/model"
```

**数据准备：**
1. 将数据上传到S3
2. 创建SageMaker训练作业
3. 配置输入数据路径

**优势：**
- 高度可扩展
- 与AWS生态集成
- 支持分布式训练

**注意事项：**
- 需要AWS账户
- 成本较高
- 学习曲线陡峭

---

## ⚙️ 训练配置调整

根据您的硬件配置调整训练参数：

```python
TRAINING_CONFIG = {
    'batch_size': 8,          # GPU内存小时减少到4或2
    'epochs': 50,             # 可根据需要调整
    'learning_rate': 1e-4,    # 标准学习率
    'validation_split': 0.2,  # 验证集比例
    'patch_size': 256,        # 图像块大小，内存不足时减少到128
    'patch_step': 224,        # 重叠步长
    'min_foreground_ratio': 0.001  # 最小前景比例
}
```

### 硬件配置建议

| GPU类型 | 推荐batch_size | 推荐patch_size | 预期训练时间 |
|---------|----------------|----------------|--------------|
| Tesla T4 (Colab) | 4-8 | 256 | 3-5小时 |
| Tesla K80 (Kaggle) | 2-4 | 128-256 | 4-6小时 |
| RTX 3080 (本地) | 8-16 | 256-512 | 2-3小时 |
| V100 (云端) | 16-32 | 256-512 | 1-2小时 |

## 🔧 常见问题解决

### 问题1：内存不足 (OOM)
**症状：** `ResourceExhaustedError: OOM when allocating tensor`

**解决方案：**
```python
# 减小批次大小
TRAINING_CONFIG['batch_size'] = 2

# 减小图像块大小
TRAINING_CONFIG['patch_size'] = 128
TRAINING_CONFIG['patch_step'] = 96
```

### 问题2：数据路径错误
**症状：** `❌ 图像文件夹不存在`

**解决方案：**
1. 检查数据路径是否正确
2. 确认文件夹结构符合要求
3. 检查文件权限

### 问题3：依赖包缺失
**症状：** `ModuleNotFoundError`

**解决方案：**
```python
# 在第一个代码单元中安装缺失的包
!pip install missing_package_name
```

### 问题4：训练中断
**症状：** 训练过程意外停止

**解决方案：**
- 检查模型检查点是否保存
- 从最后一个检查点恢复训练
- 检查平台的时间限制

## 📊 性能优化建议

### 数据优化
1. **预处理数据**：提前转换数据格式
2. **数据增强**：启用适当的数据增强
3. **数据过滤**：过滤空白或低质量图像块

### 训练优化
1. **学习率调度**：使用自适应学习率
2. **早停机制**：防止过拟合
3. **混合精度**：在支持的平台上启用

### 内存优化
1. **批次大小**：根据GPU内存调整
2. **图像块大小**：平衡精度和内存使用
3. **数据生成器**：使用按需加载

## 📁 输出文件说明

训练完成后，您将获得以下文件：

```
training_output/
├── models/
│   └── unet_model_universal.h5     # 训练好的模型
├── logs/
│   └── training_history.json       # 训练历史数据
├── results/
│   ├── training_history.png        # 训练曲线图
│   ├── prediction_demo.png         # 预测演示图
│   └── training_summary.json       # 训练总结
└── processed_data/                 # 预处理后的数据
    ├── images/
    └── masks/
```

## 🔄 模型部署

训练完成后，您可以：

1. **本地部署**：直接加载模型进行预测
2. **云端部署**：部署到云服务进行在线预测
3. **边缘部署**：转换为轻量级格式部署到边缘设备

**模型加载示例：**
```python
import tensorflow as tf

# 加载模型
model = tf.keras.models.load_model('path/to/unet_model_universal.h5')

# 进行预测
predictions = model.predict(your_image_data)
```

## 📞 技术支持

如果遇到问题：

1. **检查配置**：确认数据路径和训练配置正确
2. **查看日志**：仔细阅读错误信息
3. **调整参数**：根据硬件情况调整训练参数
4. **重启环境**：清理内存并重新开始

---

**最后更新：** 2025-07-17  
**兼容性：** TensorFlow 2.x, Python 3.7+  
**测试平台：** Jupyter, Colab, Kaggle, Azure ML
