import os
import sys
import torch
import platform
import tensorflow as tf
import cv2
import albumentations as A
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.model_selection import train_test_split
import imgaug as ia
import tqdm

def check_environment():
    print("=== 系统信息 ===")
    print(f"操作系统: {platform.platform()}")
    print(f"Python版本: {platform.python_version()}")
    
    print("\n=== CUDA环境 ===")
    print(f"CUDA是否可用: {'是' if torch.cuda.is_available() else '否'}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"cuDNN版本: {torch.backends.cudnn.version()}")
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    
    print("\n=== 环境变量 ===")
    cuda_path = os.environ.get('CUDA_PATH')
    print(f"CUDA_PATH: {cuda_path}")
    
    path = os.environ.get('PATH', '').split(os.pathsep)
    cuda_related = [p for p in path if 'cuda' in p.lower()]
    print("\nCUDA相关路径:")
    for p in cuda_related:
        print(f"  - {p}")
    
    print("\n=== DLL检查 ===")
    cuda_bin = os.path.join(cuda_path, 'bin') if cuda_path else None
    if cuda_bin and os.path.exists(cuda_bin):
        dlls = [f for f in os.listdir(cuda_bin) if f.endswith('.dll')]
        print(f"CUDA bin目录中的DLL文件:")
        for dll in dlls:
            print(f"  - {dll}")
    
    print("\n=== TensorFlow信息 ===")
    print(f"TensorFlow版本: {tf.__version__}")
    print("GPU设备列表:")
    for device in tf.config.list_physical_devices():
        print(f"  - {device}")

if __name__ == "__main__":
    check_environment()

# 检查 PyTorch 是否可以使用 CUDA
print("PyTorch version:", torch.__version__)
print("CUDA is available:", torch.cuda.is_available())
if torch.cuda.is_available():
    print("CUDA device:", torch.cuda.get_device_name(0))

# 检查 TensorFlow 版本
print("\nTensorFlow version:", tf.__version__)
print("GPU devices:", tf.config.list_physical_devices('GPU'))

# 检查 OpenCV 版本
print("\nOpenCV version:", cv2.__version__)