import os
import torch
import numpy as np
from segment_anything import sam_model_registry, SamPredictor
from PIL import Image
import cv2
import platform
import multiprocessing

class SAMInference:
    def __init__(self, project_config):
        self.project_config = project_config
        
        # 检测可用设备
        if torch.cuda.is_available():
            self.device = torch.device("cuda")
            self.device_name = torch.cuda.get_device_name(0)
            self.device_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"使用GPU进行SAM推理: {self.device_name} ({self.device_memory:.2f} GB)")
            
            # 设置GPU优化选项
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True
        else:
            self.device = torch.device("cpu")
            self.device_name = f"CPU ({platform.processor()})"
            print(f"未检测到GPU，使用CPU进行SAM推理: {self.device_name}")
            
            # 为CPU优化线程数
            cpu_count = multiprocessing.cpu_count()
            torch.set_num_threads(cpu_count)
            print(f"已设置PyTorch使用{cpu_count}个CPU线程进行推理")
        
        self.model = None
        self.predictor = None
    
    def initialize_model(self, model_type='vit_h'):
        """初始化SAM模型"""
        print(f"初始化SAM模型: {model_type}，设备: {self.device}")
        
        model_filename = {
            'vit_h': 'sam_vit_h_4b8939.pth',
            'vit_l': 'sam_vit_l_0b3195.pth',
            'vit_b': 'sam_vit_b_01ec64.pth'
        }.get(model_type)
        
        if not model_filename:
            raise ValueError(f"不支持的模型类型: {model_type}")
            
        model_path = os.path.join(self.project_config['model_path'], model_filename)
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        print(f"加载模型文件: {model_path}")
        
        # 记录初始内存使用情况
        if self.device.type == "cuda":
            init_memory = torch.cuda.memory_allocated(0) / (1024**3)
            print(f"模型加载前GPU内存使用: {init_memory:.2f} GB")
            
        # 加载模型
        self.model = sam_model_registry[model_type](checkpoint=model_path)
        self.model.to(device=self.device)
        self.predictor = SamPredictor(self.model)
        
        # 记录加载后内存使用情况
        if self.device.type == "cuda":
            post_memory = torch.cuda.memory_allocated(0) / (1024**3)
            print(f"模型加载后GPU内存使用: {post_memory:.2f} GB")
            print(f"模型占用内存: {post_memory - init_memory:.2f} GB")
        
        print(f"SAM模型初始化完成，使用设备: {self.device}")
        return True
        
    def process_image(self, image_path, input_points=None):
        """处理图像并进行推理"""
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        print(f"处理图像: {image_path}, 尺寸: {image.shape}")
        
        # 设置图像
        self.predictor.set_image(image)
        
        # 如果没有提供输入点，使用图像中心点
        if input_points is None:
            height, width = image.shape[:2]
            input_points = np.array([[width//2, height//2]])
            print(f"使用图像中心点 ({width//2}, {height//2}) 作为输入点")
        else:
            print(f"使用{len(input_points)}个用户提供的输入点")
            
        # 确保输入点格式正确
        input_points = np.array(input_points)
        
        # 监控内存使用
        if self.device.type == "cuda":
            memory_before = torch.cuda.memory_allocated(0) / (1024**3)
            print(f"推理前GPU内存使用: {memory_before:.2f} GB")
        
        # 根据点的数量选择处理策略
        if len(input_points) > 5:
            print(f"输入点较多({len(input_points)}个)，使用批处理策略")
            
            # 分批处理以避免GPU内存溢出
            batch_size = 3
            all_masks = []
            all_scores = []
            all_logits = []
            
            for i in range(0, len(input_points), batch_size):
                batch_points = input_points[i:i+batch_size]
                batch_labels = np.ones(len(batch_points))
                
                print(f"处理第{i+1}到{min(i+batch_size, len(input_points))}个点(共{len(input_points)}个)")
                
                batch_masks, batch_scores, batch_logits = self.predictor.predict(
                    point_coords=batch_points,
                    point_labels=batch_labels,
                    multimask_output=True
                )
                
                all_masks.append(batch_masks)
                all_scores.append(batch_scores)
                all_logits.append(batch_logits)
            
            # 合并结果
            masks = np.vstack([m for m in all_masks])
            scores = np.vstack([s for s in all_scores])
            logits = np.vstack([l for l in all_logits])
        else:
            # 直接处理所有点
            print(f"直接处理{len(input_points)}个点")
            masks, scores, logits = self.predictor.predict(
                point_coords=input_points,
                point_labels=np.ones(len(input_points)),
                multimask_output=True
            )
        
        # 监控推理后内存使用
        if self.device.type == "cuda":
            memory_after = torch.cuda.memory_allocated(0) / (1024**3)
            print(f"推理后GPU内存使用: {memory_after:.2f} GB")
            print(f"推理过程增加内存: {memory_after - memory_before:.2f} GB")
        
        # 处理结果
        if len(masks.shape) == 4:  # 多掩码输出
            print(f"模型输出{masks.shape[0]}个点，每个点{masks.shape[1]}个候选掩码")
            
            # 为每个点选择最佳掩码
            best_masks = []
            for i in range(len(scores)):
                best_idx = np.argmax(scores[i])
                best_masks.append(masks[i, best_idx])
            
            # 合并所有最佳掩码
            final_mask = np.any(best_masks, axis=0).astype(np.uint8)
            best_score = np.max(scores)
        else:
            # 单掩码输出
            print(f"模型输出单个掩码，形状: {masks.shape}")
            final_mask = masks.astype(np.uint8)
            best_score = scores[0] if hasattr(scores, "__iter__") else scores
        
        result = {
            'original_image': image,
            'masks': final_mask,
            'scores': best_score,
            'logits': logits,
            'input_points': input_points
        }
        
        print(f"SAM推理完成，返回掩码形状: {final_mask.shape}")
        return result
        
    def cleanup(self):
        """清理模型和GPU内存"""
        print("清理SAM模型资源...")
        
        if self.model is not None:
            if self.device.type == "cuda":
                print("将模型移至CPU以释放GPU内存...")
                self.model.cpu()
            del self.model
            self.model = None
            print("已释放模型")
            
        if self.predictor is not None:
            del self.predictor
            self.predictor = None
            print("已释放预测器")
            
        if self.device.type == "cuda" and torch.cuda.is_available():
            memory_before = torch.cuda.memory_allocated(0) / (1024**3)
            print(f"清理前GPU内存使用: {memory_before:.2f} GB")
            
            torch.cuda.empty_cache()
            
            memory_after = torch.cuda.memory_allocated(0) / (1024**3)
            print(f"清理后GPU内存使用: {memory_after:.2f} GB")
            print(f"释放内存: {memory_before - memory_after:.2f} GB")
        
        print("SAM模型资源清理完成")
