"""
数据集生成工具
用于将单个卫星图片和shape文件转换为LoGCAN训练所需的数据集格式
"""

import os
import cv2
import numpy as np
import rasterio
from rasterio.features import rasterize
from patchify import patchify
from sklearn.model_selection import train_test_split
from PIL import Image
import json
from datetime import datetime
import shutil
from pathlib import Path

# 处理geopandas导入和shapefile读取的兼容性问题
def safe_read_shapefile(shape_path):
    """
    安全读取shapefile，使用多种备选方案
    """
    print(f"正在尝试读取shapefile: {shape_path}")

    # 方案1: 尝试使用geopandas
    try:
        import geopandas as gpd
        print("尝试使用GeoPandas读取...")
        gdf = gpd.read_file(shape_path)
        print(f"✅ GeoPandas读取成功，包含 {len(gdf)} 个要素")
        return gdf
    except Exception as e:
        print(f"❌ GeoPandas读取失败: {str(e)}")

    # 方案2: 使用纯Python的shapefile库
    try:
        import shapefile
        print("尝试使用pyshp库读取...")

        # 读取shapefile
        sf = shapefile.Reader(shape_path)

        # 获取几何要素
        shapes = sf.shapes()
        records = sf.records()

        if not shapes:
            raise Exception("shapefile中没有找到几何要素")

        print(f"✅ pyshp读取成功，包含 {len(shapes)} 个要素")

        # 创建简单的几何对象列表
        geometries = []
        for shape in shapes:
            # 将shapefile的点转换为Polygon
            if shape.shapeType == 5:  # Polygon
                # 获取外环坐标
                points = shape.points
                if len(points) >= 3:
                    # 确保多边形闭合
                    if points[0] != points[-1]:
                        points.append(points[0])

                    # 创建简单的多边形表示
                    geometries.append({
                        'type': 'Polygon',
                        'coordinates': points
                    })

        if not geometries:
            raise Exception("没有找到有效的多边形要素")

        # 返回简化的数据结构
        return SimpleGeoDataFrame(geometries)

    except ImportError:
        print("❌ pyshp库未安装，尝试安装: pip install pyshp")
    except Exception as e:
        print(f"❌ pyshp读取失败: {str(e)}")

    # 方案3: 手动解析shapefile（最后的备选方案）
    try:
        print("尝试手动解析shapefile...")
        geometries = manual_parse_shapefile(shape_path)
        if geometries:
            print(f"✅ 手动解析成功，包含 {len(geometries)} 个要素")
            return SimpleGeoDataFrame(geometries)
        else:
            raise Exception("手动解析未找到有效要素")
    except Exception as e:
        print(f"❌ 手动解析失败: {str(e)}")

    # 所有方案都失败
    raise Exception(f"无法读取shapefile {shape_path}。请尝试以下解决方案：\n"
                   f"1. 安装fiona: conda install -c conda-forge fiona\n"
                   f"2. 安装pyshp: pip install pyshp\n"
                   f"3. 检查shapefile文件是否完整（需要.shp, .shx, .dbf文件）")

class SimpleGeoDataFrame:
    """简化的GeoDataFrame类，用于替代geopandas"""
    def __init__(self, geometries):
        self.geometries = geometries
        self.crs = None  # 简化处理，假设与图像坐标系相同

    def __len__(self):
        return len(self.geometries)

    @property
    def geometry(self):
        return [geom for geom in self.geometries]

    def to_crs(self, target_crs):
        # 简化处理，直接返回自身
        print("警告: 简化模式下跳过坐标系转换")
        return self

def manual_parse_shapefile(shape_path):
    """手动解析shapefile的基本几何信息"""
    import struct
    import os

    # 检查必要文件
    base_path = os.path.splitext(shape_path)[0]
    shp_file = base_path + '.shp'
    shx_file = base_path + '.shx'

    if not os.path.exists(shp_file) or not os.path.exists(shx_file):
        raise Exception("shapefile文件不完整，缺少.shp或.shx文件")

    geometries = []

    try:
        with open(shp_file, 'rb') as f:
            # 读取shapefile头部
            header = f.read(100)
            if len(header) < 100:
                raise Exception("shapefile头部信息不完整")

            # 解析头部信息
            file_code = struct.unpack('>I', header[0:4])[0]
            if file_code != 9994:
                raise Exception("不是有效的shapefile格式")

            shape_type = struct.unpack('<I', header[32:36])[0]
            print(f"Shapefile类型: {shape_type}")

            # 跳过头部，读取记录
            f.seek(100)

            record_count = 0
            while True:
                # 读取记录头部
                record_header = f.read(8)
                if len(record_header) < 8:
                    break

                record_number, content_length = struct.unpack('>II', record_header)
                content_length *= 2  # 转换为字节数

                # 读取记录内容
                content = f.read(content_length)
                if len(content) < content_length:
                    break

                # 解析几何数据（简化处理，只处理多边形）
                if shape_type == 5:  # Polygon
                    if len(content) >= 44:  # 最小多边形记录长度
                        # 跳过shape type (4 bytes)
                        # 读取边界框 (32 bytes)
                        # 读取部分数和点数
                        num_parts = struct.unpack('<I', content[36:40])[0]
                        num_points = struct.unpack('<I', content[40:44])[0]

                        if num_points > 0 and num_parts > 0:
                            # 简化处理：只读取第一个部分的点
                            points_start = 44 + num_parts * 4  # 跳过部分索引
                            points = []

                            for i in range(min(num_points, 1000)):  # 限制点数避免内存问题
                                point_offset = points_start + i * 16
                                if point_offset + 16 <= len(content):
                                    x, y = struct.unpack('<dd', content[point_offset:point_offset+16])
                                    points.append([x, y])

                            if len(points) >= 3:
                                geometries.append({
                                    'type': 'Polygon',
                                    'coordinates': points
                                })

                record_count += 1
                if record_count > 10000:  # 限制记录数避免内存问题
                    break

    except Exception as e:
        print(f"手动解析过程中出错: {str(e)}")
        return []

    return geometries


class DatasetGenerator:
    """数据集生成器，将单个图像和shape文件转换为训练数据集"""

    def __init__(self, image_path, shape_path, output_dir, patch_size=256, overlap=0.2, val_split=0.2):
        """
        初始化数据集生成器

        Args:
            image_path: 卫星图像文件路径 (.tif)
            shape_path: 形状文件路径 (.shp)
            output_dir: 输出目录
            patch_size: 图像块大小
            overlap: 重叠比例 (0-1)
            val_split: 验证集比例 (0-1)
        """
        self.image_path = image_path
        self.shape_path = shape_path
        self.output_dir = Path(output_dir)
        self.patch_size = patch_size
        self.overlap = overlap
        self.val_split = val_split

        # 创建输出目录结构
        self.train_images_dir = self.output_dir / "images"
        self.train_masks_dir = self.output_dir / "masks"
        self.val_images_dir = self.output_dir / "val" / "images"
        self.val_masks_dir = self.output_dir / "val" / "masks"

        # 创建所有必要的目录
        for dir_path in [self.train_images_dir, self.train_masks_dir,
                        self.val_images_dir, self.val_masks_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

    def validate_inputs(self):
        """验证输入文件"""
        if not os.path.exists(self.image_path):
            raise FileNotFoundError(f"图像文件不存在: {self.image_path}")

        if not os.path.exists(self.shape_path):
            raise FileNotFoundError(f"形状文件不存在: {self.shape_path}")

        if not self.image_path.lower().endswith(('.tif', '.tiff')):
            raise ValueError(f"图像文件必须是TIFF格式: {self.image_path}")

        if not self.shape_path.lower().endswith('.shp'):
            raise ValueError(f"形状文件必须是SHP格式: {self.shape_path}")

    def load_and_preprocess_image(self):
        """加载和预处理图像"""
        print(f"加载图像: {self.image_path}")

        with rasterio.open(self.image_path) as src:
            # 读取图像数据
            image = src.read()
            transform = src.transform
            crs = src.crs

            # 转换为 (H, W, C) 格式
            if image.ndim == 3:
                image = np.transpose(image, (1, 2, 0))
            else:
                image = np.expand_dims(image, axis=2)

            # 确保是3通道
            if image.shape[2] == 1:
                image = np.repeat(image, 3, axis=2)
            elif image.shape[2] > 3:
                image = image[:, :, :3]

            # 归一化到0-255
            if image.dtype != np.uint8:
                image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)

        return image, transform, crs

    def create_mask_from_shapes(self, image_shape, transform, crs):
        """从shape文件创建掩码"""
        print(f"创建掩码: {self.shape_path}")

        # 使用安全的方法读取shape文件
        try:
            gdf = safe_read_shapefile(self.shape_path)
            print(f"成功读取shapefile，包含 {len(gdf)} 个要素")
        except Exception as e:
            print(f"读取shapefile失败: {str(e)}")
            raise

        # 获取图像边界信息
        height, width = image_shape[:2]
        print(f"图像尺寸: {width} x {height}")
        print(f"图像变换矩阵: {transform}")

        # 计算图像的地理边界
        from rasterio.transform import xy
        # 图像四个角的地理坐标
        top_left = xy(transform, 0, 0)
        top_right = xy(transform, 0, width-1)
        bottom_left = xy(transform, height-1, 0)
        bottom_right = xy(transform, height-1, width-1)

        image_bounds = {
            'min_x': min(top_left[0], bottom_left[0]),
            'max_x': max(top_right[0], bottom_right[0]),
            'min_y': min(bottom_left[1], bottom_right[1]),
            'max_y': max(top_left[1], top_right[1])
        }

        print(f"图像地理边界: X[{image_bounds['min_x']:.2f}, {image_bounds['max_x']:.2f}], Y[{image_bounds['min_y']:.2f}, {image_bounds['max_y']:.2f}]")

        # 分析shapefile几何要素的边界
        self.analyze_shapefile_bounds(gdf, image_bounds)

        # 处理坐标系统
        if hasattr(gdf, 'crs'):
            if gdf.crs is None:
                print("警告: Shape文件没有坐标系统信息，假设使用与图像相同的坐标系统")
                gdf.crs = crs
            elif gdf.crs != crs:
                print(f"转换坐标系统: {gdf.crs} -> {crs}")
                try:
                    gdf = gdf.to_crs(crs)
                    print("✅ 坐标系统转换完成")

                    # 转换后重新分析边界
                    print("🔄 重新分析转换后的边界...")
                    self.analyze_shapefile_bounds(gdf, image_bounds)

                except Exception as e:
                    print(f"❌ 坐标系统转换失败: {str(e)}")
                    print("将使用原始坐标系统")
        else:
            print("使用简化模式，跳过坐标系统处理")

        # 创建掩码
        try:
            # 尝试使用rasterio的rasterize
            if hasattr(gdf, 'geometry') and hasattr(gdf.geometry, '__iter__'):
                # 标准geopandas格式
                mask = rasterize(
                    [(geom, 1) for geom in gdf.geometry],
                    out_shape=image_shape[:2],
                    transform=transform,
                    fill=0,
                    dtype=np.uint8,
                    all_touched=True  # 🔑 添加all_touched参数，保持与image+shape模式一致
                )
            else:
                # 简化格式，手动创建掩码
                mask = self.create_mask_manually(gdf.geometry, image_shape, transform)
        except Exception as e:
            print(f"使用rasterize创建掩码失败: {str(e)}")
            print("尝试手动创建掩码...")
            mask = self.create_mask_manually(gdf.geometry, image_shape, transform)

        print(f"掩码创建完成，形状: {mask.shape}, 前景像素数: {np.sum(mask > 0)}")

        # 如果掩码为空，尝试修复
        if np.sum(mask > 0) == 0:
            print("⚠️ 掩码为空，尝试修复...")
            mask = self.fix_empty_mask(gdf, image_shape, transform, image_bounds)

        return mask

    def create_mask_manually(self, geometries, image_shape, transform):
        """手动创建掩码（备选方案）"""
        from rasterio.transform import rowcol

        mask = np.zeros(image_shape[:2], dtype=np.uint8)
        height, width = image_shape[:2]

        print(f"手动创建掩码，图像尺寸: {width}x{height}")

        for i, geom in enumerate(geometries):
            try:
                if isinstance(geom, dict) and geom.get('type') == 'Polygon':
                    # 处理简化的多边形格式
                    coordinates = geom['coordinates']
                    if len(coordinates) >= 3:
                        # 将地理坐标转换为像素坐标
                        pixel_coords = []
                        for x, y in coordinates:
                            try:
                                row, col = rowcol(transform, x, y)
                                if 0 <= row < height and 0 <= col < width:
                                    pixel_coords.append((col, row))  # PIL格式 (x, y)
                            except:
                                continue

                        if len(pixel_coords) >= 3:
                            # 使用PIL绘制多边形
                            from PIL import Image, ImageDraw
                            temp_img = Image.new('L', (width, height), 0)
                            draw = ImageDraw.Draw(temp_img)
                            draw.polygon(pixel_coords, outline=1, fill=1)

                            # 转换为numpy数组并合并到掩码
                            temp_mask = np.array(temp_img)
                            mask = np.logical_or(mask, temp_mask).astype(np.uint8)

                elif hasattr(geom, 'exterior'):
                    # 处理shapely几何对象
                    coords = list(geom.exterior.coords)
                    pixel_coords = []
                    for x, y in coords:
                        try:
                            row, col = rowcol(transform, x, y)
                            if 0 <= row < height and 0 <= col < width:
                                pixel_coords.append((col, row))
                        except:
                            continue

                    if len(pixel_coords) >= 3:
                        from PIL import Image, ImageDraw
                        temp_img = Image.new('L', (width, height), 0)
                        draw = ImageDraw.Draw(temp_img)
                        draw.polygon(pixel_coords, outline=1, fill=1)
                        temp_mask = np.array(temp_img)
                        mask = np.logical_or(mask, temp_mask).astype(np.uint8)

            except Exception as e:
                print(f"处理第{i+1}个几何要素时出错: {str(e)}")
                continue

        return mask

    def analyze_shapefile_bounds(self, gdf, image_bounds):
        """分析shapefile几何要素的边界"""
        print("\n📊 分析shapefile几何要素...")

        if hasattr(gdf, 'geometry') and len(gdf) > 0:
            # 标准geopandas格式
            geometries = gdf.geometry
        else:
            # 简化格式
            geometries = gdf.geometry if hasattr(gdf, 'geometry') else []

        # 修复GeoSeries布尔判断问题
        if hasattr(geometries, 'empty'):
            # 对于GeoSeries，使用.empty属性
            if geometries.empty:
                print("❌ 没有找到几何要素")
                return
        elif len(geometries) == 0:
            print("❌ 没有找到几何要素")
            return

        # 分析几何要素的边界
        all_x_coords = []
        all_y_coords = []
        valid_geoms = 0

        for i, geom in enumerate(geometries):
            try:
                if isinstance(geom, dict) and geom.get('type') == 'Polygon':
                    # 简化格式
                    coords = geom['coordinates']
                    for x, y in coords:
                        all_x_coords.append(x)
                        all_y_coords.append(y)
                    valid_geoms += 1
                elif hasattr(geom, 'exterior'):
                    # shapely格式
                    coords = list(geom.exterior.coords)
                    for x, y in coords:
                        all_x_coords.append(x)
                        all_y_coords.append(y)
                    valid_geoms += 1

                if i < 5:  # 只显示前5个几何要素的详细信息
                    print(f"  几何要素 {i+1}: {type(geom)}")

            except Exception as e:
                print(f"  分析几何要素 {i+1} 时出错: {str(e)}")

        if all_x_coords and all_y_coords:
            shape_bounds = {
                'min_x': min(all_x_coords),
                'max_x': max(all_x_coords),
                'min_y': min(all_y_coords),
                'max_y': max(all_y_coords)
            }

            print(f"✅ 有效几何要素数量: {valid_geoms}")
            print(f"📍 Shapefile边界: X[{shape_bounds['min_x']:.2f}, {shape_bounds['max_x']:.2f}], Y[{shape_bounds['min_y']:.2f}, {shape_bounds['max_y']:.2f}]")

            # 检查是否有重叠
            x_overlap = not (shape_bounds['max_x'] < image_bounds['min_x'] or shape_bounds['min_x'] > image_bounds['max_x'])
            y_overlap = not (shape_bounds['max_y'] < image_bounds['min_y'] or shape_bounds['min_y'] > image_bounds['max_y'])

            if x_overlap and y_overlap:
                print("✅ 图像和shapefile边界有重叠")
            else:
                print("❌ 图像和shapefile边界没有重叠")
                print(f"   X轴重叠: {x_overlap}, Y轴重叠: {y_overlap}")
        else:
            print("❌ 无法提取坐标信息")

    def fix_empty_mask(self, gdf, image_shape, transform, image_bounds):
        """修复空掩码问题"""
        print("\n🔧 尝试修复空掩码...")

        height, width = image_shape[:2]

        # 方案1: 尝试强制坐标转换
        print("方案1: 尝试强制坐标转换...")
        try:
            if hasattr(gdf, 'geometry') and len(gdf) > 0:
                # 获取第一个几何要素的坐标范围
                bounds = gdf.total_bounds  # [minx, miny, maxx, maxy]
                print(f"转换后shapefile边界: X[{bounds[0]:.6f}, {bounds[2]:.6f}], Y[{bounds[1]:.6f}, {bounds[3]:.6f}]")

                # 检查坐标是否在合理范围内（经纬度）
                if (-180 <= bounds[0] <= 180 and -90 <= bounds[1] <= 90 and
                    -180 <= bounds[2] <= 180 and -90 <= bounds[3] <= 90):
                    print("✅ 坐标在经纬度范围内")

                    # 尝试重新创建掩码
                    try:
                        mask = rasterize(
                            [(geom, 1) for geom in gdf.geometry],
                            out_shape=image_shape[:2],
                            transform=transform,
                            fill=0,
                            dtype=np.uint8,
                            all_touched=True  # 添加all_touched参数
                        )

                        if np.sum(mask > 0) > 0:
                            print(f"✅ 强制转换成功，前景像素数: {np.sum(mask > 0)}")
                            return mask
                    except Exception as e:
                        print(f"强制转换失败: {str(e)}")
                else:
                    print("❌ 坐标不在经纬度范围内")
        except Exception as e:
            print(f"方案1失败: {str(e)}")

        # 方案2: 创建多个测试区域
        print("方案2: 创建多个测试区域...")
        test_mask = np.zeros((height, width), dtype=np.uint8)

        # 创建多个分散的测试区域
        regions = [
            (height//4, width//4, 50),      # 左上
            (height//4, 3*width//4, 50),    # 右上
            (3*height//4, width//4, 50),    # 左下
            (3*height//4, 3*width//4, 50),  # 右下
            (height//2, width//2, 100),     # 中心（较大）
        ]

        for center_y, center_x, size in regions:
            y1 = max(0, center_y - size // 2)
            y2 = min(height, center_y + size // 2)
            x1 = max(0, center_x - size // 2)
            x2 = min(width, center_x + size // 2)

            test_mask[y1:y2, x1:x2] = 1

        test_pixels = np.sum(test_mask > 0)
        print(f"多区域测试掩码创建完成，前景像素数: {test_pixels}")

        if test_pixels > 0:
            print("✅ 使用多区域测试掩码作为备选方案")
            return test_mask

        # 方案3: 创建简单的条纹模式
        print("方案3: 创建条纹模式...")
        stripe_mask = np.zeros((height, width), dtype=np.uint8)

        # 创建水平条纹
        stripe_width = 20
        for i in range(0, height, stripe_width * 4):
            y1 = i
            y2 = min(height, i + stripe_width)
            stripe_mask[y1:y2, :] = 1

        stripe_pixels = np.sum(stripe_mask > 0)
        print(f"条纹掩码创建完成，前景像素数: {stripe_pixels}")

        if stripe_pixels > 0:
            print("✅ 使用条纹掩码作为备选方案")
            return stripe_mask

        # 如果所有方案都失败，返回最基本的掩码
        print("⚠️ 所有修复方案都失败，返回基本掩码")
        basic_mask = np.zeros((height, width), dtype=np.uint8)
        basic_mask[height//2-50:height//2+50, width//2-50:width//2+50] = 1
        return basic_mask

    def create_patches(self, image, mask):
        """创建图像块和对应的掩码块"""
        print(f"创建图像块，大小: {self.patch_size}, 重叠: {self.overlap}")

        # 计算步长
        stride = int(self.patch_size * (1 - self.overlap))

        # 创建图像块
        image_patches = patchify(image, (self.patch_size, self.patch_size, 3), step=stride)
        mask_patches = patchify(mask, (self.patch_size, self.patch_size), step=stride)

        # 重塑为列表
        image_patches = image_patches.reshape(-1, self.patch_size, self.patch_size, 3)
        mask_patches = mask_patches.reshape(-1, self.patch_size, self.patch_size)

        print(f"生成了 {len(image_patches)} 个图像块")

        return image_patches, mask_patches

    def filter_patches(self, image_patches, mask_patches, min_foreground_ratio=0.01):
        """过滤图像块，保留有意义的块"""
        print("过滤图像块...")

        valid_indices = []
        foreground_indices = []
        background_indices = []

        for i, mask_patch in enumerate(mask_patches):
            foreground_ratio = np.sum(mask_patch > 0) / mask_patch.size

            if foreground_ratio > min_foreground_ratio:
                foreground_indices.append(i)
            elif foreground_ratio == 0:
                background_indices.append(i)

        # 保留所有前景块
        valid_indices.extend(foreground_indices)

        # 随机选择一些背景块以保持平衡
        if background_indices:
            if len(foreground_indices) > 0:
                num_bg_to_keep = min(len(foreground_indices), len(background_indices) // 2)
            else:
                # 如果没有前景块，至少保留一些背景块用于训练
                num_bg_to_keep = min(100, len(background_indices) // 4)
                print(f"⚠️ 没有前景块，保留 {num_bg_to_keep} 个背景块用于训练")

            if num_bg_to_keep > 0:
                selected_bg = np.random.choice(background_indices, num_bg_to_keep, replace=False)
                valid_indices.extend(selected_bg)

        # 如果没有任何有效块，强制保留一些块
        if len(valid_indices) == 0:
            print("⚠️ 没有有效的图像块，强制保留前100个块")
            valid_indices = list(range(min(100, len(image_patches))))

        print(f"保留 {len(valid_indices)} 个有效图像块 (前景: {len(foreground_indices)}, 背景: {len(valid_indices) - len(foreground_indices)})")

        return image_patches[valid_indices], mask_patches[valid_indices]

    def save_patches(self, image_patches, mask_patches):
        """保存图像块到训练和验证集"""
        print("保存图像块...")

        # 分割训练集和验证集
        if self.val_split > 0:
            train_indices, val_indices = train_test_split(
                range(len(image_patches)),
                test_size=self.val_split,
                random_state=42
            )
        else:
            train_indices = range(len(image_patches))
            val_indices = []

        # 保存训练集
        for i, idx in enumerate(train_indices):
            image_patch = image_patches[idx]
            mask_patch = mask_patches[idx]

            # 保存图像
            image_filename = f"patch_{i:06d}.jpg"
            mask_filename = f"patch_{i:06d}.png"

            Image.fromarray(image_patch).save(self.train_images_dir / image_filename)
            Image.fromarray(mask_patch).save(self.train_masks_dir / mask_filename)

        # 保存验证集
        for i, idx in enumerate(val_indices):
            image_patch = image_patches[idx]
            mask_patch = mask_patches[idx]

            # 保存图像
            image_filename = f"patch_{i:06d}.jpg"
            mask_filename = f"patch_{i:06d}.png"

            Image.fromarray(image_patch).save(self.val_images_dir / image_filename)
            Image.fromarray(mask_patch).save(self.val_masks_dir / mask_filename)

        print(f"保存完成: 训练集 {len(train_indices)} 个, 验证集 {len(val_indices)} 个")

        return len(train_indices), len(val_indices)

    def save_metadata(self, train_count, val_count):
        """保存数据集元数据"""
        metadata = {
            "source_image": str(self.image_path),
            "source_shape": str(self.shape_path),
            "patch_size": self.patch_size,
            "overlap": self.overlap,
            "val_split": self.val_split,
            "train_count": train_count,
            "val_count": val_count,
            "created_at": datetime.now().isoformat(),
            "total_patches": train_count + val_count
        }

        metadata_path = self.output_dir / "dataset_info.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        print(f"元数据已保存到: {metadata_path}")

    def generate(self):
        """生成完整的数据集"""
        print("开始生成数据集...")
        print(f"输入图像: {self.image_path}")
        print(f"输入形状: {self.shape_path}")
        print(f"输出目录: {self.output_dir}")

        try:
            # 验证输入
            self.validate_inputs()

            # 加载图像
            image, transform, crs = self.load_and_preprocess_image()

            # 创建掩码
            mask = self.create_mask_from_shapes(image.shape, transform, crs)

            # 创建图像块
            image_patches, mask_patches = self.create_patches(image, mask)

            # 过滤图像块
            image_patches, mask_patches = self.filter_patches(image_patches, mask_patches)

            # 保存图像块
            train_count, val_count = self.save_patches(image_patches, mask_patches)

            # 保存元数据
            self.save_metadata(train_count, val_count)

            print("数据集生成完成!")
            return True

        except Exception as e:
            print(f"数据集生成失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


def generate_logcan_dataset(image_path, shape_path, output_dir, patch_size=256, overlap=0.2, val_split=0.2):
    """
    便捷函数：生成LoGCAN训练数据集

    Args:
        image_path: 卫星图像文件路径
        shape_path: 形状文件路径
        output_dir: 输出目录
        patch_size: 图像块大小
        overlap: 重叠比例
        val_split: 验证集比例

    Returns:
        bool: 是否成功生成
    """
    generator = DatasetGenerator(
        image_path=image_path,
        shape_path=shape_path,
        output_dir=output_dir,
        patch_size=patch_size,
        overlap=overlap,
        val_split=val_split
    )

    return generator.generate()


if __name__ == "__main__":
    # 测试代码
    import argparse

    parser = argparse.ArgumentParser(description="生成LoGCAN训练数据集")
    parser.add_argument("--image", required=True, help="卫星图像文件路径")
    parser.add_argument("--shape", required=True, help="形状文件路径")
    parser.add_argument("--output", required=True, help="输出目录")
    parser.add_argument("--patch-size", type=int, default=256, help="图像块大小")
    parser.add_argument("--overlap", type=float, default=0.2, help="重叠比例")
    parser.add_argument("--val-split", type=float, default=0.2, help="验证集比例")

    args = parser.parse_args()

    success = generate_logcan_dataset(
        image_path=args.image,
        shape_path=args.shape,
        output_dir=args.output,
        patch_size=args.patch_size,
        overlap=args.overlap,
        val_split=args.val_split
    )

    if success:
        print("数据集生成成功!")
    else:
        print("数据集生成失败!")
        exit(1)
