#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集管理对话框
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QGroupBox, QFormLayout, QLineEdit,
                            QFileDialog, QMessageBox, QTabWidget, QWidget,
                            QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QProgressBar)
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtGui import QFont

# 导入工具类
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from tools.dataset_manager import DatasetManager

class DatasetScanWorker(QThread):
    """数据集扫描工作线程"""

    progress_updated = pyqtSignal(str)
    finished = pyqtSignal(dict)

    def __init__(self, dataset_path):
        super().__init__()
        self.dataset_path = dataset_path

    def run(self):
        """执行扫描任务"""
        try:
            manager = DatasetManager(self.dataset_path)

            self.progress_updated.emit("🔍 扫描数据集文件...")
            image_count, mask_count = manager.scan_dataset()

            self.progress_updated.emit("📊 计算统计信息...")
            info = manager.get_dataset_info()

            self.progress_updated.emit("🔍 验证数据集...")
            validation = manager.validate_dataset()

            result = {
                'info': info,
                'validation': validation,
                'image_count': image_count,
                'mask_count': mask_count
            }

            self.finished.emit(result)

        except Exception as e:
            self.progress_updated.emit(f"❌ 扫描失败: {str(e)}")
            self.finished.emit({})

class DatasetManagementDialog(QDialog):
    """数据集管理对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据集管理")
        self.setMinimumSize(800, 600)
        self.dataset_manager = None
        self.scan_worker = None
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 数据集路径选择
        path_group = QGroupBox("数据集路径")
        path_layout = QFormLayout()

        path_input_layout = QHBoxLayout()
        self.dataset_path = QLineEdit()
        default_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dataset')
        self.dataset_path.setText(default_path)

        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.browse_dataset_path)

        scan_button = QPushButton("扫描数据集")
        scan_button.clicked.connect(self.scan_dataset)

        path_input_layout.addWidget(self.dataset_path)
        path_input_layout.addWidget(browse_button)
        path_input_layout.addWidget(scan_button)

        path_layout.addRow("数据集目录:", path_input_layout)
        path_group.setLayout(path_layout)
        layout.addWidget(path_group)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 数据集信息选项卡
        info_tab = self.create_info_tab()
        tab_widget.addTab(info_tab, "数据集信息")

        # 文件列表选项卡
        files_tab = self.create_files_tab()
        tab_widget.addTab(files_tab, "文件列表")

        # 下载历史选项卡
        history_tab = self.create_history_tab()
        tab_widget.addTab(history_tab, "下载历史")

        layout.addWidget(tab_widget)

        # 按钮布局
        button_layout = QHBoxLayout()

        refresh_button = QPushButton("刷新")
        refresh_button.clicked.connect(self.scan_dataset)

        export_button = QPushButton("导出报告")
        export_button.clicked.connect(self.export_report)

        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)

        button_layout.addWidget(refresh_button)
        button_layout.addWidget(export_button)
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

    def create_info_tab(self):
        """创建数据集信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout()

        self.dataset_name_label = QLabel("未知")
        self.created_date_label = QLabel("未知")
        self.last_updated_label = QLabel("未知")
        self.version_label = QLabel("未知")

        basic_layout.addRow("数据集名称:", self.dataset_name_label)
        basic_layout.addRow("创建日期:", self.created_date_label)
        basic_layout.addRow("最后更新:", self.last_updated_label)
        basic_layout.addRow("版本:", self.version_label)

        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)

        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QFormLayout()

        self.image_count_label = QLabel("0")
        self.mask_count_label = QLabel("0")
        self.paired_count_label = QLabel("0")
        self.avg_size_label = QLabel("未知")
        self.foreground_ratio_label = QLabel("未知")

        stats_layout.addRow("图像文件数:", self.image_count_label)
        stats_layout.addRow("Mask文件数:", self.mask_count_label)
        stats_layout.addRow("有效配对数:", self.paired_count_label)
        stats_layout.addRow("平均图像尺寸:", self.avg_size_label)
        stats_layout.addRow("前景比例:", self.foreground_ratio_label)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # 验证结果组
        validation_group = QGroupBox("数据集验证")
        validation_layout = QVBoxLayout()

        self.validation_text = QTextEdit()
        self.validation_text.setMaximumHeight(150)
        self.validation_text.setFont(QFont("Consolas", 9))
        validation_layout.addWidget(self.validation_text)

        validation_group.setLayout(validation_layout)
        layout.addWidget(validation_group)

        # 扫描进度组
        progress_group = QGroupBox("扫描进度")
        progress_layout = QVBoxLayout()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setFont(QFont("Consolas", 9))
        self.status_text.setPlaceholderText("扫描状态将在这里显示...")
        progress_layout.addWidget(self.status_text)

        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)

        layout.addStretch()
        return widget

    def create_files_tab(self):
        """创建文件列表选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 文件列表表格
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(4)
        self.files_table.setHorizontalHeaderLabels(["文件名", "类型", "大小", "状态"])

        # 设置表格属性
        header = self.files_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        layout.addWidget(self.files_table)

        # 文件操作按钮
        file_button_layout = QHBoxLayout()

        open_folder_button = QPushButton("打开文件夹")
        open_folder_button.clicked.connect(self.open_dataset_folder)

        validate_files_button = QPushButton("验证文件")
        validate_files_button.clicked.connect(self.validate_files)

        file_button_layout.addWidget(open_folder_button)
        file_button_layout.addWidget(validate_files_button)
        file_button_layout.addStretch()

        layout.addLayout(file_button_layout)

        return widget



    def create_history_tab(self):
        """创建下载历史选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 下载历史组
        history_group = QGroupBox("下载历史记录")
        history_layout = QVBoxLayout()

        # 完整的说明文本，确保能完全显示
        info_label = QLabel("""
<b>下载历史记录</b><br>
• 记录所有通过Shape文件范围下载的数据集<br>
• 显示下载时间、数据源、成功率等信息<br>
• 帮助跟踪数据集的来源和质量<br><br>
<b>注意：</b>只有通过新的Shape文件下载功能生成的数据集才会有历史记录。
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { background-color: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 10px; }")
        # 不设置最大高度限制，让说明文本自适应内容高度
        info_label.setSizePolicy(info_label.sizePolicy().horizontalPolicy(), info_label.sizePolicy().Minimum)
        history_layout.addWidget(info_label)

        self.history_table = QTableWidget()
        self.history_table.setColumnCount(5)
        self.history_table.setHorizontalHeaderLabels(["时间", "类型", "源文件", "瓦片数", "成功率"])

        # 设置表格较小的高度，优先保证说明文本显示完整
        self.history_table.setMinimumHeight(180)  # 进一步减少表格高度
        self.history_table.setMaximumHeight(300)  # 设置最大高度，防止占用过多空间

        # 设置表格属性
        history_header = self.history_table.horizontalHeader()
        history_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        history_header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        history_header.setSectionResizeMode(2, QHeaderView.Stretch)
        history_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        history_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

        # 设置表格行高，让更多行能显示
        self.history_table.verticalHeader().setDefaultSectionSize(25)  # 减少行高
        self.history_table.verticalHeader().setMinimumSectionSize(20)  # 最小行高
        self.history_table.setAlternatingRowColors(True)  # 交替行颜色，便于阅读

        history_layout.addWidget(self.history_table)

        # 历史记录操作按钮
        history_button_layout = QHBoxLayout()

        clear_history_button = QPushButton("清空历史记录")
        clear_history_button.clicked.connect(self.clear_download_history)

        export_history_button = QPushButton("导出历史记录")
        export_history_button.clicked.connect(self.export_download_history)

        history_button_layout.addWidget(clear_history_button)
        history_button_layout.addWidget(export_history_button)
        history_button_layout.addStretch()

        history_layout.addLayout(history_button_layout)
        history_group.setLayout(history_layout)
        layout.addWidget(history_group)

        # 不添加拉伸，让历史记录组占用所有可用空间
        return widget

    def browse_dataset_path(self):
        """浏览数据集路径"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择数据集目录",
            self.dataset_path.text()
        )
        if dir_path:
            self.dataset_path.setText(dir_path)

    def scan_dataset(self):
        """扫描数据集"""
        dataset_path = self.dataset_path.text().strip()
        if not dataset_path:
            QMessageBox.warning(self, "警告", "请选择数据集目录")
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 清空状态
        self.status_text.clear()
        self.status_text.append("🔄 开始扫描数据集...")

        # 创建扫描线程
        self.scan_worker = DatasetScanWorker(dataset_path)
        self.scan_worker.progress_updated.connect(self.update_scan_progress)
        self.scan_worker.finished.connect(self.scan_finished)
        self.scan_worker.start()

    def update_scan_progress(self, message):
        """更新扫描进度"""
        self.status_text.append(message)
        self.status_text.verticalScrollBar().setValue(
            self.status_text.verticalScrollBar().maximum()
        )

    def scan_finished(self, result):
        """扫描完成处理"""
        self.progress_bar.setVisible(False)

        if not result:
            self.status_text.append("❌ 扫描失败")
            return

        self.status_text.append("✅ 扫描完成")

        # 更新界面信息
        self.update_info_display(result)
        self.update_files_display(result)
        self.update_history_display(result)

    def update_info_display(self, result):
        """更新信息显示"""
        info = result.get('info', {})
        validation = result.get('validation', {})

        # 更新基本信息
        self.dataset_name_label.setText(info.get('dataset_name', '未知'))
        self.created_date_label.setText(info.get('created_date', '未知')[:19])
        self.last_updated_label.setText(info.get('last_updated', '未知')[:19])
        self.version_label.setText(info.get('version', '未知'))

        # 更新统计信息
        self.image_count_label.setText(str(info.get('image_count', 0)))
        self.mask_count_label.setText(str(info.get('mask_count', 0)))
        self.paired_count_label.setText(str(validation.get('paired_count', 0)))

        # 更新平均尺寸
        stats = info.get('statistics', {})
        avg_size = stats.get('average_image_size', [0, 0])
        self.avg_size_label.setText(f"{avg_size[0]} x {avg_size[1]}")

        # 更新前景比例
        fg_ratio = stats.get('foreground_ratio', 0)
        self.foreground_ratio_label.setText(f"{fg_ratio*100:.1f}%" if fg_ratio else "未知")

        # 更新验证结果
        self.validation_text.clear()
        if validation.get('valid', False):
            self.validation_text.append("✅ 数据集验证通过")
        else:
            self.validation_text.append("❌ 数据集验证失败")

        for error in validation.get('errors', []):
            self.validation_text.append(f"❌ {error}")

        for warning in validation.get('warnings', []):
            self.validation_text.append(f"⚠️ {warning}")

    def update_files_display(self, result):
        """更新文件显示"""
        # 清空表格
        self.files_table.setRowCount(0)

        dataset_path = Path(self.dataset_path.text())
        image_dir = dataset_path / "image"
        mask_dir = dataset_path / "mask"

        files_info = []

        # 收集图像文件信息
        if image_dir.exists():
            for file_path in image_dir.glob("*.tif"):
                size = file_path.stat().st_size
                files_info.append({
                    'name': file_path.name,
                    'type': '图像',
                    'size': self.format_file_size(size),
                    'status': '正常'
                })

        # 收集mask文件信息
        if mask_dir.exists():
            for file_path in mask_dir.glob("*.tif"):
                size = file_path.stat().st_size
                files_info.append({
                    'name': file_path.name,
                    'type': 'Mask',
                    'size': self.format_file_size(size),
                    'status': '正常'
                })

        # 填充表格
        self.files_table.setRowCount(len(files_info))
        for row, file_info in enumerate(files_info):
            self.files_table.setItem(row, 0, QTableWidgetItem(file_info['name']))
            self.files_table.setItem(row, 1, QTableWidgetItem(file_info['type']))
            self.files_table.setItem(row, 2, QTableWidgetItem(file_info['size']))
            self.files_table.setItem(row, 3, QTableWidgetItem(file_info['status']))

    def update_history_display(self, result):
        """更新下载历史显示"""
        info = result.get('info', {})
        history = info.get('download_history', [])

        # 清空表格
        self.history_table.setRowCount(len(history))

        for row, record in enumerate(history):
            timestamp = record.get('timestamp', '')[:19]
            source_type = record.get('source_type', '')
            source_path = Path(record.get('source_path', '')).name
            tile_count = str(record.get('tile_count', 0))
            success_rate = f"{record.get('success_rate', 0)*100:.1f}%"

            self.history_table.setItem(row, 0, QTableWidgetItem(timestamp))
            self.history_table.setItem(row, 1, QTableWidgetItem(source_type))
            self.history_table.setItem(row, 2, QTableWidgetItem(source_path))
            self.history_table.setItem(row, 3, QTableWidgetItem(tile_count))
            self.history_table.setItem(row, 4, QTableWidgetItem(success_rate))

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def open_dataset_folder(self):
        """打开数据集文件夹"""
        dataset_path = self.dataset_path.text().strip()
        if dataset_path and os.path.exists(dataset_path):
            os.startfile(dataset_path)
        else:
            QMessageBox.warning(self, "警告", "数据集目录不存在")

    def validate_files(self):
        """验证文件"""
        self.scan_dataset()  # 重新扫描以验证

    def clear_download_history(self):
        """清空下载历史记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有下载历史记录吗？\n\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                dataset_path = self.dataset_path.text().strip()
                if dataset_path:
                    manager = DatasetManager(dataset_path)
                    manager.metadata["download_history"] = []
                    manager.save_metadata()

                    # 刷新显示
                    self.scan_dataset()

                    QMessageBox.information(self, "成功", "下载历史记录已清空")
                else:
                    QMessageBox.warning(self, "警告", "请先选择数据集目录")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"清空历史记录失败：\n{str(e)}")

    def export_download_history(self):
        """导出下载历史记录"""
        try:
            dataset_path = self.dataset_path.text().strip()
            if not dataset_path:
                QMessageBox.warning(self, "警告", "请先选择数据集目录")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出下载历史",
                f"download_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                manager = DatasetManager(dataset_path)
                history = manager.metadata.get("download_history", [])

                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(history, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "成功", f"下载历史已导出到：\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出历史记录失败：\n{str(e)}")

    def export_report(self):
        """导出报告"""
        dataset_path = self.dataset_path.text().strip()
        if not dataset_path:
            QMessageBox.warning(self, "警告", "请先选择数据集目录")
            return

        try:
            manager = DatasetManager(dataset_path)
            report_path = manager.export_dataset_report()

            QMessageBox.information(
                self, "成功",
                f"数据集报告已导出：\n{report_path}"
            )

            self.status_text.append(f"📄 报告已导出: {report_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出报告失败：\n{str(e)}")








    def closeEvent(self, event):
        """关闭事件处理"""
        if self.scan_worker and self.scan_worker.isRunning():
            self.scan_worker.terminate()
        event.accept()