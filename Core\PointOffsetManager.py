import numpy as np
from shapely.geometry import Point, Polygon
import geopandas as gpd
from rtree import index
from scipy.spatial import KDTree
import os
import json
import math
from typing import List, Tuple, Optional, Dict, Any
import logging
import traceback

class PointOffsetManager:
    def __init__(self):
        """初始化点偏移管理器"""
        self.obstacles = None  # 障碍物多边形
        self.grid_points = None  # 网格点
        self.grid_kdtree = None  # 网格点KD树
        self.spatial_index = None  # 障碍物空间索引
        self.points_in_obstacles = []  # 在障碍物内的点
        self.max_offset_distance = 100  # 最大偏移距离（米）
        self.occupied_grid_points = set()  # 存储已占用的网格点位置
        
        # 新增：存储偏移前后的桩号和坐标信息
        self.original_grid_numbers = {}  # 存储偏移前的桩号 {point_id: (line_number, stake_number)}
        self.offset_coordinates = {}  # 存储偏移后的坐标 {point_id: (x, y)}
        
        # 偏移策略设置
        self.offset_settings = {
            'strategy': 'auto',  # 默认策略: auto, option1, option2, ..., option8
            'surface_bin_size': 25.0,  # 面元大小（米）
            'receiver_interval': 25.0,  # 接收点间隔（米）
            'source_interval': 25.0,    # 震源点间隔（米）
            'source_salvo_spacing': 100.0,  # 震源salvo线间距（米）
            'max_deviation_angle': 45.0,  # 最大偏移角度（度）
            'enable_infill': False,     # 是否启用补充震源点
            'skip_source_points': False,  # 是否允许跳过震源点
            'prefer_original_position': True,  # 是否优先保持原始位置
            'boundary_max_distance': 100.0  # 边界最大距离（米）
        }
        
    def calculate_grid_center(self, point_x, point_y, is_utm=True):
        """
        计算点所在网格的中心坐标
        
        Args:
            point_x: 点的x坐标
            point_y: 点的y坐标
            is_utm: 是否是UTM投影坐标系统
            
        Returns:
            (center_x, center_y): 网格中心点坐标
        """
        grid_size = self.offset_settings['surface_bin_size']
        
        if is_utm:
            # UTM坐标系统直接计算
            grid_center_x = (int(point_x / grid_size) + 0.5) * grid_size
            grid_center_y = (int(point_y / grid_size) + 0.5) * grid_size
        else:
            # 对于WGS84等非投影坐标系统，需要先做单位转换
            # 经度1度约为111320*cos(lat)米，纬度1度约为111000米
            lat_meters_per_degree = 111000.0
            lon_meters_per_degree = 111320.0 * np.cos(np.radians(point_y))
            
            # 将网格大小从米转为度
            grid_size_lon = grid_size / lon_meters_per_degree
            grid_size_lat = grid_size / lat_meters_per_degree
            
            # 计算网格中心点
            grid_center_x = (int(point_x / grid_size_lon) + 0.5) * grid_size_lon
            grid_center_y = (int(point_y / grid_size_lat) + 0.5) * grid_size_lat
            
        # 返回网格中心坐标元组，保留6位精度避免浮点数比较问题
        return (round(grid_center_x, 6), round(grid_center_y, 6))
    
    def load_grid_parameters(self):
        """加载网格参数"""
        try:
            # 构建配置文件路径
            config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'config')
            config_file = os.path.join(config_dir, 'grid_parameters.json')
            
            # 检查配置文件是否存在
            if not os.path.exists(config_file):
                print(f"警告: 网格参数配置文件不存在: {config_file}")
                return None
                
            # 读取配置文件
            with open(config_file, 'r') as f:
                grid_params = json.load(f)
                
            # 验证必要的参数是否存在
            required_params = ['origin_easting', 'origin_northing', 'azimuth', 
                              'track_spacing', 'bin_spacing', 'start_line', 'start_stake']
            for param in required_params:
                if param not in grid_params:
                    print(f"警告: 网格参数文件缺少必要参数: {param}")
                    # 设置默认值而不是返回None
                    if param in ['origin_easting', 'origin_northing']:
                        grid_params[param] = 0
                    elif param == 'azimuth':
                        grid_params[param] = 0
                    elif param in ['track_spacing', 'bin_spacing']:
                        grid_params[param] = 25
                    elif param in ['start_line', 'start_stake']:
                        grid_params[param] = 1000
            
            # 确保增量参数存在
            if 'line_increment' not in grid_params:
                grid_params['line_increment'] = 1
            if 'stake_increment' not in grid_params:
                grid_params['stake_increment'] = 1
                
            print(f"已加载网格参数: {grid_params}")
            return grid_params
            
        except Exception as e:
            print(f"加载网格参数时出错: {str(e)}")
            traceback.print_exc()
            # 返回基本的默认参数而不是None
            return {
                'origin_easting': 0,
                'origin_northing': 0,
                'azimuth': 0,
                'track_spacing': 25,
                'bin_spacing': 25,
                'start_line': 1000,
                'start_stake': 1000,
                'line_increment': 1,
                'stake_increment': 1,
                'direction': 'left'
            }
    
    def calculate_grid_numbers_with_params(self, x, y, grid_params):
        """
        使用提供的网格参数计算点的网格编号
        
        Args:
            x: 点的x坐标
            y: 点的y坐标
            grid_params: 网格参数字典，包含origin_easting, origin_northing, azimuth等
            
        Returns:
            (line_number, stake_number): 计算出的线号和桩号
        """
        try:
            # 参数提取和类型转换
            origin_x = float(grid_params.get('origin_easting', 0))
            origin_y = float(grid_params.get('origin_northing', 0))
            azimuth = float(grid_params.get('azimuth', 0))
            line_interval = float(grid_params.get('track_spacing', 25))
            stake_interval = float(grid_params.get('bin_spacing', 25))
            start_line = int(grid_params.get('start_line', 1000))
            start_stake = int(grid_params.get('start_stake', 1000))
            line_increment = int(grid_params.get('line_increment', 1))
            stake_increment = int(grid_params.get('stake_increment', 1))
            line_direction = 1 if grid_params.get('direction') == 'right' else 0

            # 打印参数信息（调试用）
            debug_output = f"""
            网格参数详情:
            - 原点坐标: ({origin_x}, {origin_y})
            - 方位角: {azimuth} 度
            - 测线间距: {line_interval} m
            - 道间距: {stake_interval} m
            - 起始线号: {start_line}
            - 起始桩号: {start_stake}
            - 线号增量: {line_increment}
            - 桩号增量: {stake_increment}
            - 线方向: {'右' if line_direction == 1 else '左'}
            - 输入点坐标: ({x}, {y})
            """
            print(debug_output)

            # 将方位角转换为弧度
            azimuth_rad = math.radians(azimuth)
            
            # 计算点相对于原点的位置
            dx = x - origin_x
            dy = y - origin_y
            
            # 根据方位角旋转坐标系
            rot_dx = dx * math.cos(-azimuth_rad) - dy * math.sin(-azimuth_rad)
            rot_dy = dx * math.sin(-azimuth_rad) + dy * math.cos(-azimuth_rad)
            
            # 根据测线方向计算线号和桩号
            if line_direction == 0:  # 横向测线
                line_offset = rot_dy / line_interval
                stake_offset = rot_dx / stake_interval
            else:  # 纵向测线
                line_offset = rot_dx / line_interval
                stake_offset = rot_dy / stake_interval
            
            # 计算最终的线号和桩号
            line_number = start_line + int(round(line_offset)) * line_increment
            stake_number = start_stake + int(round(stake_offset)) * stake_increment
            
            # 打印计算过程（调试用）
            print(f"点坐标 ({x}, {y}) 相对原点偏移: dx={dx}, dy={dy}")
            print(f"旋转后的偏移: rot_dx={rot_dx}, rot_dy={rot_dy}")
            print(f"线偏移量: {line_offset} (四舍五入为: {round(line_offset)})")
            print(f"桩偏移量: {stake_offset} (四舍五入为: {round(stake_offset)})")
            print(f"计算结果: 线号={line_number}, 桩号={stake_number}")
            #计算偏移量（不使用round，而是使用floor或ceil）

            # if line_direction == 0:  # 横向测线
            #     # 使用floor除法计算偏移量
            #     line_offset = math.floor(rot_dy / line_interval)
            #     stake_offset = math.floor(rot_dx / stake_interval)
            
            #     # 如果点位置超过网格中心，则向上取整
            #     if rot_dy % line_interval >= line_interval / 2:
            #         line_offset += 1
            #     if rot_dx % stake_interval >= stake_interval / 2:
            #         stake_offset += 1
            # else:  # 纵向测线
            #     line_offset = math.floor(rot_dx / line_interval)
            #     stake_offset = math.floor(rot_dy / stake_interval)
            
            #     if rot_dx % line_interval >= line_interval / 2:
            #         line_offset += 1
            #     if rot_dy % stake_interval >= stake_interval / 2:
            #         stake_offset += 1

            # # 计算最终的线号和桩号
            # line_number = start_line + line_offset * line_increment
            # stake_number = start_stake + stake_offset * stake_increment

            # # 打印计算过程（调试用）
            # print(f"""
            #   计算过程:
            #   - 相对偏移: dx={dx}, dy={dy}
            #   - 旋转后偏移: rot_dx={rot_dx}, rot_dy={rot_dy}
            #   - 线偏移量: {line_offset}
            #   - 桩偏移量: {stake_offset}
            #   - 计算结果: 线号={line_number}, 桩号={stake_number}
            #   """)
            return (line_number, stake_number)
            
        except Exception as e:
            print(f"使用参数计算网格编号时出错: {str(e)}")
            traceback.print_exc()
            return None
            
    def calculate_grid_numbers(self, x, y):
        """根据点的坐标计算网格编号"""
        try:
            # 加载网格参数
            grid_params = self.load_grid_parameters()
            if not grid_params:
                # 如果没有网格参数，使用默认方法计算
                bin_size = self.offset_settings['surface_bin_size']
                line_number = int(x / bin_size)
                stake_number = int(y / bin_size)
                print(f"使用默认方法计算网格编号: 线号={line_number}, 桩号={stake_number}")
                return (line_number, stake_number)
            
            # 使用网格参数计算
            result = self.calculate_grid_numbers_with_params(x, y, grid_params)
            
            # 添加调试输出
            print(f"点坐标 ({x}, {y}) 计算得到网格编号: 线号={result[0]}, 桩号={result[1]}")
            print(f"使用网格参数: 原点=({grid_params['origin_easting']}, {grid_params['origin_northing']}), 方位角={grid_params['azimuth']}")
            
            return result
            
        except Exception as e:
            print(f"计算网格编号时出错: {str(e)}")
            traceback.print_exc()
            return None
        
    def load_obstacles(self, shapefile_path: str):
        """加载障碍物Shapefile"""
        try:
            print(f"开始加载shape文件: {shapefile_path}")
            
            # 读取shape文件
            self.obstacles = gpd.read_file(shapefile_path)
            original_crs = self.obstacles.crs
            print(f"成功读取shape文件，原始坐标系统: {original_crs}")
            
            # 如果shape文件没有坐标系统信息，使用默认的EPSG:4326
            if self.obstacles.crs is None:
                self.obstacles.set_crs(epsg=4326, inplace=True)
                print("Shape文件没有坐标系统信息，请使用EPSG:4326")
            
            # 获取当前选择的目标坐标系统
            from Main_Window import MainWindow
            #target_crs = MainWindow.coordinate_systems[MainWindow.crs_combo.currentText()]
            #target_crs = MainWindow.get_current_crs()
            target_crs = MainWindow.instance().get_current_crs()
            print(f"当前选择的目标坐标系统: {target_crs}")
            
            if target_crs is None:
                print("未获取到目标坐标系统，使用原始坐标系统")
                self._build_spatial_index()
                return True
                
            # 如果目标坐标系统和原始坐标系统不同，进行转换
            if str(original_crs) != str(target_crs):
                try:
                    print(f"正在将坐标系统从 {original_crs} 转换到 {target_crs}")
                    self.obstacles = self.obstacles.to_crs(target_crs)
                    print("坐标系统转换成功")
                except Exception as e:
                    print(f"坐标系统转换失败: {str(e)}")
                    print("保持原始坐标系统")
            else:
                print("原始坐标系统和目标坐标系统相同，无需转换")
            
            self._build_spatial_index()
            return True
            
        except Exception as e:
            print(f"加载shape文件失败: {str(e)}")
            return False
            
    def _build_spatial_index(self):
        """构建空间索引"""
        try:
            # 创建空间索引
            self.spatial_index = self.obstacles.sindex
            print("空间索引构建成功")
        except Exception as e:
            print(f"空间索引构建失败: {str(e)}")
            self.spatial_index = None
            
    def load_grid(self, grid_file: str = None, grid_size: float = 10.0):
        """
        加载或生成网格点
        
        Args:
            grid_file: 网格文件路径（可选）
            grid_size: 如果不提供网格文件，使用此网格大小生成网格
        """
        if grid_file and os.path.exists(grid_file):
            try:
                print(f"开始加载网格文件: {grid_file}")
                grid_data = gpd.read_file(grid_file)
                print(f"网格文件原始坐标系统: {grid_data.crs}")
                
                # 如果没有坐标系统信息，假设是UTM Zone 38N
                if grid_data.crs is None:
                    print("网格文件没有坐标系统信息，假设为UTM Zone 38N")
                    grid_data.set_crs("EPSG:32638", inplace=True)
                
                # 转换到WGS84
                print("转换坐标系统到WGS84 (EPSG:4326)")
                grid_data = grid_data.to_crs("EPSG:4326")
                print(f"转换后的坐标系统: {grid_data.crs}")
                
                # 根据几何类型获取坐标
                if grid_data.geometry.iloc[0].geom_type == 'Point':
                    print("处理点类型网格")
                    # 如果是点类型，直接获取x,y坐标
                    self.grid_points = np.array([(p.x, p.y) for p in grid_data.geometry])
                    print(f"第一个点坐标: {self.grid_points[0]}")
                else:
                    print("处理多边形类型网格")
                    # 如果是多边形类型，使用中心点
                    self.grid_points = np.array([(p.centroid.x, p.centroid.y) for p in grid_data.geometry])
                    print(f"第一个多边形中心点坐标: {self.grid_points[0]}")
                
                # 保存转换后的网格数据
                self.grid_gdf = grid_data
                print(f"成功加载网格点数量: {len(self.grid_points)}")
                
                # 构建KD树
                self.grid_kdtree = KDTree(self.grid_points)
                return True
                
            except Exception as e:
                print(f"加载网格文件失败: {str(e)}")
                traceback.print_exc()
                return False
        else:
            # 如果没有提供网格文件，根据障碍物范围生成网格
            if self.obstacles is None:
                print("未加载障碍物数据，无法生成网格")
                return False
            bounds = self.obstacles.total_bounds
            x_min, y_min, x_max, y_max = bounds
            x_grid = np.arange(x_min, x_max + grid_size, grid_size)
            y_grid = np.arange(y_min, y_max + grid_size, grid_size)
            xx, yy = np.meshgrid(x_grid, y_grid)
            self.grid_points = np.column_stack((xx.ravel(), yy.ravel()))
            
            # 构建KD树
            self.grid_kdtree = KDTree(self.grid_points)
            return True
        
    def find_points_in_obstacles(self, points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """
        查找位于障碍物内的点
        
        Args:
            points: 点位列表 [(x1, y1), (x2, y2), ...]
            
        Returns:
            位于障碍物内的点位列表
        """
        if self.obstacles is None or self.spatial_index is None:
            logging.error("未加载障碍物数据")
            return []
            
        self.points_in_obstacles = []
        for point in points:
            point_obj = Point(point)
            # 使用空间索引预筛选可能相交的障碍物
            for idx in self.spatial_index.intersection(point_obj.bounds):
                if self.obstacles.geometry.iloc[idx].contains(point_obj):
                    self.points_in_obstacles.append(point)
                    break
        return self.points_in_obstacles
        
    def find_nearest_grid_point(self, point_pos):
        """查找最近的网格单元中心
        Args:
            point_pos: [x, y] 格式的点坐标
        Returns:
            最近的网格单元中心坐标 [x, y] 或 None（如果没有找到）
        """
        try:
            if not isinstance(point_pos, (list, tuple)) or len(point_pos) != 2:
                print("输入点格式错误")
                return None

            # 获取网格大小
            grid_size = self.offset_settings['surface_bin_size']
            if grid_size <= 0:
                print("网格大小无效")
                return None

            # 计算点所在的网格单元索引
            x, y = point_pos
            # 使用 floor division 找到网格索引
            grid_x_index = x // grid_size
            grid_y_index = y // grid_size
            
            # 计算网格单元中心点
            # 对于负坐标需要特殊处理
            if x < 0:
                grid_x_index -= 1
            if y < 0:
                grid_y_index -= 1
                
            center_x = (grid_x_index + 0.5) * grid_size
            center_y = (grid_y_index + 0.5) * grid_size

            # 网格单元中心点
            center_point = [center_x, center_y]
            print(f"找到最近的网格单元中心: {center_point}, 原始点: {point_pos}")
            
            return center_point
            
        except Exception as e:
            import traceback
            print(f"查找最近网格单元中心时出错: {str(e)}")
            print(traceback.format_exc())
            return None
        
    def auto_offset_point(self, point: Tuple[float, float], point_id=None) -> Tuple[float, float]:
        """
        自动偏移点位到最近的网格点
        
        Args:
            point: 点位坐标 (x, y)
            point_id: 点的唯一标识符（可选）
            
        Returns:
            偏移后的点位坐标 (x, y)
        """
        # 如果提供了点ID，保存原始桩号信息
        if point_id is not None:
            self.store_original_grid_numbers(point_id, point)
            
        if point not in self.points_in_obstacles:
            # 如果点不在障碍物内，维持原位
            if point_id is not None:
                # 原位点的偏移后坐标就是原始坐标
                self.store_offset_coordinates(point_id, point)
            return point
            
        nearest_grid_point = self.find_nearest_grid_point(point)
        if nearest_grid_point is None:
            # 如果找不到最近网格点，维持原位
            if point_id is not None:
                self.store_offset_coordinates(point_id, point)
            return point
            
        # 计算偏移距离
        distance = np.sqrt(
            (point[0] - nearest_grid_point[0])**2 + 
            (point[1] - nearest_grid_point[1])**2
        )
        
        # 如果偏移距离超过最大限制，返回原始点位
        if distance > self.max_offset_distance:
            print(f"警告：点位 {point} 的偏移距离 {distance:.2f}m 超过最大限制 {self.max_offset_distance}m，将保持原位置")
            if point_id is not None:
                self.store_offset_coordinates(point_id, point)
            return point
            
        # 验证新位置是否在合理范围内
        x_diff = abs(nearest_grid_point[0] - point[0])
        y_diff = abs(nearest_grid_point[1] - point[1])
        
        if x_diff > self.max_offset_distance or y_diff > self.max_offset_distance:
            print(f"警告：点位 {point} 的X或Y方向偏移距离过大，将保持原位置")
            if point_id is not None:
                self.store_offset_coordinates(point_id, point)
            return point
            
        # 保存偏移后坐标
        if point_id is not None:
            self.store_offset_coordinates(point_id, nearest_grid_point)
            
        return nearest_grid_point
        
    def auto_offset_all_points(self, points: List[Tuple[float, float]], point_ids=None) -> List[Tuple[float, float]]:
        """
        自动偏移所有点位
        
        Args:
            points: 点位列表 [(x1, y1), (x2, y2), ...]
            point_ids: 点ID列表，与points一一对应（可选）
            
        Returns:
            偏移后的点位列表
        """
        if not self.points_in_obstacles:
            self.find_points_in_obstacles(points)
            
        offset_points = []
        
        # 确保point_ids长度与points相同，如果没有提供point_ids则使用None
        if point_ids is None:
            point_ids = [None] * len(points)
        elif len(point_ids) != len(points):
            print(f"警告：点ID列表长度 ({len(point_ids)}) 与点列表长度 ({len(points)}) 不匹配")
            # 截断或扩展point_ids使其长度与points相同
            if len(point_ids) < len(points):
                point_ids = point_ids + [None] * (len(points) - len(point_ids))
            else:
                point_ids = point_ids[:len(points)]
        
        # 对每个点进行偏移处理
        for i, (point, point_id) in enumerate(zip(points, point_ids)):
            offset_point = self.auto_offset_point(point, point_id)
            offset_points.append(offset_point)
            
        return offset_points
        
    def set_max_offset_distance(self, distance: float):
        """设置最大偏移距离"""
        self.max_offset_distance = distance

    def set_offset_settings(self, settings: Dict[str, Any]):
        """设置偏移策略的参数"""
        # 更新设置字典中的值
        for key, value in settings.items():
            if key in self.offset_settings:
                self.offset_settings[key] = value
                
    def get_offset_settings(self) -> Dict[str, Any]:
        """获取当前的偏移策略设置"""
        return self.offset_settings.copy()
        
    def apply_offset_strategy(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        根据当前选择的偏移策略应用偏移
        
        Args:
            point: 原始点位置
            obstacles_gdf: 障碍物GeoDataFrame
            grid_gdf: 网格点GeoDataFrame（可选）
            progress_callback: 进度回调函数（可选）
            
        Returns:
            偏移后的点或None（如果无法偏移）
        """
        # 检查坐标系统类型
        is_utm = False
        if hasattr(obstacles_gdf, 'crs'):
            crs_str = str(obstacles_gdf.crs).lower()
            # 扩展UTM投影坐标系统的识别范围，添加对EPSG:20438的支持
            is_utm = ('utm' in crs_str or 
                      '+proj=utm' in crs_str or 
                      'epsg:20438' in crs_str or 
                      '20438' in crs_str)
            
            if progress_callback:
                progress_callback("检查坐标系统...")
            
            print(f"坐标系统: {obstacles_gdf.crs}")
            print(f"是否是UTM投影坐标系统: {is_utm}")
            
            if not is_utm:
                print("警告: 当前不是UTM投影坐标系统，偏移计算可能不准确!")
                print("建议在偏移计算前将所有数据转换为UTM坐标系统。")
                print(f"当前坐标系统: {obstacles_gdf.crs}")
        
        # 打印当前点位和使用的策略参数
        if progress_callback:
            progress_callback(f"处理点: ({point.x}, {point.y})")
            
        print(f"\n开始处理点: ({point.x}, {point.y})")
        print(f"使用策略: {self.offset_settings['strategy']}")
        print(f"面元大小: {self.offset_settings['surface_bin_size']}")
        print(f"最大偏移距离: {self.max_offset_distance}")
        
        # 如果点不在障碍物内且优先保持原始位置，则返回原始点
        if self.offset_settings['prefer_original_position']:
            if progress_callback:
                progress_callback("检查点是否在障碍物内...")
                
            if not any(obstacles_gdf.intersects(point)):
                print(f"点不在障碍物内，保持原始位置")
                return point
        
        # 如果提供了网格数据，尝试找到最近的不在障碍物内的网格点
        if grid_gdf is not None:
            if progress_callback:
                progress_callback("使用网格数据寻找最优偏移位置...")
                
            print("使用网格数据寻找最优偏移位置")
            # 计算与所有网格点的距离
            distances = grid_gdf.geometry.distance(point)
            # 按距离排序，获取最近的5个网格点
            nearest_indices = distances.nsmallest(5).index
            for idx in nearest_indices:
                grid_point = grid_gdf.geometry.iloc[idx]
                # 检查网格点是否在障碍物外
                if not any(obstacles_gdf.intersects(grid_point)):
                    distance = point.distance(grid_point)
                    # 检查距离是否在允许的范围内
                    if distance <= self.max_offset_distance:
                        print(f"找到最近的有效网格点: ({grid_point.x}, {grid_point.y}), 距离: {distance}")
                        return grid_point
            
            print("未找到合适的网格点，尝试使用选定的偏移策略")
        
        strategy = self.offset_settings['strategy']
        
        # 如果策略是"auto"，则自动选择最合适的策略
        if strategy == 'auto':
            if progress_callback:
                progress_callback("评估障碍物复杂度，选择最佳策略...")
                
            # 检查障碍物的复杂性
            if obstacles_gdf is not None and len(obstacles_gdf) > 0:
                try:
                    # 计算障碍物几何形状的复杂性
                    obstacle_complexity = sum([len(geom.exterior.coords) for geom in obstacles_gdf.geometry if hasattr(geom, 'exterior')])
                    print(f"障碍物几何复杂度: {obstacle_complexity}")
                    
                    # 如果障碍物很复杂，使用边界优先策略
                    if obstacle_complexity > 100:  # 复杂度阈值可以根据实际情况调整
                        strategy = 'option9'
                        print("障碍物几何形状复杂，自动选择选项9 (障碍物边界优先偏移)")
                        if progress_callback:
                            progress_callback("使用选项9 (障碍物边界优先偏移)...")
                        return self._apply_option9(point, obstacles_gdf, grid_gdf, progress_callback)
                except Exception as e:
                    print(f"评估障碍物复杂性时出错: {str(e)}")
            
            # 如果没有选择option9，根据面元大小决定使用选项1还是选项2
            if self.offset_settings['surface_bin_size'] >= 20.0:
                strategy = 'option1'
                print("自动选择选项1 (面元≥20m)")
                if progress_callback:
                    progress_callback("使用选项1 (面元≥20m)...")
            else:
                strategy = 'option2'
                print("自动选择选项2 (面元<20m)")
                if progress_callback:
                    progress_callback("使用选项2 (面元<20m)...")
        
        # 应用所选策略
        if strategy == 'option1':
            return self._apply_option1(point, obstacles_gdf, progress_callback)
        elif strategy == 'option2':
            return self._apply_option2(point, obstacles_gdf, progress_callback)
        elif strategy == 'option3':
            return self._apply_option3(point, obstacles_gdf, grid_gdf, progress_callback)
        elif strategy == 'option4':
            return self._apply_option4(point, obstacles_gdf, grid_gdf, progress_callback)
        elif strategy == 'option5':
            return self._apply_option5(point, obstacles_gdf, grid_gdf, progress_callback)
        elif strategy == 'option6':
            return self._apply_option6(point, obstacles_gdf, grid_gdf, progress_callback)
        elif strategy == 'option7':
            return self._apply_option7(point, obstacles_gdf, grid_gdf, progress_callback)
        elif strategy == 'option8':
            # 如果允许跳过震源点
            if self.offset_settings['skip_source_points']:
                if progress_callback:
                    progress_callback("选项8: 跳过震源点...")
                print("选项8: 跳过震源点")
                return None  # 返回None表示跳过该点
            # 否则尝试使用前面的策略
            print("选项8: 尝试其他策略")
            # 先尝试边界优先策略（option9），通常比其他策略更有效
            if progress_callback:
                progress_callback("选项8: 尝试策略9...")
            print("尝试策略: option9")
            result = self.apply_offset_strategy_by_name('option9', point, obstacles_gdf, grid_gdf, progress_callback)
            if result is not None:
                return result
            
            # 然后尝试其他策略
            for opt in ['option1', 'option2', 'option3', 'option4', 'option5', 'option6', 'option7']:
                if progress_callback:
                    progress_callback(f"选项8: 尝试策略{opt}...")
                print(f"尝试策略: {opt}")
                result = self.apply_offset_strategy_by_name(opt, point, obstacles_gdf, grid_gdf, progress_callback)
                if result is not None:
                    return result
            # 如果所有策略都失败，返回None
            print("所有策略都失败，跳过该点")
            return None
        elif strategy == 'option9':
            result = self._apply_option9(point, obstacles_gdf, grid_gdf, progress_callback)
            # 选项9在失败时现在会返回原始点，不会返回None
            return result
        
        # 默认返回原始点（如果策略不匹配）
        print(f"未找到匹配的策略: {strategy}，返回原始点")
        return point
    
    def apply_offset_strategy_by_name(self, strategy_name: str, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        通过策略名称应用特定的偏移策略
        
        Args:
            strategy_name: 策略名称
            point: 原始点位置
            obstacles_gdf: 障碍物GeoDataFrame
            grid_gdf: 网格点GeoDataFrame（可选）
            progress_callback: 进度回调函数（可选）
            
        Returns:
            偏移后的点或None（如果无法偏移）
        """
        # 临时保存当前策略
        current_strategy = self.offset_settings['strategy']
        # 设置为指定策略
        self.offset_settings['strategy'] = strategy_name
        # 应用策略
        result = self.apply_offset_strategy(point, obstacles_gdf, grid_gdf, progress_callback)
        # 恢复原始策略
        self.offset_settings['strategy'] = current_strategy
        return result
    
    def _apply_option1(self, point: Point, obstacles_gdf, progress_callback=None) -> Optional[Point]:
        """
        选项1: 震源点在面元尺寸的一半范围内偏移 (适用于面元尺寸≥20m×20m)

        偏移范围最多为震源或接收点间距的一半，保持CMP点合理靠近箱中心
        """
        # 限制最大偏移距离，防止偏移过远
        max_offset = min(self.offset_settings['surface_bin_size'] / 2, self.max_offset_distance)
        grid_size = self.offset_settings['surface_bin_size']
        
        # 检查坐标系统类型
        is_utm = False
        if hasattr(obstacles_gdf, 'crs'):
            crs_str = str(obstacles_gdf.crs).lower()
            # 扩展UTM投影坐标系统的识别范围，添加对EPSG:20438的支持
            is_utm = ('utm' in crs_str or 
                      '+proj=utm' in crs_str or 
                      'epsg:20438' in crs_str or 
                      '20438' in crs_str)
        
        # 根据坐标系统类型计算网格中心点
        if is_utm:
            # UTM坐标系统直接计算
            grid_center_x = (int(point.x / grid_size) + 0.5) * grid_size
            grid_center_y = (int(point.y / grid_size) + 0.5) * grid_size
        else:
            # 对于WGS84等非投影坐标系统，需要先做单位转换
            # 经度1度约为111320*cos(lat)米，纬度1度约为111000米
            lat_meters_per_degree = 111000.0
            lon_meters_per_degree = 111320.0 * np.cos(np.radians(point.y))
            
            # 将网格大小从米转为度
            grid_size_lon = grid_size / lon_meters_per_degree
            grid_size_lat = grid_size / lat_meters_per_degree
            
            # 计算网格中心点
            grid_center_x = (int(point.x / grid_size_lon) + 0.5) * grid_size_lon
            grid_center_y = (int(point.y / grid_size_lat) + 0.5) * grid_size_lat
            
            # 最大偏移距离也需要转换
            max_offset_lon = max_offset / lon_meters_per_degree
            max_offset_lat = max_offset / lat_meters_per_degree
            print(f"网格大小(度): 经度={grid_size_lon}, 纬度={grid_size_lat}")
            print(f"最大偏移距离(度): 经度={max_offset_lon}, 纬度={max_offset_lat}")
        
        grid_center = Point(grid_center_x, grid_center_y)
        print(f"原始点: ({point.x}, {point.y}), 网格中心: ({grid_center_x}, {grid_center_y})")
        
        # 计算可能的偏移位置（优先考虑附近的网格中心点）
        possible_offsets = []
        
        # 首先检查当前网格中心点是否可用
        if not any(obstacles_gdf.intersects(grid_center)):
            distance = point.distance(grid_center)
            if distance <= max_offset:
                possible_offsets.append((grid_center, distance))
                print(f"当前网格中心点可用: ({grid_center.x}, {grid_center.y}), 距离: {distance}")
        
        # 检查周围的网格中心点 - 扩大搜索范围
        for x_offset in [-2, -1, 0, 1, 2]:
            for y_offset in [-2, -1, 0, 1, 2]:
                if x_offset == 0 and y_offset == 0:
                    continue  # 跳过已检查的当前网格中心
                
                if is_utm:
                    # UTM坐标系统
                    neighbor_center_x = grid_center_x + x_offset * grid_size
                    neighbor_center_y = grid_center_y + y_offset * grid_size
                else:
                    # WGS84坐标系统
                    neighbor_center_x = grid_center_x + x_offset * grid_size_lon
                    neighbor_center_y = grid_center_y + y_offset * grid_size_lat
                
                neighbor_center = Point(neighbor_center_x, neighbor_center_y)
                
                distance = point.distance(neighbor_center)
                if distance <= max_offset and not any(obstacles_gdf.intersects(neighbor_center)):
                    possible_offsets.append((neighbor_center, distance))
                    print(f"邻近网格中心点可用: ({neighbor_center.x}, {neighbor_center.y}), 距离: {distance}")
        
        # 如果没有找到合适的网格中心点，尝试在原点周围探索其他位置
        if not possible_offsets:
            print("未找到合适的网格中心点，尝试其他位置...")
            
            if is_utm:
                # UTM坐标系统
                step = max_offset / 16  # 使用更小的步长获得更精细的结果
                
                for x_offset in np.arange(-max_offset, max_offset + step, step):
                    for y_offset in np.arange(-max_offset, max_offset + step, step):
                        # 确保总偏移距离在最大范围内
                        offset_distance = np.sqrt(x_offset**2 + y_offset**2)
                        if offset_distance <= max_offset:
                            # 创建偏移后的点
                            new_point = Point(point.x + x_offset, point.y + y_offset)
                            # 检查新点是否在障碍物外
                            if not any(obstacles_gdf.intersects(new_point)):
                                # 存储偏移后的点和距离
                                possible_offsets.append((new_point, offset_distance))
                                if len(possible_offsets) % 10 == 0:  # 每找到10个可能的偏移点输出一次
                                    print(f"已找到 {len(possible_offsets)} 个可能的偏移位置")
            
                print(f"在UTM精细搜索中找到 {len(possible_offsets)} 个可能的偏移位置")
            else:
                # WGS84坐标系统
                step_lon = max_offset_lon / 8
                step_lat = max_offset_lat / 8
                
                for lon_offset in np.arange(-max_offset_lon, max_offset_lon + step_lon, step_lon):
                    for lat_offset in np.arange(-max_offset_lat, max_offset_lat + step_lat, step_lat):
                        # 计算米为单位的偏移距离
                        offset_meters = np.sqrt(
                            (lat_offset * lat_meters_per_degree)**2 + 
                            (lon_offset * lon_meters_per_degree)**2
                        )
                        
                        if offset_meters <= max_offset:
                            new_point = Point(point.x + lon_offset, point.y + lat_offset)
                            if not any(obstacles_gdf.intersects(new_point)):
                                possible_offsets.append((new_point, offset_meters))
        
        # 如果有可能的偏移，选择距离最近的
        if possible_offsets:
            possible_offsets.sort(key=lambda x: x[1])  # 按距离排序
            selected_point = possible_offsets[0][0]
            print(f"选择偏移位置: ({selected_point.x}, {selected_point.y}), 距原点距离: {possible_offsets[0][1]}")
            return selected_point
        
        print("无法找到合适的偏移位置")
        return None  # 无法找到合适的偏移位置
    
    def _apply_option2(self, point: Point, obstacles_gdf, progress_callback=None) -> Optional[Point]:
        """
        选项2: 震源点在面元尺寸范围内偏移 (适用于面元尺寸<20m×20m)

        偏移范围最多为震源或接收站间距（一个面元尺寸）
        """
        # 限制最大偏移距离，防止偏移过远
        max_offset = min(self.offset_settings['surface_bin_size'], self.max_offset_distance)
        grid_size = self.offset_settings['surface_bin_size']
        
        # 检查坐标系统类型
        is_utm = False
        if hasattr(obstacles_gdf, 'crs'):
            crs_str = str(obstacles_gdf.crs).lower()
            # 扩展UTM投影坐标系统的识别范围，添加对EPSG:20438的支持
            is_utm = ('utm' in crs_str or 
                      '+proj=utm' in crs_str or 
                      'epsg:20438' in crs_str or 
                      '20438' in crs_str)
        
        # 根据坐标系统类型计算网格中心点
        if is_utm:
            # UTM坐标系统直接计算
            grid_center_x = (int(point.x / grid_size) + 0.5) * grid_size
            grid_center_y = (int(point.y / grid_size) + 0.5) * grid_size
        else:
            # 对于WGS84等非投影坐标系统，需要先做单位转换
            # 经度1度约为111320*cos(lat)米，纬度1度约为111000米
            lat_meters_per_degree = 111000.0
            lon_meters_per_degree = 111320.0 * np.cos(np.radians(point.y))
            
            # 将网格大小从米转为度
            grid_size_lon = grid_size / lon_meters_per_degree
            grid_size_lat = grid_size / lat_meters_per_degree
            
            # 计算网格中心点
            grid_center_x = (int(point.x / grid_size_lon) + 0.5) * grid_size_lon
            grid_center_y = (int(point.y / grid_size_lat) + 0.5) * grid_size_lat
            
            # 最大偏移距离也需要转换
            max_offset_lon = max_offset / lon_meters_per_degree
            max_offset_lat = max_offset / lat_meters_per_degree
            print(f"网格大小(度): 经度={grid_size_lon}, 纬度={grid_size_lat}")
            print(f"最大偏移距离(度): 经度={max_offset_lon}, 纬度={max_offset_lat}")
        
        grid_center = Point(grid_center_x, grid_center_y)
        print(f"原始点: ({point.x}, {point.y}), 网格中心: ({grid_center_x}, {grid_center_y})")
        
        # 计算可能的偏移位置（优先考虑附近的网格中心点）
        possible_offsets = []
        
        # 首先检查当前网格中心点是否可用
        if not any(obstacles_gdf.intersects(grid_center)):
            distance = point.distance(grid_center)
            if distance <= max_offset:
                possible_offsets.append((grid_center, distance))
                print(f"当前网格中心点可用: ({grid_center.x}, {grid_center.y}), 距离: {distance}")
        
        # 检查周围的网格中心点 (扩大到2×2的范围)
        for x_offset in [-2, -1, 0, 1, 2]:
            for y_offset in [-2, -1, 0, 1, 2]:
                if x_offset == 0 and y_offset == 0:
                    continue  # 跳过已检查的当前网格中心
                
                if is_utm:
                    # UTM坐标系统
                    neighbor_center_x = grid_center_x + x_offset * grid_size
                    neighbor_center_y = grid_center_y + y_offset * grid_size
                else:
                    # WGS84坐标系统
                    neighbor_center_x = grid_center_x + x_offset * grid_size_lon
                    neighbor_center_y = grid_center_y + y_offset * grid_size_lat
                
                neighbor_center = Point(neighbor_center_x, neighbor_center_y)
                
                distance = point.distance(neighbor_center)
                if distance <= max_offset and not any(obstacles_gdf.intersects(neighbor_center)):
                    possible_offsets.append((neighbor_center, distance))
                    print(f"邻近网格中心点可用: ({neighbor_center.x}, {neighbor_center.y}), 距离: {distance}")
        
        # 如果没有找到合适的网格中心点，尝试在原点周围探索其他位置
        if not possible_offsets:
            print("未找到合适的网格中心点，尝试其他位置...")
            
            if is_utm:
                # UTM坐标系统
                step = max_offset / 16  # 使用更小的步长获得更精细的结果
                
                for x_offset in np.arange(-max_offset, max_offset + step, step):
                    for y_offset in np.arange(-max_offset, max_offset + step, step):
                        offset_distance = np.sqrt(x_offset**2 + y_offset**2)
                        if offset_distance <= max_offset:
                            new_point = Point(point.x + x_offset, point.y + y_offset)
                            if not any(obstacles_gdf.intersects(new_point)):
                                possible_offsets.append((new_point, offset_distance))
                                if len(possible_offsets) % 10 == 0:  # 每找到10个可能的偏移点输出一次
                                    print(f"已找到 {len(possible_offsets)} 个可能的偏移位置")
            
                print(f"在UTM精细搜索中找到 {len(possible_offsets)} 个可能的偏移位置")
            else:
                # WGS84坐标系统
                step_lon = max_offset_lon / 8
                step_lat = max_offset_lat / 8
                
                for lon_offset in np.arange(-max_offset_lon, max_offset_lon + step_lon, step_lon):
                    for lat_offset in np.arange(-max_offset_lat, max_offset_lat + step_lat, step_lat):
                        # 计算米为单位的偏移距离
                        offset_meters = np.sqrt(
                            (lat_offset * lat_meters_per_degree)**2 + 
                            (lon_offset * lon_meters_per_degree)**2
                        )
                        
                        if offset_meters <= max_offset:
                            new_point = Point(point.x + lon_offset, point.y + lat_offset)
                            if not any(obstacles_gdf.intersects(new_point)):
                                possible_offsets.append((new_point, offset_meters))
        
        if possible_offsets:
            possible_offsets.sort(key=lambda x: x[1])
            selected_point = possible_offsets[0][0]
            print(f"选择偏移位置: ({selected_point.x}, {selected_point.y}), 距原点距离: {possible_offsets[0][1]}")
            return selected_point
        
        print("无法找到合适的偏移位置")
        return None
    
    def _apply_option3(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        选项3: 沿接收线方向偏移震源点
        
        将震源点平行于接收线方向移动，步长为接收点间隔，最大距离不超过预规划震源组线间距
        """
        # 检查坐标系统类型
        is_utm = False
        if hasattr(obstacles_gdf, 'crs'):
            crs_str = str(obstacles_gdf.crs).lower()
            # 扩展UTM投影坐标系统的识别范围，添加对EPSG:20438的支持
            is_utm = ('utm' in crs_str or 
                      '+proj=utm' in crs_str or 
                      'epsg:20438' in crs_str or 
                      '20438' in crs_str)
        
        receiver_interval = self.offset_settings['receiver_interval']
        max_distance = self.offset_settings['source_salvo_spacing']
        
        # 沿接收线方向尝试偏移（假设接收线沿x轴方向）
        possible_offsets = []
        
        # 在两个方向上尝试
        for direction in [-1, 1]:  # 左右方向
            for i in range(1, int(max_distance / receiver_interval) + 1):
                offset_distance = i * receiver_interval * direction
                new_point = Point(point.x + offset_distance, point.y)
                
                # 检查新点是否在障碍物外
                if not any(obstacles_gdf.intersects(new_point)):
                    # 检查新点是否是有效的网格点（如果提供了网格数据）
                    if grid_gdf is not None and not any(grid_gdf.geometry.contains(new_point)):
                        continue
                    
                    possible_offsets.append((new_point, abs(offset_distance)))
        
        if possible_offsets:
            possible_offsets.sort(key=lambda x: x[1])  # 按距离排序
            return possible_offsets[0][0]  # 返回距离最近的点
        
        return None
    
    def _apply_option4(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        选项4: 在接收线方向上的最大震源点偏移
        
        类似于选项3，但最大距离可达到震源组线间距的两倍
        """
        receiver_interval = self.offset_settings['receiver_interval']
        max_distance = self.offset_settings['source_salvo_spacing'] * 2  # 两倍的震源组线间距
        
        # 沿接收线方向尝试偏移
        possible_offsets = []
        
        for direction in [-1, 1]:  # 左右方向
            for i in range(1, int(max_distance / receiver_interval) + 1):
                offset_distance = i * receiver_interval * direction
                new_point = Point(point.x + offset_distance, point.y)
                
                if not any(obstacles_gdf.intersects(new_point)):
                    if grid_gdf is not None and not any(grid_gdf.geometry.contains(new_point)):
                        continue
                    
                    possible_offsets.append((new_point, abs(offset_distance)))
        
        if possible_offsets:
            possible_offsets.sort(key=lambda x: x[1])
            return possible_offsets[0][0]
        
        return None
    
    def _apply_option5(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        选项5: 补充震源点
        
        当障碍物导致显著的震源跳过时，通过补充震源点维持覆盖度
        注意：这个选项通常需要更高级的逻辑和整体规划
        """
        # 如果未启用补充震源点，返回None
        if not self.offset_settings['enable_infill']:
            return None
            
        # 补充震源点的实现需要考虑整个测量区域和覆盖度计算
        # 这里提供一个简化版本，实际使用时可能需要更复杂的逻辑
        
        # 尝试在附近找一个可用点
        receiver_interval = self.offset_settings['receiver_interval']
        source_interval = self.offset_settings['source_interval']
        
        # 在周围网格点中寻找可能的补充点位置
        possible_offsets = []
        
        for x_offset in range(-2, 3):
            for y_offset in range(-2, 3):
                # 跳过原始位置
                if x_offset == 0 and y_offset == 0:
                    continue
                    
                new_x = point.x + x_offset * receiver_interval
                new_y = point.y + y_offset * source_interval
                new_point = Point(new_x, new_y)
                
                if not any(obstacles_gdf.intersects(new_point)):
                    if grid_gdf is not None and not any(grid_gdf.geometry.contains(new_point)):
                        continue
                    
                    distance = np.sqrt((x_offset * receiver_interval)**2 + (y_offset * source_interval)**2)
                    possible_offsets.append((new_point, distance))
        
        if possible_offsets:
            possible_offsets.sort(key=lambda x: x[1])
            return possible_offsets[0][0]
        
        return None
    
    def _apply_option6(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        选项6: 在相邻测线区重新定位震源点
        
        将震源点垂直于接收线方向移动一个接收线间隔
        """
        # 假设接收线沿x轴方向，相邻测线区在y轴方向上偏移
        receiver_interval = self.offset_settings['receiver_interval']
        
        possible_offsets = []
        
        # 在上下两个方向上尝试
        for direction in [-1, 1]:  # 上下方向
            new_point = Point(point.x, point.y + direction * receiver_interval)
            
            if not any(obstacles_gdf.intersects(new_point)):
                if grid_gdf is not None and not any(grid_gdf.geometry.contains(new_point)):
                    continue
                
                possible_offsets.append((new_point, receiver_interval))
        
        if possible_offsets:
            # 由于所有可能的偏移距离相同，可以随机选择一个（这里选第一个）
            return possible_offsets[0][0]
        
        return None
    
    def _apply_option7(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        选项7: 结合接收线方向移动的相邻测线区重定位
        
        首先沿接收线方向移动，如果失败，尝试相邻测线区重定位
        """
        # 首先尝试选项3（沿接收线方向偏移）
        result = self._apply_option3(point, obstacles_gdf, grid_gdf, progress_callback)
        if result is not None and result != point:
            return result
        
        # 如果沿接收线方向偏移失败，尝试选项6（相邻测线区重新定位）
        print("沿接收线方向偏移失败，尝试相邻测线区重定位")
        return self._apply_option6(point, obstacles_gdf, grid_gdf, progress_callback)

    def _apply_option9(self, point: Point, obstacles_gdf, grid_gdf=None, progress_callback=None) -> Optional[Point]:
        """
        选项9: 障碍物边界优先偏移
        
        寻找障碍物外最近的有效网格点
        """
        print("应用选项9: 障碍物边界优先偏移策略")
        
        if grid_gdf is None:
            print("错误：未提供网格点数据")
            return point  # 修改：返回原始点而不是None，避免点消失
        
        max_offset = self.max_offset_distance
        
        # 找出包含该点的障碍物
        containing_obstacles = obstacles_gdf[obstacles_gdf.intersects(point)]
        
        if containing_obstacles.empty:
            print("点不在任何障碍物内，保持原位")
            return point
        
        print(f"找到 {len(containing_obstacles)} 个包含该点的障碍物")
        
        # 检查坐标系统类型
        is_utm = False
        if hasattr(obstacles_gdf, 'crs'):
            crs_str = str(obstacles_gdf.crs).lower()
            # 扩展UTM投影坐标系统的识别范围，添加对EPSG:20438的支持
            is_utm = ('utm' in crs_str or 
                      '+proj=utm' in crs_str or 
                      'epsg:20438' in crs_str or 
                      '20438' in crs_str)

        # 计算所有网格点到当前点的距离
        distances = grid_gdf.geometry.distance(point)
        
        # 按距离排序
        sorted_grid_points = grid_gdf.iloc[distances.argsort()]
        
        # 打印已占用网格点数量
        print(f"当前已有 {len(self.occupied_grid_points)} 个网格点被占用")
        
        # 遍历排序后的网格点，找到第一个不在障碍物内且在最大偏移距离范围内的点
        for idx, grid_point in sorted_grid_points.iterrows():
            grid_geom = grid_point.geometry
            
            # 如果几何对象是多边形，使用其中心点
            if grid_geom.geom_type == 'Polygon':
                grid_geom = grid_geom.centroid
            elif grid_geom.geom_type != 'Point':
                print(f"跳过非点/多边形几何对象: {grid_geom.geom_type}")
                continue
                
            distance = point.distance(grid_geom)
            
            if distance > max_offset:
                print(f"超出最大偏移距离 {max_offset}，停止搜索")
                # 修改：如果找不到合适的点，返回原始点
                print("未找到合适的偏移位置，保持原位")
                return point
            
            # 使用统一的方法计算该点所在的网格中心
            grid_point_key = self.calculate_grid_center(grid_geom.x, grid_geom.y, is_utm)
            
            # 检查这个网格点是否已经被其他点占用
            if grid_point_key in self.occupied_grid_points:
                print(f"网格点 {grid_point_key} 已被其他点占用，继续查找下一个")
                continue
                
            if not any(obstacles_gdf.intersects(grid_geom)):
                print(f"找到最近的有效网格点，距离: {distance}")
                # 将这个网格点标记为已占用
                self.occupied_grid_points.add(grid_point_key)
                return grid_geom
        
        print("选项9未找到合适的偏移位置，保持原位")
        return point  # 修改：返回原始点而不是None，避免点消失

    def load_grid_points(self, points):
        """加载网格点数据
        Args:
            points: 网格点坐标列表，每个点是 [x, y] 格式
        Returns:
            bool: 是否成功加载
        """
        try:
            self.grid_points = np.array(points)
            # 构建KD树
            self.grid_kdtree = KDTree(self.grid_points)
            return True
        except Exception as e:
            print(f"加载网格点数据失败: {str(e)}")
            return False

    def store_original_grid_numbers(self, point_id, coordinates):
        """
        保存点的原始桩号信息
        
        Args:
            point_id: 点的唯一标识符
            coordinates: 点的原始坐标 (x, y)
        """
        try:
            x, y = coordinates
            # 计算原始桩号
            grid_numbers = self.calculate_grid_numbers(x, y)
            if grid_numbers:
                self.original_grid_numbers[point_id] = grid_numbers
                print(f"已保存点 {point_id} 的原始桩号: {grid_numbers}")
            else:
                print(f"无法计算点 {point_id} 的原始桩号")
        except Exception as e:
            print(f"保存原始桩号时出错: {str(e)}")
            traceback.print_exc()
    
    def store_offset_coordinates(self, point_id, new_coordinates):
        """
        保存点的偏移后坐标
        
        Args:
            point_id: 点的唯一标识符
            new_coordinates: 点的偏移后坐标 (x, y)
        """
        try:
            self.offset_coordinates[point_id] = new_coordinates
            print(f"已保存点 {point_id} 的偏移后坐标: {new_coordinates}")
        except Exception as e:
            print(f"保存偏移后坐标时出错: {str(e)}")
            traceback.print_exc()
    
    def get_original_grid_numbers(self, point_id):
        """
        获取点的原始桩号
        
        Args:
            point_id: 点的唯一标识符
            
        Returns:
            (line_number, stake_number): 原始桩号，如果不存在返回None
        """
        return self.original_grid_numbers.get(point_id)
    
    def get_offset_coordinates(self, point_id):
        """
        获取点的偏移后坐标
        
        Args:
            point_id: 点的唯一标识符
            
        Returns:
            (x, y): 偏移后坐标，如果不存在返回None
        """
        return self.offset_coordinates.get(point_id)
        
    def get_point_offset_info(self, point_id):
        """
        获取点的完整偏移信息
        
        Args:
            point_id: 点的唯一标识符
            
        Returns:
            {
                'original_grid_numbers': (line_number, stake_number),
                'offset_coordinates': (x, y),
                'offset_grid_numbers': (new_line_number, new_stake_number)
            }
        """
        result = {}
        
        # 获取原始桩号
        original_grid_numbers = self.get_original_grid_numbers(point_id)
        if original_grid_numbers:
            result['original_grid_numbers'] = original_grid_numbers
        
        # 获取偏移后坐标
        offset_coordinates = self.get_offset_coordinates(point_id)
        if offset_coordinates:
            result['offset_coordinates'] = offset_coordinates
            
            # 计算偏移后桩号
            offset_grid_numbers = self.calculate_grid_numbers(*offset_coordinates)
            if offset_grid_numbers:
                result['offset_grid_numbers'] = offset_grid_numbers
        
        return result if result else None

    def export_all_offset_info(self):
        """
        导出所有点的偏移信息
        
        Returns:
            {
                point_id: {
                    'original_grid_numbers': (line_number, stake_number),
                    'offset_coordinates': (x, y),
                    'offset_grid_numbers': (new_line_number, new_stake_number)
                },
                ...
            }
        """
        result = {}
        
        # 获取所有已保存点ID
        all_point_ids = set(self.original_grid_numbers.keys()) | set(self.offset_coordinates.keys())
        
        for point_id in all_point_ids:
            point_info = self.get_point_offset_info(point_id)
            if point_info:
                result[point_id] = point_info
        
        return result
        
    def export_offset_info_to_dataframe(self):
        """
        将偏移信息导出为适合DataFrame格式的数据
        
        Returns:
            [
                {
                    'point_id': id,
                    'original_line': line_number,
                    'original_stake': stake_number,
                    'offset_x': x,
                    'offset_y': y,
                    'offset_line': new_line_number,
                    'offset_stake': new_stake_number
                },
                ...
            ]
        """
        result = []
        all_info = self.export_all_offset_info()
        
        for point_id, info in all_info.items():
            row = {'point_id': point_id}
            
            # 添加原始桩号
            if 'original_grid_numbers' in info:
                orig_line, orig_stake = info['original_grid_numbers']
                row['original_line'] = orig_line
                row['original_stake'] = orig_stake
            
            # 添加偏移后坐标
            if 'offset_coordinates' in info:
                offset_x, offset_y = info['offset_coordinates']
                row['offset_x'] = offset_x
                row['offset_y'] = offset_y
            
            # 添加偏移后桩号
            if 'offset_grid_numbers' in info:
                offset_line, offset_stake = info['offset_grid_numbers']
                row['offset_line'] = offset_line
                row['offset_stake'] = offset_stake
            
            result.append(row)
        
        return result
