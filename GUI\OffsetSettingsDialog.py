from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QComboBox, QSpinBox, QDoubleSpinBox, QPushButton, 
                            QGroupBox, QFormLayout, QCheckBox, QTabWidget, QWidget,
                            QRadioButton, QButtonGroup, QMessageBox, QDialogButtonBox)
from PyQt5.QtCore import Qt
import os
import json

class OffsetSettingsDialog(QDialog):
    def __init__(self, point_offset_manager, parent=None):
        super().__init__(parent)
        self.setWindowTitle("震源点偏移设置")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)
        
        self.point_offset_manager = point_offset_manager
        self.current_settings = self.point_offset_manager.get_offset_settings()
        
        # 配置文件路径
        self.settings_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'config', 'offset_settings.json'
        )
        
        self.setup_ui()
        self.load_settings_from_file()
        
    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 添加基本设置标签页
        basic_tab = QWidget()
        tab_widget.addTab(basic_tab, "基本设置")
        
        # 添加高级设置标签页
        advanced_tab = QWidget()
        tab_widget.addTab(advanced_tab, "高级设置")
        
        # 设置基本设置页面
        self.setup_basic_tab(basic_tab)
        
        # 设置高级设置页面
        self.setup_advanced_tab(advanced_tab)
        
        main_layout.addWidget(tab_widget)
        
        # 添加说明标签
        description_label = QLabel("说明：选择合适的偏移策略对障碍物区域的震源点进行偏移")
        description_label.setStyleSheet("color: gray;")
        main_layout.addWidget(description_label)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Reset
        )
        buttons.accepted.connect(self.validate_and_accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.Reset).clicked.connect(self.reset_to_defaults)
        main_layout.addWidget(buttons)
    
    def setup_basic_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # =============== 策略选择组 ===============
        strategy_group = QGroupBox("偏移策略选择")
        strategy_layout = QVBoxLayout()
        
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItem("自动选择适合的策略", "auto")
        self.strategy_combo.addItem("选项1: 震源点在半个面元范围内偏移", "option1")
        self.strategy_combo.addItem("选项2: 震源点在面元范围内偏移", "option2")
        self.strategy_combo.addItem("选项3: 沿接收线方向偏移震源点", "option3")
        self.strategy_combo.addItem("选项4: 在接收线方向上的最大震源点偏移", "option4")
        self.strategy_combo.addItem("选项5: 补充震源点", "option5")
        self.strategy_combo.addItem("选项6: 在相邻测线区重新定位震源点", "option6")
        self.strategy_combo.addItem("选项7: 结合接收线方向移动的相邻测线区重定位", "option7")
        self.strategy_combo.addItem("选项8: 跳过震源点", "option8")
        self.strategy_combo.addItem("选项9: 障碍物边界优先偏移", "option9")
        
        # 设置当前选中的策略
        for i in range(self.strategy_combo.count()):
            if self.strategy_combo.itemData(i) == self.current_settings['strategy']:
                self.strategy_combo.setCurrentIndex(i)
                break
        
        self.strategy_combo.currentIndexChanged.connect(self.on_strategy_changed)
        strategy_layout.addWidget(self.strategy_combo)
        
        # 添加策略描述标签
        self.strategy_description = QLabel()
        self.strategy_description.setWordWrap(True)
        self.strategy_description.setStyleSheet("color: #666; margin-top: 5px;")
        strategy_layout.addWidget(self.strategy_description)
        
        strategy_group.setLayout(strategy_layout)
        layout.addWidget(strategy_group)
        
        # =============== 基本参数组 ===============
        params_group = QGroupBox("基本参数设置")
        params_layout = QFormLayout()
        
        # 面元尺寸
        self.surface_bin_size = QDoubleSpinBox()
        self.surface_bin_size.setRange(5.0, 100.0)
        self.surface_bin_size.setValue(self.current_settings['surface_bin_size'])
        self.surface_bin_size.setSuffix(" m")
        self.surface_bin_size.setSingleStep(1.0)
        self.surface_bin_size.setDecimals(1)
        self.surface_bin_size.valueChanged.connect(self.on_bin_size_changed)
        params_layout.addRow("面元尺寸:", self.surface_bin_size)
        
        # 接收点间隔
        self.receiver_interval = QDoubleSpinBox()
        self.receiver_interval.setRange(5.0, 100.0)
        self.receiver_interval.setValue(self.current_settings['receiver_interval'])
        self.receiver_interval.setSuffix(" m")
        self.receiver_interval.setSingleStep(1.0)
        self.receiver_interval.setDecimals(1)
        params_layout.addRow("接收点间隔:", self.receiver_interval)
        
        # 震源点间隔
        self.source_interval = QDoubleSpinBox()
        self.source_interval.setRange(5.0, 100.0)
        self.source_interval.setValue(self.current_settings['source_interval'])
        self.source_interval.setSuffix(" m")
        self.source_interval.setSingleStep(1.0)
        self.source_interval.setDecimals(1)
        params_layout.addRow("震源点间隔:", self.source_interval)
        
        # 最大偏移距离
        self.max_offset_distance = QDoubleSpinBox()
        self.max_offset_distance.setRange(10.0, 1000.0)
        self.max_offset_distance.setValue(self.point_offset_manager.max_offset_distance)
        self.max_offset_distance.setSuffix(" m")
        self.max_offset_distance.setSingleStep(10.0)
        self.max_offset_distance.setDecimals(1)
        self.max_offset_distance.setToolTip("建议设置为面元尺寸的1-2倍，设置过大可能导致点位偏移过远")
        # 添加最大偏移距离的建议值标签
        max_offset_layout = QHBoxLayout()
        max_offset_layout.addWidget(self.max_offset_distance)
        suggestion_label = QLabel("(推荐值: 面元尺寸的1-2倍)")
        suggestion_label.setStyleSheet("color: #888;")
        max_offset_layout.addWidget(suggestion_label)
        params_layout.addRow("最大偏移距离:", max_offset_layout)

        # 添加默认最大偏移距离按钮
        default_max_offset_button = QPushButton("设置为推荐值")
        default_max_offset_button.clicked.connect(self.set_recommended_max_offset)
        default_max_offset_button.setToolTip("将最大偏移距离设置为面元尺寸的2倍")
        max_offset_layout.addWidget(default_max_offset_button)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # 更新策略描述
        self.on_strategy_changed()
        
    def setup_advanced_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # =============== 高级参数组 ===============
        advanced_group = QGroupBox("高级参数设置")
        advanced_layout = QFormLayout()
        
        # 震源salvo线间距
        self.source_salvo_spacing = QDoubleSpinBox()
        self.source_salvo_spacing.setRange(10.0, 500.0)
        self.source_salvo_spacing.setValue(self.current_settings['source_salvo_spacing'])
        self.source_salvo_spacing.setSuffix(" m")
        self.source_salvo_spacing.setSingleStep(10.0)
        self.source_salvo_spacing.setDecimals(1)
        advanced_layout.addRow("震源salvo线间距:", self.source_salvo_spacing)
        
        # 最大偏移角度
        self.max_deviation_angle = QDoubleSpinBox()
        self.max_deviation_angle.setRange(0.0, 90.0)
        self.max_deviation_angle.setValue(self.current_settings['max_deviation_angle'])
        self.max_deviation_angle.setSuffix(" °")
        self.max_deviation_angle.setSingleStep(5.0)
        self.max_deviation_angle.setDecimals(1)
        advanced_layout.addRow("最大偏移角度:", self.max_deviation_angle)
        
        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)
        
        # =============== 特殊选项组 ===============
        options_group = QGroupBox("特殊选项")
        options_layout = QVBoxLayout()
        
        # 是否启用补充震源点
        self.enable_infill = QCheckBox("启用补充震源点")
        self.enable_infill.setChecked(self.current_settings['enable_infill'])
        self.enable_infill.setToolTip("当障碍物导致显著的震源跳过时，通过补充震源点维持覆盖度")
        options_layout.addWidget(self.enable_infill)
        
        # 是否允许跳过震源点
        self.skip_source_points = QCheckBox("允许跳过震源点")
        self.skip_source_points.setChecked(self.current_settings['skip_source_points'])
        self.skip_source_points.setToolTip("当无法找到合适的偏移位置时，是否允许跳过这个震源点")
        options_layout.addWidget(self.skip_source_points)
        
        # 是否优先保持原始位置
        self.prefer_original_position = QCheckBox("优先保持原始位置")
        self.prefer_original_position.setChecked(self.current_settings['prefer_original_position'])
        self.prefer_original_position.setToolTip("当原始位置不在障碍物内时，优先保持原位置而不是偏移")
        options_layout.addWidget(self.prefer_original_position)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
    def on_strategy_changed(self):
        # 更新策略描述
        strategy_data = self.strategy_combo.currentData()
        description = ""
        
        if strategy_data == "auto":
            description = "自动根据面元尺寸选择合适的策略。面元≥20m×20m时使用选项1，否则使用选项2。"
        elif strategy_data == "option1":
            description = "适用于面元尺寸≥20m×20m。震源点在半个面元范围内偏移，最多为震源或接收点间距的一半。"
        elif strategy_data == "option2":
            description = "适用于面元尺寸<20m×20m。震源点在面元范围内偏移，最多为震源或接收站间距。"
        elif strategy_data == "option3":
            description = "将震源点平行于接收线方向移动，步长为接收点间隔，最大距离不超过预规划震源组线间距。"
        elif strategy_data == "option4":
            description = "类似于选项3，但最大距离可达到震源组线间距的两倍，适用于较大障碍物。"
        elif strategy_data == "option5":
            description = "当障碍物导致显著的震源跳过时，通过补充震源点维持覆盖度。"
        elif strategy_data == "option6":
            description = "将震源点垂直于接收线方向移动一个接收线间隔到相邻测线区。"
        elif strategy_data == "option7":
            description = "结合选项3和选项6的方法，在相邻测线区中沿接收线方向进行偏移。"
        elif strategy_data == "option8":
            description = "当无法通过其他策略找到合适的偏移位置时，跳过震源点。"
        elif strategy_data == "option9":
            description = "先找出点到障碍物边界的最短路径，然后沿该方向偏移到障碍物外的最近网格中心点。适用于边界明确的复杂障碍物。"
            
        self.strategy_description.setText(description)
        
    def on_bin_size_changed(self):
        # 如果策略是auto，更新策略描述
        if self.strategy_combo.currentData() == "auto":
            self.on_strategy_changed()
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        # 默认设置
        default_settings = {
            'strategy': 'auto',
            'surface_bin_size': 25.0,
            'receiver_interval': 25.0,
            'source_interval': 25.0,
            'source_salvo_spacing': 100.0,
            'max_deviation_angle': 45.0,
            'enable_infill': False,
            'skip_source_points': False,
            'prefer_original_position': True
        }
        
        # 重置界面
        self.strategy_combo.setCurrentIndex(0)  # auto
        self.surface_bin_size.setValue(default_settings['surface_bin_size'])
        self.receiver_interval.setValue(default_settings['receiver_interval'])
        self.source_interval.setValue(default_settings['source_interval'])
        self.max_offset_distance.setValue(100.0)  # 默认最大偏移距离
        self.source_salvo_spacing.setValue(default_settings['source_salvo_spacing'])
        self.max_deviation_angle.setValue(default_settings['max_deviation_angle'])
        self.enable_infill.setChecked(default_settings['enable_infill'])
        self.skip_source_points.setChecked(default_settings['skip_source_points'])
        self.prefer_original_position.setChecked(default_settings['prefer_original_position'])
        
        # 更新策略描述
        self.on_strategy_changed()
    
    def validate_and_accept(self):
        """验证并接受设置"""
        try:
            # 获取当前设置
            settings = self.get_settings()
            
            # 应用设置到PointOffsetManager
            self.point_offset_manager.set_offset_settings(settings)
            self.point_offset_manager.set_max_offset_distance(self.max_offset_distance.value())
            
            # 保存设置到文件
            self.save_settings_to_file()
            
            self.accept()
        except Exception as e:
            QMessageBox.warning(self, "警告", f"保存设置时出错：{str(e)}")
    
    def get_settings(self):
        """获取当前设置"""
        return {
            'strategy': self.strategy_combo.currentData(),
            'surface_bin_size': self.surface_bin_size.value(),
            'receiver_interval': self.receiver_interval.value(),
            'source_interval': self.source_interval.value(),
            'source_salvo_spacing': self.source_salvo_spacing.value(),
            'max_deviation_angle': self.max_deviation_angle.value(),
            'enable_infill': self.enable_infill.isChecked(),
            'skip_source_points': self.skip_source_points.isChecked(),
            'prefer_original_position': self.prefer_original_position.isChecked()
        }
        
    def save_settings_to_file(self):
        """保存设置到文件"""
        try:
            settings = self.get_settings()
            settings['max_offset_distance'] = self.max_offset_distance.value()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
                
            print(f"已保存偏移设置到：{self.settings_file}")
            
        except Exception as e:
            print(f"保存设置到文件时出错：{str(e)}")
            raise
            
    def load_settings_from_file(self):
        """从文件加载设置"""
        if not os.path.exists(self.settings_file):
            print(f"偏移设置文件不存在：{self.settings_file}")
            return
            
        try:
            with open(self.settings_file, 'r') as f:
                settings = json.load(f)
                
            # 应用设置到界面
            for i in range(self.strategy_combo.count()):
                if self.strategy_combo.itemData(i) == settings.get('strategy', 'auto'):
                    self.strategy_combo.setCurrentIndex(i)
                    break
                    
            self.surface_bin_size.setValue(settings.get('surface_bin_size', 25.0))
            self.receiver_interval.setValue(settings.get('receiver_interval', 25.0))
            self.source_interval.setValue(settings.get('source_interval', 25.0))
            self.max_offset_distance.setValue(settings.get('max_offset_distance', 100.0))
            self.source_salvo_spacing.setValue(settings.get('source_salvo_spacing', 100.0))
            self.max_deviation_angle.setValue(settings.get('max_deviation_angle', 45.0))
            self.enable_infill.setChecked(settings.get('enable_infill', False))
            self.skip_source_points.setChecked(settings.get('skip_source_points', False))
            self.prefer_original_position.setChecked(settings.get('prefer_original_position', True))
            
            # 更新策略描述
            self.on_strategy_changed()
            
            print(f"已加载偏移设置：{self.settings_file}")
            
        except Exception as e:
            print(f"加载设置文件时出错：{str(e)}")

    def set_recommended_max_offset(self):
        """将最大偏移距离设置为面元尺寸的2倍"""
        recommended_value = 2 * self.surface_bin_size.value()
        self.max_offset_distance.setValue(recommended_value)
        QMessageBox.information(self, "已设置推荐值",
                             f"已将最大偏移距离设置为面元尺寸的2倍：{recommended_value}米。\n\n"
                             f"这有助于防止点位偏移过远，并且在大多数情况下可以找到合适的偏移位置。")
    
    def on_strategy_changed(self):
        # 更新策略描述
        strategy_data = self.strategy_combo.currentData()
        description = ""
        
        if strategy_data == "auto":
            description = "自动根据面元尺寸选择合适的策略。面元≥20m×20m时使用选项1，否则使用选项2。"
        elif strategy_data == "option1":
            description = "适用于面元尺寸≥20m×20m。震源点在半个面元范围内偏移，最多为震源或接收点间距的一半。"
        elif strategy_data == "option2":
            description = "适用于面元尺寸<20m×20m。震源点在面元范围内偏移，最多为震源或接收站间距。"
        elif strategy_data == "option3":
            description = "将震源点平行于接收线方向移动，步长为接收点间隔，最大距离不超过预规划震源组线间距。"
        elif strategy_data == "option4":
            description = "类似于选项3，但最大距离可达到震源组线间距的两倍，适用于较大障碍物。"
        elif strategy_data == "option5":
            description = "当障碍物导致显著的震源跳过时，通过补充震源点维持覆盖度。"
        elif strategy_data == "option6":
            description = "将震源点垂直于接收线方向移动一个接收线间隔到相邻测线区。"
        elif strategy_data == "option7":
            description = "结合选项3和选项6的方法，在相邻测线区中沿接收线方向进行偏移。"
        elif strategy_data == "option8":
            description = "当无法通过其他策略找到合适的偏移位置时，跳过震源点。"
        elif strategy_data == "option9":
            description = "先找出点到障碍物边界的最短路径，然后沿该方向偏移到障碍物外的最近网格中心点。适用于边界明确的复杂障碍物。"
            
        self.strategy_description.setText(description)
        
    def on_bin_size_changed(self):
        # 如果策略是auto，更新策略描述
        if self.strategy_combo.currentData() == "auto":
            self.on_strategy_changed()
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        # 默认设置
        default_settings = {
            'strategy': 'auto',
            'surface_bin_size': 25.0,
            'receiver_interval': 25.0,
            'source_interval': 25.0,
            'source_salvo_spacing': 100.0,
            'max_deviation_angle': 45.0,
            'enable_infill': False,
            'skip_source_points': False,
            'prefer_original_position': True
        }
        
        # 重置界面
        self.strategy_combo.setCurrentIndex(0)  # auto
        self.surface_bin_size.setValue(default_settings['surface_bin_size'])
        self.receiver_interval.setValue(default_settings['receiver_interval'])
        self.source_interval.setValue(default_settings['source_interval'])
        self.max_offset_distance.setValue(100.0)  # 默认最大偏移距离
        self.source_salvo_spacing.setValue(default_settings['source_salvo_spacing'])
        self.max_deviation_angle.setValue(default_settings['max_deviation_angle'])
        self.enable_infill.setChecked(default_settings['enable_infill'])
        self.skip_source_points.setChecked(default_settings['skip_source_points'])
        self.prefer_original_position.setChecked(default_settings['prefer_original_position'])
        
        # 更新策略描述
        self.on_strategy_changed()
    
    def validate_and_accept(self):
        """验证并接受设置"""
        try:
            # 获取当前设置
            settings = self.get_settings()
            
            # 应用设置到PointOffsetManager
            self.point_offset_manager.set_offset_settings(settings)
            self.point_offset_manager.set_max_offset_distance(self.max_offset_distance.value())
            
            # 保存设置到文件
            self.save_settings_to_file()
            
            self.accept()
        except Exception as e:
            QMessageBox.warning(self, "警告", f"保存设置时出错：{str(e)}")
    
    def get_settings(self):
        """获取当前设置"""
        return {
            'strategy': self.strategy_combo.currentData(),
            'surface_bin_size': self.surface_bin_size.value(),
            'receiver_interval': self.receiver_interval.value(),
            'source_interval': self.source_interval.value(),
            'source_salvo_spacing': self.source_salvo_spacing.value(),
            'max_deviation_angle': self.max_deviation_angle.value(),
            'enable_infill': self.enable_infill.isChecked(),
            'skip_source_points': self.skip_source_points.isChecked(),
            'prefer_original_position': self.prefer_original_position.isChecked()
        }
        
    def save_settings_to_file(self):
        """保存设置到文件"""
        try:
            settings = self.get_settings()
            settings['max_offset_distance'] = self.max_offset_distance.value()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
                
            print(f"已保存偏移设置到：{self.settings_file}")
            
        except Exception as e:
            print(f"保存设置到文件时出错：{str(e)}")
            raise
            
    def load_settings_from_file(self):
        """从文件加载设置"""
        if not os.path.exists(self.settings_file):
            print(f"偏移设置文件不存在：{self.settings_file}")
            return
            
        try:
            with open(self.settings_file, 'r') as f:
                settings = json.load(f)
                
            # 应用设置到界面
            for i in range(self.strategy_combo.count()):
                if self.strategy_combo.itemData(i) == settings.get('strategy', 'auto'):
                    self.strategy_combo.setCurrentIndex(i)
                    break
                    
            self.surface_bin_size.setValue(settings.get('surface_bin_size', 25.0))
            self.receiver_interval.setValue(settings.get('receiver_interval', 25.0))
            self.source_interval.setValue(settings.get('source_interval', 25.0))
            self.max_offset_distance.setValue(settings.get('max_offset_distance', 100.0))
            self.source_salvo_spacing.setValue(settings.get('source_salvo_spacing', 100.0))
            self.max_deviation_angle.setValue(settings.get('max_deviation_angle', 45.0))
            self.enable_infill.setChecked(settings.get('enable_infill', False))
            self.skip_source_points.setChecked(settings.get('skip_source_points', False))
            self.prefer_original_position.setChecked(settings.get('prefer_original_position', True))
            
            # 更新策略描述
            self.on_strategy_changed()
            
            print(f"已加载偏移设置：{self.settings_file}")
            
        except Exception as e:
            print(f"加载设置文件时出错：{str(e)}") 