#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mask文件转Shapefile工具
将GEE导出的障碍物掩码图像转换为矢量shapefile格式

依赖库安装：
pip install rasterio geopandas shapely fiona

作者: Assistant
日期: 2025
"""

import os
import glob
import rasterio
import geopandas as gpd
import pandas as pd
from rasterio.features import shapes
from shapely.geometry import shape
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def raster_to_shapefile(input_raster, output_shp, mask_value=1, simplify_tolerance=0.0001, block_size=1000):
    """
    将栅格掩码文件转换为shapefile（支持大文件分块处理）
    
    Parameters:
    -----------
    input_raster : str
        输入的掩码文件路径（.tif格式）
    output_shp : str
        输出的shapefile路径（.shp格式）
    mask_value : int
        需要矢量化的像素值（默认为1，即障碍物）
    simplify_tolerance : float
        几何简化容差，减少节点数量
    block_size : int
        分块处理的大小（像素）
    """
    import time
    import numpy as np
    from collections import defaultdict
    
    try:
        start_time = time.time()
        
        # 读取栅格数据
        with rasterio.open(input_raster) as src:
            # 获取基本信息
            transform = src.transform
            crs = src.crs
            height, width = src.shape
            total_pixels = height * width
            
            print(f"处理文件: {os.path.basename(input_raster)}")
            print(f"  - 影像尺寸: {height} x {width} ({total_pixels:,} 像素)")
            print(f"  - 坐标系统: {crs}")
            print(f"  - 像素分辨率: {transform[0]:.6f}°")
            print(f"  - 文件大小: {os.path.getsize(input_raster)/1024/1024:.1f} MB")
            
            # 检查文件是否过大，使用分块处理
            if total_pixels > 50_000_000:  # 超过5000万像素
                print(f"  📊 检测到大文件，使用分块处理 (块大小: {block_size}x{block_size})")
                return process_large_raster(src, output_shp, mask_value, simplify_tolerance, block_size)
            
            print(f"  🔄 正在读取影像数据...")
            # 读取第一个波段
            image = src.read(1)
            
            print(f"  🔍 正在分析像素值分布...")
            unique_values, counts = np.unique(image, return_counts=True)
            for val, count in zip(unique_values, counts):
                percentage = (count / total_pixels) * 100
                print(f"     像素值 {val}: {count:,} 个 ({percentage:.2f}%)")
            
            # 检查是否存在目标值
            if mask_value not in unique_values:
                print(f"  ⚠️  警告: 未发现值为{mask_value}的像素，跳过此文件")
                return False
            
            target_pixels = counts[unique_values == mask_value][0]
            print(f"  🎯 目标像素数量: {target_pixels:,} ({target_pixels/total_pixels*100:.2f}%)")
            
            # 创建掩码（只提取指定值的像素）
            print(f"  🔄 正在创建掩码...")
            mask = image == mask_value
            
            # 将栅格转换为矢量几何
            print(f"  🔄 正在进行矢量化转换...")
            print(f"     这可能需要几分钟时间，请耐心等待...")
            
            vectorize_start = time.time()
            results = []
            polygon_count = 0
            
            for i, (geom, value) in enumerate(shapes(image, mask=mask, transform=transform)):
                if value == mask_value:
                    # 转换为Shapely几何对象
                    geometry = shape(geom)
                    
                    # 几何简化（可选，减少文件大小）
                    if simplify_tolerance > 0:
                        geometry = geometry.simplify(simplify_tolerance)
                    
                    # 过滤掉过小的多边形（面积阈值可调整）
                    if geometry.area > 1e-8:  # 很小的面积阈值
                        results.append({
                            'geometry': geometry,
                            'obstacle_id': len(results) + 1,
                            'area_deg2': geometry.area,  # 度为单位的面积
                            'mask_value': int(value)
                        })
                        polygon_count += 1
                        
                        # 每1000个多边形显示一次进度
                        if polygon_count % 1000 == 0:
                            elapsed = time.time() - vectorize_start
                            print(f"     已处理 {polygon_count:,} 个多边形... ({elapsed:.1f}s)")
            
            vectorize_time = time.time() - vectorize_start
            print(f"  ✅ 矢量化完成！耗时: {vectorize_time:.1f}s")
            
            if not results:
                print(f"  ⚠️  警告: 矢量化后无有效多边形，跳过此文件")
                return False
                
            print(f"  🔄 正在创建GeoDataFrame...")
            # 创建GeoDataFrame
            gdf = gpd.GeoDataFrame(results, crs=crs)
            
            print(f"  🔄 正在计算面积...")
            # 如果CRS是地理坐标系，转换为适合的投影坐标系计算面积
            if crs and crs.to_string() == 'EPSG:4326':
                # 对于沙特地区，使用UTM Zone 38N
                gdf_utm = gdf.to_crs('EPSG:32638')
                gdf['area_sqm'] = gdf_utm.geometry.area
                
            print(f"  📊 统计结果:")
            print(f"     - 发现障碍物多边形数量: {len(gdf):,}")
            print(f"     - 总面积: {gdf['area_sqm'].sum()/1e6:.2f} 平方公里")
            print(f"     - 平均多边形面积: {gdf['area_sqm'].mean():.1f} 平方米")
            print(f"     - 最大多边形面积: {gdf['area_sqm'].max()/1e6:.3f} 平方公里")
            
            # 保存shapefile
            print(f"  🔄 正在保存shapefile...")
            os.makedirs(os.path.dirname(output_shp), exist_ok=True)
            gdf.to_file(output_shp)
            
            total_time = time.time() - start_time
            print(f"  ✅ 成功保存: {output_shp}")
            print(f"  ⏱️  总处理时间: {total_time:.1f}s")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 错误: {str(e)}")
        import traceback
        print(f"  📋 详细错误信息:")
        traceback.print_exc()
        return False

def process_large_raster(src, output_shp, mask_value=1, simplify_tolerance=0.0001, block_size=1000):
    """
    处理大型栅格文件的分块处理函数
    """
    import time
    from rasterio.windows import Window
    
    print(f"  🚀 启动大文件分块处理模式...")
    
    height, width = src.shape
    transform = src.transform
    crs = src.crs
    
    all_results = []
    total_blocks = 0
    processed_blocks = 0
    
    # 计算需要处理的块数
    for row in range(0, height, block_size):
        for col in range(0, width, block_size):
            total_blocks += 1
    
    print(f"  📊 总共需要处理 {total_blocks} 个数据块")
    
    start_time = time.time()
    
    # 分块处理
    for row in range(0, height, block_size):
        for col in range(0, width, block_size):
            processed_blocks += 1
            
            # 计算当前块的窗口
            window_height = min(block_size, height - row)
            window_width = min(block_size, width - col)
            window = Window(col, row, window_width, window_height)
            
            # 读取当前块
            block_data = src.read(1, window=window)
            
            # 检查是否包含目标值
            if mask_value not in block_data:
                continue
                
            # 计算当前块的变换矩阵
            block_transform = rasterio.windows.transform(window, transform)
            
            # 创建掩码
            mask = block_data == mask_value
            
            # 矢量化当前块
            for geom, value in shapes(block_data, mask=mask, transform=block_transform):
                if value == mask_value:
                    geometry = shape(geom)
                    
                    if simplify_tolerance > 0:
                        geometry = geometry.simplify(simplify_tolerance)
                    
                    if geometry.area > 1e-8:
                        all_results.append({
                            'geometry': geometry,
                            'obstacle_id': len(all_results) + 1,
                            'area_deg2': geometry.area,
                            'mask_value': int(value)
                        })
            
            # 显示进度
            progress = (processed_blocks / total_blocks) * 100
            elapsed = time.time() - start_time
            eta = elapsed * (total_blocks - processed_blocks) / processed_blocks if processed_blocks > 0 else 0
            
            print(f"  📈 进度: {processed_blocks}/{total_blocks} ({progress:.1f}%) "
                  f"| 已用时: {elapsed:.1f}s | 预计剩余: {eta:.1f}s | 多边形数: {len(all_results):,}")
    
    if not all_results:
        print(f"  ⚠️  警告: 分块处理后无有效多边形")
        return False
    
    print(f"  🔄 正在创建最终GeoDataFrame...")
    gdf = gpd.GeoDataFrame(all_results, crs=crs)
    
    # 计算面积
    if crs and crs.to_string() == 'EPSG:4326':
        print(f"  🔄 正在转换坐标系并计算面积...")
        gdf_utm = gdf.to_crs('EPSG:32638')
        gdf['area_sqm'] = gdf_utm.geometry.area
    
    # 保存结果
    print(f"  🔄 正在保存shapefile...")
    os.makedirs(os.path.dirname(output_shp), exist_ok=True)
    gdf.to_file(output_shp)
    
    total_time = time.time() - start_time
    print(f"  ✅ 分块处理完成！")
    print(f"     - 多边形数量: {len(gdf):,}")
    print(f"     - 总面积: {gdf['area_sqm'].sum()/1e6:.2f} 平方公里") 
    print(f"     - 总处理时间: {total_time:.1f}s")
    print(f"     - 输出文件: {output_shp}")
    
    return True

def batch_convert_masks(input_folder, output_folder, pattern="*mask*.tif"):
    """
    批量转换mask文件
    
    Parameters:
    -----------
    input_folder : str
        包含mask文件的输入文件夹
    output_folder : str
        shapefile输出文件夹
    pattern : str
        文件名匹配模式
    """
    
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 查找所有mask文件
    search_pattern = os.path.join(input_folder, pattern)
    mask_files = glob.glob(search_pattern)
    
    if not mask_files:
        print(f"❌ 未找到匹配的文件: {search_pattern}")
        return
    
    print(f"📁 找到 {len(mask_files)} 个mask文件")
    print("=" * 60)
    
    success_count = 0
    
    for i, mask_file in enumerate(mask_files, 1):
        print(f"\n[{i}/{len(mask_files)}] 正在处理...")
        
        # 生成输出文件名
        base_name = Path(mask_file).stem
        output_shp = os.path.join(output_folder, f"{base_name}.shp")
        
        # 转换文件
        if raster_to_shapefile(mask_file, output_shp):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 批量转换完成！成功处理 {success_count}/{len(mask_files)} 个文件")
    print(f"📂 输出位置: {output_folder}")

def merge_all_shapefiles(shapefile_folder, output_merged="merged_obstacles.shp"):
    """
    合并所有的shapefile为一个文件
    
    Parameters:
    -----------
    shapefile_folder : str
        包含shapefile的文件夹
    output_merged : str
        合并后的文件名
    """
    try:
        shp_files = glob.glob(os.path.join(shapefile_folder, "*.shp"))
        
        if not shp_files:
            print("❌ 未找到shapefile文件")
            return
        
        print(f"🔄 正在合并 {len(shp_files)} 个shapefile...")
        
        # 读取所有shapefile并合并
        gdfs = []
        for shp_file in shp_files:
            gdf = gpd.read_file(shp_file)
            gdf['source_file'] = os.path.basename(shp_file)
            gdfs.append(gdf)
        
        # 合并所有GeoDataFrame
        merged_gdf = pd.concat(gdfs, ignore_index=True)
        
        # 重新编号
        merged_gdf['obstacle_id'] = range(1, len(merged_gdf) + 1)
        
        # 保存合并结果
        output_path = os.path.join(shapefile_folder, output_merged)
        merged_gdf.to_file(output_path)
        
        print(f"✅ 合并完成!")
        print(f"  - 总多边形数量: {len(merged_gdf)}")
        print(f"  - 总面积: {merged_gdf['area_sqm'].sum()/1e6:.2f} 平方公里")
        print(f"  - 输出文件: {output_path}")
        
    except Exception as e:
        print(f"❌ 合并过程出错: {str(e)}")

if __name__ == "__main__":
    # =============================================================================
    # 🔧 配置参数 - 请根据你的实际情况修改这些路径
    # =============================================================================
    
    # 输入文件夹（存放从GEE下载的mask文件）
    INPUT_FOLDER = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted\mask"
    
    # 输出文件夹（生成的shapefile存放位置）
    OUTPUT_FOLDER = r"D:\Satellite_Image_Auto_Offset\GIS Data from GEE\converted\mask\output"
    
    # 文件匹配模式（匹配所有包含"mask"的.tif文件）
    FILE_PATTERN = "*mask*.tif"
    
    # =============================================================================
    # 🚀 执行转换
    # =============================================================================
    
    print("🔄 开始Mask转Shapefile任务...")
    print(f"📂 输入目录: {INPUT_FOLDER}")
    print(f"📂 输出目录: {OUTPUT_FOLDER}")
    
    # 批量转换
    batch_convert_masks(INPUT_FOLDER, OUTPUT_FOLDER, FILE_PATTERN)
    
    # 询问是否合并所有shapefile
    print("\n" + "="*60)
    merge_choice = input("是否要将所有shapefile合并为一个文件？(y/n): ").lower().strip()
    
    if merge_choice in ['y', 'yes', '是']:
        merge_all_shapefiles(OUTPUT_FOLDER, "saudi_north_obstacles_merged.shp")
    
    print("\n🎯 任务完成！")
    
    # =============================================================================
    # 💡 使用说明
    # =============================================================================
    print("""
💡 使用说明：
1. 确保已安装必要的Python库：pip install rasterio geopandas shapely fiona
2. 修改脚本中的 INPUT_FOLDER 和 OUTPUT_FOLDER 路径
3. 将GEE下载的mask文件放在INPUT_FOLDER中
4. 运行脚本，程序会自动处理所有匹配的mask文件
5. 生成的shapefile可以在QGIS、ArcGIS等GIS软件中打开
    
📊 输出信息：
- obstacle_id: 障碍物编号
- area_sqm: 面积（平方米）
- mask_value: 原始掩码值
- source_file: 来源文件名（仅在合并文件中）
    """)