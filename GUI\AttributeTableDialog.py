#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
属性表对话框
显示图层中每个要素的属性信息，类似GIS软件的属性表功能
"""

import os
import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QLabel, QPushButton, QHeaderView,
                            QMessageBox, QLineEdit, QComboBox, QCheckBox,
                            QToolBar, QAction, QFileDialog, QProgressBar,
                            QSplitter, QFrame, QSpinBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QColor, QPalette
import geopandas as gpd
import pandas as pd
import numpy as np

class AttributeTableDialog(QDialog):
    """属性表对话框类"""
    
    # 信号定义
    feature_selected = pyqtSignal(int)  # 要素选中信号
    feature_highlighted = pyqtSignal(int)  # 要素高亮信号
    
    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.gdf = None
        self.filtered_gdf = None
        self.current_selection = []
        
        # 设置对话框属性
        self.setWindowTitle(f"属性表 - {os.path.basename(file_path)}")
        self.setMinimumSize(800, 600)
        self.resize(1200, 800)
        
        # 加载数据
        self.load_data()
        
        # 初始化UI
        self.init_ui()
        
        # 填充表格
        self.populate_table()
        
    def load_data(self):
        """加载图层数据"""
        try:
            self.gdf = gpd.read_file(self.file_path)
            self.filtered_gdf = self.gdf.copy()
            print(f"成功加载图层数据: {len(self.gdf)} 个要素")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法加载图层数据:\n{str(e)}")
            self.reject()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建工具栏
        self.create_toolbar(layout)
        
        # 创建状态栏
        self.create_status_bar(layout)
        
        # 创建主要内容区域
        self.create_main_content(layout)
        
        # 创建底部按钮栏
        self.create_button_bar(layout)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(2, 2, 2, 2)
        
        # 选择工具
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_features)
        toolbar_layout.addWidget(self.select_all_btn)
        
        self.clear_selection_btn = QPushButton("清除选择")
        self.clear_selection_btn.clicked.connect(self.clear_selection)
        toolbar_layout.addWidget(self.clear_selection_btn)
        
        toolbar_layout.addWidget(QFrame())  # 分隔符
        
        # 过滤工具
        toolbar_layout.addWidget(QLabel("过滤:"))
        self.filter_column_combo = QComboBox()
        self.filter_column_combo.setMinimumWidth(120)
        toolbar_layout.addWidget(self.filter_column_combo)
        
        self.filter_value_edit = QLineEdit()
        self.filter_value_edit.setPlaceholderText("输入过滤值...")
        self.filter_value_edit.returnPressed.connect(self.apply_filter)
        toolbar_layout.addWidget(self.filter_value_edit)
        
        self.apply_filter_btn = QPushButton("应用过滤")
        self.apply_filter_btn.clicked.connect(self.apply_filter)
        toolbar_layout.addWidget(self.apply_filter_btn)
        
        self.clear_filter_btn = QPushButton("清除过滤")
        self.clear_filter_btn.clicked.connect(self.clear_filter)
        toolbar_layout.addWidget(self.clear_filter_btn)
        
        toolbar_layout.addStretch()
        
        # 导出按钮
        self.export_btn = QPushButton("导出...")
        self.export_btn.clicked.connect(self.export_data)
        toolbar_layout.addWidget(self.export_btn)
        
        parent_layout.addWidget(toolbar_frame)
    
    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(5, 2, 5, 2)
        
        self.status_label = QLabel()
        self.update_status()
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 显示几何信息
        if len(self.gdf) > 0:
            geom_type = self.gdf.geometry.iloc[0].geom_type
            geom_label = QLabel(f"几何类型: {geom_type}")
            status_layout.addWidget(geom_label)
        
        parent_layout.addWidget(status_frame)
    
    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 创建表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.ExtendedSelection)
        
        # 连接信号
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        parent_layout.addWidget(self.table)
    
    def create_button_bar(self, parent_layout):
        """创建底部按钮栏"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(5, 5, 5, 5)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        parent_layout.addWidget(button_frame)
    
    def populate_table(self):
        """填充表格数据"""
        if self.filtered_gdf is None or len(self.filtered_gdf) == 0:
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return
        
        # 准备数据（排除几何列）
        display_data = self.filtered_gdf.drop(columns=['geometry'])
        
        # 设置表格尺寸
        self.table.setRowCount(len(display_data))
        self.table.setColumnCount(len(display_data.columns))
        
        # 设置列标题
        self.table.setHorizontalHeaderLabels(display_data.columns.tolist())
        
        # 填充过滤下拉框
        self.filter_column_combo.clear()
        self.filter_column_combo.addItem("-- 选择列 --")
        self.filter_column_combo.addItems(display_data.columns.tolist())
        
        # 填充数据
        for row_idx, (_, row) in enumerate(display_data.iterrows()):
            for col_idx, value in enumerate(row):
                # 处理不同类型的值
                if pd.isna(value):
                    display_value = "NULL"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor(128, 128, 128))  # 灰色显示NULL值
                else:
                    display_value = str(value)
                    item = QTableWidgetItem(display_value)
                
                # 设置项目属性
                item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
                self.table.setItem(row_idx, col_idx, item)
        
        # 调整列宽
        self.table.resizeColumnsToContents()
        
        # 设置行号
        for i in range(len(display_data)):
            self.table.setVerticalHeaderItem(i, QTableWidgetItem(str(i + 1)))
        
        # 更新状态
        self.update_status()
    
    def update_status(self):
        """更新状态栏"""
        if self.filtered_gdf is not None:
            total_features = len(self.gdf)
            filtered_features = len(self.filtered_gdf)
            selected_features = len(self.current_selection)
            
            if filtered_features == total_features:
                status_text = f"要素总数: {total_features}, 已选择: {selected_features}"
            else:
                status_text = f"要素总数: {total_features}, 已过滤: {filtered_features}, 已选择: {selected_features}"
            
            self.status_label.setText(status_text)
    
    def on_selection_changed(self):
        """处理选择变化"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())
        
        self.current_selection = list(selected_rows)
        self.update_status()
        
        # 发出选择信号
        if len(self.current_selection) == 1:
            actual_index = self.filtered_gdf.index[self.current_selection[0]]
            self.feature_selected.emit(actual_index)
    
    def on_cell_double_clicked(self, row, column):
        """处理单元格双击"""
        if row < len(self.filtered_gdf):
            actual_index = self.filtered_gdf.index[row]
            self.feature_highlighted.emit(actual_index)
    
    def select_all_features(self):
        """选择所有要素"""
        self.table.selectAll()
    
    def clear_selection(self):
        """清除选择"""
        self.table.clearSelection()
    
    def apply_filter(self):
        """应用过滤"""
        column = self.filter_column_combo.currentText()
        value = self.filter_value_edit.text().strip()
        
        if column == "-- 选择列 --" or not value:
            return
        
        try:
            # 应用过滤
            if column in self.gdf.columns:
                # 字符串包含过滤
                mask = self.gdf[column].astype(str).str.contains(value, case=False, na=False)
                self.filtered_gdf = self.gdf[mask]
                
                # 重新填充表格
                self.populate_table()
                
                QMessageBox.information(self, "过滤完成", 
                    f"过滤完成，显示 {len(self.filtered_gdf)} / {len(self.gdf)} 个要素")
            
        except Exception as e:
            QMessageBox.warning(self, "过滤错误", f"过滤时出错:\n{str(e)}")
    
    def clear_filter(self):
        """清除过滤"""
        self.filtered_gdf = self.gdf.copy()
        self.filter_value_edit.clear()
        self.filter_column_combo.setCurrentIndex(0)
        self.populate_table()
    
    def export_data(self):
        """导出数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出属性表", 
                f"{os.path.splitext(os.path.basename(self.file_path))[0]}_attributes.csv",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*.*)"
            )
            
            if file_path:
                # 准备导出数据（排除几何列）
                export_data = self.filtered_gdf.drop(columns=['geometry'])
                
                if file_path.lower().endswith('.xlsx'):
                    export_data.to_excel(file_path, index=False)
                else:
                    export_data.to_csv(file_path, index=False, encoding='utf-8-sig')
                
                QMessageBox.information(self, "导出成功", f"属性表已导出到:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出时出错:\n{str(e)}")
