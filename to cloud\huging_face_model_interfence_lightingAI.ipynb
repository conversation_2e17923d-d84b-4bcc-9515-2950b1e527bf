{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 456, "status": "ok", "timestamp": 1739615779417, "user": {"displayName": "跃龙孙", "userId": "12942605446822083389"}, "user_tz": -180}, "id": "o6aIU1hyERmc", "outputId": "b9e10c41-4fa1-4e79-b907-7f9299434e83"}, "outputs": [], "source": ["!ls \"/teamspace/studios/this_studio/\""]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 99828, "status": "ok", "timestamp": 1739615883241, "user": {"displayName": "跃龙孙", "userId": "12942605446822083389"}, "user_tz": -180}, "id": "DxtLw4oj_Byv", "outputId": "fbd7fa27-dce6-42ff-8d77-e3f6fde11454"}, "outputs": [], "source": ["# 安装依赖\n", "!pip install -q mercantile pyproj pillow requests rasterio transformers torch geopandas huggingface_hub leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install opencv-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda update pytorch torchvision torchaudio -c pytorch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda uninstall numpy scipy scikit-learn --yes\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda install numpy scipy scikit-learn --yes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda uninstall numpy scikit-image --yes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conda install numpy scikit-image --yes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hf4w-PpxJq6u"}, "outputs": [], "source": ["# -*- coding: utf-8 -*-\n", "\"\"\"\n", "卫星影像智能解译系统\n", "功能：下载指定区域的卫星图像并进行语义分割\n", "\"\"\"\n", "\n", "import os\n", "import math\n", "import random\n", "import numpy as np\n", "import math\n", "import requests\n", "import rasterio\n", "import mercantile\n", "from PIL import Image\n", "from io import BytesIO\n", "from tqdm import tqdm\n", "from rasterio.transform import from_origin\n", "from huggingface_hub import snapshot_download\n", "from transformers import SegformerImageProcessor, SegformerForSemanticSegmentation\n", "import torch\n", "from skimage.transform import resize\n", "from requests.adapters import HTTPAdapter\n", "from urllib3.util.retry import Retry\n", "import geopandas as gpd\n", "from shapely.geometry import Polygon, mapping\n", "import rasterio.features\n", "from rasterio.warp import transform_geom\n", "import cv2\n", "\n", "# 配置参数\n", "ZOOM_LEVEL = 18  # 卫星图像缩放级别\n", "TILE_SIZE = 256  # 瓦片大小\n", "MAX_TILES = 200  # 最大下载瓦片数\n", "MODEL_NAME = \"nvidia/segformer-b5-finetuned-ade-640-640\"  # Hugging Face模型\n", "HF_TOKEN = None  # Hugging Face Token\n", "DEVICE = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "class CoordinateReader:\n", "    \"\"\"坐标文件读取器\"\"\"\n", "    @staticmethod\n", "    def read_coords(filename):\n", "        \"\"\"读取坐标文件\"\"\"\n", "        try:\n", "            with open(filename, 'r') as f:\n", "                coords = []\n", "                for line in f:\n", "                    line = line.strip()\n", "                    if not line or line.startswith('#'):\n", "                        continue\n", "                    try:\n", "                        lon, lat = map(float, line.split(','))\n", "                        coords.append((lon, lat))\n", "                    except ValueError:\n", "                        print(f\"跳过无效行: {line}\")\n", "                \n", "                if len(coords) < 3:\n", "                    raise ValueError(\"至少需要3个坐标点\")\n", "                \n", "                # 确保多边形闭合\n", "                if coords[0] != coords[-1]:\n", "                    coords.append(coords[0])\n", "                \n", "                print(f\"成功读取多边形坐标，顶点数: {len(coords)}\")\n", "                return coords\n", "        except Exception as e:\n", "            print(f\"读取坐标文件失败: {e}\")\n", "            return None\n", "\n", "class SatelliteDownloader:\n", "    \"\"\"卫星图像下载器\"\"\"\n", "    def __init__(self, map_source=\"Google\"):\n", "        self.map_source = map_source\n", "        self.session = self._configure_session()\n", "        self.tile_url_template = \"https://mt{}.google.com/vt/lyrs=s&x={}&y={}&z={}\"\n", "        \n", "    def _configure_session(self):\n", "        \"\"\"配置请求会话\"\"\"\n", "        session = requests.Session()\n", "        retry = Retry(\n", "            total=5,\n", "            backoff_factor=0.5,\n", "            status_forcelist=[429, 500, 502, 503, 504]\n", "        )\n", "        session.mount('https://', HTTPAdapter(max_retries=retry))\n", "        return session\n", "    \n", "    def download_tiles(self, bounds):\n", "        \"\"\"下载卫星图像瓦片\"\"\"\n", "        try:\n", "            print(\"\\n=== 下载瓦片调试信息 ===\")\n", "            print(\"输入边界 (minlon,minlat,maxlon,maxlat):\", bounds)\n", "            \n", "            # 打印转换后的边界\n", "            print(\"\\n转换后的边界:\")\n", "            print(f\"西经: {bounds[0]:.6f}\")\n", "            print(f\"南纬: {bounds[1]:.6f}\")\n", "            print(f\"东经: {bounds[2]:.6f}\")\n", "            print(f\"北纬: {bounds[3]:.6f}\")\n", "            \n", "            # 计算需要下载的瓦片\n", "            west, south, east, north = bounds\n", "            tiles = list(mercantile.tiles(\n", "                west=west,\n", "                south=south,\n", "                east=east,\n", "                north=north,\n", "                zooms=[ZOOM_LEVEL]  # 使用zooms参数代替zoom\n", "            ))\n", "            \n", "            print(f\"\\n需要下载的瓦片数量: {len(tiles)}\")\n", "            if len(tiles) > MAX_TILES:\n", "                raise Exception(f\"瓦片数量({len(tiles)})超过限制({MAX_TILES})\")\n", "            \n", "            # 下载并拼接瓦片\n", "            tile_size = TILE_SIZE\n", "            rows = set(tile.y for tile in tiles)  # 使用y代替row\n", "            cols = set(tile.x for tile in tiles)  # 使用x代替col\n", "            \n", "            total_width = len(cols) * tile_size\n", "            total_height = len(rows) * tile_size\n", "            \n", "            print(f\"拼接图像大小: {total_width}x{total_height}\")\n", "            mosaic = np.zeros((total_height, total_width, 3), dtype=np.uint8)\n", "            \n", "            min_row = min(rows)\n", "            min_col = min(cols)\n", "            \n", "            for tile in tiles:\n", "                # 计算瓦片在拼接图像中的位置\n", "                x = (tile.x - min_col) * tile_size\n", "                y = (tile.y - min_row) * tile_size\n", "                \n", "                # 构建瓦片URL\n", "                server = random.randint(0, 3)  # 随机选择服务器\n", "                url = f\"https://mt{server}.google.com/vt/lyrs=s&x={tile.x}&y={tile.y}&z={tile.z}\"\n", "                \n", "                print(f\"\\n下载瓦片: z={tile.z} x={tile.x} y={tile.y}\")\n", "                print(f\"位置: ({x},{y})\")\n", "                \n", "                # 下载瓦片\n", "                try:\n", "                    response = requests.get(url)\n", "                    if response.status_code == 200:\n", "                        # 将瓦片图像数据转换为numpy数组\n", "                        img_data = np.frombuffer(response.content, np.uint8)\n", "                        img = Image.open(BytesIO(response.content))\n", "                        img_array = np.array(img)\n", "                        \n", "                        if img_array.shape[:2] == (tile_size, tile_size):\n", "                            mosaic[y:y+tile_size, x:x+tile_size] = img_array\n", "                        else:\n", "                            print(f\"警告: 瓦片图像格式不正确\")\n", "                    else:\n", "                        print(f\"警告: 下载瓦片失败，状态码: {response.status_code}\")\n", "                except Exception as e:\n", "                    print(f\"警告: 下载瓦片出错: {e}\")\n", "                    continue\n", "            \n", "            # 保存拼接后的图像\n", "            with rasterio.open(\n", "                'raw.tif',\n", "                'w',\n", "                driver='GTiff',\n", "                height=total_height,\n", "                width=total_width,\n", "                count=3,\n", "                dtype=mosaic.dtype,\n", "                crs='EPSG:4326',  # WGS84\n", "                transform=rasterio.transform.from_bounds(\n", "                    south=bounds[1],  # 最小纬度\n", "                    west=bounds[0],   # 最小经度\n", "                    north=bounds[3],  # 最大纬度\n", "                    east=bounds[2],   # 最大经度\n", "                    width=total_width,\n", "                    height=total_height\n", "                )\n", "            ) as dst:\n", "                # 写入RGB通道\n", "                for i in range(3):\n", "                    dst.write(mosaic[:, :, i], i + 1)\n", "            \n", "            print(\"\\n=== 下载完成 ===\")\n", "            return mosaic\n", "            \n", "        except Exception as e:\n", "            print(f\"下载瓦片失败: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            return None\n", "\n", "class ImageProcessor:\n", "    \"\"\"图像处理器\"\"\"\n", "    @staticmethod\n", "    def prepare_inference_data(coords):\n", "        \"\"\"准备推理数据\"\"\"\n", "        try:\n", "            print(\"\\n=== 准备推理数据 ===\")\n", "            # 计算边界框\n", "            lons = [p[0] for p in coords]  # 经度\n", "            lats = [p[1] for p in coords]  # 纬度\n", "            \n", "            # 交换经纬度顺序\n", "            bounds = [\n", "                min(lats),  # 最小纬度\n", "                min(lons),  # 最小经度\n", "                max(lats),  # 最大纬度\n", "                max(lons)   # 最大经度\n", "            ]\n", "            \n", "            print(\"输入坐标:\")\n", "            for i, (lon, lat) in enumerate(coords):\n", "                print(f\"点{i+1}: 经度={lon:.6f}, 纬度={lat:.6f}\")\n", "            \n", "            print(\"\\n边界框 (minlat,minlon,maxlat,maxlon):\", bounds)\n", "            \n", "            # 计算中心点\n", "            center_lat = (bounds[0] + bounds[2]) / 2\n", "            center_lon = (bounds[1] + bounds[3]) / 2\n", "            print(f\"中心点: 纬度={center_lat:.6f}, 经度={center_lon:.6f}\")\n", "            \n", "            # 下载卫星图像，注意：需要将边界框转换回[minlon,minlat,maxlon,maxlat]格式\n", "            bounds_converted = [\n", "                bounds[1],  # min<PERSON> (最小经度)\n", "                bounds[0],  # min<PERSON> (最小纬度)\n", "                bounds[3],  # maxlon (最大经度)\n", "                bounds[2]   # maxlat (最大纬度)\n", "            ]\n", "            \n", "            print(\"\\n转换后边界框 (minlon,minlat,maxlon,maxlat):\", bounds_converted)\n", "            \n", "            downloader = SatelliteDownloader()\n", "            mosaic = downloader.download_tiles(bounds_converted)\n", "            if mosaic is None:\n", "                raise Exception(\"下载卫星图像失败\")\n", "            \n", "            print(\"=== 准备数据完成 ===\\n\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"准备推理数据失败: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            return False\n", "\n", "class ModelInference:\n", "    \"\"\"模型推理\"\"\"\n", "    def __init__(self):\n", "        self.model = None\n", "        self.processor = None\n", "    \n", "    def setup_model(self):\n", "        \"\"\"初始化模型\"\"\"\n", "        try:\n", "            if not os.path.exists(MODEL_NAME):\n", "                print(\"开始下载模型...\")\n", "                cache_dir = snapshot_download(\n", "                    repo_id=MODEL_NAME,\n", "                    local_dir=MODEL_NAME,\n", "                    token=HF_TOKEN\n", "                )\n", "            \n", "            self.model = SegformerForSemanticSegmentation.from_pretrained(\n", "                MODEL_NAME\n", "            ).to(DEVICE)\n", "            self.processor = SegformerImageProcessor.from_pretrained(MODEL_NAME)\n", "            print(\"模型初始化完成\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"模型初始化失败: {e}\")\n", "            return False\n", "    \n", "    def run_inference(self):\n", "        \"\"\"运行推理\"\"\"\n", "        try:\n", "            # 读取图像\n", "            with rasterio.open(\"raw.tif\") as src:\n", "                img = src.read([1, 2, 3]).transpose(1, 2, 0)\n", "                height, width = img.shape[:2]\n", "                print(f\"原始图像大小: {width}x{height}\")\n", "                \n", "                # 获取原始变换矩阵\n", "                orig_transform = src.transform\n", "                \n", "                # 创建新的变换矩阵，交换经纬度顺序\n", "                new_transform = rasterio.Affine(\n", "                    orig_transform.a,  # a\n", "                    orig_transform.b,  # b\n", "                    orig_transform.c,  # c\n", "                    orig_transform.d,  # d\n", "                    orig_transform.e,  # e\n", "                    orig_transform.f   # f\n", "                )\n", "                \n", "                print(\"原始变换矩阵:\", orig_transform)\n", "                print(\"修正后变换矩阵:\", new_transform)\n", "                \n", "                # 获取图像边界\n", "                bounds = src.bounds\n", "                print(\"\\n图像边界:\")\n", "                print(f\"西经: {bounds.left:.6f}\")\n", "                print(f\"东经: {bounds.right:.6f}\")\n", "                print(f\"南纬: {bounds.bottom:.6f}\")\n", "                print(f\"北纬: {bounds.top:.6f}\")\n", "                \n", "                # 分块处理\n", "                block_size = 640  # 模型输入大小\n", "                overlap = 32      # 重叠区域大小\n", "                pred_mask = np.zeros((height, width), dtype=np.uint8)\n", "                \n", "                for y in range(0, height, block_size - overlap):\n", "                    for x in range(0, width, block_size - overlap):\n", "                        # 提取块\n", "                        y_end = min(y + block_size, height)\n", "                        x_end = min(x + block_size, width)\n", "                        block = img[y:y_end, x:x_end]\n", "                        \n", "                        print(f\"\\n处理块: ({x},{y}) -> ({x_end},{y_end})\")\n", "                        print(f\"块原始大小: {block.shape}\")\n", "                        \n", "                        # 调整块大小为640x640\n", "                        if block.shape[:2] != (block_size, block_size):\n", "                            block_resized = resize(\n", "                                block,\n", "                                (block_size, block_size, 3),\n", "                                preserve_range=True\n", "                            ).astype(np.uint8)\n", "                        else:\n", "                            block_resized = block\n", "                            \n", "                        print(f\"调整后块大小: {block_resized.shape}\")\n", "                        \n", "                        # 处理块\n", "                        inputs = self.processor(\n", "                            images=block_resized,\n", "                            return_tensors=\"pt\"\n", "                        ).to(DEVICE)\n", "                        \n", "                        with torch.no_grad():\n", "                            outputs = self.model(**inputs)\n", "                            logits = outputs.logits\n", "                            print(f\"模型输出大小: {logits.shape}\")\n", "                            pred = logits.argmax(1).cpu().numpy()[0]\n", "                            print(f\"预测结果大小: {pred.shape}\")\n", "                        \n", "                        # 将预测结果调整回原始块大小\n", "                        if pred.shape != block.shape[:2]:\n", "                            pred_resized = resize(\n", "                                pred,\n", "                                block.shape[:2],\n", "                                preserve_range=True,\n", "                                order=0  # 最近邻插值，保持标签值\n", "                            ).astype(np.uint8)\n", "                        else:\n", "                            pred_resized = pred\n", "                            \n", "                        print(f\"调整回原始大小: {pred_resized.shape}\")\n", "                        \n", "                        # 计算有效区域\n", "                        valid_h = min(block_size - overlap, y_end - y)\n", "                        valid_w = min(block_size - overlap, x_end - x)\n", "                        \n", "                        # 确保尺寸匹配\n", "                        write_h = min(valid_h, pred_resized.shape[0])\n", "                        write_w = min(valid_w, pred_resized.shape[1])\n", "                        \n", "                        print(f\"写入区域: ({write_h},{write_w})\")\n", "                        \n", "                        # 将结果写回，只写入非重叠区域\n", "                        pred_mask[y:y+write_h, x:x+write_w] = pred_resized[:write_h, :write_w]\n", "                \n", "                print(f\"\\n预测掩码最终大小: {pred_mask.shape}\")\n", "                \n", "                # 保存结果\n", "                meta = src.meta.copy()\n", "                meta.update({\n", "                    'count': 1,\n", "                    'dtype': 'uint8',\n", "                    'transform': new_transform,  # 使用修正后的变换矩阵\n", "                    'crs': src.crs              # 保持原始坐标系统\n", "                })\n", "                \n", "                with rasterio.open('pred_mask.tif', 'w', **meta) as dst:\n", "                    dst.write(pred_mask, 1)\n", "                \n", "                return pred_mask, new_transform\n", "                \n", "        except Exception as e:\n", "            print(f\"推理失败: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            return None, None\n", "\n", "class ResultProcessor:\n", "    \"\"\"结果处理器\"\"\"\n", "    \n", "    def __init__(self):\n", "        # ADE20K数据集的类别映射\n", "        self.class_mapping = {\n", "            0: \"背景\",\n", "            1: \"建筑\",\n", "            2: \"道路\",\n", "            3: \"植被\",\n", "            4: \"水体\",\n", "            \n", "            # 可以根据需要添加更多类别\n", "        }\n", "    \n", "    def mask_to_shapefile(self, mask, transform, output_path=\"prediction.shp\"):\n", "        \"\"\"将预测掩码转换为shapefile\n", "        \n", "        Args:\n", "            mask: 预测掩码\n", "            transform: 地理变换矩阵\n", "            output_path: 输出shapefile路径\n", "        \"\"\"\n", "        try:\n", "            print(\"\\n=== 转换预测结果为Shapefile ===\")\n", "            print(\"变换矩阵:\", transform)\n", "            \n", "            # 创建GeoDataFrame\n", "            features = []\n", "            for class_id in np.unique(mask):\n", "                if class_id == 0:  # 跳过背景\n", "                    continue\n", "                    \n", "                print(f\"\\n处理类别 {class_id} ({self.class_mapping.get(int(class_id), '未知')})\")\n", "                \n", "                # 获取当前类别的掩码\n", "                class_mask = mask == class_id\n", "                \n", "                # 提取轮廓\n", "                shapes = rasterio.features.shapes(\n", "                    class_mask.astype(np.uint8),\n", "                    mask=class_mask,\n", "                    transform=transform\n", "                )\n", "                \n", "                # 转换为GeoJSON特征\n", "                for geom, _ in shapes:\n", "                    print(\"\\n原始几何形状的第一个坐标:\", geom['coordinates'][0][0])\n", "                    \n", "                    # 直接使用原始几何形状\n", "                    new_geom = geom\n", "                    \n", "                    # 添加属性信息\n", "                    feature = {\n", "                        'geometry': new_geom,\n", "                        'properties': {\n", "                            'class_id': int(class_id),\n", "                            'class_name': self.class_mapping.get(int(class_id), \"未知\")\n", "                        }\n", "                    }\n", "                    features.append(feature)\n", "                    \n", "                    # 输出调试信息\n", "                    coords = new_geom['coordinates'][0]\n", "                    print(\"\\n多边形坐标:\")\n", "                    for i, coord in enumerate(coords[:4]):  # 只显示前4个点\n", "                        print(f\"点{i+1}: ({coord[0]}, {coord[1]})\")\n", "            \n", "            if not features:\n", "                print(\"警告: 未找到任何有效地物\")\n", "                return False\n", "            \n", "            # 创建GeoDataFrame\n", "            gdf = gpd.GeoDataFrame.from_features(features)\n", "            \n", "            # 确保使用正确的坐标系统\n", "            gdf.crs = 'EPSG:4326'\n", "            \n", "            # 输出更多调试信息\n", "            print(\"\\n第一个要素的信息:\")\n", "            first_geom = gdf.geometry.iloc[0]\n", "            print(f\"类型: {first_geom.geom_type}\")\n", "            print(f\"坐标系统: {gdf.crs}\")\n", "            print(f\"边界框: {first_geom.bounds}\")\n", "            print(f\"第一个坐标: {list(first_geom.exterior.coords)[0]}\")\n", "            print(f\"最后一个坐标: {list(first_geom.exterior.coords)[-1]}\")\n", "            \n", "            # 保存为shapefile\n", "            gdf.to_file(output_path)\n", "            \n", "            # 输出统计信息\n", "            print(\"\\n地物统计:\")\n", "            for class_id, group in gdf.groupby('class_id'):\n", "                class_name = self.class_mapping.get(int(class_id), \"未知\")\n", "                feature_count = len(group)\n", "                print(f\"类别 {class_id} ({class_name}):\")\n", "                print(f\"  - 要素数量: {feature_count}\")\n", "            \n", "            print(f\"\\nShapefile已保存至: {output_path}\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"转换Shapefile失败: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            return False\n", "\n", "def main():\n", "    \"\"\"主函数\"\"\"\n", "    # 1. 读取坐标\n", "    filename='/teamspace/studios/this_studio/selected_area_coords_20250403_101054.txt'\n", "    coords = CoordinateReader.read_coords(filename)\n", "    if coords is None:\n", "        return\n", "    \n", "    # 2. 准备数据\n", "    if not ImageProcessor.prepare_inference_data(coords):\n", "        return\n", "    \n", "    # 3. 初始化模型\n", "    model = ModelInference()\n", "    if not model.setup_model():\n", "        return\n", "    \n", "    # 4. 运行推理\n", "    pred_mask, transform = model.run_inference()\n", "    if pred_mask is None:\n", "        return\n", "        \n", "    # 5. 转换为shapefile\n", "    processor = ResultProcessor()\n", "    if not processor.mask_to_shapefile(pred_mask, transform):\n", "        return\n", "    \n", "    print(\"处理完成\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}